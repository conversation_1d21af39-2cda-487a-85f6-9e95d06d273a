› Planning build
› Preparing Pods/FirebaseCoreInternal-FirebaseCoreInternal_Privacy » ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist
› Preparing Pods/expo-dev-menu-EXDevMenu » ResourceBundle-EXDevMenu-expo-dev-menu-Info.plist
› Preparing Pods/TOCropViewController-TOCropViewControllerBundle » ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist
› Preparing Pods/RNDeviceInfo-RNDeviceInfoPrivacyInfo » ResourceBundle-RNDeviceInfoPrivacyInfo-RNDeviceInfo-Info.plist
› Preparing Pods/PromisesObjC-FBLPromises_Privacy » ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist
› Preparing Pods/Sentry-Sentry » ResourceBundle-Sentry-Sentry-Info.plist
› Preparing react-native Pods/React-Core-AccessibilityResources » ResourceBundle-AccessibilityResources-React-Core-Info.plist
› Preparing expo-updates Pods/ReachabilitySwift-ReachabilitySwift » ResourceBundle-ReachabilitySwift-ReachabilitySwift-Info.plist
› Preparing Pods/GoogleUtilities-GoogleUtilities_Privacy » ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist
› Preparing Pods/GoogleDataTransport-GoogleDataTransport_Privacy » ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist
› Preparing Pods/FirebaseInstallations-FirebaseInstallations_Privacy » ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist
› Preparing Pods/expo-dev-launcher-EXDevLauncher » ResourceBundle-EXDevLauncher-expo-dev-launcher-Info.plist
› Preparing Pods/RNImageCropPicker-QBImagePicker » ResourceBundle-QBImagePicker-RNImageCropPicker-Info.plist
› Preparing expo-updates Pods/EXUpdates-EXUpdates » ResourceBundle-EXUpdates-EXUpdates-Info.plist
› Preparing expo-constants Pods/EXConstants-EXConstants » ResourceBundle-EXConstants-EXConstants-Info.plist
› Compiling react-native Pods/React-Core » NSDataBigString.mm
› Compiling react-native Pods/React-Core » RCTUIManagerObserverCoordinator.mm
› Compiling react-native Pods/React-Core » RCTSurfaceView.mm
› Compiling react-native Pods/React-Core » RCTSurfaceSizeMeasureMode.mm
› Compiling react-native Pods/React-Core » RCTSurfaceRootView.mm
› Compiling react-native Pods/React-Core » RCTSurfaceHostingView.mm
› Compiling react-native Pods/React-Core » RCTSurfaceHostingProxyRootView.mm
› Compiling react-native Pods/React-Core » RCTSurface.mm
› Compiling react-native Pods/React-Core » RCTRuntimeExecutorFromBridge.mm
› Compiling react-native Pods/React-Core » RCTPackagerConnection.mm
› Compiling react-native Pods/React-Core » RCTObjcExecutor.mm
› Compiling react-native Pods/React-Core » RCTNativeModule.mm
› Compiling react-native Pods/React-Core » RCTModuleMethod.mm
› Compiling react-native Pods/React-Core » RCTModuleData.mm
› Compiling react-native Pods/React-Core » RCTMessageThread.mm
› Compiling react-native Pods/React-Core » RCTManagedPointer.mm
› Compiling react-native Pods/React-Core » RCTLog.mm
› Compiling react-native Pods/React-Core » RCTJavaScriptLoader.mm
› Compiling react-native Pods/React-Core » RCTJSIExecutorRuntimeInstaller.mm
› Compiling react-native Pods/React-Core » RCTInspectorDevServerHelper.mm
› Compiling react-native Pods/React-Core » RCTInspector.mm
› Compiling react-native Pods/React-Core » RCTFont.mm
› Compiling react-native Pods/React-Core » RCTFollyConvert.mm
› Compiling react-native Pods/React-Core » RCTDefaultCxxLogFunction.mm
› Compiling react-native Pods/React-Core » RCTCxxUtils.mm
› Compiling react-native Pods/React-Core » RCTCxxModule.mm
› Compiling react-native Pods/React-Core » RCTCxxMethod.mm
› Compiling react-native Pods/React-Core » RCTCxxBridge.mm
› Compiling react-native Pods/React-Core » RCTBundleURLProvider.mm
› Compiling react-native Pods/React-Core » React-Core-dummy.m
› Compiling react-native Pods/React-Core » RCTConstants.m
› Compiling react-native Pods/React-Core » RCTErrorInfo.m
› Compiling react-native Pods/React-Core » RCTCxxConvert.m
› Compiling react-native Pods/React-Core » RCTDisplayLink.m
› Compiling react-native Pods/React-Core » RCTJSThread.m
› Compiling react-native Pods/React-Core » RCTReloadCommand.m
› Compiling react-native Pods/React-Core » RCTRedBoxSetEnabled.m
› Compiling react-native Pods/React-Core » RCTSafeAreaViewLocalData.m
› Compiling react-native Pods/React-Core » RCTRefreshControl.m
› Compiling react-native Pods/React-Core » RCTScrollEvent.m
› Compiling react-native Pods/React-Core » RCTVersion.m
› Compiling react-native Pods/React-Core » RCTUtilsUIOverride.m
› Compiling react-native Pods/React-Core » RCTUIUtils.m
› Compiling react-native Pods/React-Core » RCTUtils.m
› Compiling react-native Pods/React-Core » RCTUIManagerUtils.m
› Compiling react-native Pods/React-Core » RCTTouchEvent.m
› Compiling react-native Pods/React-Core » RCTSurfaceStage.m
› Compiling react-native Pods/React-Core » RCTSurfacePresenterStub.m
› Compiling react-native Pods/React-Core » RCTRedBoxExtraDataViewController.m
› Compiling react-native Pods/React-Core » RCTPerformanceLoggerLabels.m
› Compiling react-native Pods/React-Core » RCTPerformanceLogger.m
› Compiling react-native Pods/React-Core » RCTParserUtils.m
› Compiling react-native Pods/React-Core » RCTPackagerClient.m
› Compiling react-native Pods/React-Core » RCTMultipartStreamReader.m
› Compiling react-native Pods/React-Core » RCTMultipartDataTask.m
› Compiling react-native Pods/React-Core » RCTModuleRegistry.m
› Compiling react-native Pods/React-Core » RCTModalManager.m
› Compiling react-native Pods/React-Core » RCTKeyCommands.m
› Compiling react-native Pods/React-Core » RCTJSStackFrame.m
› Compiling react-native Pods/React-Core » RCTInspectorPackagerConnection.m
› Compiling react-native Pods/React-Core » RCTI18nUtil.m
› Compiling react-native Pods/React-Core » RCTFrameUpdate.m
› Compiling react-native Pods/React-Core » RCTEventEmitter.m
› Compiling react-native Pods/React-Core » RCTEventDispatcher.m
› Compiling react-native Pods/React-Core » RCTDevLoadingViewSetEnabled.m
› Compiling react-native Pods/React-Core » RCTComponentEvent.m
› Compiling react-native Pods/React-Core » RCTCallableJSModules.m
› Compiling react-native Pods/React-Core » RCTBundleManager.m
› Compiling react-native Pods/React-Core » RCTBridgeModuleDecorator.m
› Compiling react-native Pods/React-Core » RCTBridgeConstants.m
› Compiling react-native Pods/React-Core » RCTBorderDrawing.m
› Compiling react-native Pods/React-Core » RCTAssert.m
› Compiling react-native Pods/React-Core » RCTActivityIndicatorView.m
› Compiling react-native Pods/React-Core » RCTView.m
› Compiling react-native Pods/React-Core » RCTSwitch.m
› Compiling react-native Pods/React-Core » RCTWrapperViewController.m
› Compiling react-native Pods/React-Core » RCTViewUtils.m
› Compiling react-native Pods/React-Core » RCTScrollContentView.m
› Compiling react-native Pods/React-Core » RCTConvert.m
› Compiling react-native Pods/React-Core » RCTLayoutAnimation.m
› Compiling react-native Pods/React-Core » RCTConvert+Transform.m
› Compiling react-native Pods/React-Core » RCTSegmentedControlManager.m
› Compiling react-native Pods/React-Core » RCTImageSource.m
› Compiling react-native Pods/React-Core » RCTViewRegistry.m
› Compiling react-native Pods/React-Core » UIView+React.m
› Compiling react-native Pods/React-Core » RCTViewManager.m
› Compiling react-native Pods/React-Core » RCTUIManager.m
› Compiling react-native Pods/React-Core » RCTTouchHandler.m
› Compiling react-native Pods/React-Core » RCTSwitchManager.m
› Compiling react-native Pods/React-Core » RCTSurfaceRootShadowView.m
› Compiling react-native Pods/React-Core » RCTShadowView.m
› Compiling react-native Pods/React-Core » RCTShadowView+Layout.m
› Compiling react-native Pods/React-Core » RCTShadowView+Internal.m
› Compiling react-native Pods/React-Core » RCTSegmentedControl.m
› Compiling react-native Pods/React-Core » RCTScrollViewManager.m
› Compiling react-native Pods/React-Core » RCTScrollView.m
› Compiling react-native Pods/React-Core » RCTScrollContentViewManager.m
› Compiling react-native Pods/React-Core » RCTScrollContentShadowView.m
› Compiling react-native Pods/React-Core » RCTSafeAreaViewManager.m
› Compiling react-native Pods/React-Core » RCTSafeAreaView.m
› Compiling react-native Pods/React-Core » RCTSafeAreaShadowView.m
› Compiling react-native Pods/React-Core » RCTRootView.m
› Compiling react-native Pods/React-Core » RCTRootShadowView.m
› Compiling react-native Pods/React-Core » RCTRootContentView.m
› Compiling react-native Pods/React-Core » RCTRefreshControlManager.m
› Compiling react-native Pods/React-Core » RCTReconnectingWebSocket.m
› Compiling react-native Pods/React-Core » RCTProfile.m
› Compiling react-native Pods/React-Core » RCTModalHostViewManager.m
› Compiling react-native Pods/React-Core » RCTModalHostViewController.m
› Compiling react-native Pods/React-Core » RCTModalHostView.m
› Compiling react-native Pods/React-Core » RCTLayoutAnimationGroup.m
› Compiling react-native Pods/React-Core » RCTLayout.m
› Compiling react-native Pods/React-Core » RCTConvert+CoreLocation.m
› Compiling react-native Pods/React-Core » RCTComponentData.m
› Compiling react-native Pods/React-Core » RCTBridge.m
› Compiling react-native Pods/React-Core » RCTActivityIndicatorViewManager.m
› Packaging react-native Pods/React-Core » libReact-Core.a
    Run script build phase '[CP-User] Generate app.manifest for expo-updates' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'EXUpdates' from project 'Pods')
› Executing expo-updates Pods/EXUpdates » [CP-User] Generate app.manifest for expo-updates
› Executing expo-constants Pods/EXConstants » [CP-User] Generate app.config for prebuilt Constants.manifest
    Run script build phase 'Start Packager' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'riverbank' from project 'riverbank')
    Run script build phase 'Bundle React Native code and images' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'riverbank' from project 'riverbank')
    Run script build phase '[CP-User] [RNFB] Core Configuration' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'riverbank' from project 'riverbank')
› Executing riverbank » Start Packager on http://localhost:8083
› Executing riverbank » [Expo] Configure project
› Preparing riverbank » Info.plist
    Run script build phase '[CP-User] Generate app.config for prebuilt Constants.manifest' will be run during every build because it does not specify any outputs. To address this issue, either add output dependencies to the script phase, or configure it to run in every build by unchecking "Based on dependency analysis" in the script phase. (in target 'EXConstants' from project 'Pods')

› 0 error(s), and 5 warning(s)

CommandError: Failed to build iOS project. "xcodebuild" exited with error code 65.
To view more error logs, try building the app with Xcode directly, by opening /Users/<USER>/riverbank_mobile/ios/riverbank.xcworkspace.

Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -workspace /Users/<USER>/riverbank_mobile/ios/riverbank.xcworkspace -configuration Debug -scheme riverbank -destination id=9010677A-6FFC-400D-8C13-F5C622D872F1

User defaults from command line:
    IDEPackageSupportUseBuiltinSCM = YES

Prepare packages

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (127 targets)
    Target 'riverbank' in project 'riverbank'
        ➜ Implicit dependency on target 'Pods-riverbank' in project 'Pods' via file 'libPods-riverbank.a' in build phase 'Link Binary'
        ➜ Implicit dependency on target 'Google-Maps-iOS-Utils' in project 'Pods' via options '-lGoogle-Maps-iOS-Utils' in build setting 'OTHER_LDFLAGS'
    Target 'Pods-riverbank' in project 'Pods'
        ➜ Explicit dependency on target 'ASN1Decoder' in project 'Pods'
        ➜ Explicit dependency on target 'BVLinearGradient' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'EASClient' in project 'Pods'
        ➜ Explicit dependency on target 'EXApplication' in project 'Pods'
        ➜ Explicit dependency on target 'EXCamera' in project 'Pods'
        ➜ Explicit dependency on target 'EXConstants' in project 'Pods'
        ➜ Explicit dependency on target 'EXFileSystem' in project 'Pods'
        ➜ Explicit dependency on target 'EXFont' in project 'Pods'
        ➜ Explicit dependency on target 'EXImageLoader' in project 'Pods'
        ➜ Explicit dependency on target 'EXJSONUtils' in project 'Pods'
        ➜ Explicit dependency on target 'EXLocation' in project 'Pods'
        ➜ Explicit dependency on target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'EXNotifications' in project 'Pods'
        ➜ Explicit dependency on target 'EXSplashScreen' in project 'Pods'
        ➜ Explicit dependency on target 'EXStructuredHeaders' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdates' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdatesInterface' in project 'Pods'
        ➜ Explicit dependency on target 'Expo' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoDevice' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoDocumentPicker' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoImagePicker' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoKeepAwake' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoWebBrowser' in project 'Pods'
        ➜ Explicit dependency on target 'FBLazyVector' in project 'Pods'
        ➜ Explicit dependency on target 'FBReactNativeSpec' in project 'Pods'
        ➜ Explicit dependency on target 'Firebase' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseMessaging' in project 'Pods'
        ➜ Explicit dependency on target 'Google-Maps-iOS-Utils' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleMaps' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
        ➜ Explicit dependency on target 'PromisesObjC' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'RNCAsyncStorage' in project 'Pods'
        ➜ Explicit dependency on target 'RNCClipboard' in project 'Pods'
        ➜ Explicit dependency on target 'RNCMaskedView' in project 'Pods'
        ➜ Explicit dependency on target 'RNDateTimePicker' in project 'Pods'
        ➜ Explicit dependency on target 'RNDeviceInfo' in project 'Pods'
        ➜ Explicit dependency on target 'RNFBApp' in project 'Pods'
        ➜ Explicit dependency on target 'RNFBMessaging' in project 'Pods'
        ➜ Explicit dependency on target 'RNFS' in project 'Pods'
        ➜ Explicit dependency on target 'RNGestureHandler' in project 'Pods'
        ➜ Explicit dependency on target 'RNImageCropPicker' in project 'Pods'
        ➜ Explicit dependency on target 'RNInAppBrowser' in project 'Pods'
        ➜ Explicit dependency on target 'RNReanimated' in project 'Pods'
        ➜ Explicit dependency on target 'RNSVG' in project 'Pods'
        ➜ Explicit dependency on target 'RNScreens' in project 'Pods'
        ➜ Explicit dependency on target 'RNSentry' in project 'Pods'
        ➜ Explicit dependency on target 'ReachabilitySwift' in project 'Pods'
        ➜ Explicit dependency on target 'React' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-CoreModules' in project 'Pods'
        ➜ Explicit dependency on target 'React-NativeModulesApple' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTActionSheet' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAnimation' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAppDelegate' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTBlob' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTLinking' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTSettings' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTText' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTVibration' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-debug' in project 'Pods'
        ➜ Explicit dependency on target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsinspector' in project 'Pods'
        ➜ Explicit dependency on target 'React-logger' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'React-rncore' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimescheduler' in project 'Pods'
        ➜ Explicit dependency on target 'React-utils' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'Sentry' in project 'Pods'
        ➜ Explicit dependency on target 'SocketRocket' in project 'Pods'
        ➜ Explicit dependency on target 'TOCropViewController' in project 'Pods'
        ➜ Explicit dependency on target 'Yoga' in project 'Pods'
        ➜ Explicit dependency on target 'boost' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-client' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-launcher' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu-interface' in project 'Pods'
        ➜ Explicit dependency on target 'fmt' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
        ➜ Explicit dependency on target 'libevent' in project 'Pods'
        ➜ Explicit dependency on target 'lottie-ios' in project 'Pods'
        ➜ Explicit dependency on target 'lottie-react-native' in project 'Pods'
        ➜ Explicit dependency on target 'nanopb' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-google-maps' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-maps' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-netinfo' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-pager-view' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-render-html' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-safe-area-context' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-video' in project 'Pods'
        ➜ Explicit dependency on target 'react-native-webview' in project 'Pods'
    Target 'react-native-webview' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-video' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-safe-area-context' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'react-native-render-html' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-pager-view' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-netinfo' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-maps' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'react-native-google-maps' in project 'Pods'
        ➜ Explicit dependency on target 'Google-Maps-iOS-Utils' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleMaps' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'lottie-react-native' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'lottie-ios' in project 'Pods'
    Target 'lottie-ios' in project 'Pods' (no dependencies)
    Target 'expo-dev-client' in project 'Pods'
        ➜ Explicit dependency on target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-launcher' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu-interface' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdatesInterface' in project 'Pods'
    Target 'expo-dev-launcher' in project 'Pods'
        ➜ Explicit dependency on target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdatesInterface' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAppDelegate' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-launcher-EXDevLauncher' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu-interface' in project 'Pods'
    Target 'expo-dev-menu' in project 'Pods'
        ➜ Explicit dependency on target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu-EXDevMenu' in project 'Pods'
        ➜ Explicit dependency on target 'expo-dev-menu-interface' in project 'Pods'
    Target 'expo-dev-menu-interface' in project 'Pods' (no dependencies)
    Target 'expo-dev-menu-EXDevMenu' in project 'Pods' (no dependencies)
    Target 'expo-dev-launcher-EXDevLauncher' in project 'Pods' (no dependencies)
    Target 'React' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTActionSheet' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAnimation' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTBlob' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTLinking' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTSettings' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTText' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTVibration' in project 'Pods'
    Target 'React-RCTVibration' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'RNSentry' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'Sentry' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'Sentry' in project 'Pods'
        ➜ Explicit dependency on target 'Sentry-Sentry' in project 'Pods'
    Target 'Sentry-Sentry' in project 'Pods' (no dependencies)
    Target 'RNScreens' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
    Target 'RNSVG' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNReanimated' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'FBLazyVector' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-CoreModules' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTActionSheet' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAnimation' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAppDelegate' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTBlob' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTLinking' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTSettings' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTText' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsinspector' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'Yoga' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-RCTText' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'React-RCTSettings' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-RCTLinking' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-RCTAnimation' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-RCTActionSheet' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNInAppBrowser' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNImageCropPicker' in project 'Pods'
        ➜ Explicit dependency on target 'RNImageCropPicker-QBImagePicker' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'TOCropViewController' in project 'Pods'
    Target 'TOCropViewController' in project 'Pods'
        ➜ Explicit dependency on target 'TOCropViewController-TOCropViewControllerBundle' in project 'Pods'
    Target 'TOCropViewController-TOCropViewControllerBundle' in project 'Pods' (no dependencies)
    Target 'RNImageCropPicker-QBImagePicker' in project 'Pods' (no dependencies)
    Target 'RNGestureHandler' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNFS' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNFBMessaging' in project 'Pods'
        ➜ Explicit dependency on target 'Firebase' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Pods'
        ➜ Explicit dependency on target 'RNFBApp' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNFBApp' in project 'Pods'
        ➜ Explicit dependency on target 'Firebase' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNDeviceInfo' in project 'Pods'
        ➜ Explicit dependency on target 'RNDeviceInfo-RNDeviceInfoPrivacyInfo' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNDeviceInfo-RNDeviceInfoPrivacyInfo' in project 'Pods' (no dependencies)
    Target 'RNDateTimePicker' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNCMaskedView' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNCClipboard' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'RNCAsyncStorage' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'Google-Maps-iOS-Utils' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleMaps' in project 'Pods'
    Target 'GoogleMaps' in project 'Pods' (no dependencies)
    Target 'FirebaseCoreExtension' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Pods'
    Target 'Firebase' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseMessaging' in project 'Pods'
    Target 'FirebaseMessaging' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
        ➜ Explicit dependency on target 'nanopb' in project 'Pods'
    Target 'GoogleDataTransport' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleDataTransport-GoogleDataTransport_Privacy' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
        ➜ Explicit dependency on target 'PromisesObjC' in project 'Pods'
        ➜ Explicit dependency on target 'nanopb' in project 'Pods'
    Target 'nanopb' in project 'Pods' (no dependencies)
    Target 'GoogleDataTransport-GoogleDataTransport_Privacy' in project 'Pods' (no dependencies)
    Target 'FirebaseInstallations' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseInstallations-FirebaseInstallations_Privacy' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
        ➜ Explicit dependency on target 'PromisesObjC' in project 'Pods'
    Target 'FirebaseInstallations-FirebaseInstallations_Privacy' in project 'Pods' (no dependencies)
    Target 'FirebaseCore' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
    Target 'FirebaseCoreInternal' in project 'Pods'
        ➜ Explicit dependency on target 'FirebaseCoreInternal-FirebaseCoreInternal_Privacy' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities' in project 'Pods'
    Target 'GoogleUtilities' in project 'Pods'
        ➜ Explicit dependency on target 'GoogleUtilities-GoogleUtilities_Privacy' in project 'Pods'
        ➜ Explicit dependency on target 'PromisesObjC' in project 'Pods'
    Target 'PromisesObjC' in project 'Pods'
        ➜ Explicit dependency on target 'PromisesObjC-FBLPromises_Privacy' in project 'Pods'
    Target 'PromisesObjC-FBLPromises_Privacy' in project 'Pods' (no dependencies)
    Target 'GoogleUtilities-GoogleUtilities_Privacy' in project 'Pods' (no dependencies)
    Target 'FirebaseCoreInternal-FirebaseCoreInternal_Privacy' in project 'Pods' (no dependencies)
    Target 'ExpoWebBrowser' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'ExpoKeepAwake' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'ExpoImagePicker' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'ExpoDocumentPicker' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'ExpoDevice' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'Expo' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXUpdates' in project 'Pods'
        ➜ Explicit dependency on target 'ASN1Decoder' in project 'Pods'
        ➜ Explicit dependency on target 'EASClient' in project 'Pods'
        ➜ Explicit dependency on target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'EXStructuredHeaders' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdates-EXUpdates' in project 'Pods'
        ➜ Explicit dependency on target 'EXUpdatesInterface' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'ReachabilitySwift' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'ReachabilitySwift' in project 'Pods'
        ➜ Explicit dependency on target 'ReachabilitySwift-ReachabilitySwift' in project 'Pods'
    Target 'ReachabilitySwift-ReachabilitySwift' in project 'Pods' (no dependencies)
    Target 'EXUpdatesInterface' in project 'Pods' (no dependencies)
    Target 'EXUpdates-EXUpdates' in project 'Pods' (no dependencies)
    Target 'EXStructuredHeaders' in project 'Pods' (no dependencies)
    Target 'EXSplashScreen' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'EXNotifications' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXManifests' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXLocation' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXJSONUtils' in project 'Pods' (no dependencies)
    Target 'EXImageLoader' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'EXFont' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXFileSystem' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXConstants' in project 'Pods'
        ➜ Explicit dependency on target 'EXConstants-EXConstants' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXConstants-EXConstants' in project 'Pods' (no dependencies)
    Target 'EXCamera' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EXApplication' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'EASClient' in project 'Pods'
        ➜ Explicit dependency on target 'ExpoModulesCore' in project 'Pods'
    Target 'ExpoModulesCore' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-NativeModulesApple' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTAppDelegate' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-RCTAppDelegate' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-CoreModules' in project 'Pods'
        ➜ Explicit dependency on target 'React-NativeModulesApple' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimescheduler' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-runtimescheduler' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-debug' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-CoreModules' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTBlob' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'SocketRocket' in project 'Pods'
    Target 'React-RCTImage' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-RCTBlob' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-RCTNetwork' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'React-Codegen' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'FBReactNativeSpec' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-NativeModulesApple' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-rncore' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-rncore' in project 'Pods' (no dependencies)
    Target 'FBReactNativeSpec' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
    Target 'RCTTypeSafety' in project 'Pods'
        ➜ Explicit dependency on target 'FBLazyVector' in project 'Pods'
        ➜ Explicit dependency on target 'RCTRequired' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'FBLazyVector' in project 'Pods' (no dependencies)
    Target 'RCTRequired' in project 'Pods' (no dependencies)
    Target 'React-NativeModulesApple' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'ReactCommon' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-logger' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'BVLinearGradient' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core' in project 'Pods'
    Target 'React-Core' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-Core-AccessibilityResources' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsinspector' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-utils' in project 'Pods'
        ➜ Explicit dependency on target 'SocketRocket' in project 'Pods'
        ➜ Explicit dependency on target 'Yoga' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'Yoga' in project 'Pods' (no dependencies)
    Target 'SocketRocket' in project 'Pods' (no dependencies)
    Target 'React-utils' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-debug' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
    Target 'React-hermes' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsinspector' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-jsiexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-cxxreact' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'React-callinvoker' in project 'Pods'
        ➜ Explicit dependency on target 'React-debug' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsinspector' in project 'Pods'
        ➜ Explicit dependency on target 'React-logger' in project 'Pods'
        ➜ Explicit dependency on target 'React-perflogger' in project 'Pods'
        ➜ Explicit dependency on target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'boost' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'React-runtimeexecutor' in project 'Pods'
        ➜ Explicit dependency on target 'React-jsi' in project 'Pods'
    Target 'React-perflogger' in project 'Pods' (no dependencies)
    Target 'React-logger' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
    Target 'React-jsinspector' in project 'Pods' (no dependencies)
    Target 'React-jsi' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'boost' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'hermes-engine' in project 'Pods'
    Target 'hermes-engine' in project 'Pods' (no dependencies)
    Target 'React-debug' in project 'Pods' (no dependencies)
    Target 'React-callinvoker' in project 'Pods' (no dependencies)
    Target 'React-Core-AccessibilityResources' in project 'Pods' (no dependencies)
    Target 'RCT-Folly' in project 'Pods'
        ➜ Explicit dependency on target 'DoubleConversion' in project 'Pods'
        ➜ Explicit dependency on target 'boost' in project 'Pods'
        ➜ Explicit dependency on target 'fmt' in project 'Pods'
        ➜ Explicit dependency on target 'glog' in project 'Pods'
        ➜ Explicit dependency on target 'libevent' in project 'Pods'
    Target 'libevent' in project 'Pods' (no dependencies)
    Target 'glog' in project 'Pods' (no dependencies)
    Target 'fmt' in project 'Pods' (no dependencies)
    Target 'boost' in project 'Pods' (no dependencies)
    Target 'DoubleConversion' in project 'Pods' (no dependencies)
    Target 'ASN1Decoder' in project 'Pods' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x objective-c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -V

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/ibtool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x objective-c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/riverbank_mobile/ios/riverbank/Images.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -x assembler-with-cpp -c /dev/null

Build description signature: 97576f7dffee26f3c32067841e460d29
Build description path: /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/XCBuildData/97576f7dffee26f3c32067841e460d29.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache
    cd /Users/<USER>/riverbank_mobile/ios
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -o /Users/<USER>/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist (in target 'FirebaseCoreInternal-FirebaseCoreInternal_Privacy' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle/Info.plist

WriteAuxiliaryFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp (in target 'React-Core' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    write-file /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp
-target arm64-apple-ios13.0-simulator '-std=c++17' '-stdlib=libc++' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/DerivedData/ModuleCache.noindex' '-fmodule-name=React' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' -D_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION '-DCOCOAPODS=1' '-DRCT_METRO_PORT=8083' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/React-Core-generated-files.hmap -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/React-Core-own-target-headers.hmap -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/React-Core-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/React-Core-project-headers.hmap -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/React-Core/include -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Private -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Private/React-Core -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/DoubleConversion -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/RCT-Folly -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-Core -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-callinvoker -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-cxxreact -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-debug -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-hermes -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-jsi -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-jsiexecutor -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-jsinspector -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-logger -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-perflogger -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-runtimeexecutor -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-utils -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/Yoga -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/fmt -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/glog -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/hermes-engine -I/Users/<USER>/riverbank_mobile/node_modules/react-native/ReactCommon -I/Users/<USER>/riverbank_mobile/ios/Pods/boost -I/Users/<USER>/riverbank_mobile/ios/Pods/DoubleConversion -I/Users/<USER>/riverbank_mobile/ios/Pods/RCT-Folly -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/FlipperKit -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/ReactCommon -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-RCTFabric -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/React-hermes -I/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/hermes-engine -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/DerivedSources-normal/arm64 -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/DerivedSources/arm64 -I/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/React-Core.build/DerivedSources -F/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/React-Core -F/Users/<USER>/riverbank_mobile/ios/Pods/hermes-engine/destroot/Library/Frameworks/universal -F/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/hermes-engine/Pre-built -F/Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/React-hermes '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/DoubleConversion/DoubleConversion.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/SocketRocket/SocketRocket.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/cxxreact/React-cxxreact.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/fmt/fmt.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/folly/RCT-Folly.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/glog/glog.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/jsi/React-jsi.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/jsinspector/React-jsinspector.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/jsireact/React-jsiexecutor.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/libevent/libevent.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/logger/React-logger.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/react_debug/React-debug.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/react_utils/React-utils.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/reacthermes/React-hermes.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/reactperflogger/React-perflogger.modulemap' '-fmodule-map-file=/Users/<USER>/riverbank_mobile/ios/Pods/Headers/Public/yoga/Yoga.modulemap'

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/expo-dev-menu/EXDevMenu.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/expo-dev-menu/ResourceBundle-EXDevMenu-expo-dev-menu-Info.plist (in target 'expo-dev-menu-EXDevMenu' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/expo-dev-menu/ResourceBundle-EXDevMenu-expo-dev-menu-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/expo-dev-menu/EXDevMenu.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/TOCropViewController/TOCropViewControllerBundle.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist (in target 'TOCropViewController-TOCropViewControllerBundle' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/TOCropViewController/TOCropViewControllerBundle.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/RNDeviceInfo/RNDeviceInfoPrivacyInfo.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/RNDeviceInfo/ResourceBundle-RNDeviceInfoPrivacyInfo-RNDeviceInfo-Info.plist (in target 'RNDeviceInfo-RNDeviceInfoPrivacyInfo' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/RNDeviceInfo/ResourceBundle-RNDeviceInfoPrivacyInfo-RNDeviceInfo-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/RNDeviceInfo/RNDeviceInfoPrivacyInfo.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/PromisesObjC/FBLPromises_Privacy.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/PromisesObjC/ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist (in target 'PromisesObjC-FBLPromises_Privacy' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/PromisesObjC/ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/PromisesObjC/FBLPromises_Privacy.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/Sentry/Sentry.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/Sentry/ResourceBundle-Sentry-Sentry-Info.plist (in target 'Sentry-Sentry' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/Sentry/ResourceBundle-Sentry-Sentry-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/Sentry/Sentry.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/React-Core/AccessibilityResources.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/React-Core/ResourceBundle-AccessibilityResources-React-Core-Info.plist (in target 'React-Core-AccessibilityResources' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/React-Core/ResourceBundle-AccessibilityResources-React-Core-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/React-Core/AccessibilityResources.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/ReachabilitySwift/ReachabilitySwift.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/ReachabilitySwift/ResourceBundle-ReachabilitySwift-ReachabilitySwift-Info.plist (in target 'ReachabilitySwift-ReachabilitySwift' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/ReachabilitySwift/ResourceBundle-ReachabilitySwift-ReachabilitySwift-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/ReachabilitySwift/ReachabilitySwift.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities_Privacy.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/GoogleUtilities/ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist (in target 'GoogleUtilities-GoogleUtilities_Privacy' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/GoogleUtilities/ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities_Privacy.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport_Privacy.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/GoogleDataTransport/ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist (in target 'GoogleDataTransport-GoogleDataTransport_Privacy' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/GoogleDataTransport/ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport_Privacy.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations_Privacy.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist (in target 'FirebaseInstallations-FirebaseInstallations_Privacy' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations_Privacy.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/expo-dev-launcher/EXDevLauncher.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/expo-dev-launcher/ResourceBundle-EXDevLauncher-expo-dev-launcher-Info.plist (in target 'expo-dev-launcher-EXDevLauncher' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/expo-dev-launcher/ResourceBundle-EXDevLauncher-expo-dev-launcher-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -additionalcontentfile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/expo-dev-launcher-EXDevLauncher.build/EXDevLauncherErrorView-SBPartialInfo.plist -o /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/expo-dev-launcher/EXDevLauncher.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/DerivedData/riverbank-bkldznlpviaadmgiurstupnpkiqq/Build/Products/Debug-iphonesimulator/RNImageCropPicker/QBImagePicker.bundle/Info.plist /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/RNImageCropPicker/ResourceBundle-QBImagePicker-RNImageCropPicker-Info.plist (in target 'RNImageCropPicker-QBImagePicker' from project 'Pods')
    cd /Users/<USER>/riverbank_mobile/ios/Pods
    builtin-infoPlistUtility /Users/<USER>/riverbank_mobile/ios/Pods/Target\ Support\ Files/RNImageCropPicker/ResourceBundle-QBImagePicker-RNImageCropPicker-Info.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -additio