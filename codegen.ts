import type { CodegenConfig } from '@graphql-codegen/cli'

const config: CodegenConfig = {
  overwrite: true,
  schema: 'https://riverbank-staging.vessel.cloud/api/graphql',
  documents: 'src/graphql/all-operations.graphql',
  generates: {
    'src/generated/graphql.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-react-apollo'
      ],
      config: {
        withHooks: true,
        withHOC: false,
        withComponent: false,
        apolloReactHooksImportFrom: '@apollo/client'
      }
    },
    'src/generated/introspection.json': {
      plugins: ['introspection']
    }
  }
}

export default config