import { View, SafeAreaView, StatusBar, useColorScheme, Platform, LogBox, ActivityIndicator, Dimensions } from 'react-native'
import React, { useState, useEffect, useReducer, useContext } from 'react'
import * as Sentry from '@sentry/react-native'
import { AuthContext } from './src/context/Auth/auth'
import ThemeReducer from './src/context/ThemeContext/ThemeReducer'
import ThemeContext from './src/context/ThemeContext/ThemeContext'
import AppContainer from './src/routes'
import FlashMessage from 'react-native-flash-message'
import { ApolloProvider } from '@apollo/client'
import setupApolloClient from './src/apollo/index'
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as SplashScreen from 'expo-splash-screen'
import Loader from './src/component/Loader/Loader'
import 'react-native-url-polyfill/auto'
import Loading from './src/context/Loading/Loading'
import Share from './src/context/Share/Share'
import messaging from '@react-native-firebase/messaging'
import { disconnectSocketConnection, onDeactivated, onDeleted } from './src/socket/Socket'
import * as Notifications from 'expo-notifications'
import NotificationContext, { MessageContext } from './src/context/Notification/Notification'
import SharePopup from './src/component/Modals/SharePopup'
import NetInfo from '@react-native-community/netinfo'
import StripeEligibleModal from './src/component/Modals/StripeEligibleModal'
import Eligible from './src/context/EligibleContext/EligibleContext'
import ApplyCheckersModal from './src/component/Modals/ApplyCheckersModal'
import getEnvVars from './environment'
import ErrorBoundary from './src/screens/ErrorScreen/ErrorBoundary'
import navigationService from './src/routes/navigationService'
import NotificationListeners from './src/utils/NotificationListeners'
import InAppUpdate from './InAppUpdate'
import Report from './src/context/Report/Report'
import ReportModal from './src/component/Modals/ReportModal'

Sentry.init({
	dsn: 'http://<EMAIL>:7777/1',
	debug: __DEV__,
	environment: __DEV__ ? 'development' : 'production',
	tracesSampleRate: 1.0,
})

const themeValue = 'Yellow'
const { TYPE } = getEnvVars()
const STATUSBAR_HEIGHT = StatusBar.currentHeight
const { width } = Dimensions.get('screen')
const App = props => {
	const [theme, themeSetter] = useReducer(ThemeReducer, themeValue)
	const _isDarkMode = useColorScheme() === 'dark'
	const [token, setToken] = useState(false)
	const [refreshToken, setRefreshToken] = useState('')
	const [client, setupClient] = useState(null)
	const [isProfileCompleted, setIsProfileCompleted] = useState('')
	const [loading, setLoading] = useState(false)
	const [share, setShare] = useState(false)
	const [showReportModal, setShowReportModal] = useState(false)
	const [report, setReport] = useState('')
	const [onUpdateReport, setOnUpdateReport] = useState(false)
	const [shareData, setShareData] = useState('')
	const { reloadAppData, unreads } = useContext(NotificationContext)
	const [newMessage, setNewMessage] = useState(false)
	const [networkInfo, setNetworkInfo] = useState('')
	const [isStripeEligibleModal, setIsStripeEligibleModal] = useState(false)
	const [isCheckrEligible, setIsCheckrEligible] = useState(false)

	const isNewMessage = boolean => {
		setNewMessage(boolean)
	}

	function updateValue(val) {
		themeSetter({ type: val })
	}

	function isLoader(boolean) {
		setLoading(boolean)
	}

	function isShare(boolean) {
		setShare(boolean)
	}

	function shareNow(data) {
		setShareData(data)
	}

	function setStripeEligible(data) {
		setIsStripeEligibleModal(data)
	}

	function setCheckrEligible(data) {
		setIsCheckrEligible(data)
	}

	const setTokenAsync = async token => {
		await AsyncStorage.setItem('token', token)
		setToken(token)
	}

	async function configurePushNotification() {
		try {
			Notifications.setNotificationHandler({
				handleNotification: async () => ({
					shouldShowAlert: true,
					shouldPlaySound: true,
					shouldSetBadge: true,
				}),
			})

			const { status: existingStatus } = await Notifications.getPermissionsAsync()
			let finalStatus = existingStatus
			if (existingStatus !== 'granted') {
				const { status } = await Notifications.requestPermissionsAsync()
				finalStatus = status
			}
			if (finalStatus === 'granted') {
				const token = await Notifications.getExpoPushTokenAsync()
				await AsyncStorage.setItem('notification_token', token.data)
			}

			const notificationListener = Notifications.addNotificationReceivedListener(notification => {
				console.log('Notification received:', notification)
			})

			const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
				console.log('Pressed Notification', JSON.stringify(response.notification.request.content.data))
			})

			return () => {
				Notifications.removeNotificationSubscription(notificationListener)
				Notifications.removeNotificationSubscription(responseListener)
			}
		} catch (error) {
			console.log('configurePushNotification catch:', error)
		}
	}

	const checkNotificationPermission = async () => {
		console.log('Notification permission check skipped')
	}

	const requestNotificationPermission = async () => {
		console.log('Notification permission request skipped')
	}

	async function byAdminLogout() {
		try {
			const _userId = await AsyncStorage.getItem('userId')
			// appLeave(userId);
			await AsyncStorage.removeItem('user')
			await AsyncStorage.removeItem('isProfileCompleted')
			await AsyncStorage.removeItem('userId')
			logout()
			console.log('async clear - logout!')
			navigationService.ResetNavigation()
		} catch (error) {
			console.log('logout Err :', error)
		}
	}

	const logout = async () => {
		try {
			await AsyncStorage.removeItem('token')
			await AsyncStorage.removeItem('refreshToken')
			setToken(null)
			setRefreshToken(null)
		} catch (e) {
			console.log('Logout App: ', e)
		}
	}

	const MyStatusBar = ({ backgroundColor, ...props }) => (
		<View style={[{ height: STATUSBAR_HEIGHT }, { position: 'absolute', top: 0 }, { backgroundColor }]}>
			<SafeAreaView
				style={
					{
						// paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
					}
				}
			>
				<StatusBar
					translucent={true}
					barStyle={'dark-content'}
					backgroundColor={backgroundColor}
					{...props}
				/>
			</SafeAreaView>
		</View>
	)

	const LocationPermission = async () => {
		console.log('Location permission check skipped')
	}
	async function permissionCamera() {
		console.log('Camera permission check skipped')
	}

	async function requestMessagingPermissionAndToken() {
		const authStatus = await messaging().requestPermission()
		const _getFcmToken = await AsyncStorage.getItem('notification_token')
		const enabled = authStatus === messaging.AuthorizationStatus.AUTHORIZED || authStatus === messaging.AuthorizationStatus.PROVISIONAL
		console.log('messaging Authorization status:', authStatus)
		if (enabled) {
			console.log('messaging Authorization status if enabled:', authStatus)
			const notification_token = (await messaging().getToken())?.toString()
			console.log('notification_token :', notification_token)
			await AsyncStorage.setItem('notification_token', notification_token)
		} else {
			console.log('messaging Authorization status if disabled:', authStatus)
		}
	}

	const createChannel = async () => {
		if (Platform.OS === 'android') {
			await Notifications.setNotificationChannelAsync('riverbank', {
				name: 'riverbank',
				importance: Notifications.AndroidImportance.MAX,
				vibrationPattern: [0, 250, 250, 250],
				lightColor: '#A79441',
			})
		}
	}

	const _handleNotification = notification => {
		const currentScreen = navigationService.getCurrentRouteName()
		const isroomID = notification?.data?.hasOwnProperty('roomId')
		if (!['Chat', 'Chats'].includes(currentScreen) || !isroomID) {
			Notifications.scheduleNotificationAsync({
				content: {
					title: notification?.notification?.title,
					body: notification?.notification?.body,
					sound: true,
				},
				trigger: null,
			})
		}
		if (isroomID) {
			isNewMessage(true)
		}
	}

	async function LoadApp() {
		try {
			LogBox.ignoreAllLogs()
			await configurePushNotification()
			const client = await setupApolloClient()
			const token = await AsyncStorage.getItem('token')
			const refreshToken = await AsyncStorage.getItem('refreshToken')
			const isProfileCompleted = await AsyncStorage.getItem('isProfileCompleted')
			const userId = await AsyncStorage.getItem('userId')
			createChannel()
			// new_message_listener('messageEmit', async msg => {
			//   console.log('new message :', msg);
			//   // new_message_listener(userId, async msg => {
			//   let user = await AsyncStorage.getItem('user');
			//   if (user?._id?.toString() !== msg?.from?.id) {
			//     isNewMessage(true);
			//   }
			// });
			onDeleted(userId, async res => {
				console.log('onDeleted :', res)
				if (res?.deleted) {
					byAdminLogout()
				}
			})
			onDeactivated(userId, async res => {
				console.log('onDeactivated :', res)
				if (res?.deactivated) {
					byAdminLogout()
				}
			})
			setIsProfileCompleted(isProfileCompleted)
			setupClient(client)
			setToken(token)
			setRefreshToken(refreshToken)
			SplashScreen.hideAsync()
		} catch (e) {
			console.log('catch loadApp :', e)
			SplashScreen.hideAsync()
		}
	}

	const permissionsMedia = async () => {
		console.log('Media permissions check skipped')
	}

	const checkAudioPermission = async () => {
		console.log('Audio permission check skipped')
	}
	useEffect(() => {
		LogBox.ignoreAllLogs()
		setTimeout(() => {
			permissionsMedia()
		}, 2000)

		;(async () => {
			try {
				checkNotificationPermission()
				await requestMessagingPermissionAndToken()
				await LocationPermission()
				// NotificationListeners();
				await permissionsMedia()
				await permissionCamera()
				await checkAudioPermission()
			} catch (e) {
				console.log('async useeffect catch :', e)
			}
		})()
		LoadApp()
	}, [])

	useEffect(() => {
		// Initialize the socket connection
		// const socket = initiateSocketConnection();

		// Clean up the socket connection when the component unmounts
		return () => {
			disconnectSocketConnection()
		}
	}, [])

	useEffect(() => {
		var net
		const unsubscribe = NetInfo.addEventListener(state => {
			setNetworkInfo(state)
			// console.log('Network state:', state);
			net = setTimeout(() => {
				setNetworkInfo(state)
			}, 10000)

			if (!state.isConnected) {
				navigationService.navigate('NetworkNavigator')
				console.log('Device is not connected to the internet')
				// You can navigate to another screen or take any other action here
			}
		})

		return () => {
			clearTimeout(net)
			unsubscribe() // Cleanup the subscription when the component unmounts
		}
	}, [])

	// console.log('clinet :', client);
	if (client) {
		return (
			<ApolloProvider client={client}>
				<ErrorBoundary {...props}>
					<View style={{ flex: 1 }}>
						<InAppUpdate />
						<Loading.Provider value={{ loading, isLoader }}>
							<Eligible.Provider
								value={{
									isStripeEligibleModal,
									setStripeEligible,
									isCheckrEligible,
									setCheckrEligible,
								}}
							>
								<Report.Provider
									value={{
										report,
										showReportModal,
										onUpdateReport,
										setShowReportModal,
										setReport,
										setOnUpdateReport,
									}}
								>
									<Share.Provider value={{ share, isShare, shareData, shareNow }}>
										<MessageContext.Provider value={{ newMessage, isNewMessage }}>
											<ThemeContext.Provider
												value={{
													ThemeValue: theme,
													dispatch: themeSetter,
													updateValue,
												}}
											>
												<AuthContext.Provider
													value={{
														token,
														refreshToken,
														isProfileCompleted,
														setTokenAsync,
														logout,
														networkInfo,
													}}
												>
													<FlashMessage position="top" />
													{Platform.OS === 'android' && (
														<MyStatusBar
															backgroundColor={'transparent'}
															style="dark"
														/>
													)}
													{Platform.OS === 'ios' && (
														<StatusBar
															style="dark"
															backgroundColor="transparent"
															translucent={true}
														/>
													)}
													<NotificationListeners />
													<Loader loading={loading} />
													{token && <SharePopup modalVisible={share} />}
													{token && (
														<ReportModal
															setReport={x => setReport(x)}
															report={report}
															updateReport={() => setOnUpdateReport(true)}
															modalVisible={showReportModal}
															onClose={() => setShowReportModal(false)}
														/>
													)}
													<StripeEligibleModal modalVisible={isStripeEligibleModal} />
													<ApplyCheckersModal modalVisible={isCheckrEligible} />
													<AppContainer />
												</AuthContext.Provider>
											</ThemeContext.Provider>
										</MessageContext.Provider>
									</Share.Provider>
								</Report.Provider>
							</Eligible.Provider>
						</Loading.Provider>
					</View>
				</ErrorBoundary>
			</ApolloProvider>
		)
	}
	return (
		<SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
			<ActivityIndicator
				size="large"
				color={'#A79441'}
			/>
		</SafeAreaView>
	)
}

export default Sentry.wrap(React.memo(App))
