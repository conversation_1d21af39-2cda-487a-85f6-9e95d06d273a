{"name": "riverbank", "version": "0.0.1", "private": true, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "npm run verify-ios && expo run:ios", "web": "expo start --web", "eject": "expo eject", "verify-ios": "node ./scripts/verify-ios-config.js", "prebuild": "expo prebuild && npm run verify-ios", "eas-build-pre-install": "npm install --save-dev expo@~49.0.0", "eas-build-post-install": "node ./scripts/fix-ios-modules.js && node ./scripts/verify-ios-config.js", "eas:build:ios": "eas build --platform ios", "eas:build:android": "eas build --platform android", "eas:build": "eas build --platform all", "lint": "biome lint", "lint:fix": "biome lint --write --unsafe", "lint:summary": "biome check --reporter=summary", "lint:errors": "biome lint --max-diagnostics=100 --diagnostic-level=error ./src", "format": "biome format --write", "test": "jest", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch"}, "dependencies": {"@apollo/client": "^3.7.11", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "^1.14.2", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/app": "^18.0.0", "@react-native-firebase/messaging": "^18.0.0", "@react-native-masked-view/masked-view": "0.3.1", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/drawer": "^6.6.2", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@sentry/react-native": "~5.24.3", "apollo3-cache-persist": "^0.14.1", "axios": "^1.6.7", "base-64": "^1.0.0", "deprecated-react-native-prop-types": "^4.1.0", "expo": "~51.0.39", "expo-build-properties": "~0.12.5", "expo-camera": "~15.0.16", "expo-constants": "~16.0.2", "expo-dev-client": "~4.0.29", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.2", "expo-file-system": "~17.0.1", "expo-image-picker": "~15.1.0", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-modules": "^0.0.0", "expo-notifications": "~0.28.19", "expo-splash-screen": "~0.27.7", "expo-status-bar": "~1.12.1", "expo-updates": "~0.25.28", "graphql": "^16.6.0", "graphql-ws": "^5.12.1", "imagekit-javascript": "^1.5.5", "lottie-react-native": "6.7.0", "moment": "^2.29.4", "react": "18.2.0", "react-file-base64": "^1.0.3", "react-native": "0.74.5", "react-native-circular-progress": "^1.3.9", "react-native-confirmation-code-field": "^7.3.1", "react-native-device-info": "^11.1.0", "react-native-flash-message": "^0.4.1", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "~2.16.1", "react-native-gifted-chat": "^2.4.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-image-crop-picker": "^0.51.0", "react-native-image-picker": "^8.2.1", "react-native-image-viewing": "^0.2.2", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-keyboard-controller": "^1.18.6", "react-native-linear-gradient": "^2.6.2", "react-native-maps": "1.14.0", "react-native-modalize": "^2.1.1", "react-native-pager-view": "6.3.0", "react-native-reanimated": "~3.10.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-signature-canvas": "^4.7.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "15.2.0", "react-native-tab-view": "^3.5.1", "react-native-url-polyfill": "^1.3.0", "react-native-video": "^5.2.1", "react-native-walkthrough-tooltip": "^1.5.0", "react-native-webview": "13.8.6", "socket.io-client": "^4.7.1", "subscriptions-transport-ws": "^0.11.0", "uri-scheme": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.28.3", "@biomejs/biome": "^2.0.5", "@expo/config-plugins": "~8.0.11", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@react-native/babel-preset": "^0.80.2", "@react-native/metro-config": "0.72.12", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "~18.2.79", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.77.0", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "jest": {"preset": "react-native"}}