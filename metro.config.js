const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add Sentry support and custom configurations
config.resolver.extraNodeModules = {
  "@": __dirname + "/src"
};

// Disable package exports to prevent 404 errors with axios and other packages
config.resolver.unstable_enablePackageExports = false;

// Disable inline requires to fix bundling issues
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: false,
  },
});

module.exports = config;
