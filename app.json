{"expo": {"name": "riverbank", "slug": "riverbank", "version": "11.0.1", "orientation": "portrait", "icon": "./assets/icon.png", "entryPoint": "./index.js", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "plugins": ["./plugins/withAppDelegate", ["expo-build-properties", {"ios": {"useFrameworks": "static", "deploymentTarget": "15.1"}, "android": {"compileSdkVersion": 36, "targetSdkVersion": 34, "minSdkVersion": 23}}], "expo-location", "expo-camera", "expo-document-picker", "expo-image-picker", "expo-notifications", "expo-dev-client", "@react-native-firebase/app", "@react-native-firebase/messaging", ["@sentry/react-native/expo", {"organization": "riverbank", "project": "riverbank"}], "@react-native-flipper/flipper-plugin"], "hooks": {"postPublish": [{"file": "@sentry/react-native/expo/hooks/post-publish.js", "config": {}}]}, "android": {"googleServicesFile": "./google-services.json", "package": "com.app.riverbank", "adaptiveIcon": {"foregroundImage": "./assets/icon.png", "backgroundColor": "#ffffff"}}, "ios": {"googleServicesFile": "./GoogleService-Info.plist", "bundleIdentifier": "com.app.riverbank", "icon": "./assets/icon.png"}}, "name": "riverbank"}