/**
 * @format
 */

import { AppRegistry } from "react-native";
import App from "./App";
import { name as appName } from "./app.json";
import messaging from "@react-native-firebase/messaging";
import firebase from "@react-native-firebase/app";

// Initialize Firebase if not already initialized
if (!firebase.apps.length) {
  firebase.initializeApp();
}

// Set background message handler after Firebase is initialized
setTimeout(() => {
  messaging().setBackgroundMessageHandler(async (_remoteMessage) => {
    // console.log('Message handled in the background index.js! ', remoteMessage);
  });
}, 0);

AppRegistry.registerComponent(appName, () => App);
AppRegistry.registerComponent("main", () => App);
