Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.default = void 0

var _asyncStorage = _interopRequireDefault(require('@react-native-async-storage/async-storage'))

var _client = require('@apollo/client')

var _ws = require('@apollo/client/link/ws')

var _utilities = require('@apollo/client/utilities')

var _apollo3CachePersist = require('apollo3-cache-persist')

var _subscriptionsTransportWs = require('subscriptions-transport-ws')

var _environment = _interopRequireDefault(require('../../environment'))

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var _getEnvVars = (0, _environment.default)()
var GRAPHQL_URL = _getEnvVars.GRAPHQL_URL
var WS_GRAPHQL_URL = _getEnvVars.WS_GRAPHQL_URL

var cache = new _client.InMemoryCache({
	dataIdFromObject: function dataIdFromObject(object) {
		switch (object.__typename) {
			default:
				return (0, _client.defaultDataIdFromObject)(object)
			// fall back to default handling
		}
	},
})
var httpLink = (0, _client.createHttpLink)({
	uri: GRAPHQL_URL,
	options: {
		reconnect: true,
	},
})
var wsLink = new _ws.WebSocketLink({
	uri: WS_GRAPHQL_URL,
	options: {
		reconnect: true,
	},
}) // const wsLink = new WebSocketLink(
//   new SubscriptionClient(WS_GRAPHQL_URL, {
//     reconnect: true, // Enable automatic reconnection
//   }),
// );

var request = function request(operation) {
	var token
	return regeneratorRuntime.async(function request$(_context) {
		while (1) {
			switch ((_context.prev = _context.next)) {
				case 0:
					_context.next = 2
					return regeneratorRuntime.awrap(_asyncStorage.default.getItem('token'))

				case 2:
					token = _context.sent
					operation.setContext({
						// get the authentication token from local storage if it exists
						// return the headers to the context so httpLink can read them
						headers: {
							authorization: token,
						},
					})

				case 4:
				case 'end':
					return _context.stop()
			}
		}
	})
}

var requestLink = new _client.ApolloLink(
	(operation, forward) =>
		new _client.Observable(observer => {
			// console.log(observer)
			var handle
			Promise.resolve(operation)
				.then(oper => request(oper))
				.then(() => {
					handle = forward(operation).subscribe({
						next: observer.next.bind(observer),
						error: observer.error.bind(observer),
						complete: observer.complete.bind(observer),
					})
				})
				.catch(observer.error.bind(observer))
			return () => {
				if (handle) handle.unsubscribe()
			}
		})
)
var terminatingLink = (0, _client.split)(_ref => {
	var query = _ref.query

	var _getMainDefinition = (0, _utilities.getMainDefinition)(query)
	var kind = _getMainDefinition.kind
	var operation = _getMainDefinition.operation

	return kind === 'OperationDefinition' && operation === 'subscription'
}, wsLink)

var setupData = function setupData(_client) {
	var _token
	return regeneratorRuntime.async(
		function setupData$(_context2) {
			while (1) {
				switch ((_context2.prev = _context2.next)) {
					case 0:
						_context2.prev = 0
						_context2.next = 3
						return regeneratorRuntime.awrap(_asyncStorage.default.getItem('token'))

					case 3:
						_token = _context2.sent
						_context2.next = 9
						break

					case 6:
						_context2.prev = 6
						_context2.t0 = _context2.catch(0)
						console.log('setupData Error', _context2.t0)

					case 9:
					case 'end':
						return _context2.stop()
				}
			}
		},
		null,
		null,
		[[0, 6]]
	)
}

var setupApollo = function setupApollo() {
	var client
	return regeneratorRuntime.async(function setupApollo$(_context4) {
		while (1) {
			switch ((_context4.prev = _context4.next)) {
				case 0:
					_context4.next = 2
					return regeneratorRuntime.awrap(
						(0, _apollo3CachePersist.persistCache)({
							cache: cache,
							storage: _asyncStorage.default,
						})
					)

				case 2:
					client = new _client.ApolloClient({
						link: (0, _client.concat)(_client.ApolloLink.from([terminatingLink, requestLink]), httpLink),
						cache: cache,
						resolvers: {
							User: {
								isLoggedIn: function isLoggedIn(_profile, _args, _ref2) {
									var _cache
									var token
									return regeneratorRuntime.async(
										function isLoggedIn$(_context3) {
											while (1) {
												switch ((_context3.prev = _context3.next)) {
													case 0:
														_cache = _ref2.cache
														_context3.prev = 1
														_context3.next = 4
														return regeneratorRuntime.awrap(_asyncStorage.default.getItem('token'))

													case 4:
														token = _context3.sent
														return _context3.abrupt('return', !!token)

													case 8:
														_context3.prev = 8
														_context3.t0 = _context3.catch(1)

													case 10:
														return _context3.abrupt('return', false)

													case 11:
													case 'end':
														return _context3.stop()
												}
											}
										},
										null,
										null,
										[[1, 8]]
									)
								},
							},
						},
					})
					_context4.next = 5
					return regeneratorRuntime.awrap(setupData(client))

				case 5:
					client.onClearStore(setupData)
					client.onResetStore(setupData)
					return _context4.abrupt('return', client)

				case 8:
				case 'end':
					return _context4.stop()
			}
		}
	})
}

var _default = setupApollo
exports.default = _default
