Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.refreshTokens =
	exports.reqLicensing =
	exports.getInvoiceTransaction =
	exports.getInvoiceById =
	exports.updateUserCheckrInfo =
	exports.createStripeAccount =
	exports.getKeyByFilter =
	exports.getMyBadgeRequests =
	exports.getJobCities =
	exports.DeleteAccount =
	exports.verifyRecoveryEmail =
	exports.sendRecoveryEmailVerification =
	exports.NearMeCaptian =
	exports.GetLicensingQuestions =
	exports.Licensings =
	exports.Invoices =
	exports.applyBadge =
	exports.getMyBadges =
	exports.getAvailableBalance =
	exports.getRecommendationsById =
	exports.getMyRecommendations =
	exports.giveRecommendation =
	exports.getCheckRideOffers =
	exports.checkFriendship =
	exports.hasFriendRequest =
	exports.getPostById =
	exports.threadComment =
	exports.getThreadById =
	exports.Threads =
	exports.createThread =
	exports.postComment =
	exports.postLike =
	exports.deletePost =
	exports.getWallPosts =
	exports.posts =
	exports.createPost =
	exports.getUserDetail =
	exports.removeFriendship =
	exports.acceptFriendship =
	exports.addFriendship =
	exports.users =
	exports.allUsersSearch =
	exports.getFriendsOfFriends =
	exports.myFriendshipRequests =
	exports.myFriendships =
	exports.postContactUs =
	exports.getRejectedJobs =
	exports.acceptCheckRideOffer =
	exports.rejectCheckRideOffer =
	exports.createChatRoom =
	exports.getMessages =
	exports.getChatRooms =
	exports.deactiveAccount =
	exports.getActiveJobs =
	exports.jobs =
	exports.applicationListener =
	exports.readAll =
	exports.readNotification =
	exports.getNotificationsForContext =
	exports.getNotifications =
	exports.applyCheckRide =
	exports.Ratings =
	exports.getAppliedCheckRides =
	exports.changepassword =
	exports.languages =
	exports.rejectOffer =
	exports.acceptOffer =
	exports.getCompanyDetail =
	exports.waterWays =
	exports.profile =
	exports.companies =
	exports.removeSavedJob =
	exports.getCities =
	exports.getCompletedJobs =
	exports.getSavedJobs =
	exports.saveJob =
	exports.createApplication =
	exports.getJob =
	exports.createOffer =
	exports.applications =
	exports.getAppliedJobs =
	exports.updateUser =
	exports.roles =
	exports.verifyEmail =
	exports.sendVerificationEmail =
	exports.ForgetPasswordChange =
	exports.ForgotPasswordVerification =
	exports.forgotPassword =
	exports.mobileLogin =
	exports.crewmemberRegister =
	exports.signup =
		void 0

var _client = require('@apollo/client')

function _templateObject91() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject91 = function _templateObject91() {
		return data
	}

	return data
}

function _templateObject90() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject90 = function _templateObject90() {
		return data
	}

	return data
}

function _templateObject89() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject89 = function _templateObject89() {
		return data
	}

	return data
}

function _templateObject88() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject88 = function _templateObject88() {
		return data
	}

	return data
}

function _templateObject87() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject87 = function _templateObject87() {
		return data
	}

	return data
}

function _templateObject86() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject86 = function _templateObject86() {
		return data
	}

	return data
}

function _templateObject85() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject85 = function _templateObject85() {
		return data
	}

	return data
}

function _templateObject84() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject84 = function _templateObject84() {
		return data
	}

	return data
}

function _templateObject83() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject83 = function _templateObject83() {
		return data
	}

	return data
}

function _templateObject82() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject82 = function _templateObject82() {
		return data
	}

	return data
}

function _templateObject81() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject81 = function _templateObject81() {
		return data
	}

	return data
}

function _templateObject80() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject80 = function _templateObject80() {
		return data
	}

	return data
}

function _templateObject79() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject79 = function _templateObject79() {
		return data
	}

	return data
}

function _templateObject78() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject78 = function _templateObject78() {
		return data
	}

	return data
}

function _templateObject77() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject77 = function _templateObject77() {
		return data
	}

	return data
}

function _templateObject76() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject76 = function _templateObject76() {
		return data
	}

	return data
}

function _templateObject75() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject75 = function _templateObject75() {
		return data
	}

	return data
}

function _templateObject74() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject74 = function _templateObject74() {
		return data
	}

	return data
}

function _templateObject73() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject73 = function _templateObject73() {
		return data
	}

	return data
}

function _templateObject72() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject72 = function _templateObject72() {
		return data
	}

	return data
}

function _templateObject71() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject71 = function _templateObject71() {
		return data
	}

	return data
}

function _templateObject70() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject70 = function _templateObject70() {
		return data
	}

	return data
}

function _templateObject69() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject69 = function _templateObject69() {
		return data
	}

	return data
}

function _templateObject68() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject68 = function _templateObject68() {
		return data
	}

	return data
}

function _templateObject67() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject67 = function _templateObject67() {
		return data
	}

	return data
}

function _templateObject66() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject66 = function _templateObject66() {
		return data
	}

	return data
}

function _templateObject65() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject65 = function _templateObject65() {
		return data
	}

	return data
}

function _templateObject64() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject64 = function _templateObject64() {
		return data
	}

	return data
}

function _templateObject63() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject63 = function _templateObject63() {
		return data
	}

	return data
}

function _templateObject62() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject62 = function _templateObject62() {
		return data
	}

	return data
}

function _templateObject61() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject61 = function _templateObject61() {
		return data
	}

	return data
}

function _templateObject60() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject60 = function _templateObject60() {
		return data
	}

	return data
}

function _templateObject59() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject59 = function _templateObject59() {
		return data
	}

	return data
}

function _templateObject58() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject58 = function _templateObject58() {
		return data
	}

	return data
}

function _templateObject57() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject57 = function _templateObject57() {
		return data
	}

	return data
}

function _templateObject56() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject56 = function _templateObject56() {
		return data
	}

	return data
}

function _templateObject55() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject55 = function _templateObject55() {
		return data
	}

	return data
}

function _templateObject54() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject54 = function _templateObject54() {
		return data
	}

	return data
}

function _templateObject53() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject53 = function _templateObject53() {
		return data
	}

	return data
}

function _templateObject52() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject52 = function _templateObject52() {
		return data
	}

	return data
}

function _templateObject51() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject51 = function _templateObject51() {
		return data
	}

	return data
}

function _templateObject50() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject50 = function _templateObject50() {
		return data
	}

	return data
}

function _templateObject49() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject49 = function _templateObject49() {
		return data
	}

	return data
}

function _templateObject48() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject48 = function _templateObject48() {
		return data
	}

	return data
}

function _templateObject47() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject47 = function _templateObject47() {
		return data
	}

	return data
}

function _templateObject46() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject46 = function _templateObject46() {
		return data
	}

	return data
}

function _templateObject45() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject45 = function _templateObject45() {
		return data
	}

	return data
}

function _templateObject44() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject44 = function _templateObject44() {
		return data
	}

	return data
}

function _templateObject43() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject43 = function _templateObject43() {
		return data
	}

	return data
}

function _templateObject42() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject42 = function _templateObject42() {
		return data
	}

	return data
}

function _templateObject41() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject41 = function _templateObject41() {
		return data
	}

	return data
}

function _templateObject40() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject40 = function _templateObject40() {
		return data
	}

	return data
}

function _templateObject39() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject39 = function _templateObject39() {
		return data
	}

	return data
}

function _templateObject38() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject38 = function _templateObject38() {
		return data
	}

	return data
}

function _templateObject37() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject37 = function _templateObject37() {
		return data
	}

	return data
}

function _templateObject36() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject36 = function _templateObject36() {
		return data
	}

	return data
}

function _templateObject35() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject35 = function _templateObject35() {
		return data
	}

	return data
}

function _templateObject34() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject34 = function _templateObject34() {
		return data
	}

	return data
}

function _templateObject33() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject33 = function _templateObject33() {
		return data
	}

	return data
}

function _templateObject32() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject32 = function _templateObject32() {
		return data
	}

	return data
}

function _templateObject31() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject31 = function _templateObject31() {
		return data
	}

	return data
}

function _templateObject30() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject30 = function _templateObject30() {
		return data
	}

	return data
}

function _templateObject29() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject29 = function _templateObject29() {
		return data
	}

	return data
}

function _templateObject28() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject28 = function _templateObject28() {
		return data
	}

	return data
}

function _templateObject27() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject27 = function _templateObject27() {
		return data
	}

	return data
}

function _templateObject26() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject26 = function _templateObject26() {
		return data
	}

	return data
}

function _templateObject25() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject25 = function _templateObject25() {
		return data
	}

	return data
}

function _templateObject24() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject24 = function _templateObject24() {
		return data
	}

	return data
}

function _templateObject23() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject23 = function _templateObject23() {
		return data
	}

	return data
}

function _templateObject22() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject22 = function _templateObject22() {
		return data
	}

	return data
}

function _templateObject21() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject21 = function _templateObject21() {
		return data
	}

	return data
}

function _templateObject20() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject20 = function _templateObject20() {
		return data
	}

	return data
}

function _templateObject19() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject19 = function _templateObject19() {
		return data
	}

	return data
}

function _templateObject18() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject18 = function _templateObject18() {
		return data
	}

	return data
}

function _templateObject17() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject17 = function _templateObject17() {
		return data
	}

	return data
}

function _templateObject16() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject16 = function _templateObject16() {
		return data
	}

	return data
}

function _templateObject15() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject15 = function _templateObject15() {
		return data
	}

	return data
}

function _templateObject14() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject14 = function _templateObject14() {
		return data
	}

	return data
}

function _templateObject13() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject13 = function _templateObject13() {
		return data
	}

	return data
}

function _templateObject12() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject12 = function _templateObject12() {
		return data
	}

	return data
}

function _templateObject11() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject11 = function _templateObject11() {
		return data
	}

	return data
}

function _templateObject10() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject10 = function _templateObject10() {
		return data
	}

	return data
}

function _templateObject9() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject9 = function _templateObject9() {
		return data
	}

	return data
}

function _templateObject8() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject8 = function _templateObject8() {
		return data
	}

	return data
}

function _templateObject7() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject7 = function _templateObject7() {
		return data
	}

	return data
}

function _templateObject6() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject6 = function _templateObject6() {
		return data
	}

	return data
}

function _templateObject5() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject5 = function _templateObject5() {
		return data
	}

	return data
}

function _templateObject4() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject4 = function _templateObject4() {
		return data
	}

	return data
}

function _templateObject3() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject3 = function _templateObject3() {
		return data
	}

	return data
}

function _templateObject2() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject2 = function _templateObject2() {
		return data
	}

	return data
}

function _templateObject() {
	var data = _taggedTemplateLiteral(['\n  ', '\n'])

	_templateObject = function _templateObject() {
		return data
	}

	return data
}

function _taggedTemplateLiteral(strings, raw) {
	if (!raw) {
		raw = strings.slice(0)
	}
	return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }))
}

var signup = (0, _client.gql)(
	_templateObject(),
	'\n  mutation Mutation($userInput: UserInput) {\n  signup(userInput: $userInput) {\n    _id\n    token\n    tokenExpiration\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    city\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    video\n    coverImage\n    createdAt\n    updatedAt\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    about\n    loc {\n      coordinates\n      type\n    }\n    isProfileCompleted\n  }\n}\n'
)
exports.signup = signup
var crewmemberRegister = (0, _client.gql)(
	_templateObject2(),
	'mutation CrewmemberRegister($userInput: UserInput) {\n  crewmemberRegister(userInput: $userInput) {\n     _id\n    token\n    tokenExpiration\n    role\n    name\n    refrence_no\n    email\n    auth\n    phoneVerified\n    isEmailVerified\n    notificationToken\n    isProfileCompleted\n    checkrInviteUrl\n    checkr_candidate_id\n    checkr_decision\n    checkr_status\n  }\n}'
)
exports.crewmemberRegister = crewmemberRegister
var mobileLogin = (0, _client.gql)(
	_templateObject3(),
	'mutation MobileLogin($email: String, $password: String, $notificationToken: String) {\n  mobileLogin(email: $email, password: $password, notificationToken: $notificationToken) {\n    _id\n    isProfileCompleted\n    token\n    tokenExpiration\n    refreshToken\n    refreshTokenExpires\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    city\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    video\n    coverImage\n    createdAt\n    updatedAt\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    about\n    loc {\n      coordinates\n      type\n    }\n  }\n}'
)
exports.mobileLogin = mobileLogin
var forgotPassword = (0, _client.gql)(_templateObject4(), 'mutation ForgotPassword($email: String!) {\n  forgotPassword(email: $email) {\n    result\n  }\n}')
exports.forgotPassword = forgotPassword
var ForgotPasswordVerification = (0, _client.gql)(
	_templateObject5(),
	'mutation ForgotPasswordVerification($email: String!, $code: String!) {\n  forgotPasswordVerification(email: $email, code: $code) {\n    result\n    token\n  }\n}'
)
exports.ForgotPasswordVerification = ForgotPasswordVerification
var ForgetPasswordChange = (0, _client.gql)(
	_templateObject6(),
	'mutation ForgetPasswordChange($token: String!, $password: String!) {\n  forgetPasswordChange(token: $token, password: $password) {\n    result\n  }\n}'
)
exports.ForgetPasswordChange = ForgetPasswordChange
var sendVerificationEmail = (0, _client.gql)(
	_templateObject7(),
	'mutation SendVerificationEmail($email: String) {\n  sendVerificationEmail(email: $email) {\n    email\n    result\n  }\n}'
)
exports.sendVerificationEmail = sendVerificationEmail
var verifyEmail = (0, _client.gql)(
	_templateObject8(),
	'mutation VerifyEmail($code: String, $email: String) {\n  verifyEmail(code: $code, email: $email) {\n    email\n    result\n  }\n}'
)
exports.verifyEmail = verifyEmail
var roles = (0, _client.gql)(_templateObject9(), 'query Query {\n  roles\n}')
exports.roles = roles
var updateUser = (0, _client.gql)(
	_templateObject10(),
	'mutation UpdateUser($updateUserInput: UpdateUserInput) {\n  updateUser(updateUserInput: $updateUserInput) {\n    _id\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    city\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    video\n    coverImage\n    createdAt\n    updatedAt\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    loc {\n      coordinates\n      type\n    }\n    isProfileCompleted\n    postedAddress\n    company {\n      _id\n      email\n      name\n    }\n    drugTest\n    physical\n    facebook\n    linkedIn\n    twitter\n    google\n    companyWorkFor\n    yearsOfExperience\n    canYouHeadline\n    workedInFleetBefore\n    twicFront\n    twicBack\n    driverLicense\n    captainLicense\n    fccLicenseFront\n    radarCertificateFront\n    radioLicense\n    medicalCertificate\n    educations {\n      endYear\n      institute\n      startYear\n      title\n    }\n    experiances {\n      title\n      startYear\n      company\n      endYear\n    }\n    awards {\n      attachments\n      company\n      detail\n      endYear\n      startYear\n      title\n    }\n    waterWays {\n      _id\n      name\n      parentWaterWay {\n        _id\n        name\n      }\n    }\n    dob\n    gender\n    languages\n    skills\n    haveDE\n    licences\n    numberOfBargesPushThroughCanals\n    numberOfBargesPushThroughRiver\n    pictureOfInformationOnthereLicences\n    routes\n    tankedForCompanies\n    costGuardPhysical\n    about\n    hourlyRate\n    hideEmail\n    hideContact\n    jobOfferNotification\n    generalNotification\n    friendRequestNotification\n    postNotification\n    ratings\n    ratingsCount\n    totalExperianceDays\n    chatNotifications\n    profileVerficationRequested\n    profileVerified\n    backAccountNo\n    bankName\n    friends\n    ifscCode\n    backAccountTitle\n    hideRecommendations\n    isApprovedByAdmin\n    licenceingReq\n    postSettings\n    routingNo\n    hideReviews\n    state\n    checkrInviteUrl\n    checkr_candidate_id\n    checkr_decision\n    checkr_status\n    deckhand_text\n    deckhand_video\n    deleted\n    disposable\n    payouts_enabled\n    stripe_business_account_id\n    transfer_enabled\n  }\n}\n'
)
exports.updateUser = updateUser
var getAppliedJobs = (0, _client.gql)(
	_templateObject11(),
	'query GetAppliedJobs($options: options) {\n  getAppliedJobs(options: $options) {\n    results {\n      _id\n      company {\n        _id\n        email\n        name\n        coverImage\n        website\n        photo\n      }\n      title\n      description\n      email\n      speciality\n      type\n      jobType\n      offeredSalary\n      careerLevel\n      experience\n      industry\n      qualification\n      country\n      city\n      address\n      zipCode\n      expireAt\n      createdAt\n      updatedAt\n      rates\n      ratesType\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.getAppliedJobs = getAppliedJobs
var applications = (0, _client.gql)(
	_templateObject12(),
	'query Applications($filters: applicationfilters) {\n  applications(filters: $filters) {\n    results {\n         contract {\n        status\n        reason\n        startDate\n        endDate\n        acceptedOffer {\n          by {\n            name\n          }\n          status\n        }\n      }\n      offers {\n        _id\n        by {\n          _id\n          name\n          role\n        }\n        rates\n        status\n      }\n      _id\n      applicant {\n        _id\n        email\n        name\n      }\n      hourlyRates\n      isHired\n    }\n    limit\n    page\n    totalPages\n    totalResults\n  }\n}'
)
exports.applications = applications
var createOffer = (0, _client.gql)(
	_templateObject13(),
	'mutation CreateOffer($applicationId: ID!, $inputOffer: InputOffer) {\n  createOffer(applicationId: $applicationId, inputOffer: $inputOffer) {\n    _id\n    by {\n      _id\n      name\n      role\n    }\n    rates\n    status\n  }\n}'
)
exports.createOffer = createOffer
var getJob = (0, _client.gql)(
	_templateObject14(),
	'query GetJob($jobId: ID!) {\n  getJob(jobId: $jobId) {\n    _id\n    company {\n      _id\n      address\n      email\n      coverImage\n      linkedIn\n      facebook\n      twitter\n      website\n      photo\n    }\n    loc {\n      type\n      coordinates\n    }\n    title\n    description\n    email\n    speciality\n    type\n    jobType\n    offeredSalary\n    careerLevel\n    experience\n    industry\n    qualification\n    country\n    city\n    address\n    zipCode\n    expireAt\n    rates\n    ratesType\n    createdAt\n    updatedAt\n    startDate\n    noOfApplications\n    noOfCandidates\n    horsePower\n    skills\n    jobShift\n  }\n}'
)
exports.getJob = getJob
var createApplication = (0, _client.gql)(
	_templateObject15(),
	'mutation CreateApplication($inputApplication: InputApplication) {\n  createApplication(inputApplication: $inputApplication) {\n    _id\n    company {\n      _id\n    }\n    job {\n      _id\n    }\n    applicant {\n      _id\n      email\n    }\n    experience\n    currentSalary\n    expectedSalary\n    gender\n    type\n    language\n    educationLevel\n    createdAt\n    updatedAt\n    coverletter\n    hourlyRates\n  }\n}'
)
exports.createApplication = createApplication
var saveJob = (0, _client.gql)(_templateObject16(), 'mutation SaveJob($saveJobId: ID!) {\n  saveJob(id: $saveJobId) {\n    message\n  }\n}')
exports.saveJob = saveJob
var getSavedJobs = (0, _client.gql)(
	_templateObject17(),
	'query GetSavedJobs($options: options) {\n  getSavedJobs(options: $options) {\n    results {\n      _id\n      company {\n      _id\n        email\n        name\n        coverImage\n        website\n        photo\n      }\n      title\n      description\n      email\n      speciality\n      type\n      jobType\n      offeredSalary\n      careerLevel\n      experience\n      industry\n      qualification\n      country\n      city\n      address\n      zipCode\n      expireAt\n      rates\n      ratesType\n      createdAt\n      updatedAt\n      status\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.getSavedJobs = getSavedJobs
var getCompletedJobs = (0, _client.gql)(
	_templateObject18(),
	'query GetCompletedJobs($options: options, $filters: CompletedJobFilter) {\n  getCompletedJobs(options: $options, filters: $filters) {\n    results {\n      _id\n      company {\n        _id\n        coverImage\n        email\n        name\n        website\n        photo\n      }\n      title\n      description\n      email\n      speciality\n      type\n      jobType\n      offeredSalary\n      careerLevel\n      experience\n      industry\n      qualification\n      country\n      city\n      address\n      zipCode\n      expireAt\n      rates\n      ratesType\n      createdAt\n      updatedAt\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.getCompletedJobs = getCompletedJobs
var getCities = (0, _client.gql)(_templateObject19(), 'query Query {\n  getCities\n}')
exports.getCities = getCities
var removeSavedJob = (0, _client.gql)(
	_templateObject20(),
	'mutation RemoveSavedJob($removeSavedJobId: ID!) {\n  removeSavedJob(id: $removeSavedJobId) {\n    message\n  }\n}'
)
exports.removeSavedJob = removeSavedJob
var companies = (0, _client.gql)(
	_templateObject21(),
	'query Query($filters: companyfilters, $options: options) {\n  companies(filters: $filters, options: $options) {\n    totalPages\n    totalResults\n    limit\n    page\n    results {\n      _id\n      role\n      name\n      refrence_no\n      phone\n      email\n      address\n      country\n      city\n      zipCode\n      auth\n      phoneVerified\n      isEmailVerified\n      photo\n      video\n      openJobs\n      coverImage\n      createdAt\n      updatedAt\n      notificationToken\n      is_active\n      socketId\n      isOnline\n      about\n      isProfileCompleted\n      aboutFleet\n      gallery\n      industry\n      establishedIn\n      companySize\n      foundIn\n      website\n      facebook\n      linkedIn\n      twitter\n      google\n      emergencyContactInformation {\n        email\n        phone\n      }\n    }\n  }\n}'
)
exports.companies = companies
var profile = (0, _client.gql)(
	_templateObject22(),
	'query Profile {\n  profile {\n    _id\n    hourlyRate\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    chatNotifications\n    city\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    savedJobs\n    video\n    coverImage\n    createdAt\n    updatedAt\n    appliedJobs\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    ratingsCount\n    ratings\n    about\n    loc {\n      coordinates\n      type\n    }\n    isProfileCompleted\n    postedAddress\n    company {\n      _id\n      email\n      name\n    }\n    experiances {\n      company\n      endYear\n      title\n      startYear\n      detail\n    }\n    educations {\n      endYear\n      institute\n      startYear\n      title\n    }\n    awards {\n      attachments\n      company\n      detail\n      endYear\n      startYear\n      title\n      _id\n    }\n    drugTest\n    physical\n    licences\n    tankedForCompanies\n    routes\n    numberOfBargesPushThroughRiver\n    numberOfBargesPushThroughCanals\n    haveDE\n    pictureOfInformationOnthereLicences\n    costGuardPhysical\n    facebook\n    linkedIn\n    twitter\n    google\n    skills\n    companyWorkFor\n    yearsOfExperience\n    canYouHeadline\n    workedInFleetBefore\n    twicFront\n    twicBack\n    driverLicense\n    captainLicense\n    fccLicenseFront\n    radarCertificateFront\n    radioLicense\n    medicalCertificate\n    languages\n    dob\n    gender\n    friendRequestNotification\n    generalNotification\n    jobOfferNotification\n    hideEmail\n    hideContact\n    postNotification\n    waterWays {\n      _id\n      name\n      parentWaterWay {\n        name\n        _id\n      }\n    }\n    bankName\n    backAccountNo\n    profileVerified\n    profileVerficationRequested\n    friends\n    ifscCode\n    totalExperianceDays\n    backAccountTitle\n    isApprovedByAdmin\n    hideRecommendations\n    licenceingReq\n    postSettings\n    routingNo\n    hideReviews\n    state\n    deleted\n    checkrInviteUrl\n    checkr_candidate_id\n    checkr_decision\n    checkr_status\n    payouts_enabled\n    transfer_enabled\n    stripe_business_account_id\n    deckhand_text\n    deckhand_video\n    disposable\n  }\n}'
)
exports.profile = profile
var waterWays = (0, _client.gql)(
	_templateObject23(),
	'query WaterWays($filters: WaterWayfilters, $options: options) {\n  WaterWays(filters: $filters, options: $options) {\n    results {\n      _id\n      name\n      subWaterWays {\n        name\n        _id\n      }\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.waterWays = waterWays
var getCompanyDetail = (0, _client.gql)(
	_templateObject24(),
	'query GetCompanyDetail($companyId: ID!) {\n  getCompanyDetail(companyId: $companyId) {\n    _id\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    city\n    state\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    video\n    coverImage\n    createdAt\n    updatedAt\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    about\n    loc {\n      coordinates\n      type\n    }\n    isProfileCompleted\n    aboutFleet\n    fleetImage\n    gallery\n    industry\n    establishedIn\n    companySize\n    foundIn\n    checkRideData {\n      hitchMax\n      hitchMin\n      price\n      terms\n    }\n    isCheckRideEnabled\n    website\n    facebook\n    linkedIn\n    twitter\n    google\n    emergencyContactInformation {\n      photo\n      phone\n      name\n      email\n      designation\n    }\n    openJobs\n    hideEmail\n    hideContact\n  }\n}'
)
exports.getCompanyDetail = getCompanyDetail
var acceptOffer = (0, _client.gql)(
	_templateObject25(),
	'mutation AcceptOffer($applicationId: ID!, $offerId: ID!) {\n  acceptOffer(applicationId: $applicationId, offerId: $offerId) {\n    _id\n    by {\n      _id\n      name\n      role\n    }\n    status\n    rates\n  }\n}'
)
exports.acceptOffer = acceptOffer
var rejectOffer = (0, _client.gql)(
	_templateObject26(),
	'mutation RejectOffer($applicationId: ID!, $offerId: ID!) {\n  rejectOffer(applicationId: $applicationId, offerId: $offerId) {\n    _id\n    by {\n      _id\n      name\n      role\n    }\n    rates\n    status\n  }\n}'
)
exports.rejectOffer = rejectOffer
var languages = (0, _client.gql)(
	_templateObject27(),
	'query Languages($options: options, $filters: Languagefilters) {\n  Languages(options: $options, filters: $filters) {\n    limit\n    page\n    results {\n      _id\n      language\n    }\n    totalPages\n    totalResults\n  }\n}'
)
exports.languages = languages
var changepassword = (0, _client.gql)(
	_templateObject28(),
	'mutation ChangePassword($password: String!, $newPassword: String!, $email: String!) {\n  changePassword(password: $password, newPassword: $newPassword, email: $email) {\n    result\n  }\n}'
)
exports.changepassword = changepassword
var getAppliedCheckRides = (0, _client.gql)(
	_templateObject29(),
	'query GetAppliedCheckRides($options: options) {\n  getAppliedCheckRides(options: $options) {\n    limit\n    results {\n      _id\n      company {\n        isOnline\n        _id\n        email\n        photo\n        name\n        website\n        about\n        companySize\n        city\n         checkRideData {\n          hitchMax\n          hitchMin\n          price\n          terms\n        }\n      }\n      createdAt\n      offers {\n        _id\n        completedAt\n        endDate\n        expireAt\n        startDate\n        status\n        price\n      }\n      user {\n        _id\n      }\n      updatedAt\n    }\n    totalPages\n    totalResults\n    page\n  }\n}'
)
exports.getAppliedCheckRides = getAppliedCheckRides
var Ratings = (0, _client.gql)(
	_templateObject30(),
	'query Ratings($filters: Ratingfilters, $options: options) {\n  Ratings(filters: $filters, options: $options) {\n      page\n    limit\n    totalPages\n    totalResults\n    results {\n      _id\n      avgRating\n      by {\n        _id\n        name\n        photo\n        phone\n        email\n      }\n      review\n      reliability\n      punctuality\n      professional_development\n      productivity\n      performance\n      createdAt\n      user {\n        ratings\n        ratingsCount\n        _id\n      }\n    }\n  \n  }\n}'
)
exports.Ratings = Ratings
var applyCheckRide = (0, _client.gql)(
	_templateObject31(),
	'mutation ApplyCheckRide($companyId: ID!) {\n  applyCheckRide(companyId: $companyId) {\n    _id\n    createdAt\n  }\n}'
)
exports.applyCheckRide = applyCheckRide
var getNotifications = (0, _client.gql)(
	_templateObject32(),
	'query GetNotifications($filters: Notificationfilters,$options: options) {\n  getNotifications(filters: $filters,options: $options) {\n    page\n    limit\n    totalPages\n    totalResults\n    results {\n      _id\n      image\n      meta {\n        applicationId\n        id\n        jobId\n        postId\n      }\n      seen\n      subTitle\n      text\n      title\n      to {\n        _id\n        name\n      }\n      createdAt\n      from {\n        _id\n        photo\n        name\n      }\n      type\n    }\n  }\n}'
)
exports.getNotifications = getNotifications
var getNotificationsForContext = (0, _client.gql)(
	_templateObject33(),
	'query GetNotifications($filters: Notificationfilters,$options: options) {\n  getNotifications(filters: $filters,options: $options) {\n    page\n    limit\n    totalPages\n    totalResults\n    results {\n      _id\n      seen\n    }\n  }\n}'
)
exports.getNotificationsForContext = getNotificationsForContext
var readNotification = (0, _client.gql)(
	_templateObject34(),
	'mutation ReadNotification($notificationId: ID!) {\n  readNotification(notificationId: $notificationId) {\n    seen\n    to {\n      _id\n      name\n    }\n    from {\n      _id\n      name\n    }\n  }\n}'
)
exports.readNotification = readNotification
var readAll = (0, _client.gql)(_templateObject35(), 'mutation ReadAll {\n  readAll {\n    message\n  }\n}')
exports.readAll = readAll
var applicationListener = (0, _client.gql)(
	_templateObject36(),
	'subscription ApplicationListener($applicationId: ID!) {\n  applicationListener(applicationId: $applicationId) {\n    _id\n    currentSalary\n    type\n    createdAt\n    applicant {\n      _id\n    }\n    offers {\n      _id\n      by {\n        _id\n        name\n        role\n      }\n      status\n      rates\n    }\n  }\n}'
)
exports.applicationListener = applicationListener
var jobs = (0, _client.gql)(
	_templateObject37(),
	'query Jobs($filters: jobfilters, $options: options) {\n  jobs(filters: $filters, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      company {\n        photo\n      }\n      title\n      type\n      jobType\n      city\n      createdAt\n      rates\n      description\n    }\n  }\n}'
)
exports.jobs = jobs
var getActiveJobs = (0, _client.gql)(
	_templateObject38(),
	'query GetActiveJobs($options: options) {\n  getActiveJobs(options: $options) {\n    limit\n    page\n    results {\n      _id\n      company {\n        _id\n        name\n        photo\n        website\n      }\n      title\n      city\n      expireAt\n      rates\n      type\n      jobType\n      createdAt\n      description\n    }\n  }\n}'
)
exports.getActiveJobs = getActiveJobs
var deactiveAccount = (0, _client.gql)(
	_templateObject39(),
	'mutation DeactiveAccount($email: String) {\n  deactiveAccount(email: $email) {\n    message\n  }\n}'
)
exports.deactiveAccount = deactiveAccount
var getChatRooms = (0, _client.gql)(
	_templateObject40(),
	'query GetChatRooms($filters: roomfilters, $options: options) {\n  getChatRooms(filters: $filters, options: $options) {\n    results {\n      _id\n      chatName\n      connectedUsers {\n        isOnline\n        name\n        _id\n      }\n      lastMessage {\n        at\n        message\n        seenBy\n        status\n      }\n      primeUser {\n        _id\n        isOnline\n        name\n      }\n      users {\n        _id\n        isOnline\n        name\n        photo\n        role\n      }\n      updatedAt\n      unseenCount\n      createdAt\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.getChatRooms = getChatRooms
var getMessages = (0, _client.gql)(
	_templateObject41(),
	'query GetMessages($options: options, $filters: messagefilters) {\n  getMessages(options: $options, filters: $filters) {\n    limit\n    page\n     totalPages\n    totalResults\n    results {\n      message\n      from {\n        _id\n        name\n        photo\n      }\n      seenBy \n      to {\n        _id\n        name\n        photo\n      }\n      type\n      at\n      files\n      _id\n      createdAt\n       cardData {\n        cardID\n        description\n        photo\n        title\n        shareType\n      }\n    }\n  }\n}'
)
exports.getMessages = getMessages
var createChatRoom = (0, _client.gql)(
	_templateObject42(),
	'mutation CreateChatRoom($inputRoom: InputRoom) {\n  createChatRoom(inputRoom: $inputRoom) {\n    connectedUsers {\n      _id\n      name\n    }\n    chatName\n    _id\n    users {\n      _id\n      isOnline\n      name\n      photo\n      role\n    }\n    createdAt\n  }\n}'
)
exports.createChatRoom = createChatRoom
var rejectCheckRideOffer = (0, _client.gql)(
	_templateObject43(),
	'mutation RejectCheckRideOffer($checkRideId: ID!, $offerId: ID!) {\n  rejectCheckRideOffer(checkRideId: $checkRideId, offerId: $offerId) {\n    checkRideData {\n      hitchMax\n      hitchMin\n      price\n      terms\n    }\n    company {\n      _id\n    }\n    offers {\n      status\n      startDate\n      expireAt\n      endDate\n      completedAt\n    }\n    user {\n      _id\n    }\n    _id\n  }\n}'
)
exports.rejectCheckRideOffer = rejectCheckRideOffer
var acceptCheckRideOffer = (0, _client.gql)(
	_templateObject44(),
	'mutation AcceptCheckRideOffer($checkRideId: ID!, $offerId: ID!) {\n  acceptCheckRideOffer(checkRideId: $checkRideId, offerId: $offerId) {\n    _id\n    checkRideData {\n      terms\n      price\n      hitchMin\n      hitchMax\n    }\n    company {\n      _id\n    }\n    createdAt\n    offers {\n      startDate\n      endDate\n      status\n      expireAt\n      completedAt\n    }\n    user {\n      _id\n    }\n  }\n}'
)
exports.acceptCheckRideOffer = acceptCheckRideOffer
var getRejectedJobs = (0, _client.gql)(
	_templateObject45(),
	'query GetRejectedJobs($options: options) {\n  getRejectedJobs(options: $options) {\n    results {\n      _id\n      company {\n        _id\n        name\n        website\n        photo\n      }\n      status\n      rates\n      title\n      description\n      createdAt\n      expireAt\n      city\n      jobType\n      type\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.getRejectedJobs = getRejectedJobs
var postContactUs = (0, _client.gql)(
	_templateObject46(),
	'mutation Mutation($inputContactUs: contactUsInput) {\n  postContactUs(inputContactUs: $inputContactUs) {\n    message\n  }\n}'
)
exports.postContactUs = postContactUs
var myFriendships = (0, _client.gql)(
	_templateObject47(),
	'query MyFriendships($userId: ID!, $options: options) {\n  myFriendships(userId: $userId, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      name\n      profileVerified\n      role\n      photo\n    }\n  }\n}'
)
exports.myFriendships = myFriendships
var myFriendshipRequests = (0, _client.gql)(
	_templateObject48(),
	'query MyFriendshipRequests($options: options, $userId: ID!) {\n  myFriendshipRequests(options: $options, userId: $userId) {\n    limit\n    page\n    results {\n      _id\n      role\n      photo\n      name\n      profileVerified\n    }\n    totalPages\n    totalResults\n  }\n}'
)
exports.myFriendshipRequests = myFriendshipRequests
var getFriendsOfFriends = (0, _client.gql)(
	_templateObject49(),
	'query GetFriendsOfFriends($userId: ID!, $options: options) {\n  getFriendsOfFriends(userId: $userId, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      name\n      role\n      photo\n      profileVerified\n    }\n  }\n}'
)
exports.getFriendsOfFriends = getFriendsOfFriends
var allUsersSearch = (0, _client.gql)(
	_templateObject50(),
	'query AllUsersSearch($filters: SearchUserFilter, $options: options) {\n  allUsersSearch(filters: $filters, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      isOnline\n      name\n      photo\n      role\n    }\n  }\n}'
)
exports.allUsersSearch = allUsersSearch
var users = (0, _client.gql)(
	_templateObject51(),
	'query Users($options: options, $filters: userfilters) {\n  users(options: $options, filters: $filters) {\n    limit\n    page\n    results {\n      _id\n      role\n      photo\n      name\n      profileVerified\n    }\n    totalPages\n    totalResults\n  }\n}'
)
exports.users = users
var addFriendship = (0, _client.gql)(
	_templateObject52(),
	'mutation AddFriendship($friendId: ID!) {\n  addFriendship(friendId: $friendId) {\n    _id\n    name\n    role\n  }\n}'
)
exports.addFriendship = addFriendship
var acceptFriendship = (0, _client.gql)(
	_templateObject53(),
	'mutation AcceptFriendship($friendId: ID!) {\n  acceptFriendship(friendId: $friendId) {\n    _id\n    name\n    role\n  }\n}'
)
exports.acceptFriendship = acceptFriendship
var removeFriendship = (0, _client.gql)(
	_templateObject54(),
	'mutation RemoveFriendship($friendId: ID!) {\n  removeFriendship(friendId: $friendId) {\n    _id\n    role\n    name\n  }\n}'
)
exports.removeFriendship = removeFriendship
var getUserDetail = (0, _client.gql)(
	_templateObject55(),
	'query GetUserDetail($userId: ID!) {\n  getUserDetail(userId: $userId) {\n    _id\n    role\n    name\n    refrence_no\n    phone\n    email\n    address\n    country\n    city\n    zipCode\n    auth\n    phoneVerified\n    isEmailVerified\n    photo\n    video\n    coverImage\n    createdAt\n    updatedAt\n    notificationToken\n    is_active\n    socketId\n    isOnline\n    about\n    loc {\n      coordinates\n      type\n    }\n    isProfileCompleted\n    postedAddress\n    company {\n      _id\n      city\n      address\n      name\n      photo\n    }\n    experiances {\n      _id\n      company\n      endYear\n      startYear\n      title\n      detail\n    }\n    totalExperianceDays\n    educations {\n      title\n      startYear\n      institute\n      endYear\n      _id\n    }\n    awards {\n      _id\n      attachments\n      company\n      detail\n      endYear\n      startYear\n      title\n    }\n    drugTest\n    physical\n    licences\n    tankedForCompanies\n    routes\n\n    numberOfBargesPushThroughRiver\n    numberOfBargesPushThroughCanals\n    haveDE\n    pictureOfInformationOnthereLicences\n    costGuardPhysical\n    facebook\n    linkedIn\n    twitter\n    google\n    skills\n    companyWorkFor\n    yearsOfExperience\n    canYouHeadline\n    workedInFleetBefore\n    twicFront\n    twicBack\n    driverLicense\n    captainLicense\n    fccLicenseFront\n    radarCertificateFront\n    radioLicense\n    medicalCertificate\n    waterWays {\n        _id\n      name\n      parentWaterWay {\n        name\n        _id\n      }\n    }\n    languages\n    appliedJobs\n    savedJobs\n    completedJobs\n    dob\n    gender\n    ratings\n    ratingsCount\n    jobOfferNotification\n    friendRequestNotification\n    postNotification\n    generalNotification\n    chatNotifications\n    hideEmail\n    hideContact\n    hourlyRate\n    profileVerified\n    profileVerficationRequested\n    isApprovedByAdmin\n    hideRecommendations\n    licenceingReq\n    postSettings\n    hideReviews\n    deckhand_video\n    deckhand_text\n    disposable\n  }\n}'
)
exports.getUserDetail = getUserDetail
var createPost = (0, _client.gql)(
	_templateObject56(),
	'mutation CreatePost($inputPost: InputPost) {\n  createPost(inputPost: $inputPost) {\n    _id\n    content\n    videos\n    images\n    settings\n    author {\n      _id\n      photo\n      name\n    }\n    createdAt\n  }\n}'
)
exports.createPost = createPost
var posts = (0, _client.gql)(
	_templateObject57(),
	'query Posts($options: options, $filters: Postfilters) {\n  Posts(options: $options, filters: $filters) {\n     limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      author {\n        _id\n        photo\n        name\n        role\n      }\n      comments {\n        text\n        user {\n          _id\n          name\n          photo\n        }\n        _id\n        reply {\n          _id\n          text\n          user {\n            _id\n            name\n            photo\n          }\n          createdAt\n        }\n        createdAt\n      }\n      content\n      createdAt\n      images\n      likes\n      settings\n      videos\n    }\n  }\n}'
)
exports.posts = posts
var getWallPosts = (0, _client.gql)(
	_templateObject58(),
	'query GetWallPosts($options: options) {\n  getWallPosts(options: $options) {\n      limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      author {\n        _id\n        photo\n        name\n        role\n        profileVerified\n      }\n      comments {\n        text\n        user {\n          _id\n          name\n          photo\n        }\n        _id\n        reply {\n          _id\n          text\n          user {\n            _id\n            name\n            photo\n          }\n          createdAt\n        }\n        createdAt\n      }\n      content\n      createdAt\n      images\n      likes\n      settings\n      videos\n  }\n}\n}'
)
exports.getWallPosts = getWallPosts
var deletePost = (0, _client.gql)(
	_templateObject59(),
	'mutation DeletePost($deletePostInput: DeletePostInput) {\n  deletePost(deletePostInput: $deletePostInput) {\n    _id\n    author {\n      _id\n    }\n  }\n}'
)
exports.deletePost = deletePost
var postLike = (0, _client.gql)(
	_templateObject60(),
	'mutation PostLike($postLikeId: ID!, $inputLike: InputLike) {\n  postLike(id: $postLikeId, inputLike: $inputLike) {\n    _id\n    likes\n    author {\n      _id\n    }\n  }\n}'
)
exports.postLike = postLike
var postComment = (0, _client.gql)(
	_templateObject61(),
	'mutation PostComment($postId: ID!, $commentId: ID, $inputComment: InputComment, $replyId: ID) {\n  postComment(postId: $postId, commentId: $commentId, inputComment: $inputComment, replyId: $replyId) {\n    _id\n      comments {\n        _id\n        text\n        user {\n          _id\n          name\n          photo\n        }\n        reply {\n          _id\n          text\n          user {\n            _id\n            name\n            photo\n          }\n          createdAt\n        }\n        createdAt\n      }\n    type\n    title\n    status\n    createdAt\n    content\n    author {\n      _id\n      name\n      photo\n    }\n  }\n}'
)
exports.postComment = postComment
var createThread = (0, _client.gql)(
	_templateObject62(),
	'mutation CreateThread($inputThread: InputThread) {\n  createThread(inputThread: $inputThread) {\n    _id\n    content\n    type\n    author {\n      _id\n    }\n    createdAt\n    title\n  }\n}'
)
exports.createThread = createThread
var Threads = (0, _client.gql)(
	_templateObject63(),
	'query Threads($filters: Threadfilters, $options: options) {\n  Threads(filters: $filters, options: $options) {\n    results {\n      _id\n      thread_ref\n      author {\n        _id\n        name\n        photo\n      }\n      content\n      comments {\n        text\n        user {\n          _id\n          name\n          photo\n        }\n      }\n      createdAt\n      title\n      type\n      status\n    }\n    page\n    limit\n    totalPages\n    totalResults\n  }\n}'
)
exports.Threads = Threads
var getThreadById = (0, _client.gql)(
	_templateObject64(),
	'query GetThreadById($threadId: ID!) {\n  getThreadById(ThreadId: $threadId) {\n    _id\n    content\n    title\n    type\n    author {\n      _id\n      name\n      photo\n    }\n    comments {\n      createdAt\n      text\n      user {\n        _id\n        name\n        photo\n      }\n    }\n    createdAt\n    status\n    thread_ref\n  }\n}'
)
exports.getThreadById = getThreadById
var threadComment = (0, _client.gql)(
	_templateObject65(),
	'mutation ThreadComment($threadCommentId: ID!, $inputComment: InputComment) {\n  threadComment(id: $threadCommentId, inputComment: $inputComment) {\n    _id\n    author {\n      _id\n      name\n    }\n  }\n}'
)
exports.threadComment = threadComment
var getPostById = (0, _client.gql)(
	_templateObject66(),
	'query GetPostById($postId: ID!) {\n  getPostById(PostId: $postId) {\n    _id\n    content\n    images\n    videos\n    settings\n    author {\n      _id\n      name\n      photo\n      profileVerified\n    }\n      comments {\n        _id\n        text\n        user {\n          _id\n          name\n          photo\n        }\n        reply {\n          _id\n          text\n          user {\n            _id\n            name\n            photo\n          }\n          createdAt\n        }\n        createdAt\n      }\n    likes\n    createdAt\n  }\n}'
)
exports.getPostById = getPostById
var hasFriendRequest = (0, _client.gql)(_templateObject67(), 'query Query($friendId: ID!) {\n  hasFriendRequest(friendId: $friendId)\n}')
exports.hasFriendRequest = hasFriendRequest
var checkFriendship = (0, _client.gql)(
	_templateObject68(),
	'query CheckFriendship($friendId: ID!) {\n  checkFriendship(friendId: $friendId) {\n    _id\n    status\n    user {\n      _id\n      name\n      email\n      photo\n    }\n    friend {\n      _id\n      email\n      name\n      photo\n    }\n  }\n}'
)
exports.checkFriendship = checkFriendship
var getCheckRideOffers = (0, _client.gql)(
	_templateObject69(),
	'query GetCheckRideOffers($options: options, $filters: CheckRideOffersfilters) {\n  getCheckRideOffers(options: $options, filters: $filters) {\n    limit\n    page\n     totalPages\n    totalResults\n    results {\n      _id\n      checkRideId\n      company {\n        _id\n        name\n        photo\n         checkRideData {\n          hitchMax\n          hitchMin\n          price\n          terms\n        }\n      }\n      completedAt\n      createdAt\n      endDate\n      expireAt\n      price\n      startDate\n      status\n      user {\n        _id\n        photo\n        name\n      }\n    }\n  }\n}'
)
exports.getCheckRideOffers = getCheckRideOffers
var giveRecommendation = (0, _client.gql)(
	_templateObject70(),
	'mutation GiveRecommendation($userId: ID!, $text: String!) {\n  giveRecommendation(userId: $userId, text: $text) {\n    message\n  }\n}'
)
exports.giveRecommendation = giveRecommendation
var getMyRecommendations = (0, _client.gql)(
	_templateObject71(),
	'query Query($options: options) {\n  getMyRecommendations(options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      createdAt\n      text\n      updatedAt\n      user {\n        _id\n        name\n        photo\n        role\n      }\n    }\n  }\n}'
)
exports.getMyRecommendations = getMyRecommendations
var getRecommendationsById = (0, _client.gql)(
	_templateObject72(),
	'query GetRecommendationsById($userId: ID!, $options: options) {\n  getRecommendationsById(userId: $userId, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      createdAt\n      text\n      updatedAt\n      user {\n        _id\n        name\n        photo\n        role\n      }\n    }\n  }\n}'
)
exports.getRecommendationsById = getRecommendationsById
var getAvailableBalance = (0, _client.gql)(_templateObject73(), 'query GetAvailableBalance {\n  getAvailableBalance {\n    available\n  }\n}')
exports.getAvailableBalance = getAvailableBalance
var getMyBadges = (0, _client.gql)(_templateObject74(), 'query GetMyBadges {\n  getMyBadges {\n    badge\n    photo\n    name\n  }\n}')
exports.getMyBadges = getMyBadges
var applyBadge = (0, _client.gql)(_templateObject75(), 'mutation ApplyBadge($companyId: ID!) {\n  applyBadge(companyId: $companyId) {\n    message\n  }\n}')
exports.applyBadge = applyBadge
var Invoices = (0, _client.gql)(
	_templateObject76(),
	'query Invoices($filters: Invoicefilters, $options: options) {\n  Invoices(filters: $filters, options: $options) {\n    limit\n    page\n    totalPages\n    totalResults\n    results {\n      _id\n      amount\n      createdAt\n      status\n      application {\n        job {\n          title\n          company {\n            name\n            photo\n            _id\n          }\n        }\n      }\n      paymentOf\n      checkRide {\n        company {\n          name\n          _id\n          photo\n        }\n      }\n    }\n  }\n}'
)
exports.Invoices = Invoices
var Licensings = (0, _client.gql)(
	_templateObject77(),
	'query Licensings($filters: Licensingfilters, $options: options) {\n  Licensings(filters: $filters, options: $options) {\n    results {\n      _id\n      title\n      addedBy {\n        name\n        photo\n      }\n      createdAt\n    }\n    page\n    totalPages\n    totalResults\n    limit\n  }\n}'
)
exports.Licensings = Licensings
var GetLicensingQuestions = (0, _client.gql)(
	_templateObject78(),
	'query GetLicensingQuestions($licensingId: ID) {\n  getLicensingQuestions(LicensingId: $licensingId) {\n    _id\n    answer\n    isActive\n    question\n  }\n}'
)
exports.GetLicensingQuestions = GetLicensingQuestions
var NearMeCaptian = (0, _client.gql)(
	_templateObject79(),
	'query NearMeCaptian($latlng: String!, $maxDistance: String!, $options: options) {\n  nearMeCaptian(latlng: $latlng, maxDistance: $maxDistance, options: $options) {\n    limit\n    page\n    totalPages\n    results {\n      _id\n      companyWorkFor\n      distance\n      email\n      name\n      isOnline\n      phone\n      photo\n      role\n        loc {\n        coordinates\n        type\n      }\n    }\n    totalResults\n  }\n}'
)
exports.NearMeCaptian = NearMeCaptian
var sendRecoveryEmailVerification = (0, _client.gql)(
	_templateObject80(),
	'mutation SendRecoveryEmailVerification($email: String) {\n  sendRecoveryEmailVerification(email: $email) {\n    email\n    result\n  }\n}'
)
exports.sendRecoveryEmailVerification = sendRecoveryEmailVerification
var verifyRecoveryEmail = (0, _client.gql)(
	_templateObject81(),
	'mutation VerifyRecoveryEmail($code: String, $email: String) {\n  verifyRecoveryEmail(code: $code, email: $email) {\n    email\n    result\n  }\n}'
)
exports.verifyRecoveryEmail = verifyRecoveryEmail
var DeleteAccount = (0, _client.gql)(_templateObject82(), 'mutation DeleteAccount($email: String) {\n  deleteAccount(email: $email) {\n    message\n  }\n}')
exports.DeleteAccount = DeleteAccount
var getJobCities = (0, _client.gql)(_templateObject83(), 'query Query($text: String) {\n  getJobCities(text: $text)\n}')
exports.getJobCities = getJobCities
var getMyBadgeRequests = (0, _client.gql)(_templateObject84(), 'query Query {\n  getMyBadgeRequests\n}')
exports.getMyBadgeRequests = getMyBadgeRequests
var getKeyByFilter = (0, _client.gql)(
	_templateObject85(),
	'query GetKeyByFilter($filters: SystemConfigfilters) {\n  getKeyByFilter(filters: $filters) {\n    createdAt\n    description\n    key\n    specific_for\n    unit\n    updatedAt\n    value\n  }\n}'
)
exports.getKeyByFilter = getKeyByFilter
var createStripeAccount = (0, _client.gql)(_templateObject86(), 'mutation CreateStripeAccount {\n  createStripeAccount {\n    expires_at\n    url\n  }\n}')
exports.createStripeAccount = createStripeAccount
var updateUserCheckrInfo = (0, _client.gql)(
	_templateObject87(),
	'mutation UpdateUserCheckrInfo($updateCheckrInput: UpdateCheckrInput) {\n  updateUserCheckrInfo(updateCheckrInput: $updateCheckrInput) {\n    _id\n    checkrInviteUrl\n    checkr_candidate_id\n    checkr_decision\n    checkr_status\n  }\n}'
)
exports.updateUserCheckrInfo = updateUserCheckrInfo
var getInvoiceById = (0, _client.gql)(
	_templateObject88(),
	'query GetInvoiceById($invoiceId: ID) {\n  getInvoiceById(InvoiceId: $invoiceId) {\n    _id\n    updatedAt\n    amount\n    application {\n      _id\n      company {\n        photo\n        name\n        city\n      }\n      currentSalary\n      createdAt\n      job {\n        type\n        title\n      }\n    }\n    commission\n    createdAt\n    invoice_group\n    invoice_ref\n    jobDays\n    paymentOf\n    status\n    stripePaymentId\n    checkRide {\n      _id\n      checkRideData {\n        hitchMax\n        hitchMin\n      }\n       company {\n        photo\n        name\n        city\n      }\n    }\n  }\n}'
)
exports.getInvoiceById = getInvoiceById
var getInvoiceTransaction = (0, _client.gql)(
	_templateObject89(),
	'query GetInvoiceTransaction($invoiceId: ID!) {\n  getInvoiceTransaction(invoiceId: $invoiceId) {\n    action\n    amount\n    createdAt\n    current_update\n    description\n    invoice_id\n    money_movement {\n      title\n      type\n      description\n    }\n    negative_balance {\n      amount\n      createdAt\n    }\n    status\n    transaction_fees\n    type\n    receipt_url\n  }\n}'
)
exports.getInvoiceTransaction = getInvoiceTransaction
var reqLicensing = (0, _client.gql)(_templateObject90(), 'mutation ReqLicensing {\n  reqLicensing {\n    _id\n  }\n}')
exports.reqLicensing = reqLicensing
var refreshTokens = (0, _client.gql)(
	_templateObject91(),
	'mutation RefreshTokens($refreshToken: String!) {\n  refreshTokens(refreshToken: $refreshToken) {\n    refreshToken\n    refreshTokenExpires\n    token\n    tokenExpiration\n  }\n}'
)
exports.refreshTokens = refreshTokens
