import AsyncStorage from '@react-native-async-storage/async-storage'
import { ApolloClient, InMemoryCache, ApolloLink, createHttpLink, split, concat, Observable, defaultDataIdFromObject } from '@apollo/client'
import { WebSocketLink } from '@apollo/client/link/ws'
import { onError } from '@apollo/client/link/error'
import { getMainDefinition } from '@apollo/client/utilities'
import { persistCache } from 'apollo3-cache-persist'

import getEnvVars from '../../environment'
import navigationService from '../routes/navigationService'

const { GRAPHQL_URL, WS_GRAPHQL_URL } = getEnvVars()

const cache = new InMemoryCache({
	dataIdFromObject: object => {
		switch (object.__typename) {
			default:
				return defaultDataIdFromObject(object) // fall back to default handling
		}
	},
})
const httpLink = createHttpLink({
	uri: GRAPHQL_URL,
	options: {
		reconnect: true,
	},
})

const wsLink = new WebSocketLink({
	uri: WS_GRAPHQL_URL,
	options: {
		reconnect: true,
	},
})

// const wsLink = new WebSocketLink(
//   new SubscriptionClient(WS_GRAPHQL_URL, {
//     reconnect: true, // Enable automatic reconnection
//   }),
// );

const request = async operation => {
	const token = await AsyncStorage.getItem('token')
	operation.setContext({
		// get the authentication token from local storage if it exists
		// return the headers to the context so httpLink can read them
		headers: {
			authorization: token,
		},
	})
}

const requestLink = new ApolloLink(
	(operation, forward) =>
		new Observable(observer => {
			// console.log(observer)
			let handle
			Promise.resolve(operation)
				.then(oper => request(oper))
				.then(() => {
					handle = forward(operation).subscribe({
						next: observer.next.bind(observer),
						error: observer.error.bind(observer),
						complete: observer.complete.bind(observer),
					})
				})
				.catch(observer.error.bind(observer))

			return () => {
				if (handle) handle.unsubscribe()
			}
		})
)

const terminatingLink = split(({ query }) => {
	const { kind, operation } = getMainDefinition(query)
	return kind === 'OperationDefinition' && operation === 'subscription'
}, wsLink)

const setupData = async _client => {
	try {
		const _token = await AsyncStorage.getItem('token')
		// cache.writeData({
		//     data: {
		//         // configuration: data.configuration,
		//         // notifications: [],
		//         isLoggedIn: !!token,
		//         // cartItems: cartItems ? JSON.parse(cartItems).length : 0
		//     }
		// })
	} catch (error) {
		console.log('setupData Error', error)
	}
}

const errorLink = onError(({ graphQLErrors, networkError }) => {
	if (graphQLErrors) {
		graphQLErrors?.forEach(({ message, locations, path }) => {
			console.log(`[GraphQL error]: Message: ${JSON.stringify(message)}, Location: ${JSON.stringify(locations)}, Path: ${path}`)
			// if (message.includes('Cannot')) {
			//   navigationService.navigate('ErrorScreen');
			// }
		})
	}
	if (networkError) {
		console.log(`[Network error]: ${networkError?.message}`)
		if (networkError?.message === 'Network request failed') {
			navigationService.ResetNavigationTo()
		}
	}
})

const setupApollo = async () => {
	await persistCache({
		cache,
		storage: AsyncStorage,
	})
	const client = new ApolloClient({
		link: concat(ApolloLink.from([errorLink, requestLink, terminatingLink]), httpLink),
		cache,
		defaultOptions: {
			watchQuery: {
				fetchPolicy: 'network-only',
				nextFetchPolicy: 'network-only',
			},
		},
		resolvers: {
			User: {
				isLoggedIn: async (_profile, _args, { cache }) => {
					try {
						const token = await AsyncStorage.getItem('token')
						return !!token
					} catch (err) {
						console.log('setupApollo catch:', err)
					}
					return false
				},
			},
		},
	})
	await setupData(client)
	client.onClearStore(setupData)
	client.onResetStore(setupData)
	return client
}

export default setupApollo
