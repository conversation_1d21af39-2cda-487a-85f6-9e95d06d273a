import { gql } from '@apollo/client'

export const signup = gql`
  ${`
  mutation Mutation($userInput: UserInput) {
  signup(userInput: $userInput) {
    _id
    token
    tokenExpiration
    role
    name
    refrence_no
    phone
    email
    address
    country
    city
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    video
    coverImage
    createdAt
    updatedAt
    notificationToken
    is_active
    socketId
    isOnline
    about
    loc {
      coordinates
      type
    }
    isProfileCompleted
  }
}
`}
`

export const crewmemberRegister = gql`
  ${`mutation CrewmemberRegister($userInput: UserInput) {
  crewmemberRegister(userInput: $userInput) {
     _id
    token
    tokenExpiration
    role
    name
    refrence_no
    email
    auth
    phoneVerified
    isEmailVerified
    notificationToken
    isProfileCompleted
    
    
    
    
  }
}`}
`

export const mobileLogin = gql`
  ${`mutation MobileLogin($email: String, $password: String, $notificationToken: String) {
  mobileLogin(email: $email, password: $password, notificationToken: $notificationToken) {
    _id
    isProfileCompleted
    token
    tokenExpiration
    refreshToken
    refreshTokenExpires
    role
    name
    refrence_no
    phone
    email
    address
    country
    city
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    video
    coverImage
    createdAt
    updatedAt
    notificationToken
    is_active
    socketId
    isOnline
    about
    loc {
      coordinates
      type
    }
  }
}`}
`

export const forgotPassword = gql`
  ${`mutation ForgotPassword($email: String!) {
  forgotPassword(email: $email) {
    result
  }
}`}
`

export const ForgotPasswordVerification = gql`
  ${`mutation ForgotPasswordVerification($email: String!, $code: String!) {
  forgotPasswordVerification(email: $email, code: $code) {
    result
    token
  }
}`}
`

export const ForgetPasswordChange = gql`
  ${`mutation ForgetPasswordChange($token: String!, $password: String!) {
  forgetPasswordChange(token: $token, password: $password) {
    result
  }
}`}
`

export const sendVerificationEmail = gql`
  ${`mutation SendVerificationEmail($email: String) {
  sendVerificationEmail(email: $email) {
    email
    result
  }
}`}
`

export const verifyEmail = gql`
  ${`mutation VerifyEmail($code: String, $email: String) {
  verifyEmail(code: $code, email: $email) {
    email
    result
  }
}`}
`

export const roles = gql`
  ${`query Query {
  roles
}`}
`

export const updateUser = gql`
  ${`mutation UpdateUser($updateUserInput: UpdateUserInput) {
  updateUser(updateUserInput: $updateUserInput) {
    _id
    role
    name
    first_name
    last_name
    middle_name
    name_initial
    otherWaterWays
    refrence_no
    phone
    email
    address
    country
    city
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    video
    coverImage
    createdAt
    updatedAt
    notificationToken
    is_active
    socketId
    isOnline
    loc {
      coordinates
      type
    }
    isProfileCompleted
    postedAddress
    company {
      _id
      email
      name
    }
    
    
    facebook
    linkedIn
    twitter
    google
    companyWorkFor
    yearsOfExperience
    canYouHeadline
    workedInFleetBefore
    twicFront
    twicBack
    
    
    
    
    
    
    educations {
      endYear
      institute
      startYear
      title
    }
    experiances {
      title
      startYear
      company
      endYear
    }
    awards {
      attachments
      company
      detail
      endYear
      startYear
      title
    }
    waterWays {
      _id
      name
      parentWaterWay {
        _id
        name
      }
    }
    dob
    gender
    languages
    skills
    haveDE
    
    numberOfBargesPushThroughCanals
    numberOfBargesPushThroughRiver
    
    routes
    tankedForCompanies
    
    about
    hourlyRate
    hideEmail
    hideContact
    jobOfferNotification
    generalNotification
    friendRequestNotification
    postNotification
    ratings
    ratingsCount
    totalExperianceDays
    chatNotifications
    profileVerficationRequested
    profileVerified
    bankAccountNo
    bankName
    friends
    ifscCode
    bankAccountTitle
    hideRecommendations
    isApprovedByAdmin
    licenceingReq
    postSettings
    routingNo
    hideReviews
    state
    
    
    
    
    deckhand_text
    deckhand_video
    deleted
    disposable
    waterways_learning_enabled
    waterways_learning {
      _id
      name
      parentWaterWay {
        name
        _id
      }
    }
  }
}
`}
`

export const getAppliedJobs = gql`
  ${`query GetAppliedJobs($options: options) {
  getAppliedJobs(options: $options) {
    results {
      _id
      company {
        _id
        email
        name
        coverImage
        website
        photo
      }
      title
      description
      email
      speciality
      type
      jobType
      offeredSalary
      careerLevel
      experience
      name_of_vessel
      industry
      qualification
      country
      city
      address
      zipCode
      expireAt
      createdAt
      updatedAt
      rates
      ratesType
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getAppliedJobsIds = gql`
  ${`query GetAppliedJobs($options: options) {
  getAppliedJobs(options: $options) {
    results {
      _id
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const applications = gql`
  ${`query Applications($filters: applicationfilters) {
  applications(filters: $filters) {
    results {
         contract {
        status
        reason
        startDate
        endDate
        acceptedOffer {
          by {
            name
          }
          status
        }
      }
      offers {
        _id
        by {
          _id
          name
          role
        }
        rates
        status
      }
      _id
      applicant {
        _id
        email
        name
      }
      hourlyRates
      isHired
    }
    limit
    page
    totalPages
    totalResults
  }
}`}
`

export const createOffer = gql`
  ${`mutation CreateOffer($applicationId: ID!, $inputOffer: InputOffer) {
  createOffer(applicationId: $applicationId, inputOffer: $inputOffer) {
    _id
    by {
      _id
      name
      role
    }
    rates
    status
  }
}`}
`

export const getJob = gql`
  ${`query GetJob($jobId: ID!) {
  getJob(jobId: $jobId) {
    _id
    company {
      _id
      address
      email
      coverImage
      linkedIn
      facebook
      twitter
      website
      photo
    }
    loc {
      type
      coordinates
    }
    title
    description
    email
    speciality
    deleted
    type
    jobType
    offeredSalary
    careerLevel
    experience
    industry
    qualification
    country
    city
    address
    zipCode
    expireAt
    rates
    ratesType
    createdAt
    updatedAt
    startDate
    noOfApplications
    noOfCandidates
    horsePower
    skills
    jobShift
    name_of_vessel
    status
    isActive
  }
}`}
`

export const createApplication = gql`
  ${`mutation CreateApplication($inputApplication: InputApplication) {
  createApplication(inputApplication: $inputApplication) {
    _id
    company {
      _id
    }
    job {
      _id
    }
    applicant {
      _id
      email
    }
    experience
    currentSalary
    expectedSalary
    gender
    type
    language
    educationLevel
    createdAt
    updatedAt
    coverletter
    hourlyRates
  }
}`}
`

export const saveJob = gql`
  ${`mutation SaveJob($saveJobId: ID!) {
  saveJob(id: $saveJobId) {
    message
  }
}`}
`

export const getSavedJobs = gql`
  ${`query GetSavedJobs($options: options) {
  getSavedJobs(options: $options) {
    results {
      _id
      company {
      _id
        email
        name
        coverImage
        website
        photo
      }
      title
      description
      email
      speciality
      type
      jobType
      offeredSalary
      careerLevel
      experience
      industry
      qualification
      country
      city
      address
      zipCode
      expireAt
      rates
      ratesType
      createdAt
      updatedAt
      status
      name_of_vessel
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getCompletedJobs = gql`
  ${`query GetCompletedJobs($options: options, $filters: CompletedJobFilter) {
  getCompletedJobs(options: $options, filters: $filters) {
    results {
      _id
      company {
        _id
        coverImage
        email
        name
        website
        photo
      }
      title
      description
      email
      speciality
      type
      jobType
      offeredSalary
      careerLevel
      experience
      industry
      qualification
      country
      city
      address
      zipCode
      expireAt
      rates
      ratesType
      createdAt
      updatedAt
      name_of_vessel
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getCities = gql`
  ${`query Query {
  getCities
}`}
`

export const removeSavedJob = gql`
  ${`mutation RemoveSavedJob($removeSavedJobId: ID!) {
  removeSavedJob(id: $removeSavedJobId) {
    message
  }
}`}
`

export const companies = gql`
  ${`query Query($filters: companyfilters, $options: options) {
  companies(filters: $filters, options: $options) {
    totalPages
    totalResults
    limit
    page
    results {
      _id
      role
      name
      refrence_no
      phone
      email
      address
      country
      city
      zipCode
      auth
      phoneVerified
      isEmailVerified
      photo
      video
      openJobs
      coverImage
      createdAt
      updatedAt
      notificationToken
      is_active
      socketId
      isOnline
      about
      isProfileCompleted
      aboutFleet
      gallery
      industry
      establishedIn
      companySize
      foundIn
      website
      facebook
      linkedIn
      twitter
      google
      emergencyContactInformation {
        email
        phone
      }
    }
  }
}`}
`

export const profile = gql`
  ${`query Profile {
  profile {
    _id
    hourlyRate
    role
    name
    first_name
    last_name
    middle_name
    name_initial
    otherWaterWays
    refrence_no
    phone
    email
    address
    country
    chatNotifications
    city
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    savedJobs
    video
    coverImage
    createdAt
    updatedAt
    appliedJobs
    notificationToken
    is_active
    socketId
    isOnline
    ratingsCount
    ratings
    about
    loc {
      coordinates
      type
    }
    isProfileCompleted
    postedAddress
    company {
      _id
      email
      name
    }
    experiances {
      company
      endYear
      title
      startYear
      detail
    }
    educations {
      endYear
      institute
      startYear
      title
    }
    awards {
      attachments
      company
      detail
      endYear
      startYear
      title
      _id
    }
    tankedForCompanies
    routes
    numberOfBargesPushThroughRiver
    numberOfBargesPushThroughCanals
    haveDE
    facebook
    linkedIn
    twitter
    google
    skills
    companyWorkFor
    yearsOfExperience
    canYouHeadline
    workedInFleetBefore
    twicFront
    twicBack
    languages
    dob
    gender
    friendRequestNotification
    generalNotification
    jobOfferNotification
    hideEmail
    hideContact
    postNotification
    waterWays {
      _id
      name
      parentWaterWay {
        name
        _id
      }
    }
    bankName
    bankAccountNo
    profileVerified
    profileVerficationRequested
    friends
    ifscCode
    totalExperianceDays
    bankAccountTitle
    isApprovedByAdmin
    hideRecommendations
    licenceingReq
    postSettings
    routingNo
    hideReviews
    state
    deleted
    deckhand_text
    deckhand_video
    disposable
    checkr_decision
    checkr_status
    waterways_learning_enabled
    waterways_learning {
      _id
      name
      parentWaterWay {
        name
        _id
      }
    }
  }
}`}
`

export const waterWays = gql`
  ${`query WaterWays($filters: WaterWayfilters, $options: options) {
  WaterWays(filters: $filters, options: $options) {
    results {
      _id
      name
      subWaterWays {
        name
        _id
      }
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getCompanyDetail = gql`
  ${`query GetCompanyDetail($companyId: ID!) {
  getCompanyDetail(companyId: $companyId) {
    _id
    role
    name
    refrence_no
    phone
    email
    address
    country
    city
    state
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    video
    coverImage
    createdAt
    updatedAt
    deleted
    notificationToken
    is_active
    socketId
    isOnline
    about
    loc {
      coordinates
      type
    }
    isProfileCompleted
    aboutFleet
    fleetImage
    gallery
    industry
    establishedIn
    companySize
    foundIn
    checkRideData {
      hitchMax
      hitchMin
      price
      terms
    }
    isCheckRideEnabled
    website
    facebook
    linkedIn
    twitter
    google
    emergencyContactInformation {
      photo
      phone
      name
      email
      designation
    }
    openJobs
    hideEmail
    hideContact
  }
}`}
`

export const acceptOffer = gql`
  ${`mutation AcceptOffer($applicationId: ID!, $offerId: ID!) {
  acceptOffer(applicationId: $applicationId, offerId: $offerId) {
    _id
    by {
      _id
      name
      role
    }
    status
    rates
  }
}`}
`

export const rejectOffer = gql`
  ${`mutation RejectOffer($applicationId: ID!, $offerId: ID!) {
  rejectOffer(applicationId: $applicationId, offerId: $offerId) {
    _id
    by {
      _id
      name
      role
    }
    rates
    status
  }
}`}
`

export const languages = gql`
  ${`query Languages($options: options, $filters: Languagefilters) {
  Languages(options: $options, filters: $filters) {
    limit
    page
    results {
      _id
      language
    }
    totalPages
    totalResults
  }
}`}
`

export const changepassword = gql`
  ${`mutation ChangePassword($password: String!, $newPassword: String!, $email: String!) {
  changePassword(password: $password, newPassword: $newPassword, email: $email) {
    result
  }
}`}
`

export const getAppliedCheckRides = gql`
  ${`query GetAppliedCheckRides($options: options) {
  getAppliedCheckRides(options: $options) {
    limit
    results {
      _id
      company {
        isOnline
        _id
        email
        photo
        name
        website
        about
        companySize
        city
         checkRideData {
          hitchMax
          hitchMin
          price
          terms
        }
      }
      createdAt
      offers {
        _id
        completedAt
        endDate
        expireAt
        startDate
        status
        price
      }
      user {
        _id
      }
      updatedAt
    }
    totalPages
    totalResults
    page
  }
}`}
`

export const Ratings = gql`
  ${`query Ratings($filters: Ratingfilters, $options: options) {
  Ratings(filters: $filters, options: $options) {
      page
    limit
    totalPages
    totalResults
    results {
      _id
      avgRating
      by {
        _id
        name
        photo
        phone
        email
      }
      review
      reliability
      punctuality
      professional_development
      productivity
      performance
      createdAt
      user {
        ratings
        ratingsCount
        _id
      }
    }
  
  }
}`}
`

export const applyCheckRide = gql`
  ${`mutation ApplyCheckRide($companyId: ID!) {
  applyCheckRide(companyId: $companyId) {
    _id
    createdAt
  }
}`}
`

export const getNotifications = gql`
  ${`query GetNotifications($filters: Notificationfilters,$options: options) {
  getNotifications(filters: $filters,options: $options) {
    page
    limit
    totalPages
    totalResults
    results {
      _id
      image
      meta {
        applicationId
        id
        jobId
        postId
        invoiceId
        invoiceType
        bookingId
        RBPId
        appointmentId
      }
      seen
      subTitle
      text
      title
      to {
        _id
        name
      }
      createdAt
      from {
        _id
        photo
        name
      }
      type
    }
  }
}`}
`

export const getNotificationsForContext = gql`
  ${`query GetNotifications($filters: Notificationfilters,$options: options) {
  getNotifications(filters: $filters,options: $options) {
    page
    limit
    totalPages
    totalResults
    results {
      _id
      seen
    }
  }
}`}
`

export const readNotification = gql`
  ${`mutation ReadNotification($notificationId: ID!) {
  readNotification(notificationId: $notificationId) {
    seen
    to {
      _id
      name
    }
    from {
      _id
      name
    }
  }
}`}
`

export const readAll = gql`
  ${`mutation ReadAll {
  readAll {
    message
  }
}`}
`

export const applicationListener = gql`
  ${`subscription ApplicationListener($applicationId: ID!) {
  applicationListener(applicationId: $applicationId) {
    _id
    currentSalary
    type
    createdAt
    applicant {
      _id
    }
    offers {
      _id
      by {
        _id
        name
        role
      }
      status
      rates
    }
  }
}`}
`

export const jobs = gql`
  ${`query Jobs($filters: jobfilters, $options: options) {
  jobs(filters: $filters, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      company {
        photo
      }
      title
      type
      jobType
      city
      createdAt
      rates
      description
      name_of_vessel
    }
  }
}`}
`

export const getActiveJobs = gql`
  ${`query GetActiveJobs($options: options) {
  getActiveJobs(options: $options) {
    limit
    page
    results {
      _id
      company {
        _id
        name
        photo
        website
      }
      title
      city
      expireAt
      rates
      type
      jobType
      createdAt
      description
      name_of_vessel
    }
  }
}`}
`

export const deactiveAccount = gql`
  ${`mutation DeactiveAccount($email: String) {
  deactiveAccount(email: $email) {
    message
  }
}`}
`

export const getChatRooms = gql`
  ${`query GetChatRooms($filters: roomfilters, $options: options) {
  getChatRooms(filters: $filters, options: $options) {
    results {
      _id
      chatName
      connectedUsers {
        isOnline
        name
        _id
      }
      lastMessage {
        at
        message
        seenBy
        status
      }
      primeUser {
        _id
        isOnline
        name
      }
      users {
        _id
        isOnline
        name
        photo
        role
      }
      updatedAt
      unseenCount
      createdAt
      isAnonymousChat
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getRoomById = gql`
  ${`query GetRoomById($getRoomByIdId: ID!) {
  getRoomById(id: $getRoomByIdId) {
    _id
    chatName
    connectedUsers {
       isOnline
        name
        _id
    }
    createdAt
    isAnonymousChat
    lastMessage {
       at
        message
        seenBy
        status 
    }
    users {
       _id
        isOnline
        name
        photo
        role
    }
  }
}`}
`

export const getMessages = gql`
  ${`query GetMessages($options: options, $filters: messagefilters) {
  getMessages(options: $options, filters: $filters) {
    limit
    page
     totalPages
    totalResults
    results {
      message
      from {
        _id
        name
        photo
      }
      seenBy 
      to {
        _id
        name
        photo
      }
      type
      at
      files
      _id
      createdAt
       cardData {
        cardID
        description
        photo
        title
        shareType
      }
    }
  }
}`}
`

export const createChatRoom = gql`
  ${`mutation CreateChatRoom($inputRoom: InputRoom) {
  createChatRoom(inputRoom: $inputRoom) {
    connectedUsers {
      _id
      name
    }
    chatName
    _id
    users {
      _id
      isOnline
      name
      photo
      role
    }
    createdAt
    isAnonymousChat
  }
}`}
`

export const rejectCheckRideOffer = gql`
  ${`mutation RejectCheckRideOffer($checkRideId: ID!, $offerId: ID!) {
  rejectCheckRideOffer(checkRideId: $checkRideId, offerId: $offerId) {
    checkRideData {
      hitchMax
      hitchMin
      price
      terms
    }
    company {
      _id
    }
    offers {
      status
      startDate
      expireAt
      endDate
      completedAt
    }
    user {
      _id
    }
    _id
  }
}`}
`

export const acceptCheckRideOffer = gql`
  ${`mutation AcceptCheckRideOffer($checkRideId: ID!, $offerId: ID!) {
  acceptCheckRideOffer(checkRideId: $checkRideId, offerId: $offerId) {
    _id
    checkRideData {
      terms
      price
      hitchMin
      hitchMax
    }
    company {
      _id
    }
    createdAt
    offers {
      startDate
      endDate
      status
      expireAt
      completedAt
    }
    user {
      _id
    }
  }
}`}
`

export const getRejectedJobs = gql`
  ${`query GetRejectedJobs($options: options) {
  getRejectedJobs(options: $options) {
    results {
      _id
      company {
        _id
        name
        website
        photo
      }
      status
      rates
      title
      description
      createdAt
      expireAt
      city
      jobType
      type
      name_of_vessel
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const postContactUs = gql`
  ${`mutation Mutation($inputContactUs: contactUsInput) {
  postContactUs(inputContactUs: $inputContactUs) {
    message
  }
}`}
`

export const myFriendships = gql`
  ${`query MyFriendships($userId: ID!, $options: options) {
  myFriendships(userId: $userId, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      name
      profileVerified
      role
      photo
    }
  }
}`}
`

export const myFriendshipRequests = gql`
  ${`query MyFriendshipRequests($options: options, $userId: ID!) {
  myFriendshipRequests(options: $options, userId: $userId) {
    limit
    page
    results {
      _id
      role
      photo
      name
      profileVerified
    }
    totalPages
    totalResults
  }
}`}
`

export const getFriendsOfFriends = gql`
  ${`query GetFriendsOfFriends($userId: ID!, $options: options) {
  getFriendsOfFriends(userId: $userId, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      name
      role
      photo
      profileVerified
    }
  }
}`}
`

export const getSuggestionList = gql`
  ${`query GetSuggestionList($userId: ID!, $options: options) {
  getSuggestionList(userId: $userId, options: $options) {
    totalPages
    totalResults
    limit
    page
    results {
      _id
      role
      name
      refrence_no
      phone
      email
      address
      country
      city
      photo
      profileVerified
    }
  }
}`}
`

export const allUsersSearch = gql`
  ${`query AllUsersSearch($filters: SearchUserFilter, $options: options) {
  allUsersSearch(filters: $filters, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      isOnline
      name
      photo
      role
    }
  }
}`}
`

export const users = gql`
  ${`query Users($options: options, $filters: userfilters) {
  users(options: $options, filters: $filters) {
    limit
    page
    results {
      _id
      role
      photo
      name
      profileVerified
    }
    totalPages
    totalResults
  }
}`}
`

export const addFriendship = gql`
  ${`mutation AddFriendship($friendId: ID!) {
  addFriendship(friendId: $friendId) {
    _id
    name
    role
  }
}`}
`

export const acceptFriendship = gql`
  ${`mutation AcceptFriendship($friendId: ID!) {
  acceptFriendship(friendId: $friendId) {
    _id
    name
    role
  }
}`}
`

export const removeFriendship = gql`
  ${`mutation RemoveFriendship($friendId: ID!) {
  removeFriendship(friendId: $friendId) {
    _id
    role
    name
  }
}`}
`

export const getUserDetail = gql`
  ${`query GetUserDetail($userId: ID!) {
  getUserDetail(userId: $userId) {
    _id
    role
    name
    refrence_no
    phone
    email
    address
    country
    city
    zipCode
    auth
    phoneVerified
    isEmailVerified
    photo
    video
    coverImage
    createdAt
    updatedAt
    notificationToken
    is_active
    socketId
    isOnline
    about
    loc {
      coordinates
      type
    }
    isProfileCompleted
    postedAddress
    company {
      _id
      city
      address
      name
      photo
    }
    experiances {
      _id
      company
      endYear
      startYear
      title
      detail
    }
    totalExperianceDays
    educations {
      title
      startYear
      institute
      endYear
      _id
    }
    awards {
      _id
      attachments
      company
      detail
      endYear
      startYear
      title
    }
    
    
    
    tankedForCompanies
    routes

    numberOfBargesPushThroughRiver
    numberOfBargesPushThroughCanals
    haveDE
    
    
    facebook
    linkedIn
    twitter
    google
    skills
    companyWorkFor
    yearsOfExperience
    canYouHeadline
    workedInFleetBefore
    twicFront
    twicBack
    
    
    
    
    
    
    waterWays {
        _id
      name
      parentWaterWay {
        name
        _id
      }
    }
    languages
    appliedJobs
    savedJobs
    completedJobs
    dob
    gender
    ratings
    ratingsCount
    jobOfferNotification
    friendRequestNotification
    postNotification
    generalNotification
    chatNotifications
    hideEmail
    hideContact
    hourlyRate
    profileVerified
    profileVerficationRequested
    isApprovedByAdmin
    hideRecommendations
    licenceingReq
    postSettings
    hideReviews
    deckhand_video
    deckhand_text
    disposable
     waterways_learning_enabled
    waterways_learning {
      _id
      name
      parentWaterWay {
        name
        _id
      }
    }
  }
}`}
`

export const createPost = gql`
  ${`mutation CreatePost($inputPost: InputPost) {
  createPost(inputPost: $inputPost) {
    _id
    content
    videos
    images
    settings
    status
    author {
      _id
      photo
      name
    }
    comments {
        text
        user {
          _id
          name
          photo
        }
        _id
        reply {
          _id
          text
          user {
            _id
            name
            photo
          }
          createdAt
        }
        createdAt
      }
    likes
    createdAt
  }
}`}
`

export const posts = gql`
  ${`query Posts($options: options, $filters: Postfilters) {
  Posts(options: $options, filters: $filters) {
     limit
    page
    totalPages
    totalResults
    results {
      _id
      author {
        _id
        photo
        name
        role
      }
      comments {
        text
        user {
          _id
          name
          photo
        }
        _id
        reply {
          _id
          text
          user {
            _id
            name
            photo
          }
          createdAt
        }
        createdAt
      }
      content
      createdAt
      images
      likes
      settings
      status
      videos
    }
  }
}`}
`

export const getWallPosts = gql`
  ${`query GetWallPosts($options: options) {
  getWallPosts(options: $options) {
      limit
    page
    totalPages
    totalResults
    results {
      _id
      author {
        _id
        photo
        name
        role
        profileVerified
      }
      comments {
        text
        user {
          _id
          name
          photo
        }
        _id
        reply {
          _id
          text
          user {
            _id
            name
            photo
          }
          createdAt
        }
        createdAt
      }
      content
      createdAt
      images
      likes
      settings
      status
      videos
  }
}
}`}
`

export const deletePost = gql`
  ${`mutation DeletePost($deletePostInput: DeletePostInput) {
  deletePost(deletePostInput: $deletePostInput) {
    _id
    author {
      _id
    }
  }
}`}
`

export const postLike = gql`
  ${`mutation PostLike($postLikeId: ID!, $inputLike: InputLike) {
  postLike(id: $postLikeId, inputLike: $inputLike) {
    _id
    likes
    author {
      _id
    }
  }
}`}
`

export const postComment = gql`
  ${`mutation PostComment($postId: ID!, $commentId: ID, $inputComment: InputComment, $replyId: ID) {
  postComment(postId: $postId, commentId: $commentId, inputComment: $inputComment, replyId: $replyId) {
    _id
      comments {
        _id
        text
        user {
          _id
          name
          photo
        }
        reply {
          _id
          text
          user {
            _id
            name
            photo
          }
          createdAt
        }
        createdAt
      }
    type
    title
    status
    createdAt
    content
    author {
      _id
      name
      photo
    }
  }
}`}
`

export const createThread = gql`
  ${`mutation CreateThread($inputThread: InputThread) {
  createThread(inputThread: $inputThread) {
    _id
    content
    type
    author {
      _id
    }
    createdAt
    title
  }
}`}
`

export const Threads = gql`
  ${`query Threads($filters: Threadfilters, $options: options) {
  Threads(filters: $filters, options: $options) {
    results {
      _id
      thread_ref
      author {
        _id
        name
        photo
      }
      content
      comments {
        text
        user {
          _id
          name
          photo
        }
      }
      createdAt
      title
      type
      status
    }
    page
    limit
    totalPages
    totalResults
  }
}`}
`

export const getThreadById = gql`
  ${`query GetThreadById($threadId: ID!) {
  getThreadById(ThreadId: $threadId) {
    _id
    content
    title
    type
    author {
      _id
      name
      photo
    }
    comments {
      createdAt
      text
      user {
        _id
        name
        photo
      }
    }
    createdAt
    status
    thread_ref
  }
}`}
`

export const threadComment = gql`
  ${`mutation ThreadComment($threadCommentId: ID!, $inputComment: InputComment) {
  threadComment(id: $threadCommentId, inputComment: $inputComment) {
    _id
    author {
      _id
      name
    }
  }
}`}
`

export const getPostById = gql`
  ${`query GetPostById($postId: ID!) {
  getPostById(PostId: $postId) {
    _id
    content
    images
    videos
    settings
    author {
      _id
      name
      photo
      profileVerified
    }
      comments {
        _id
        text
        user {
          _id
          name
          photo
        }
        reply {
          _id
          text
          user {
            _id
            name
            photo
          }
          createdAt
        }
        createdAt
      }
    likes
    createdAt
  }
}`}
`

export const hasFriendRequest = gql`
  ${`query Query($friendId: ID!) {
  hasFriendRequest(friendId: $friendId)
}`}
`

export const checkFriendship = gql`
  ${`query CheckFriendship($friendId: ID!) {
  checkFriendship(friendId: $friendId) {
    _id
    status
    user {
      _id
      name
      email
      photo
    }
    friend {
      _id
      email
      name
      photo
    }
  }
}`}
`

export const getCheckRideOffers = gql`
  ${`query GetCheckRideOffers($options: options, $filters: CheckRideOffersfilters) {
  getCheckRideOffers(options: $options, filters: $filters) {
    limit
    page
     totalPages
    totalResults
    results {
      _id
      checkRideId
      company {
        _id
        name
        photo
         checkRideData {
          hitchMax
          hitchMin
          price
          terms
        }
      }
      completedAt
      createdAt
      endDate
      expireAt
      price
      startDate
      status
      user {
        _id
        photo
        name
      }
    }
  }
}`}
`

export const giveRecommendation = gql`
  ${`mutation GiveRecommendation($userId: ID!, $text: String!) {
  giveRecommendation(userId: $userId, text: $text) {
    message
  }
}`}
`

export const getMyRecommendations = gql`
  ${`query Query($options: options) {
  getMyRecommendations(options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      createdAt
      text
      updatedAt
      user {
        _id
        name
        photo
        role
      }
    }
  }
}`}
`
export const getRecommendationsById = gql`
  ${`query GetRecommendationsById($userId: ID!, $options: options) {
  getRecommendationsById(userId: $userId, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      createdAt
      text
      updatedAt
      user {
        _id
        name
        photo
        role
      }
    }
  }
}`}
`

export const getAvailableBalance = gql`
  ${`query GetAvailableBalance {
  getAvailableBalance {
    available
  }
}`}
`

export const getMyBadges = gql`
  ${`query GetMyBadges {
  getMyBadges {
    badge
    photo
    name
  }
}`}
`

export const applyBadge = gql`
  ${`mutation ApplyBadge($companyId: ID!) {
  applyBadge(companyId: $companyId) {
    message
  }
}`}
`

export const Invoices = gql`
  ${`query Invoices($filters: Invoicefilters, $options: options) {
  Invoices(filters: $filters, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      amount
      createdAt
      status
      application {
        job {
          title
          company {
            name
            photo
            _id
          }
        }
      }
      paymentOf
      checkRide {
        company {
          name
          _id
          photo
        }
      }
    }
  }
}`}
`

export const Licensings = gql`
  ${`query Licensings($filters: Licensingfilters, $options: options) {
  Licensings(filters: $filters, options: $options) {
    results {
      _id
      title
      addedBy {
        name
        photo
        _id
      }
      createdAt
      category
      faqs {
        _id
        question
        answer
        appointmentPrice
        isActive
      }
    }
    page
    totalPages
    totalResults
    limit
  }
}`}
`

export const LicenseBookings = gql`
  ${`query LicenseBookings($filters: LicenseBookingfilters, $options: options) {
  LicenseBookings(filters: $filters, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      booking {
        appointmentPrice
        _id
        answer
        category
        question
      }
      status
      meeting_date
      meeting_time_slot
      meeting_secs
      createdAt
      payment_link
      meeting_link
      meeting_id
      meeting_password
    }
  }
}`}
`

export const GetLicensingQuestions = gql`
  ${`query GetLicensingQuestions($licensingId: ID) {
  getLicensingQuestions(LicensingId: $licensingId) {
    _id
    answer
    isActive
    question
    appointmentPrice
  }
}`}
`

export const getAvaBookingSlots = gql`
  ${`query Query($date: String!) {
  getAvaBookingSlots(date: $date)
}`}
`

export const NearMeCaptian = gql`
  ${`query NearMeCaptian($latlng: String!, $maxDistance: String!, $options: options) {
  nearMeCaptian(latlng: $latlng, maxDistance: $maxDistance, options: $options) {
    limit
    page
    totalPages
    results {
      _id
      companyWorkFor
      distance
      email
      name
      isOnline
      phone
      photo
      role
        loc {
        coordinates
        type
      }
    }
    totalResults
  }
}`}
`

export const sendRecoveryEmailVerification = gql`
  ${`mutation SendRecoveryEmailVerification($email: String) {
  sendRecoveryEmailVerification(email: $email) {
    email
    result
  }
}`}
`

export const verifyRecoveryEmail = gql`
  ${`mutation VerifyRecoveryEmail($code: String, $email: String) {
  verifyRecoveryEmail(code: $code, email: $email) {
    email
    result
  }
}`}
`

export const DeleteAccount = gql`
  ${`mutation DeleteAccount($email: String) {
  deleteAccount(email: $email) {
    message
  }
}`}
`

export const getJobCities = gql`
  ${`query Query($text: String) {
  getJobCities(text: $text)
}`}
`

export const getMyBadgeRequests = gql`
  ${`query Query {
  getMyBadgeRequests
}`}
`

export const getKeyByFilter = gql`
  ${`query GetKeyByFilter($filters: SystemConfigfilters) {
  getKeyByFilter(filters: $filters) {
    createdAt
    description
    key
    specific_for
    unit
    updatedAt
    value
  }
}`}
`

export const createStripeAccount = gql`
  ${`mutation CreateStripeAccount {
  createStripeAccount {
    expires_at
    url
  }
}`}
`

export const updateUserCheckrInfo = gql`
  ${`mutation UpdateUserCheckrInfo($updateCheckrInput: UpdateCheckrInput) {
  updateUserCheckrInfo(updateCheckrInput: $updateCheckrInput) {
    _id
    
    
    
    
  }
}`}
`

export const getInvoiceById = gql`
  ${`query GetInvoiceById($invoiceId: ID) {
  getInvoiceById(InvoiceId: $invoiceId) {
    _id
    updatedAt
    amount
    application {
      _id
      company {
        photo
        name
        city
      }
      currentSalary
      createdAt
      job {
        type
        title
        city
      }
    }
    commission
    createdAt
    invoice_group
    invoice_ref
    jobDays
    paymentOf
    status
    stripePaymentId
    checkRide {
      _id
      checkRideData {
        hitchMax
        hitchMin
      }
       company {
        photo
        name
        city
      }
    }
  }
}`}
`
export const getInvoiceTransaction = gql`
  ${`query GetInvoiceTransaction($invoiceId: ID!) {
  getInvoiceTransaction(invoiceId: $invoiceId) {
    action
    amount
    createdAt
    current_update
    description
    invoice_id
    money_movement {
      title
      type
      description
    }
    negative_balance {
      amount
      createdAt
    }
    status
    transaction_fees
    type
    receipt_url
  }
}`}
`

export const reqLicensing = gql`
  ${`mutation ReqLicensing {
  reqLicensing {
    _id
  }
}`}
`

export const refreshTokens = gql`
  ${`mutation RefreshTokens($refreshToken: String!) {
  refreshTokens(refreshToken: $refreshToken) {
    refreshToken
    refreshTokenExpires
    token
    tokenExpiration
  }
}`}
`

export const createRBPVerification = gql`
  ${`mutation CreateRBPVerification {
  createRBPVerification {
    _id
    user {
      _id
      email
      checkr_status
      checkr_decision
    }
    verifications {
      _id
      step
      status
    }
    progress
    status
    currentStep
    rejectReason
    reviewComment
  }
}`}
`

export const getRBPVerificationFieldsByStepName = gql`
  ${`query GetRBPVerificationFieldsByStepName($stepName: String!) {
  getRBPVerificationFieldsByStepName(stepName: $stepName) {
    step
    description
    fields {
      name
      type
      title
      placeholder
      options
      required
      keyboardType
      limit
      visible
      validation {
        message
        regex
      }
    }
  }
}`}
`

export const updateRBPVerificationStep = gql`
  ${`mutation UpdateRBPVerificationStep($verificationId: ID!, $stepId: ID!, $updateRbpVerificationInput: InputVerification) {
  updateRBPVerificationStep(verificationId: $verificationId, stepId: $stepId, updateRBPVerificationInput: $updateRbpVerificationInput) {
    _id
    currentStep
    progress
    status
    user {
      _id
      email
    }
  }
}`}
`

export const SubmitVerification = gql`
  ${`mutation SubmitVerification($submitVerificationId: ID!) {
  SubmitVerification(id: $submitVerificationId) {
    _id
    status
    currentStep
  }
}`}
`

export const getBadgesByUserId = gql`
  ${`query GetBadgesByUserId($userId: ID!) {
  getBadgesByUserId(userId: $userId) {
    badge
    name
    photo
  }
}`}
`

export const getBookingSchedule = gql`
  ${`query GetBookingSchedule {
  getBookingSchedule {
    day
    time_slot
  }
}`}
`

export const createLicenseBooking = gql`
  ${`mutation Mutation($inputLicenseBooking: InputLicenseBooking) {
  createLicenseBooking(inputLicenseBooking: $inputLicenseBooking) {
     _id
    payment_link
    booking {
      appointmentPrice
    }
  }
}`}
`

export const postMessage = gql`
  ${`mutation PostMessage($inputMessage: InputMessage) {
  postMessage(inputMessage: $inputMessage) {
    _id
  }
}`}
`

export const JoinRoom = gql`
  ${`mutation JoinRoom($joinRoomId: ID!) {
  joinRoom(id: $joinRoomId) {
    _id
  }
}`}
`

export const LeaveRoom = gql`
  ${`mutation LeaveRoom($leaveRoomId: ID!) {
  leaveRoom(id: $leaveRoomId) {
    _id
  }
}`}
`

export const getLicenseBookingById = gql`
  ${`query GetLicenseBookingById($licenseBookingId: ID) {
  getLicenseBookingById(LicenseBookingId: $licenseBookingId) {
    _id
    booking {
      _id
      question
      answer
      appointmentPrice
      category
    }
    user {
      _id
      name
      photo
    }
    status
    meeting_date
    payment_link
    meeting_time_slot
    meeting_secs
    meeting_link
    meeting_password
    meeting_id
    createdAt
    updatedAt
  }
}`}
`

export const ToggleWaterWaysLearning = gql`
  ${`
mutation ToggleWaterWaysLearning($isEnabled: Boolean, $waterWayIds: [ID]) {
  toggleWaterWaysLearning(isEnabled: $isEnabled, waterWayIds: $waterWayIds) {
    message
  }
}
`}
`

export const getRBPVerificationStepData = gql`
  ${`query GetRBPVerificationStepData($verificationId: ID!, $stepId: ID!) {
  getRBPVerificationStepData(verificationId: $verificationId, stepId: $stepId) {
    _id
    step
    status
    firstName
    middleName
    lastName
    email
    phoneNo
    address
    city
    state
    zipcode
    referContactMethod
    drivingFront
    drivingBack
    licenseNo
    expirationDate
    drivingLicenseState
    twicFront
    twicBack
    documentNo
    referenceNo
    issueDate
    medicalCardPicture
    merchentMarinerCreds
    additionalMerchentMarinerCreds
    date
    yearsOnTicket
    vettedFor
    proficiency
    shoreTankingExp
    bunkingExp
    provideYourOwnPPE
    otherSpecialCertificates
    name
    bankAccountNo
    routingNo
    bankName
    bankAccontType
    authrizedToDepositInBankAccount
    typedSignature
    ssn
    dob
    portCaptainName
    portCaptainPhoneNumber
    reasonForLeaving
    startDate
    signature
    coastGuardMedicalLicenses
    companyName
    endDate
    fccLicenses
    acceptTermsAndConditions
  }
}`}
`

export const NewsLetters = gql`
  ${`query NewsLetters($filters: NewsLetterfilters, $options: options) {
  NewsLetters(filters: $filters, options: $options) {
    limit
    page
    totalPages
    totalResults
    results {
      _id
      company {
        _id
      }
      createdAt
      pdf_link
      title
      updatedAt
    }
  }
}`}
`

export const getMyWaterWayBadges = gql`
  ${`query GetMyWaterWayBadges($userId: ID) {
  getMyWaterWayBadges(userId: $userId) {
    _id
    company {
      _id
      name
      badge
      photo
    }
    waterwaysId {
      _id
      name
    }
  }
}`}
`

export const getAvailableCompaniesForBadges = gql`
  ${`query GetAvailableCompaniesForBadges {
  getAvailableCompaniesForBadges {
    _id
    name
    photo
    city
    about
    waterWays {
      _id
      name
    }
  }
}`}
`

export const reportPost = gql`
  ${`mutation ReportPost($reportPostId: ID!, $reportInput: ReportInput) {
  reportPost(id: $reportPostId, reportInput: $reportInput) {
    message
  }
}`}
`
