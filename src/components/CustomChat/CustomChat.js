import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'
import {
	View,
	Text,
	FlatList,
	TextInput,
	TouchableOpacity,
	Image,
	Linking,
	Animated,
	Platform,
	KeyboardAvoidingView,
	ActivityIndicator,
} from 'react-native'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Ionicons from '@expo/vector-icons/Ionicons'
import AntDesign from '@expo/vector-icons/AntDesign'
import Entypo from '@expo/vector-icons/Entypo'
import styles from '../../screens/styles'
import fontStyles from '../../utils/fonts/fontStyles'
import { get_url_extension } from '../../utils/Constants'

const CustomChat = forwardRef((props, ref) => {
	const {
		messages = [],
		onSend,
		user,
		text,
		onInputTextChanged,
		renderActions,
		renderFooter,
		isLoadingEarlier = false,
		onLoadEarlier,
		listViewProps = {},
		currentTheme,
		...otherProps
	} = props

	const flatListRef = useRef(null)
	const [showScrollToBottom, setShowScrollToBottom] = useState(false)
	const [isNearBottom, setIsNearBottom] = useState(true)

	// Expose scrollToBottom method via ref
	useImperativeHandle(ref, () => ({
		scrollToEnd: (animated = true) => {
			flatListRef.current?.scrollToOffset({ offset: 0, animated })
		},
	}))

	// Helper function to check if user is near bottom
	const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
		const paddingToBottom = 50
		return layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
	}

	// Handle scroll events
	const handleScroll = useCallback(
		event => {
			const { nativeEvent } = event
			const nearBottom = isCloseToBottom(nativeEvent)
			setIsNearBottom(nearBottom)
			setShowScrollToBottom(!nearBottom)

			// Call custom scroll handler if provided
			if (listViewProps.onScroll) {
				listViewProps.onScroll(event)
			}
		},
		[listViewProps.onScroll]
	)

	// Scroll to bottom function
	const scrollToBottom = useCallback(() => {
		flatListRef.current?.scrollToOffset({ offset: 0, animated: true })
	}, [])

	// Get user initials for avatar placeholder
	const getUserInitials = userObj => {
		if (!userObj?.name) return 'U'
		const names = userObj.name.split(' ')
		if (names.length >= 2) {
			return `${names[0][0]}${names[1][0]}`.toUpperCase()
		}
		return userObj.name[0].toUpperCase()
	}

	// Render File Message Component
	const RenderMessageFile = ({ currentMessage }) => {
		const handleFilePress = () => {
			if (currentMessage?.file) {
				Linking.openURL(currentMessage?.file)
			}
		}

		return (
			<TouchableOpacity
				activeOpacity={0.7}
				style={{
					padding: 10,
					alignItems: 'center',
					justifyContent: 'center',
					backgroundColor: currentTheme.lightGold,
					borderTopLeftRadius: 10,
					borderBottomRightRadius: 10,
					width: 100,
				}}
				onPress={handleFilePress}
			>
				<FontAwesome5 name={'file-pdf'} size={45} color={currentTheme.white} />
			</TouchableOpacity>
		)
	}

	// Render Image Message Component
	const RenderMessageImage = ({ currentMessage }) => {
		const handleImagePress = () => {
			if (currentMessage?.image) {
				Linking.openURL(currentMessage?.image)
			}
		}

		return (
			<View
				style={{
					height: 150,
					width: '100%',
					minWidth: 170,
					overflow: 'hidden',
					borderTopLeftRadius: 10,
				}}
			>
				<TouchableOpacity onPress={handleImagePress} activeOpacity={0.7}>
					<Image
						source={{ uri: currentMessage?.image }}
						style={{ height: '100%', width: '100%' }}
						resizeMode="cover"
					/>
				</TouchableOpacity>
			</View>
		)
	}

	// Custom Card Component
	const CustomCard = ({ currentMessage, navigation }) => {
		const check = user?.appliedJobs?.find(applied => currentMessage?.cardData?.cardID === applied)

		return (
			<TouchableOpacity
				activeOpacity={0.4}
				onPress={() => {
					if (currentMessage.cardData.shareType === 'job') {
						navigation?.navigate('JobDetails', {
							item: { _id: currentMessage?.cardData?.cardID },
							jobId: currentMessage?.cardData?.cardID,
							alreadyApplied: !!check,
						})
					}
					if (currentMessage.cardData.shareType === 'company') {
						navigation?.navigate('CompanyDetails', {
							item: { _id: currentMessage?.cardData?.cardID },
						})
					}
				}}
				style={{
					backgroundColor: currentTheme.F8F9FA,
					padding: 10,
					borderRadius: 8,
					flexDirection: 'row',
					alignItems: 'flex-start',
					borderWidth: 1,
					borderColor: currentTheme.F8F9FA,
					paddingVertical: 15,
					paddingRight: 20,
					paddingLeft: 10,
					marginTop: 10,
					maxWidth: '80%',
				}}
			>
				<View style={{ marginRight: 10 }}>
					<View
						style={{
							height: 40,
							width: 40,
							borderRadius: 100,
							alignItems: 'center',
							justifyContent: 'center',
							overflow: 'hidden',
							borderWidth: currentMessage?.cardData?.photo ? 0 : 1,
							borderColor: currentTheme.themeBackground,
						}}
					>
						{currentMessage?.cardData?.photo ? (
							<Image
								source={{ uri: currentMessage?.cardData?.photo }}
								style={{ height: '100%', width: '100%' }}
								resizeMode="cover"
							/>
						) : (
							<View>
								{currentMessage?.cardData?.shareType === 'company' ? (
									<MaterialCommunityIcons name="city-variant" size={25} color={currentTheme.themeBackground} />
								) : (
									<Ionicons name="md-briefcase" size={20} color={currentTheme.themeBackground} />
								)}
							</View>
						)}
					</View>
				</View>
				<View style={{ flex: 1 }}>
					<Text
						numberOfLines={2}
						style={{
							fontSize: 14,
							fontFamily: fontStyles.PoppinsMedium,
							color: currentTheme.black,
							textDecorationLine: 'underline',
						}}
					>
						{currentMessage?.cardData?.title}
					</Text>
					<Text
						numberOfLines={3}
						style={{
							fontSize: 12,
							marginTop: 5,
							color: currentTheme.blackish,
							fontFamily: fontStyles.PoppinsRegular,
						}}
					>
						{currentMessage?.cardData?.description ? currentMessage?.cardData?.description : 'N/A'}
					</Text>
				</View>
			</TouchableOpacity>
		)
	}

	// Badge Card Component
	const BadgeCard = ({ currentMessage }) => {
		if (currentMessage?.cardData?.cardType === 'assign' || !currentMessage?.cardData?.cardType) {
			return (
				<TouchableOpacity
					activeOpacity={1}
					style={{
						alignItems: 'center',
						justifyContent: 'center',
						width: '80%',
						marginVertical: 5,
					}}
				>
					<View
						style={{
							backgroundColor: currentTheme.badgeCardBG,
							paddingVertical: 8,
							paddingHorizontal: 10,
							alignItems: 'center',
							justifyContent: 'center',
							borderRadius: 5,
							marginBottom: 10,
							width: '100%',
						}}
					>
						<Text
							style={{
								color: currentTheme.themeBackground,
								textAlign: 'center',
								fontSize: 12,
								fontFamily: fontStyles.PoppinsMedium,
							}}
						>
							Badge Has Been Assigned
						</Text>
					</View>
					<View style={{ aspectRatio: 1, width: 50 }}>
						<Image
							source={require('../../assets/images/badge_success.png')}
							style={{ height: '100%', width: '100%' }}
							resizeMode="contain"
						/>
					</View>
				</TouchableOpacity>
			)
		}
		if (currentMessage?.cardData?.cardType === 'take') {
			return (
				<TouchableOpacity
					activeOpacity={1}
					style={{
						alignItems: 'center',
						justifyContent: 'center',
						width: '80%',
						marginVertical: 5,
					}}
				>
					<View
						style={{
							backgroundColor: currentTheme.white,
							paddingVertical: 8,
							paddingHorizontal: 10,
							alignItems: 'center',
							justifyContent: 'center',
							borderRadius: 5,
							marginBottom: 10,
							width: '100%',
							borderWidth: 1,
							borderColor: currentTheme.themeBackground,
						}}
					>
						<Text
							style={{
								color: currentTheme.themeBackground,
								fontSize: 12,
								textAlign: 'center',
								fontFamily: fontStyles.PoppinsMedium,
							}}
						>
							Badge Has Been Removed
						</Text>
					</View>
					<View style={{ aspectRatio: 1, width: 50 }}>
						<Image
							source={require('../../assets/images/cardtake.png')}
							style={{ height: '100%', width: '100%' }}
							resizeMode="contain"
						/>
					</View>
				</TouchableOpacity>
			)
		}
	}

	// Job Application Card Component
	const JobApplicationCard = ({ currentMessage }) => {
		return (
			<TouchableOpacity
				activeOpacity={1}
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					width: '80%',
					marginVertical: 5,
				}}
			>
				<View
					style={{
						backgroundColor:
							currentMessage?.cardData?.cardType === 'jobComplete'
								? currentTheme.backgroundGreen
								: currentMessage?.cardData?.cardType === 'terminate'
								? currentTheme.FFE5E5
								: currentTheme.badgeCardBG,
						paddingVertical: 8,
						paddingHorizontal: 10,
						alignItems: 'center',
						justifyContent: 'center',
						borderRadius: 5,
						marginBottom: 10,
						width: '100%',
					}}
				>
					<Text
						style={{
							color:
								currentMessage?.cardData?.cardType === 'jobComplete'
									? currentTheme.lightGreen
									: currentMessage?.cardData?.cardType === 'terminate'
									? currentTheme.red
									: currentTheme.themeBackground,
							fontSize: 12,
							textAlign: 'center',
							fontFamily: fontStyles.PoppinsMedium,
						}}
					>
						{currentMessage?.text}
					</Text>
				</View>
				<View style={{ height: 60, width: 60 }}>
					<Image
						source={
							currentMessage?.cardData?.cardType === 'jobComplete'
								? require('../../assets/images/job_complete.png')
								: currentMessage?.cardData?.cardType === 'terminate'
								? require('../../assets/images/job_terminate.png')
								: require('../../assets/images/application_chat.png')
						}
						style={{
							height: '100%',
							width: '100%',
							tintColor:
								currentMessage?.cardData?.cardType === 'jobComplete'
									? currentTheme.lightGreen
									: currentMessage?.cardData?.cardType === 'terminate'
									? currentTheme.red
									: null,
						}}
						resizeMode="contain"
					/>
				</View>
			</TouchableOpacity>
		)
	}

	// Day separator component
	const renderDay = ({ item }) => {
		if (!item.isDay) return null
		return (
			<View
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					marginVertical: 10,
				}}
			>
				<View
					style={{
						paddingHorizontal: 10,
						borderRadius: 5,
						paddingVertical: 7,
						backgroundColor: currentTheme.timeContainerColor,
						width: '50%',
						alignItems: 'center',
						justifyContent: 'center',
					}}
				>
					<Text
						style={{
							color: currentTheme.themeBackground,
							fontSize: 10,
							fontFamily: fontStyles.PoppinsMedium,
						}}
					>
						{item.text}
					</Text>
				</View>
			</View>
		)
	}

	// Main message bubble component
	const renderMessageBubble = ({ item, index }) => {
		if (item.isDay) {
			return renderDay({ item })
		}

		const currentMessage = item
		const nextMessage = messages[index + 1]
		const isMyMessage = currentMessage?.user?._id === user?._id

		// Show avatar only for latest message from other users
		const shouldShowAvatar =
			!isMyMessage && (!nextMessage || !nextMessage.user || nextMessage.user._id !== currentMessage.user._id)

		// Handle special message types
		if (currentMessage?.type === 'card') {
			return (
				<View style={{ marginVertical: 5, alignItems: 'center' }}>
					<CustomCard currentMessage={currentMessage} navigation={props.navigation} />
				</View>
			)
		}
		if (currentMessage?.type === 'badge') {
			return (
				<View style={{ marginVertical: 5, alignItems: 'center' }}>
					<BadgeCard currentMessage={currentMessage} />
				</View>
			)
		}
		if (currentMessage?.type === 'application') {
			return (
				<View style={{ marginVertical: 5, alignItems: 'center' }}>
					<JobApplicationCard currentMessage={currentMessage} />
				</View>
			)
		}

		return (
			<View
				style={[
					styles().flexRow,
					styles().alignItems,
					{
						justifyContent: isMyMessage ? 'flex-end' : 'flex-start',
						marginVertical: 2,
						paddingHorizontal: 15,
					}
				]}
			>
				{/* Avatar space for ALL other user messages */}
				{!isMyMessage && (
					<View
						style={{
							width: 30,
							height: 30,
							marginRight: 8,
							marginBottom: 2,
						}}
					>
						{shouldShowAvatar &&
							(currentMessage?.user?.avatar ? (
								<Image
									source={{ uri: currentMessage.user.avatar }}
									style={{
										width: 30,
										height: 30,
										borderRadius: 15,
									}}
									resizeMode="cover"
								/>
							) : (
								<View
									style={{
										width: 30,
										height: 30,
										borderRadius: 15,
										backgroundColor: currentTheme.themeBackground,
										justifyContent: 'center',
										alignItems: 'center',
									}}
								>
									<Text
										style={{
											color: currentTheme.white,
											fontSize: 12,
											fontWeight: 'bold',
										}}
									>
										{getUserInitials(currentMessage?.user)}
									</Text>
								</View>
							))}
					</View>
				)}

				{/* Message bubble */}
				<View
					style={{
						backgroundColor: isMyMessage ? currentTheme.lightGold : currentTheme.F4F5F6,
						borderRadius: 20,
						borderTopRightRadius: isMyMessage ? 4 : 20,
						borderTopLeftRadius: isMyMessage ? 20 : 4,
						paddingHorizontal: 15,
						paddingVertical: 10,
						marginBottom: 3,
						maxWidth: '75%',
						minWidth: currentMessage.file || currentMessage.image ? 'auto' : 50,
					}}
				>
					{/* Custom file rendering */}
					{currentMessage.file && <RenderMessageFile currentMessage={currentMessage} />}

					{/* Custom image rendering */}
					{currentMessage.image && <RenderMessageImage currentMessage={currentMessage} />}

					{/* Text content */}
					{currentMessage.text && (
						<Text
							style={{
								color: isMyMessage ? currentTheme.white : currentTheme.black,
								fontSize: 12,
								fontWeight: '400',
								fontFamily: fontStyles.PoppinsRegular,
								marginTop: (currentMessage.file || currentMessage.image) ? 5 : 0,
							}}
						>
							{currentMessage.text}
						</Text>
					)}
				</View>
			</View>
		)
	}

	// Input toolbar component
	const renderInputToolbar = () => {
		return (
			<View
				style={{
					flexDirection: 'row',
					alignItems: 'flex-end',
					paddingHorizontal: 10,
					paddingVertical: 8,
					backgroundColor: currentTheme.white,
					minHeight: 50,
				}}
			>
				{/* Actions (attachments) */}
				{renderActions && renderActions()}

				{/* Text input */}
				<View
					style={{
						flex: 1,
						backgroundColor: currentTheme.white,
						borderRadius: 25,
						borderWidth: 1,
						borderColor: currentTheme.E8E8E8,
						marginHorizontal: 8,
						paddingHorizontal: 15,
						paddingVertical: Platform.OS === 'ios' ? 12 : 8,
						maxHeight: 100,
					}}
				>
					<TextInput
						value={text}
						onChangeText={onInputTextChanged}
						placeholder="Write a Message..."
						placeholderTextColor={currentTheme.c9E9E9E}
						style={{
							color: currentTheme.black,
							fontSize: 14,
							fontFamily: fontStyles.PoppinsRegular,
							minHeight: Platform.OS === 'ios' ? 20 : 25,
						}}
						multiline
						textAlignVertical="center"
					/>
				</View>

				{/* Send button */}
				<TouchableOpacity
					onPress={() => {
						if (text?.trim() || otherProps.alwaysShowSend) {
							onSend([
								{
									_id: Math.round(Math.random() * 1000000),
									text: text?.trim() || '',
									createdAt: new Date(),
									user: {
										_id: user?._id,
									},
								},
							])
						}
					}}
					style={{
						height: 35,
						width: 35,
						alignItems: 'center',
						justifyContent: 'center',
						marginBottom: 5,
					}}
				>
					<Ionicons name="send" color={currentTheme.themeBackground} size={18} />
				</TouchableOpacity>
			</View>
		)
	}

	// Empty chat component
	const renderChatEmpty = () => {
		if (messages?.length === 0) {
			return (
				<View style={[styles().flex, styles().justifyCenter, styles().alignCenter]}>
					<Text
						style={{
							color: currentTheme.c9E9E9E,
							fontSize: 16,
							fontFamily: fontStyles.PoppinsRegular,
						}}
					>
						No messages yet
					</Text>
				</View>
			)
		}
		return null
	}

	// Scroll to bottom button
	const renderScrollToBottomButton = () => {
		if (!showScrollToBottom) return null

		return (
			<TouchableOpacity
				onPress={scrollToBottom}
				style={{
					position: 'absolute',
					bottom: 80,
					right: 20,
					backgroundColor: currentTheme.themeBackground,
					width: 40,
					height: 40,
					borderRadius: 20,
					justifyContent: 'center',
					alignItems: 'center',
					shadowColor: '#000',
					shadowOffset: {
						width: 0,
						height: 2,
					},
					shadowOpacity: 0.25,
					shadowRadius: 3.84,
					elevation: 5,
				}}
			>
				<FontAwesome name="angle-down" color={currentTheme.white} size={22} />
			</TouchableOpacity>
		)
	}

	// Load earlier indicator
	const renderLoadEarlier = () => {
		if (!isLoadingEarlier) return null

		return (
			<View style={{ paddingVertical: 10, alignItems: 'center' }}>
				<ActivityIndicator size="small" color={currentTheme.themeBackground} />
			</View>
		)
	}

	// Process messages to add day separators
	const processMessagesWithDays = useCallback(() => {
		if (!messages || messages.length === 0) return []

		const sortedMessages = [...messages].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
		const processedMessages = []
		let lastDate = null

		sortedMessages.forEach((message, index) => {
			const messageDate = new Date(message.createdAt)
			const currentDate = messageDate.toDateString()

			// Add day separator if date changed
			if (lastDate && lastDate !== currentDate) {
				processedMessages.push({
					_id: `day-${currentDate}`,
					isDay: true,
					text: messageDate.toLocaleDateString('en-US', {
						weekday: 'long',
						year: 'numeric',
						month: 'long',
						day: 'numeric',
					}),
					createdAt: messageDate,
				})
			}

			processedMessages.push(message)
			lastDate = currentDate
		})

		// Add day separator for the oldest message
		if (sortedMessages.length > 0) {
			const oldestMessage = sortedMessages[sortedMessages.length - 1]
			const oldestDate = new Date(oldestMessage.createdAt)
			processedMessages.push({
				_id: `day-${oldestDate.toDateString()}`,
				isDay: true,
				text: oldestDate.toLocaleDateString('en-US', {
					weekday: 'long',
					year: 'numeric',
					month: 'long',
					day: 'numeric',
				}),
				createdAt: oldestDate,
			})
		}

		return processedMessages
	}, [messages])

	const processedMessages = processMessagesWithDays()

	return (
		<View style={{ flex: 1 }}>
			<FlatList
				ref={flatListRef}
				data={processedMessages}
				keyExtractor={item => item._id?.toString()}
				renderItem={renderMessageBubble}
				onScroll={handleScroll}
				onEndReached={onLoadEarlier}
				onEndReachedThreshold={0.1}
				scrollEventThrottle={16}
				inverted
				showsVerticalScrollIndicator={false}
				ListEmptyComponent={renderChatEmpty}
				ListHeaderComponent={renderLoadEarlier}
				ListFooterComponent={renderFooter}
				contentContainerStyle={{
					flexGrow: 1,
					paddingVertical: 10,
				}}
				{...listViewProps}
			/>

			{renderScrollToBottomButton()}
			{renderInputToolbar()}
		</View>
	)
})

CustomChat.displayName = 'CustomChat'

export default CustomChat