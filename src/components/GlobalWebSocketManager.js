import React, { useContext } from 'react'
import UserContext from '../context/User/User'
import useUserStatus from '../hooks/useUserStatus'
import useJobNotifications from '../hooks/useJobNotifications'

const GlobalWebSocketManager = ({ children }) => {
  const user = useContext(UserContext)
  
  // Initialize user online/offline status tracking
  const { isOnline } = useUserStatus()
  
  // Initialize job notifications
  useJobNotifications()

  console.log(`User ${user?.name} is ${isOnline ? 'online' : 'offline'}`)

  return children
}

export default GlobalWebSocketManager