Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.checkers_onBoarding = void 0

var _checker_env = require('./checker_env')

var _base = require('base-64')

var paths = {
	deckhand: '39a7bd4e-2520-45a8-9b69-8dd5c918e8cc',
	tankerman: '27f3397b-79c3-450a-893e-4173f5ddfc72',
	captain: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
	matePilot: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
	steersman: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
}
var username = _checker_env.CHECKERS_API_KEY
var password = ''
var headers = {
	Authorization: `Basic ${(0, _base.encode)(''.concat(username, ':').concat(password))}`,
	'Content-Type': 'application/json',
}

var checkers_onBoarding = function checkers_onBoarding(role, email) {
	var url
	var response
	return regeneratorRuntime.async(
		function checkers_onBoarding$(_context) {
			while (1) {
				switch ((_context.prev = _context.next)) {
					case 0:
						if (__DEV__) {
							url = ''.concat(_checker_env.Checkers_BaseURL.development.CHECKERS_URL, '/invitations')
						} else {
							url = ''.concat(_checker_env.Checkers_BaseURL.production.CHECKERS_URL, '/invitations')
						} //   console.log('check url :', url);
						//   console.log('check data :', paths[role], email);
						//   console.log('header :', headers);

						_context.prev = 1
						_context.next = 4
						return regeneratorRuntime.awrap(
							fetch(url, {
								method: 'post',
								headers: headers,
								body: JSON.stringify({
									candidate: {
										email: email,
									},
									path_template_id: paths[role],
									auto_send: true,
								}),
							})
						)

					case 4:
						response = _context.sent

						if (response.ok) {
							_context.next = 9
							break
						}

						_context.next = 8
						return regeneratorRuntime.awrap(response.json())

					case 8:
						throw _context.sent

					case 9:
						_context.next = 11
						return regeneratorRuntime.awrap(response.json())

					case 11:
						response = _context.sent
						console.log('checkers res :', response)
						return _context.abrupt('return', response)

					case 16:
						_context.prev = 16
						_context.t0 = _context.catch(1)
						console.log('checkers Err :', _context.t0)
						return _context.abrupt('return', _context.t0)

					case 20:
					case 'end':
						return _context.stop()
				}
			}
		},
		null,
		null,
		[[1, 16]]
	)
}

exports.checkers_onBoarding = checkers_onBoarding
