import { CHECKERS_API_KEY, Checkers_BaseURL } from './checker_env'
import { encode } from 'base-64'

const paths = {
	deckhand: '39a7bd4e-2520-45a8-9b69-8dd5c918e8cc',
	tankerman: '27f3397b-79c3-450a-893e-4173f5ddfc72',
	matePilot: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
	captain: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
	master: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
	steersman: 'edae1e0b-fdc3-4c89-bad3-1599bb6de327',
}

const username = CHECKERS_API_KEY
const password = ''

const headers = {
	Authorization: `Basic ${encode(`${username}:${password}`)}`,
	'Content-Type': 'application/json',
}

export const checkers_onBoarding = async (role, email) => {
	let url
	if (__DEV__) {
		url = `${Checkers_BaseURL.development.CHECKERS_URL}/invitations`
	} else {
		// url = `${Checkers_BaseURL.production.CHECKERS_URL}/invitations`;
		url = `${Checkers_BaseURL.development.CHECKERS_URL}/invitations`
	}
	//   console.log('check url :', url);
	console.log('check data in api :', paths[role], email)
	//   console.log('header :', headers);
	try {
		let response = await fetch(url, {
			method: 'post',
			headers: headers,
			body: JSON.stringify({
				candidate: { email: email },
				path_template_id: paths[role],
				auto_send: true,
			}),
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		console.log('checkers res :', response)
		return response
	} catch (error) {
		console.log('checkers Err :', error)
		return error
	}
}
