import { io } from 'socket.io-client'
import getEnvVars from '../../environment'
const { WS_GRAPHQL_URL } = getEnvVars()
export var socket

export const initiateSocketConnection = _user => {
	socket = io(WS_GRAPHQL_URL, {
		transports: ['websocket', 'polling'],
		autoConnect: true,
		reconnection: true,
		reconnectionAttempts: 5,
		timeout: 20000
	})
	socket.on('connect', _res => {
		console.log('✅ Socket CONNECTED successfully!', socket.id)
		console.log('   - Socket ID:', socket.id)
		console.log('   - Transport:', socket.io.engine.transport.name)
	})
	
	socket.on('connect_error', error => {
		console.error('❌ Socket CONNECTION ERROR:', error)
		console.error('   - Error message:', error.message)
		console.error('   - Error type:', error.type)
		console.error('   - Error description:', error.description)
	})
	
	socket.on('disconnect', reason => {
		console.log('❌ Socket DISCONNECTED:', reason)
	})
	
	socket.on('error', error => {
		console.error('❌ Socket ERROR:', error)
	})
	return socket
}

// export const disconnectSocketConnection = () => {
//   socket?.disconnect();
//   socket?.on('disconnect', reason => {
//     console.log(`Socket DISCONNECTED due to ${reason}`);
//   });
//   // socket.off('connect');
//   // console.log(`socket disconnected status :`, socket); // false
//   return socket;
// };

export const disconnectSocketConnection = () => {
	if (socket) {
		socket.disconnect()
		socket.off('connect')
		socket.off('connect_error')
		socket.off('disconnect')
	}
	return socket
}

export const appJoin = authId => {
	const data = {
		authId: authId,
	}
	socket?.emit('appJoin', data)
}

export const appLeave = authId => {
	const data = {
		authId: authId,
	}
	socket?.emit('appLeave', data)
}

export const roomJoin = (authId, roomId) => {
	const data = {
		authId: authId,
		roomId: roomId,
	}
	socket?.emit('roomJoin', data)
}

export const roomLeave = (authId, roomId) => {
	const data = {
		authId: authId,
		roomId: roomId,
	}
	socket?.emit('roomLeave', data)
}

export const sendMessage = (authId, roomId, message, messageType, files) => {
	const data = {
		authId: authId,
		roomId: roomId,
		message: message,
		messageType: messageType,
		files: files,
	}
	socket?.emit('sendMessage', data)
}

export const sendMessageCard = (authId, roomId, message, messageType, files, cardData) => {
	const data = {
		authId: authId,
		roomId: roomId,
		message: message,
		messageType: messageType,
		files: files,
		cardData: cardData,
	}
	socket?.emit('sendMessage', data)
}

export const message_listener = (roomId, cb) => {
	socket?.on(roomId, msg => {
		return cb(msg)
	})
}

export const new_message_listener = (userId, cb) => {
	socket?.on(userId, msg => {
		return cb(msg)
	})
}
export const socket_event_listener = (listenTo, cb) => {
	socket?.on(listenTo, msg => {
		return cb(msg)
	})
}

export const onDeactivated = (userId, cb) => {
	socket?.on(`deactivated-${userId}`, msg => {
		return cb(msg)
	})
}

export const onDeleted = (userId, cb) => {
	socket?.on(`deleted-${userId}`, msg => {
		return cb(msg)
	})
}

// Typing indicators
export const startTyping = (authId, roomId) => {
	const data = {
		authId: authId,
		roomId: roomId,
	}
	socket?.emit('typing', data)
}

export const stopTyping = (roomId) => {
	socket?.emit('stopTyping', { roomId })
}

// Removed - using direct socket.on in Chat component

// Job notifications
export const jobNotificationListener = (userId, cb) => {
	socket?.on(`notification-${userId}`, notification => {
		return cb(notification)
	})
}

// General notification listener
export const notificationListener = (userId, cb) => {
	socket?.on(`notification-${userId}`, notification => {
		return cb(notification)
	})
}
