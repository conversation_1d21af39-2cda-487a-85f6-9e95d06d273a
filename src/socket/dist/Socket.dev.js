Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.message_listener =
	exports.sendMessage =
	exports.roomLeave =
	exports.roomJoin =
	exports.appLeave =
	exports.appJoin =
	exports.disconnectSocketConnection =
	exports.initiateSocketConnection =
		void 0

var _socket = require('socket.io-client')

var _environment = _interopRequireDefault(require('../../environment'))

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var _getEnvVars = (0, _environment.default)()
var WS_GRAPHQL_URL = _getEnvVars.WS_GRAPHQL_URL

var socket

var initiateSocketConnection = function initiateSocketConnection(_user) {
	socket = (0, _socket.io)(WS_GRAPHQL_URL)
	socket.on('connect', () => {
		console.log('socket connected status :', socket.connected) // true
	})
	return socket
}

exports.initiateSocketConnection = initiateSocketConnection

var disconnectSocketConnection = function disconnectSocketConnection() {
	socket.disconnect()
	socket.on('disconnect', reason => {
		console.log('disconnected due to '.concat(reason))
	}) // socket.off('connect');

	console.log('socket disconnected status :', socket.connected) // false

	return socket
}

exports.disconnectSocketConnection = disconnectSocketConnection

var appJoin = function appJoin(authId) {
	var data = {
		authId: authId,
	}
	socket.emit('appJoin', data)
}

exports.appJoin = appJoin

var appLeave = function appLeave(authId) {
	var data = {
		authId: authId,
	}
	socket.emit('appLeave', data)
}

exports.appLeave = appLeave

var roomJoin = function roomJoin(authId, roomId) {
	var data = {
		authId: authId,
		roomId: roomId,
	}
	socket.emit('roomJoin', data)
}

exports.roomJoin = roomJoin

var roomLeave = function roomLeave(authId, roomId) {
	var data = {
		authId: authId,
		roomId: roomId,
	}
	socket.emit('roomLeave', data)
}

exports.roomLeave = roomLeave

var sendMessage = function sendMessage(authId, roomId, message, messageType, files) {
	var data = {
		authId: authId,
		roomId: roomId,
		message: message,
		messageType: messageType,
		files: files,
	}
	socket.emit('sendMessage', data)
}

exports.sendMessage = sendMessage

var message_listener = function message_listener(roomId, cb) {
	socket.on(roomId, msg => cb(msg))
}

exports.message_listener = message_listener
