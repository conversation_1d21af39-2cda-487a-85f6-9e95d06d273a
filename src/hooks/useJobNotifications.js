import { useEffect, useContext } from 'react'
import { notificationListener, appJoin } from '../socket/Socket'
import UserContext from '../context/User/User'
import FlashMessage from '../component/FlashMessage/FlashMessage'

const useJobNotifications = () => {
  const user = useContext(UserContext)

  useEffect(() => {
    if (!user?._id) return

    // Join app for notifications
    appJoin(user._id)

    // Listen for job-related notifications
    notificationListener(user._id, (notification) => {
      console.log('Job notification received:', notification)

      // Handle different notification types
      switch (notification.type) {
        case 'offer':
          // Job application notifications
          FlashMessage({
            msg: notification.title,
            type: 'info'
          })
          break
        
        case 'jobInvitation':
          // Job invitation notifications
          FlashMessage({
            msg: `Job Invitation: ${notification.title}`,
            type: 'success'
          })
          break
          
        case 'jobPosted':
          // New job posted notifications
          FlashMessage({
            msg: `New Job: ${notification.title}`,
            type: 'info'
          })
          break
          
        case 'offerAccepted':
          // Offer acceptance notifications
          FlashMessage({
            msg: 'Your job offer was accepted!',
            type: 'success'
          })
          break
          
        case 'offerRejected':
          // Offer rejection notifications
          FlashMessage({
            msg: 'Your job offer was declined',
            type: 'warning'
          })
          break
          
        case 'contractCompleted':
          // Contract completion notifications
          FlashMessage({
            msg: 'Job contract completed',
            type: 'success'
          })
          break
          
        case 'contractTerminated':
          // Contract termination notifications
          FlashMessage({
            msg: 'Job contract terminated',
            type: 'warning'
          })
          break

        case 'checkRideUpdate':
          // Check ride notifications (already handled by our previous implementation)
          FlashMessage({
            msg: notification.text || 'Check ride status updated',
            type: 'info'
          })
          break

        case 'badgeUpdate':
          // Badge notifications
          FlashMessage({
            msg: notification.text || 'Badge status updated',
            type: 'info'
          })
          break
          
        default:
          // Generic notification
          if (notification.title) {
            FlashMessage({
              msg: notification.title,
              type: 'info'
            })
          }
          break
      }

      // Emit custom event for other parts of the app to listen
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('job-notification', { 
          detail: notification 
        }))
      }
    })

  }, [user?._id])

  return {
    userId: user?._id
  }
}

export default useJobNotifications