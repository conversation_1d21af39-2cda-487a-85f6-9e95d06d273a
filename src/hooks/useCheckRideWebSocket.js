import { useEffect, useState, useRef } from 'react'
import io from 'socket.io-client'
import FlashMessage from '../component/FlashMessage/FlashMessage'

const useCheckRideWebSocket = (userId, checkRideId = null, onUpdate = null) => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const hasShownMaxAttemptsMessage = useRef(false)

  const connectSocket = () => {
    try {
      // Use your actual WebSocket server URL
      const socketUrl = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:3001'
      
      // Skip WebSocket connection if we've already failed max attempts
      if (hasShownMaxAttemptsMessage.current) {
        return null
      }
      
      const newSocket = io(socketUrl, {
        transports: ['websocket'],
        timeout: 5000,
        forceNew: true,
      })

      newSocket.on('connect', () => {
        console.log('WebSocket connected for check rides')
        setIsConnected(true)
        reconnectAttempts.current = 0
        hasShownMaxAttemptsMessage.current = false

        // Join user's personal check ride room
        if (userId) {
          newSocket.emit('join-user-checkrides', userId)
        }

        // Join specific check ride room if provided
        if (checkRideId) {
          newSocket.emit('join-checkride', checkRideId)
        }
      })

      newSocket.on('disconnect', () => {
        console.log('WebSocket disconnected')
        setIsConnected(false)
      })

      newSocket.on('connect_error', (error) => {
        console.log('WebSocket connection error:', error)
        setIsConnected(false)
        // Only attempt to reconnect if we haven't exceeded max attempts
        if (reconnectAttempts.current < maxReconnectAttempts) {
          handleReconnect()
        }
      })

      // Listen to user's personal check ride updates
      newSocket.on(`user-checkrides-${userId}`, (data) => {
        console.log('User check ride update:', data)
        handleCheckRideUpdate(data)
      })

      // Listen to specific check ride updates
      if (checkRideId) {
        newSocket.on(`checkride-${checkRideId}`, (data) => {
          console.log('Specific check ride update:', data)
          handleCheckRideUpdate(data)
        })
      }

      setSocket(newSocket)
      return newSocket

    } catch (error) {
      console.error('Error connecting to WebSocket:', error)
      handleReconnect()
      return null
    }
  }

  const handleReconnect = () => {
    if (reconnectAttempts.current < maxReconnectAttempts) {
      reconnectAttempts.current += 1
      const timeout = Math.pow(2, reconnectAttempts.current) * 1000 // Exponential backoff
      
      console.log(`Attempting to reconnect in ${timeout}ms (attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`)
      
      reconnectTimeoutRef.current = setTimeout(() => {
        connectSocket()
      }, timeout)
    } else if (!hasShownMaxAttemptsMessage.current) {
      console.log('Max reconnection attempts reached')
      hasShownMaxAttemptsMessage.current = true
      FlashMessage({
        msg: 'Unable to connect to real-time updates. The app will work normally without live notifications.',
        type: 'warning'
      })
    }
  }

  const handleCheckRideUpdate = (data) => {
    try {
      // Call custom update handler if provided
      if (onUpdate && typeof onUpdate === 'function') {
        onUpdate(data)
      }

      // Default handling based on update type
      switch (data.type) {
        case 'CHECKRIDE_OFFER_ACCEPTED':
          FlashMessage({
            msg: 'Your check ride offer has been accepted!',
            type: 'success'
          })
          break
        case 'CHECKRIDE_OFFER_REJECTED':
          FlashMessage({
            msg: 'Your check ride offer has been rejected.',
            type: 'info'
          })
          break
        case 'NEW_CHECKRIDE_OFFER':
          FlashMessage({
            msg: 'You have a new check ride offer!',
            type: 'info'
          })
          break
        case 'CHECKRIDE_STATUS_CHANGED':
          FlashMessage({
            msg: `Check ride status updated to: ${data.status}`,
            type: 'info'
          })
          break
        default:
          console.log('Unknown check ride update type:', data.type)
      }

      // Emit custom event for components to listen to
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('checkride-update', { detail: data }))
      }

    } catch (error) {
      console.error('Error handling check ride update:', error)
    }
  }

  useEffect(() => {
    if (userId) {
      connectSocket()
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socket) {
        socket.disconnect()
      }
    }
  }, [userId, checkRideId])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.disconnect()
        setSocket(null)
        setIsConnected(false)
      }
    }
  }, [])

  const emitEvent = (eventName, data) => {
    if (socket && isConnected) {
      socket.emit(eventName, data)
    } else {
      console.warn('Socket not connected, cannot emit event:', eventName)
    }
  }

  return {
    socket,
    isConnected,
    emitEvent,
    reconnectAttempts: reconnectAttempts.current
  }
}

export default useCheckRideWebSocket