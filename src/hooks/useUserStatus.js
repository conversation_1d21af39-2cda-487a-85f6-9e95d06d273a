import { useEffect, useContext, useState } from 'react'
import { AppState } from 'react-native'
import { appJoin, appLeave } from '../socket/Socket'
import UserContext from '../context/User/User'

const useUserStatus = () => {
  const user = useContext(UserContext)
  const [appState, setAppState] = useState(AppState.currentState)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    if (!user?._id) return

    const handleAppStateChange = (nextAppState) => {
      console.log('App state changed:', nextAppState)
      
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // App came to foreground - user is online
        console.log('User came online')
        appJoin(user._id)
        setIsOnline(true)
      } else if (nextAppState.match(/inactive|background/)) {
        // App went to background - user is offline
        console.log('User went offline')
        appLeave(user._id)
        setIsOnline(false)
      }
      
      setAppState(nextAppState)
    }

    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange)

    // Join app when hook initializes (user becomes online)
    appJoin(user._id)
    setIsOnline(true)

    return () => {
      // Leave app when component unmounts
      appLeave(user._id)
      setIsOnline(false)
      subscription?.remove()
    }
  }, [user?._id, appState])

  // Manual controls for online/offline status
  const goOnline = () => {
    if (user?._id) {
      appJoin(user._id)
      setIsOnline(true)
    }
  }

  const goOffline = () => {
    if (user?._id) {
      appLeave(user._id)
      setIsOnline(false)
    }
  }

  return {
    isOnline,
    goOnline,
    goOffline,
    appState
  }
}

export default useUserStatus