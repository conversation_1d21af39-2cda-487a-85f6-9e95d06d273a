{"nm": "Frame 4", "ddd": 0, "h": 1125, "w": 1125, "meta": {"g": "LottieFiles AE 0.1.21"}, "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 300, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [568, 556, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 0}, {"s": [180], "t": 135}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Vector", "ix": 1, "cix": 2, "np": 11, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.774, -7.089], [0, 0], [-1.013, 1.745]], "o": [[-4.393, 5.635], [0, 0], [0.464, -1.962], [0, 0]], "v": [[-29.513, -23.891], [-37.415, -4.43], [-17.846, -4.377], [-15.618, -9.968]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5.359, -4.311], [0, 0], [-1.661, 0.515]], "o": [[-6.823, 0.97], [0, 0], [1.473, -0.92], [0, 0]], "v": [[-4.534, -38.817], [-23.149, -30.747], [-9.254, -16.877], [-4.534, -19.039]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 3", "ix": 3, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7.648, 0.759], [0, 0], [-2.101, -1.391]], "o": [[-5.943, -4.846], [0, 0], [2.482, 0.454], [0, 0]], "v": [[25.694, -30.484], [4.853, -39.08], [4.853, -19.461], [11.8, -16.666]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 4", "ix": 4, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4.26, 5.631], [0, 0], [-0.416, -1.845]], "o": [[-0.774, -7.004], [0, 0], [0.921, 1.654], [0, 0]], "v": [[39.854, -4.483], [32.164, -23.786], [18.217, -9.757], [20.232, -4.483]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 5", "ix": 5, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.893, 7.006], [0, 0], [1.04, -1.631]], "o": [[4.382, -5.557], [0, 0], [-0.507, 1.865], [0, 0]], "v": [[31.687, 23.997], [39.748, 4.799], [20.073, 4.852], [17.74, 10.126]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 6", "ix": 6, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5.783, 4.551], [0, 0], [2.189, -0.433]], "o": [[7.339, -0.717], [0, 0], [-1.917, 1.137], [0, 0]], "v": [[4.906, 38.658], [25.005, 30.589], [11.11, 16.877], [4.906, 19.25]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 7", "ix": 7, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6.588, -0.93], [0, 0], [1.286, 0.74]], "o": [[5.264, 4.047], [0, 0], [-1.426, -0.416], [0, 0]], "v": [[-22.512, 30.853], [-4.428, 38.447], [-4.481, 18.617], [-8.565, 16.877]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 8", "ix": 8, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4.539, -5.632], [0, 0], [0.517, 2.045]], "o": [[0.905, 7.162], [0, 0], [-1.168, -1.76], [0, 0]], "v": [[-37.574, 4.747], [-29.248, 24.313], [-15.353, 10.495], [-17.899, 4.747]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 9", "ix": 9, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [1.469, 1.214], [0.188, 1.89], [-0.459, 0.842], [-0.749, 0.603], [-0.924, 0.271], [-0.958, -0.102], [0, 0], [-7.028, 8.862], [0, 0], [0.178, 1.888], [-1.189, 1.483], [-1.898, -0.178], [-1.219, -1.458], [0, 0], [-11.383, 1.171], [0, 0], [-0.236, 0.948], [-0.622, 0.756], [-0.888, 0.419], [-0.982, 0], [-0.888, -0.419], [-0.622, -0.756], [-0.236, -0.948], [0.195, -0.957], [0, 0], [-8.913, -7.226], [0, 0], [-1.898, 0.178], [-1.472, -1.205], [0.178, -1.892], [1.457, -1.23], [0, 0], [-1.212, -11.169], [0, 0], [-1.467, -1.222], [-0.188, -1.894], [1.469, -1.214], [1.903, 0.162], [0, 0], [7.237, -8.906], [0, 0], [-0.503, -0.834], [-0.097, -0.968], [0.328, -0.917], [0.69, -0.69], [0.92, -0.331], [0.974, 0.091], [0.842, 0.496], [0.549, 0.805], [0, 0], [11.217, -1.175], [0, 0], [0.236, -0.948], [0.622, -0.756], [0.888, -0.419], [0.982, 0], [0.888, 0.419], [0.622, 0.756], [0.236, 0.948], [-0.195, 0.957], [0, 0], [8.741, 6.899], [0, 0], [1.889, -0.178], [1.463, 1.202], [-0.168, 1.892], [-1.449, 1.237], [0, 0], [1.274, 11.382]], "o": [[0, 0], [-1.903, 0.162], [-1.469, -1.214], [0.099, -0.953], [0.459, -0.842], [0.749, -0.603], [0.924, -0.271], [0, 0], [1.183, -11.225], [0, 0], [-1.445, -1.237], [-0.178, -1.888], [1.472, -1.205], [1.898, 0.178], [0, 0], [8.897, -7.158], [0, 0], [-0.195, -0.957], [0.236, -0.948], [0.622, -0.756], [0.888, -0.419], [0.982, 0], [0.888, 0.419], [0.622, 0.756], [0.236, 0.948], [0, 0], [11.435, 1.206], [0, 0], [1.219, -1.458], [1.898, -0.178], [1.202, 1.478], [-0.178, 1.892], [0, 0], [7, 8.817], [0, 0], [1.908, -0.147], [1.467, 1.222], [-0.188, 1.89], [-1.469, 1.214], [0, 0], [-1.283, 11.38], [0, 0], [0.813, 0.541], [0.503, 0.834], [0.097, 0.968], [-0.328, 0.917], [-0.69, 0.69], [-0.92, 0.331], [-0.974, -0.091], [-0.842, -0.496], [0, 0], [-8.796, 7.02], [0, 0], [0.195, 0.957], [-0.236, 0.948], [-0.622, 0.756], [-0.888, 0.419], [-0.982, 0], [-0.888, -0.419], [-0.622, -0.756], [-0.236, -0.948], [0, 0], [-11.086, -1.271], [0, 0], [-1.21, 1.454], [-1.889, 0.178], [-1.208, -1.471], [0.168, -1.892], [0, 0], [-7.245, -8.902], [0, 0]], "v": [[-58.257, 6.487], [-72.151, 6.487], [-77.414, 4.845], [-80, 0], [-79.154, -2.719], [-77.324, -4.908], [-74.79, -6.232], [-71.939, -6.487], [-58.097, -6.487], [-45.529, -37.234], [-54.969, -46.675], [-57.499, -51.547], [-55.923, -56.801], [-50.661, -58.405], [-45.794, -55.852], [-36.407, -46.464], [-5.33, -59.227], [-5.33, -72.096], [-5.268, -74.99], [-3.966, -77.58], [-1.674, -79.364], [1.167, -80], [4.007, -79.364], [6.299, -77.58], [7.602, -74.99], [7.663, -72.096], [7.663, -59.227], [38.847, -46.306], [47.544, -55.061], [52.412, -57.614], [57.673, -56.01], [59.27, -50.754], [56.719, -45.884], [48.127, -37.076], [60.696, -6.487], [72.151, -6.487], [77.417, -4.81], [80, 0.053], [77.414, 4.897], [72.151, 6.54], [60.643, 6.54], [47.597, 37.604], [56.135, 46.253], [58.134, 48.342], [59.045, 51.079], [58.695, 53.941], [57.15, 56.38], [54.706, 57.931], [51.83, 58.296], [49.072, 57.405], [46.961, 55.43], [38.316, 46.78], [7.663, 59.333], [7.663, 72.096], [7.602, 74.99], [6.299, 77.58], [4.007, 79.364], [1.167, 80], [-1.674, 79.364], [-3.966, 77.58], [-5.268, 74.99], [-5.33, 72.096], [-5.33, 59.438], [-35.665, 46.939], [-44.945, 56.168], [-49.786, 58.717], [-55.022, 57.117], [-56.643, 51.872], [-54.12, 46.991], [-44.945, 37.762], [-57.991, 6.698]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1255, 0.1137, 0.1137], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 1}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 300, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [-94.074, -94.074, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [568, 562, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [180], "t": 0}, {"s": [0], "t": 135}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Vector", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [1.576, -0.56], [1.222, -1.139], [0.666, -1.53], [0, -1.668], [-0.666, -1.53], [-1.222, -1.139], [-1.576, -0.56], [-1.669, 0.112], [0, 0], [-5.787, -8.239], [0, 0], [0, -3.227], [-2.271, -2.299], [-1.479, -0.613], [-1.602, 0], [-1.479, 0.613], [-1.13, 1.133], [0, 0], [-9.503, -1.769], [0, 0], [-0.556, -1.558], [-1.132, -1.208], [-1.521, -0.658], [-1.658, 0], [-1.521, 0.658], [-1.132, 1.208], [-0.556, 1.558], [0.112, 1.65], [0, 0], [-8.043, 5.574], [0, 0], [-1.475, -0.613], [-1.598, 0], [-1.475, 0.613], [-1.125, 1.131], [0, 3.232], [2.284, 2.292], [0, 0], [-1.749, 9.907], [0, 0], [-1.576, 0.56], [-1.222, 1.139], [-0.666, 1.53], [0, 1.668], [0.666, 1.53], [1.222, 1.139], [1.576, 0.56], [1.669, -0.112], [0, 0], [5.571, 8.092], [0, 0], [0, 3.232], [2.284, 2.292], [1.475, 0.613], [1.598, 0], [1.475, -0.613], [1.125, -1.131], [0, 0], [9.953, 1.774], [0, 0], [0.556, 1.558], [1.132, 1.208], [1.521, 0.658], [1.658, 0], [1.521, -0.658], [1.132, -1.208], [0.556, -1.558], [-0.112, -1.65], [0, 0], [8.161, -5.619], [0, 0], [1.475, 0.613], [1.598, 0], [1.475, -0.613], [1.125, -1.131], [0, -3.232], [-2.284, -2.292], [0, 0], [1.712, -9.666]], "o": [[0, 0], [-1.669, -0.112], [-1.576, 0.56], [-1.222, 1.139], [-0.666, 1.53], [0, 1.668], [0.666, 1.53], [1.222, 1.139], [1.576, 0.56], [0, 0], [1.746, 9.908], [0, 0], [-2.271, 2.299], [0, 3.227], [1.13, 1.133], [1.479, 0.613], [1.602, 0], [1.479, -0.613], [0, 0], [7.943, 5.496], [0, 0], [-0.112, 1.65], [0.556, 1.558], [1.132, 1.208], [1.521, 0.658], [1.658, 0], [1.521, -0.658], [1.132, -1.208], [0.556, -1.558], [0, 0], [9.647, -1.686], [0, 0], [1.125, 1.131], [1.475, 0.613], [1.598, 0], [1.475, -0.613], [2.284, -2.292], [0, -3.232], [0, 0], [5.784, -8.241], [0, 0], [1.669, 0.112], [1.576, -0.56], [1.222, -1.139], [0.666, -1.53], [0, -1.668], [-0.666, -1.53], [-1.222, -1.139], [-1.576, -0.56], [0, 0], [-1.712, -9.666], [0, 0], [2.284, -2.292], [0, -3.232], [-1.125, -1.131], [-1.475, -0.613], [-1.598, 0], [-1.475, 0.613], [0, 0], [-8.238, -5.846], [0, 0], [0.112, -1.65], [-0.556, -1.558], [-1.132, -1.208], [-1.521, -0.658], [-1.658, 0], [-1.521, 0.658], [-1.132, 1.208], [-0.556, 1.558], [0, 0], [-9.78, 1.639], [0, 0], [-1.125, -1.131], [-1.475, -0.613], [-1.598, 0], [-1.475, 0.613], [-2.284, 2.292], [0, 3.232], [0, 0], [-5.571, 8.092], [0, 0]], "v": [[-66.857, -12.316], [-76.968, -12.316], [-81.887, -11.637], [-86.128, -9.061], [-88.99, -5.014], [-90, -0.165], [-88.99, 4.684], [-86.128, 8.731], [-81.887, 11.306], [-76.968, 11.985], [-65.917, 11.985], [-54.48, 39.537], [-60.889, 45.984], [-64.433, 54.608], [-60.889, 63.231], [-56.937, 65.877], [-52.27, 66.806], [-47.602, 65.877], [-43.65, 63.231], [-37.13, 56.674], [-10.664, 67.695], [-10.664, 77.117], [-9.991, 81.98], [-7.432, 86.172], [-3.41, 89.002], [1.409, 90], [6.228, 89.002], [10.25, 86.172], [12.809, 81.98], [13.482, 77.117], [13.482, 68.191], [40.335, 57.17], [46.247, 63.066], [50.185, 65.708], [54.839, 66.636], [59.493, 65.708], [63.431, 63.066], [66.997, 54.442], [63.431, 45.819], [57.906, 40.033], [69.343, 12.481], [76.968, 12.481], [81.887, 11.802], [86.128, 9.226], [88.99, 5.179], [90, 0.331], [88.99, -4.518], [86.128, -8.565], [81.887, -11.141], [76.968, -11.82], [69.398, -11.82], [58.348, -38.765], [64.868, -45.323], [68.434, -53.946], [64.868, -62.57], [60.93, -65.212], [56.276, -66.14], [51.622, -65.212], [47.684, -62.57], [41.274, -56.178], [13.648, -67.75], [13.648, -77.117], [12.974, -81.98], [10.415, -86.172], [6.394, -89.002], [1.575, -90], [-3.244, -89.002], [-7.266, -86.172], [-9.825, -81.98], [-10.498, -77.117], [-10.498, -67.364], [-37.738, -56.343], [-44.645, -63.341], [-48.583, -65.983], [-53.237, -66.912], [-57.891, -65.983], [-61.829, -63.341], [-65.395, -54.718], [-61.829, -46.094], [-54.646, -38.876], [-65.696, -11.93]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.6431, 0.5608, 0.1961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 2}], "v": "5.5.7", "fr": 100, "op": 300, "ip": 0, "assets": []}