import { Linking } from 'react-native'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'

export async function openLink(urlLink) {
	try {
		const url = urlLink
		if (await InAppBrowser.isAvailable()) {
			const result = await InAppBrowser.open(url, {
				// iOS Properties
				dismissButtonStyle: 'cancel',
				preferredBarTintColor: '#A79441',
				preferredControlTintColor: 'white',
				readerMode: false,
				animated: true,
				modalPresentationStyle: 'fullScreen',
				modalTransitionStyle: 'coverVertical',
				modalEnabled: true,
				enableBarCollapsing: false,
				// Android Properties
				showTitle: true,
				toolbarColor: '#A79441',
				secondaryToolbarColor: 'black',
				navigationBarColor: 'black',
				navigationBarDividerColor: 'white',
				enableUrlBarHiding: true,
				enableDefaultShare: true,
				forceCloseOnRedirection: false,
				showInRecents: true,
				// Specify full animation resource identifier(package:anim/name)
				// or only resource name(in case of animation bundled with app).
				animations: {
					startEnter: 'slide_in_right',
					startExit: 'slide_out_left',
					endEnter: 'slide_in_left',
					endExit: 'slide_out_right',
				},

				headers: {
					'my-custom-header': 'RiverBank Professional',
				},
			})
			console.log('InAppBrowser result :', JSON.stringify(result))
			const data = { ok: result }
			return { ...result, ...data }
		}
		Linking.openURL(url)
	} catch (error) {
		console.log('InAppBrowser catch :', error.message)
	}
}

export async function openAuthLink(urlLink) {
	try {
		const url = urlLink
		const redirect_url = 'https://www.facebook.com/'
		if (await InAppBrowser.isAvailable()) {
			const result = await InAppBrowser.openAuth(url, redirect_url)
			console.log('InAppBrowser result :', JSON.stringify(result))
			if (result.type === 'success' && !result.url.includes(redirect_url)) {
				// Handle the result data here
				console.log('Result:', result)
			}
			const data = { ok: result.type, ok2: result.type }
			return { ...result, ...data }
		}
		Linking.openURL(url)
	} catch (error) {
		console.log('InAppBrowser catch :', error.message)
	}
}
