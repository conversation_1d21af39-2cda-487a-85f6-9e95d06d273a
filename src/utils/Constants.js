import AsyncStorage from '@react-native-async-storage/async-storage'
import { useCallback, useState } from 'react'
import { Alert, Linking, Platform, Share } from 'react-native'
import RNFS from 'react-native-fs'

export const APP_STORE_ID = '**********'
export const packageName = 'com.riverbank'
export const playStoreURL = `https://play.google.com/store/apps/details?id=${packageName}`
export const appStoreURL = `https://apps.apple.com/pl/app/riverbank-pro/id${APP_STORE_ID}`

export const pdfViewerUrl = url => {
	if (url) {
		const pdfViewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}`
		return pdfViewerUrl
	}
}

export const urls = {
	domain: 'https://riverbankpro.com/',
	scheme: 'app.riverbankpro://',
}

export const onShare = async ({ message, endpoint, title, shareType }) => {
	const pre_content =
		shareType === 'job'
			? 'Check out this job opportunity at RiverBank Pro! If you’re interested, click the link below to view full job details and apply:'
			: shareType === 'company'
				? 'Discover more about RiverBank Pro! Learn about our mission, values, and the services we offer. Visit our website to explore:'
				: 'RiverBank Pro.'
	const post_content =
		shareType === 'job'
			? 'Click to see job detail in RiverBank Pro'
			: shareType === 'company'
				? 'Click to see company detail in RiverBank Pro'
				: 'No Description Available'

	const storeLinks = `Don’t have the app? Download it now!\n \nPlay Store:\n${playStoreURL}\nApp Store:\n${appStoreURL}`
	const description = `${pre_content}\n${urls.domain}${message}\n${post_content}\n \n${storeLinks}`
	try {
		const result = await Share.share({
			message: description,
			// url: `https://resturant-test-app-frontend.vercel.app/app${endpoint}`,
			// title: title,
		})
		if (result.action === Share.sharedAction) {
			if (result.activityType) {
				// shared with activity type of result.activityType
			} else {
				// shared
			}
		} else if (result.action === Share.dismissedAction) {
			// dismissed
		}
	} catch (error) {
		Alert.alert(error.message)
	}
}

export const handleSocial = url => {
	if (url) {
		Linking.openURL(url)
	}
}
export const emailregex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
export const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(\S*)/g
export const passwordRegex = /^(?=.*?[A-Z])(?=.*[a-z])(?=.*?[#?!@$%^&*-]).{8,}$/

export const getFileNameFromPath = filePath => {
	if (filePath) {
		const parts = filePath?.split('/')
		return parts[parts?.length - 1]
	}
}

export const isEmailValid = input => {
	// Regular expression pattern to match email format
	const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
	// Regular expression pattern to match the word "email" in a sentence
	const emailWordPattern = /\bemail\b/i

	// Test if the input string matches the email format
	const isEmail = emailPattern.test(input)
	// Test if the input string contains the word "email"
	const hasEmailWord = emailWordPattern.test(input)

	// Return true if either the email format is matched or the word "email" is found
	return isEmail || hasEmailWord
}

export const getFileExtension = filePath => {
	const parts = filePath.split('/')
	const fileName = parts[parts.length - 1]
	const fileNameParts = fileName.split('.')

	if (fileNameParts.length > 1) {
		return `.${fileNameParts.pop()}`
	}
	return ''
}

export const getFileSize = async filePath => {
	try {
		const fileInfo = await RNFS.stat(filePath)
		const fileSizeInBytes = fileInfo.size
		const fileSizeInKB = fileSizeInBytes / 1024
		const fileSizeInMB = fileSizeInKB / 1024

		console.log('File Size:', fileSizeInBytes, 'bytes')
		console.log('File Size:', fileSizeInKB, 'KB')
		console.log('File Size:', fileSizeInMB, 'MB')

		return { fileSizeInBytes, fileSizeInKB, fileSizeInMB } // You can return the size in bytes, KB, or MB based on your needs
	} catch (error) {
		console.error('Error getting file size:', error)
		return null
	}
}

export const delta = {
	latitudeDelta: 0.015,
	longitudeDelta: 0.0121,
}

export const switchSize = Platform.OS === 'ios' ? [{ scaleX: 0.8 }, { scaleY: 0.8 }] : [{ scaleX: 1 }, { scaleY: 1 }]

export const surffixValue = ['Jr.', 'Mr.', 'Ms.', 'Mrs.']

export const removeRoles = ['supportAgent', 'superAdmin', 'subAdmin', 'admin', 'guest', 'captain', 'examiner', 'subCompany']

export function formatPhoneNumber(phoneNumberString) {
	if (phoneNumberString) {
		var cleaned = `${phoneNumberString}`.replace(/\D/g, '')
		var match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/)
		if (match) {
			var intlCode = match[1] ? '+1 ' : ''
			return [intlCode, '(', match[2], ') ', match[3], '-', match[4]].join('')
		}
		return phoneNumberString
	}
}

// export const user_eligible = userinfo => {
//   var user_checkr_eligible = false;
//   var user_stripe_eligible = false;
//   if (userinfo?.checkr_decision === 'ENGAGED') {
//     user_checkr_eligible = true;
//     if (userinfo?.payouts_enabled && userinfo?.transfer_enabled) {
//       user_stripe_eligible = true;
//     }
//   }
//   if (user_checkr_eligible && user_stripe_eligible) {
//     return true;
//   }
// };

export const user_eligible = userinfo => {
	let user_checkr_eligible = false
	const _user_stripe_eligible = false
	if (userinfo?.checkr_status === 'ENGAGE') {
		user_checkr_eligible = true
	} else {
		return { status: false, type: 'checkr' }
	}
	// if (userinfo?.payouts_enabled && userinfo?.transfer_enabled) {
	//   user_stripe_eligible = true;
	// } else {
	//   return {status: false, type: 'stripe'};
	// }
	if (
		user_checkr_eligible
		// && user_stripe_eligible
	) {
		console.log('eligible')
		return { status: true, type: 'all' }
	}
	console.log('not eligible')
	return false
}

// export const user_eligible = userinfo => {
//   let user_checkr_eligible = false;
//   let user_stripe_eligible = false;
//   if (userinfo?.checkr_decision === 'ENGAGED') {
//     user_checkr_eligible = true;
//   } else {
//     return {status: false, type: 'checkr'};
//   }
//   // if (userinfo?.payouts_enabled && userinfo?.transfer_enabled) {
//   //   user_stripe_eligible = true;
//   // } else {
//   //   return {status: false, type: 'stripe'};
//   // }
//   if (
//     user_checkr_eligible
//     // && user_stripe_eligible
//   ) {
//     console.log('eligible');
//     return {status: true, type: 'all'};
//   } else {
//     console.log('not eligible');
//     return false;
//   }
// };

export function get_url_extension(url) {
	if (url) {
		return url?.split(/[#?]/)[0]?.split('.')?.pop()?.trim()
	}
	return 'new'
}

export const renderText = html => {
	if (html) {
		const regex = /(<([^>]+)>)/gi
		const plainText = html?.replace(regex, '')
		return plainText
	}
}

export const createParentObjects = (arr, key) => {
	if (arr) {
		const parentObjects = {}

		arr?.forEach(obj => {
			const value = obj[key]
			if (value in parentObjects) {
				parentObjects[value].children.push(obj)
			} else {
				parentObjects[value] = { value, children: [obj] }
			}
		})

		return Object.values(parentObjects)
	}
}

export function capitalizeFirstLetter(string) {
	if (string) {
		const text = string?.trim()
		return text?.charAt(0)?.toUpperCase() + text?.slice(1)
	}
}

export function validateArray(array) {
	for (let i = 0; i < array.length; i++) {
		const object = array[i]
		const keys = Object.keys(object)
		for (let j = 0; j < keys.length; j++) {
			const key = keys[j]
			const value = object[key]

			if (value === null || value === undefined || value === '') {
				return false // Found an empty value
			}
		}
	}

	return true // No empty values found
}

export const removetags = content => {
	if (content) {
		const outputString = content?.replaceAll(/<[^>]+>|&[^;]+;/g, '')
		const remove1 = outputString?.replaceAll('.p>p>', '\n')
		const remove2 = remove1?.replaceAll('p>p>', '\n')
		const string = remove2?.replaceAll('.p>', '\n')
		const string2 = string?.replaceAll(':p>', '\n')
		return string2
	}
}

export const getDataAsync = async key => {
	try {
		const data = await AsyncStorage.getItem(key)
		if (data !== null) {
			// console.log(`getDataAsync ${key} :`, data);
			return JSON.parse(data)
		}
	} catch (error) {
		console.log('getDataAsync catch :', error)
	}
}

export async function removeItemAsync(key) {
	try {
		await AsyncStorage.removeItem(key)
		return true
	} catch (exception) {
		console.log('removeItemValue catch :', exception)
		return false
	}
}

export const useThrottledPress = (callback, delay = 2500) => {
	const [isThrottled, setThrottle] = useState(false)
	const handlePress = useCallback(
		(...args) => {
			if (!isThrottled) {
				callback(...args)
				setThrottle(true)
				setTimeout(() => {
					setThrottle(false)
				}, delay)
			} else {
				console.log('too many hits')
			}
		},
		[callback, isThrottled, delay]
	)

	return handlePress
}
