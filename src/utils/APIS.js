export const checkDisposibleEmail = async email => {
	const url = `https://open.kickbox.com/v1/disposable/${email}`
	try {
		let response = await fetch(url, {
			method: 'get',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
			},
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		console.log(`disposable ${email} Res :`, response)
		return response
	} catch (error) {
		console.log(`disposable ${email} Err :`, error)
		return error
	} finally {
	}
}
