import { useEffect, useContext } from 'react'
import messaging from '@react-native-firebase/messaging'
import navigationService from '../routes/navigationService'
import * as Notifications from 'expo-notifications'
import { MessageContext } from '../context/Notification/Notification'
import { urls } from './Constants'
import { Linking } from 'react-native'

export const handleNotification = notification => {
	const currentScreen = navigationService.getCurrentRouteName()
	const isRoomID = notification?.data?.hasOwnProperty('roomId')
	if (!['Chat', 'Chats'].includes(currentScreen) || !isRoomID) {
		Notifications.scheduleNotificationAsync({
			content: {
				title: notification?.notification?.title,
				body: notification?.notification?.body,
				sound: true,
			},
			trigger: null,
		})
		console.log('me chala hun')
	}
	if (isRoomID) {
		isNewMessage(true)
	}
}

export const handleDeepLink = async item => {
	const ids = {
		jobId: item?.jobId,
		postId: item?.postId,
		roomId: item?.roomId,
		invoiceId: item?.invoiceId,
		appointmentId: item?.appointmentId,
		ticketId: item?.ticketId,
	}

	const typeToIdMap = {
		newMessage: ids.roomId,
		job: ids.jobId,
		offer: ids.jobId,
		invite: ids.jobId,
		newJob: ids.jobId,
		closeJob: ids.jobId,
		ticket: ids.ticketId,
		checkride: null, // No ID needed for this type
		friendship: null, // No ID needed for this type
		ratings: null, // No ID needed for this type
		recommendation: null, // No ID needed for this type
		post: ids.postId,
		badge: null, // No ID needed for this type
		invoice: ids.invoiceId,
		appointment: ids.appointmentId,
		verification: null, // No ID needed for this type
	}

	const id = typeToIdMap[item?.type]
	const editProfileTypes = ['badge', 'recommendation', 'ratings']
	const jobTypes = ['invite', 'newJob', 'closeJob', 'job', 'offer']
	const type = editProfileTypes.includes(item?.type) ? 'editprofile' : jobTypes.includes(item?.type) ? 'job' : item?.type

	let deepLink = ''
	if (id) {
		deepLink = `${urls.scheme}${type}/${id}`
	} else {
		deepLink = `${urls.scheme}${type}`
	}

	console.log('handleDeepLink :', deepLink)
	Linking.openURL(deepLink).catch(err => console.error('handleDeepLink An error occurred :', err))
}

const NotificationListeners = () => {
	const { isNewMessage } = useContext(MessageContext)

	const _handleNavigation = async item => {
		const jobParams = {
			item: { _id: item?.jobId },
			jobId: item?.jobId,
			// alreadyApplied: true,
		}

		const postParams = {
			_id: item?.postId,
		}

		const badgeParams = {
			edit: false,
			rating: false,
			recommendation: false,
		}

		const ratingParams = {
			rating: true,
			edit: false,
			recommendation: false,
		}

		const recommendationParams = {
			recommendation: true,
			edit: false,
			rating: false,
		}

		const invoiceParams = {
			_id: item?.invoiceId,
		}

		const messageParam = {
			item: { _id: item?.roomId },
			roomId: item?.roomId,
		}

		const appointmenetParams = {
			_id: item?.appointmentId,
		}

		if (item?.type === 'newMessage') {
			navigationService.navigate('Chat', messageParam)
		}
		if (item?.type === 'job' || item?.type === 'offer') {
			navigationService.navigate('JobDetails', jobParams)
		}

		if (item?.type === 'invite' || item?.type === 'newJob' || item?.type === 'closeJob') {
			navigationService.navigate('JobDetails', jobParams)
		}
		if (item?.type === 'checkride') {
			navigationService.navigate('MyCheckRides', { activeTab: 0 })
		}
		if (item?.type === 'friendship') {
			navigationService.navigate('FriendsNavigator', { activeTab: 0 })
		}
		if (item?.type === 'ratings') {
			navigationService.navigate('EditProfile', ratingParams)
		}
		if (item?.type === 'recommendation') {
			navigationService.navigate('EditProfile', recommendationParams)
		}
		if (item?.type === 'post') {
			navigationService.navigate('PostDetails', {
				item: postParams,
				postId: item?.postId,
			})
		}
		if (item?.type === 'badge') {
			navigationService.navigate('EditProfile', badgeParams)
		}
		if (item?.type === 'invoice') {
			navigationService.navigate('PaymentDetails', {
				invoice: invoiceParams,
				invoiceId: item?.invoiceId,
			})
		}
		if (item?.type === 'appointment') {
			navigationService.navigate('AppointmenetDetails', {
				appointmenetParams: appointmenetParams,
				appointmentId: item?.appointmentId,
			})
		}
		if (item?.type === 'verification') {
			navigationService.navigate('Menu')
		}
	}

	useEffect(() => {
		messaging().onNotificationOpenedApp(async remoteMessage => {
			console.log('Notification caused app to open from background state:', remoteMessage.notification)
			const { data } = remoteMessage
			if (data) {
				// handleNavigation(data);
				handleDeepLink(data)
			}
		})

		messaging().onMessage(async remoteMessage => {
			// isNewMessage(true);
			console.log('A new message arrived on onMessage!', JSON.stringify(remoteMessage))
		})

		messaging().setBackgroundMessageHandler(async remoteMessage => {
			console.log('Message handled in the background/setBackgroundMessageHandler :', remoteMessage)
			const { data } = remoteMessage
			if (data) {
				// handleNavigation(data);
				handleDeepLink(data)
			}
		})

		messaging().onNotificationOpenedApp(async remoteMessage => {
			console.log('Message handled in the onNotificationOpenedApp :', remoteMessage)

			const { data } = remoteMessage
			if (data) {
				// handleNavigation(data);
				handleDeepLink(data)
			}
		})

		messaging()
			.getInitialNotification()
			.then(async remoteMessage => {
				if (remoteMessage) {
					console.log('Notification caused app to open from quit state:', remoteMessage)
					const { data } = remoteMessage
					if (data) {
						// handleNavigation(data);
						handleDeepLink(data)
					}
				}
			})
			.catch(() => {})
	}, [])

	return null // This component doesn't render anything
}

export default NotificationListeners
