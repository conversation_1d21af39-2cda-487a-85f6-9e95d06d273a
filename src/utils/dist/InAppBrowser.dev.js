Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.openLink = openLink

var _reactNativeInappbrowserReborn = require('react-native-inappbrowser-reborn')

function openLink(urlLink) {
	var url
	var result
	return regeneratorRuntime.async(
		function openLink$(_context) {
			while (1) {
				switch ((_context.prev = _context.next)) {
					case 0:
						_context.prev = 0
						url = urlLink
						_context.next = 4
						return regeneratorRuntime.awrap(_reactNativeInappbrowserReborn.InAppBrowser.isAvailable())

					case 4:
						if (!_context.sent) {
							_context.next = 13
							break
						}

						_context.next = 7
						return regeneratorRuntime.awrap(
							_reactNativeInappbrowserReborn.InAppBrowser.open(url, {
								// iOS Properties
								dismissButtonStyle: 'cancel',
								preferredBarTintColor: '#A79441',
								preferredControlTintColor: 'white',
								readerMode: false,
								animated: true,
								modalPresentationStyle: 'fullScreen',
								modalTransitionStyle: 'coverVertical',
								modalEnabled: true,
								enableBarCollapsing: false,
								// Android Properties
								showTitle: true,
								toolbarColor: '#A79441',
								secondaryToolbarColor: 'black',
								navigationBarColor: 'black',
								navigationBarDividerColor: 'white',
								enableUrlBarHiding: true,
								enableDefaultShare: true,
								forceCloseOnRedirection: false,
								// Specify full animation resource identifier(package:anim/name)
								// or only resource name(in case of animation bundled with app).
								animations: {
									startEnter: 'slide_in_right',
									startExit: 'slide_out_left',
									endEnter: 'slide_in_left',
									endExit: 'slide_out_right',
								},
								headers: {
									'my-custom-header': 'RiverBank Pro',
								},
							})
						)

					case 7:
						result = _context.sent
						_context.next = 10
						return regeneratorRuntime.awrap(this.sleep(800))

					case 10:
						Alert.alert(JSON.stringify(result))
						_context.next = 14
						break

					case 13:
						Linking.openURL(url)

					case 14:
						_context.next = 19
						break

					case 16:
						_context.prev = 16
						_context.t0 = _context.catch(0)
						Alert.alert(_context.t0.message)

					case 19:
					case 'end':
						return _context.stop()
				}
			}
		},
		null,
		this,
		[[0, 16]]
	)
}
