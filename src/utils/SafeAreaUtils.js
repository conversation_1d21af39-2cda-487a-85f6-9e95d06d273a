import { Platform } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

export const useAndroidSafeTop = (maxPadding = 50) => {
  const insets = useSafeAreaInsets()
  
  if (Platform.OS === 'android') {
    return Math.min(insets.top, maxPadding)
  }
  
  return 0
}

export const useIOSSafeBottom = (maxPadding = 30) => {
  const insets = useSafeAreaInsets()
  
  if (Platform.OS === 'ios') {
    return Math.min(insets.bottom, maxPadding)
  }
  
  return 0
}