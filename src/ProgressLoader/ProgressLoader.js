import { useContext, useRef } from 'react'
import { View, Text, StyleSheet, Animated } from 'react-native'
import { AnimatedCircularProgress } from 'react-native-circular-progress'
import ThemeContext from '../context/ThemeContext/ThemeContext'
import { theme } from '../context/ThemeContext/ThemeColor'

const ProgressLoader = ({ progress }) => {
	const _animatedValue = useRef(new Animated.Value(0)).current
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<View style={styles.container}>
			<AnimatedCircularProgress
				size={100}
				width={8}
				fill={progress}
				tintColor="#A79441"
				backgroundColor={currentTheme.F3F3F3}
			>
				{fill => <Text style={styles.progressText}>{`${Math?.round(fill)}%`}</Text>}
			</AnimatedCircularProgress>
		</View>
	)
}

const styles = StyleSheet.create({
	container: {
		alignItems: 'center',
		justifyContent: 'center',
	},
	progressText: {
		fontFamily: 'Poppins-Medium',
		fontSize: 14,
		color: '#A79441', // Neon color
	},
})

export default ProgressLoader
