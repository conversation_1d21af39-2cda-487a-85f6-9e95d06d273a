import React, { useState } from 'react'
import { useEffect } from 'react'
import { useQuery } from '@apollo/client'
import { getNotificationsForContext } from '../../apollo/server'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../component/ErrorHandler/ErrorHandler'

const NotificationContext = React.createContext({})
export const MessageContext = React.createContext({})

export const NotificationProvider = props => {
	const [unreads, setUnreads] = useState('')
	const [reload, setReload] = useState(false)

	const reloadAppData = () => {
		setReload(true)
	}

	const { data, loading, error, refetch } = useQuery(getNotificationsForContext, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		nextFetchPolicy: 'network-only',
		variables: {
			options: { sortBy: 'createdAt:desc' },
			filters: {
				seen: false,
			},
		},
		onCompleted: async data => {
			console.log('NOTIFICATION RELOADS')
			//   let unreads = data?.getNotifications?.results
			//     ?.filter(item => {
			//       return item?.seen === false;
			//     })
			//     .map(item => item?._id);
			setUnreads(data?.getNotifications?.results?.length)
		},
		onError: err => {
			console.log('Notification context err :', err.message)
			ErrorHandler(err.message)
		},
	})

	// console.log('unreads :', unreads);

	useEffect(() => {
		if (reload) {
			refetch().then(() => {
				setReload(false)
			})
		}
	}, [reload])

	return <NotificationContext.Provider value={{ unreads, reloadAppData }}>{props.children}</NotificationContext.Provider>
}

export const NotificationConsumer = NotificationContext.Consumer
export default NotificationContext
