import React, { useContext, useRef, useState, useEffect, useMemo } from 'react'
import { AppState } from 'react-native'
import { useMutation, useQuery } from '@apollo/client'
import { profile, refreshTokens, updateUser } from '../../apollo/server'
import { AuthContext } from '../Auth/auth'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { <PERSON>rror<PERSON>andler } from '../../component/ErrorHandler/ErrorHandler'
import { appJoin, initiateSocketConnection, new_message_listener, socket_event_listener } from '../../socket/Socket'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import GeolocationService from 'react-native-geolocation-service'
import Loading from '../Loading/Loading'
import { MessageContext } from '../Notification/Notification'
import ThemeContext from '../ThemeContext/ThemeContext'
import { theme } from '../ThemeContext/ThemeColor'

const UserContext = React.createContext({})

export const UserProvider = props => {
	const { token, refreshToken, setTokenAsync } = useContext(AuthContext)
	const { isNewMessage } = useContext(MessageContext)
	const [user, setUser] = useState('')
	const [_myLocation, setMyLocation] = useState('')
	const { isLoader } = useContext(Loading)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [refreshToken_mutate, {}] = useMutation(refreshTokens, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		context: {
			headers: null,
		},
		onCompleted: async res => {
			console.log('refreshToken res :', res)
			setTokenAsync(res?.refreshTokens?.token)
			await AsyncStorage.setItem('token', res?.refreshTokens?.token?.toString())
			await AsyncStorage.setItem('refreshToken', res?.refreshTokens?.refreshToken?.toString())
			isLoader(false)
		},
		onError: err => {
			console.log('refreshToken error ', err)
			isLoader(false)
			// appLeave(user?._id);
			ErrorHandler(err.message, props)
		},
	})

	const isTokenExpired = message => {
		if (message === 'Please authenticated') {
			return true
		}
		return false
		// Add your logic to check if the token is expired
		// You may compare the current timestamp with the token's expiration timestamp
		// Return true if expired, false otherwise
	}

	const handleTokenRefresh = async message => {
		const isRemember = await AsyncStorage.getItem('rememberMe')
		console.log('isRemember :', isRemember)
		if (isTokenExpired(message)) {
			if (isRemember === 'true') {
				// isLoader(true);
				console.log('hitting refreshtoken')
				await refreshToken_mutate({ variables: { refreshToken: refreshToken } })
			} else {
				console.log('not hitting refreshtoken')
				ErrorHandler(message)
				isLoader(false)
				// appLeave(user?._id);
			}
			// Token is expired, initiate token refresh
		}

		// Token is either refreshed or still valid, continue with the normal flow
		// ...
	}

	const { data, loading, error, refetch } = useQuery(profile, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		nextFetchPolicy: 'network-only',
		onCompleted: async data => {
			initiateSocketConnection()
			appJoin(data?.profile?._id)
			new_message_listener('messageEmit', async msg => {
				// new_message_listener(userId, async msg => {
				console.log('new message in context :', msg)
				isNewMessage(true)
				//  let user = await AsyncStorage.getItem('user');
				//  if (user?._id?.toString() !== msg?.from?.id) {
				//  }
			})
			socket_event_listener('notification_event', async msg => {
				console.log('notification_event event :', msg)
				FlashMessage({
					desc: `${msg.from?.name} ${msg?.text}`,
					msg: msg?.title,
					type: 'info',
					position: 'top',
					icon: 'none',
					backgroundColor: currentTheme.lighterGold,
				})
			})
			// console.log('Profile res :', JSON.stringify(data?.profile));
			console.log('Profile Refetched.')
			setUser(data.profile)
			await AsyncStorage.setItem('isProfileCompleted', data?.profile?.isProfileCompleted?.toString())
			await AsyncStorage.setItem('user', JSON.stringify(data?.profile))
			await AsyncStorage.setItem('userId', data?.profile?._id?.toString())
		},
		onError: async err => {
			console.log('Profile err :', err.message)
			// isLoader(false);
			// appLeave(user?._id);
			await handleTokenRefresh(err.message)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const [mutate, { client }] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted: _res => {
			console.log('user location update!')
		},
		onError: err => {
			console.log('user location did not update! ', err)
		},
	})

	async function getUserLocation() {
		GeolocationService.getCurrentPosition(
			async position => {
				const { latitude, longitude } = position.coords
				console.log(position.coords)
				setMyLocation(position.coords)
				await updateUserLocation(latitude, longitude)
			},
			error => {
				// See error code charts below.
				console.log('error in usercontext location :', error)
			},
			{ enableHighAccuracy: true, timeout: 20000, maximumAge: 10000 }
		)
	}

	async function refetchProfile() {
		const response = (await refetch()).data?.profile
		setUser(response)
		await AsyncStorage.setItem('isProfileCompleted', response?.isProfileCompleted?.toString())
		// console.log(response);
		await AsyncStorage.setItem('user', JSON.stringify(response))
		await AsyncStorage.setItem('userId', response?._id?.toString())
		appJoin(response?._id)
		new_message_listener('messageEmit', async msg => {
			console.log('new message in context :', msg)
			isNewMessage(true)
		})
		socket_event_listener('notification_event', async msg => {
			console.log('notification_event event :', msg)
			FlashMessage({
				desc: msg?.text,
				msg: msg?.title,
				type: 'info',
				position: 'top',
				icon: 'none',
				backgroundColor: currentTheme.lighterGold,
			})
		})
	}

	async function updateUserLocation(latitude, longitude) {
		if (data?.profile?.role === 'master' && data?.profile?.disposable === 'false') {
			await mutate({
				variables: {
					updateUserInput: {
						loc: {
							coordinates: [String(longitude), String(latitude)],
							type: 'Point',
						},
					},
				},
			})
		}
	}

	const appState = useRef(AppState.currentState)
	const [appStateVisible, setAppStateVisible] = useState(appState.current)

	useEffect(() => {
		const subscription = AppState.addEventListener('change', nextAppState => {
			appState.current = nextAppState
			if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
			}
			setAppStateVisible(appState.current)
		})
		return () => {
			subscription.remove()
		}
	}, [])

	useEffect(() => {
		if (appStateVisible === 'active') {
			refetchProfile()
			// initiateSocketConnection();
			// console.log('app join');
			// appJoin(user?._id);
		}
		// if (appStateVisible === 'background' || appStateVisible === 'inactive')
		else {
			// console.log('app leave');
			// appLeave(user?._id);
			// disconnectSocketConnection();
		}
	}, [appStateVisible])

	// useEffect(() => {
	//   refetch();
	// }, [token]);

	useEffect(() => {
		;async () => {
			await getUserLocation()
			await refetchProfile()
		}
	}, [token])

	useMemo(() => {
		if (loading) {
			// isLoader(true);
		} else {
			isLoader(false)
		}
	}, [loading])

	console.log('token :', token)
	// console.log('AppState :', appStateVisible);
	// console.log('refreshToken :', refreshToken);
	console.log('user._id :', data?.profile?._id)
	// console.log('user.disposable :', data?.profile?.disposable);
	// console.log('user.role :', data?.profile?.role);
	// console.log('user.checkr_decision :', data?.profile?.checkr_decision);

	// const isUser =
	//   loading || error || !data?.profile
	//     ? {user, isLoggedIn: false}
	//     : data?.profile
	//     ? {...data?.profile, refetch}
	//     : {...user, refetch};

	const info = useMemo(
		() =>
			data?.profile
				? { ...data?.profile, isLoggedIn: true, refetch }
				: error?.message
					? { user, isLoggedIn: false, refetch }
					: { ...user, isLoggedIn: true, refetch },
		[data]
	)

	// console.log('info in user', JSON.stringify(info));
	// console.log('error in pro ',error?.message)
	return <UserContext.Provider value={info}>{props.children}</UserContext.Provider>
}
export const UserConsumer = UserContext.Consumer
export default UserContext
