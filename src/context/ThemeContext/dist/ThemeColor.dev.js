Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.theme = void 0

var _Yellow

function _defineProperty(obj, key, value) {
	if (key in obj) {
		Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true })
	} else {
		obj[key] = value
	}
	return obj
}

var theme = {
	Yellow:
		((_Yellow = {
			themeBackground: '#A79441',
			themeBackgroundHalf: 'rgba(162,8,0,0.5)',
			themeSecondary: '#4285F4',
			white: 'white',
			white50: 'rgba(255,255,255,0.5)',
			black: 'black',
			black50: 'rgba(0,0,0,0.5)',
			green: '#32CD32',
			red: '#D51926',
			headingColor: '#2A241A',
			B7B7B7: '#B7B7B7',
			c737373: '#737373',
			D9D9D9: '#D9D9D9',
			C3C3C3: '#C3C3C3',
			CFCFCF: '#CFCFCF',
			E8E8C8: '#8E8C8C',
			EEE8D5: '#EEE8D5',
			F3F0E4: '#F3F0E4',
			F8F9FA: '#F8F9FA',
			c707070: '#70707021',
			e6e6e6: '#e6e6e6',
			c8E97B7: '#8E97B7',
			c444D6E: '#444D6E',
			F5F9FC: '#F5F9FC',
			ebebeb: '#ebebeb',
			F3F3F3: '#F3F3F3',
			c9E9E9E: '#9E9E9E',
			E2E2E2: '#E2E2E2',
			E8E8E8: '#E8E8E8',
			c9F9F9F: '#9F9F9F',
		}),
		_defineProperty(_Yellow, 'green', '#00F86B'),
		_defineProperty(_Yellow, 'D5D4D4', '#D5D4D4'),
		_defineProperty(_Yellow, 'F4F5F6', '#F4F5F6'),
		_defineProperty(_Yellow, 'FFE5E5', '#FFE5E5'),
		_defineProperty(_Yellow, 'E6FFEA', '#E6FFEA'),
		_defineProperty(_Yellow, 'green', '#29E648'),
		_defineProperty(_Yellow, 'red', '#E33636'),
		_defineProperty(_Yellow, 'navyBlue', '#131224'),
		_defineProperty(_Yellow, 'c6a6974', '#6a6974'),
		_defineProperty(_Yellow, 'darkBlue', '#1967D2'),
		_defineProperty(_Yellow, 'lightBlue', '#1967D220'),
		_defineProperty(_Yellow, 'lighterBlue', '#2D3F7B'),
		_defineProperty(_Yellow, 'anotherBlue', '#1977F3'),
		_defineProperty(_Yellow, 'lightGold', '#B3A36E'),
		_defineProperty(_Yellow, 'lighterGold', '#C3B379'),
		_defineProperty(_Yellow, 'themeBackgroundLight', '#eee8d5'),
		_defineProperty(_Yellow, 'lightGreen', '#63C48C'),
		_defineProperty(_Yellow, 'starColor', '#F8BA24'),
		_defineProperty(_Yellow, 'starColorBG', '#FBF8D1'),
		_defineProperty(_Yellow, 'starBg', '#F98600'),
		_defineProperty(_Yellow, 'timeContainerColor', '#f8f5ed'),
		_defineProperty(_Yellow, 'blackish', '#404040'),
		_defineProperty(_Yellow, 'skeletonHightlight', '#beb389'),
		_defineProperty(_Yellow, 'recommendBlue', '#2487F8'),
		_defineProperty(_Yellow, 'BABDC9', '#BABDC9'),
		_defineProperty(_Yellow, 'EFF2F7', '#EFF2F7'),
		_defineProperty(_Yellow, 'c969EB7', '#969EB7'),
		_defineProperty(_Yellow, 'c747EA0', '#747EA0'),
		_defineProperty(_Yellow, 'c7A8FA6', '#7A8FA6'),
		_defineProperty(_Yellow, 'c1B1B1B', '#1B1B1B'),
		_defineProperty(_Yellow, 'postBackground', '#F6F6F6'),
		_defineProperty(_Yellow, 'textColorAuth', '#BFBFBF'),
		_defineProperty(_Yellow, 'EFEFEF', '#EFEFEF'),
		_defineProperty(_Yellow, 'c969AA8', '#969AA8'),
		_defineProperty(_Yellow, 'underLine', '#A20800'),
		_defineProperty(_Yellow, 'inputBackground', '#1F2430'),
		_defineProperty(_Yellow, 'GradientBg', ['#A20800', '#A20800', '#A20800']),
		_Yellow),
}
exports.theme = theme
