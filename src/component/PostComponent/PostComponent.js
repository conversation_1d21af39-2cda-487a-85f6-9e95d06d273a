import {
	Text,
	View,
	TouchableOpacity,
	StatusBar,
	Image,
	Platform,
	Dimensions,
	LayoutAnimation,
	UIManager,
	Animated,
	Easing,
	StyleSheet,
	Linking,
} from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import AntDesign from '@expo/vector-icons/AntDesign'
import EvilIcons from '@expo/vector-icons/EvilIcons'
import Octicons from '@expo/vector-icons/Octicons'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Tooltip from 'react-native-walkthrough-tooltip'
import moment from 'moment'
import ImageView from 'react-native-image-viewing'
import Video from 'react-native-video'
import { deletePost, postLike } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import FlashMessage from '../FlashMessage/FlashMessage'
import UserContext from '../../context/User/User'
import { urlRegex } from '../../utils/Constants'
import Report from '../../context/Report/Report'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const { width, height } = Dimensions.get('window')

const PostComponent = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const { showReportModal, onUpdateReport, setShowReportModal, setReport } = useContext(Report)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, index, update, navigation, isFocused, activeIndex } = props
	const [toolTipVisible, settoolTipVisible] = useState(false)
	const [mute, setMute] = useState({})
	const [pause, setPause] = useState({})
	const [isVideoWidgets, isSetVideoWidgets] = useState({})
	const [readmore, setReadmore] = useState(3)
	const videoRef = useRef(null)
	const [isbuffer, _setIsbuffer] = useState({})

	const lastComment = item?.comments ? item?.comments[item?.comments?.length - 1] : null
	const [visible, setIsVisible] = useState({
		dp: false,
		cp: false,
	})
	const DP = [{ uri: item?.images ? item?.images[0] : null }]
	// console.log('activeIndex :', activeIndex)

	const handlePausePlay = () => {
		setPause({ ...pause, [`pause${index}`]: !pause[`pause${index}`] })
		setMute({ ...mute, [`mute${index}`]: false })
		setTimeout(() => {
			if (pause[`pause${index}`]) {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				isSetVideoWidgets({ ...isVideoWidgets, [`widget${index}`]: false })
			}
		}, 2000)
	}

	const spinnner = new Animated.Value(0)
	const spin = spinnner.interpolate({
		inputRange: [0, 1],
		outputRange: ['0deg', '360deg'],
	})

	const _startImageRotateFunction = async () => {
		await Animated.timing(spinnner, {
			toValue: 1,
			duration: 2000, // Adjust the duration as needed for the spinning speed
			easing: Easing.linear,
			useNativeDriver: true,
		}).start(() => {
			// Reset the animation value and start the animation again
			spinnner.setValue(0)
			_startImageRotateFunction()
		})
	}

	const onVideoEnd = () => {
		videoRef.current.seek(0)
		setPause({ ...pause, [`pause${index}`]: true })
		isSetVideoWidgets({ ...isVideoWidgets, [`widget${index}`]: true })
	}

	const MutedButton = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => setMute({ ...mute, [`mute${index}`]: !mute[`mute${index}`] })}
				style={postStyles.mute}
			>
				<Octicons
					name={mute[`mute${index}`] ? 'mute' : 'unmute'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const ReplayButton = ({ index }) => {
		const handleReplay = () => {
			if (videoRef.current) {
				videoRef.current.seek(0) // Seek to the beginning of the video
				setPause({ ...pause, [`pause${index}`]: false })
			}
		}
		return (
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={handleReplay}
				style={postStyles.reply}
			>
				<MaterialCommunityIcons
					name={'reload'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const PausePlay = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => handlePausePlay()}
				style={postStyles.pausePlay}
			>
				<AntDesign
					name={pause[`pause${index}`] ? 'play' : 'pausecircle'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const RenderTextWithLinks = ({ text, readmore }) => {
		if (text) {
			// Split text by URLs
			const parts = text?.split(urlRegex)
			return parts.map((part, index) => {
				if (urlRegex.test(part)) {
					// Ensure the URL is complete (prepend "http://" for non-prefixed URLs like "www" or domain names)
					const url = part.startsWith('http') ? part : `http://${part}`

					// Render URLs as clickable blue text
					return (
						<TouchableOpacity
							activeOpacity={0.5}
							key={index}
							onPress={() => Linking.openURL(url)}
						>
							<Text
								style={[
									{
										color: currentTheme.darkBlue,
										textDecorationLine: 'underline',
									},
								]}
							>
								{part}
							</Text>
						</TouchableOpacity>
					)
				}
				// Render normal text
				return (
					<Text
						numberOfLines={readmore}
						style={[styles().fs12, styles().fontRegular, { color: currentTheme.c444D6E }]}
						key={index}
					>
						{part}
					</Text>
				)
			})
		}
	}

	const _BufferLoader = ({ index }) => {
		if (!isbuffer[`buffer${index}`]) {
			return null
		}
		return (
			<Animated.View
				style={[
					postStyles.buffer,
					{
						transform: [
							{
								rotate: spin,
							},
						],
						backgroundColor: currentTheme.themeBackground,
					},
				]}
			>
				<EvilIcons
					name={'spinner-3'}
					color={currentTheme.white}
					size={20}
				/>
				{/* <ActivityIndicator color={currentTheme.white} size={'large'} /> */}
			</Animated.View>
		)
	}

	const FullScreenIcon = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => setIsVisible({ ...visible, dp: true })}
				style={postStyles.fullscreen}
			>
				<Octicons
					name={'screen-full'}
					color={currentTheme.themeBackground}
					size={14}
				/>
			</TouchableOpacity>
		)
	}

	const [postDeleteMutate, {}] = useMutation(deletePost, {
		errorPolicy: 'all',
		onCompleted: async ({ deletePost }) => {
			console.log('deletePost res :', deletePost)
			settoolTipVisible(false)
			update('deletePost', deletePost?._id)
		},
		onError: err => {
			console.log('deletePost err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			settoolTipVisible(false)
		},
	})

	const [LikeMutate, {}] = useMutation(postLike, {
		errorPolicy: 'all',
		onCompleted: async ({ postLike }) => {
			console.log('postLike res :', postLike)
			update('postLike', postLike._id)
		},
		onError: err => {
			console.log('postLike err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function DeletePost(id) {
		console.log('post id :', id)
		await postDeleteMutate({
			variables: {
				deletePostInput: {
					id: id,
				},
			},
		})
	}

	async function LikePost(id) {
		// if (!item?.likes?.includes(user?._id)) {
		console.log('post id :', id)
		await LikeMutate({
			variables: {
				postLikeId: id,
				inputLike: {
					user: user?._id,
				},
			},
		})
		// } else {
		//   console.log('already liked')
		// }
	}

	useEffect(() => {
		if (!isFocused) {
			setPause({ ...pause, [`pause${index}`]: true })
		} else {
			setPause({ ...pause, [`pause${index}`]: true })
			setMute({ ...mute, [`mute${index}`]: true })
			isSetVideoWidgets({ ...isVideoWidgets, [`widget${index}`]: true })
		}
	}, [isFocused])

	useEffect(() => {
		if (activeIndex === index) {
			handlePausePlay()
		} else {
			setPause({ ...pause, [`pause${index}`]: true })
			setMute({ ...mute, [`mute${index}`]: true })
			isSetVideoWidgets({ ...isVideoWidgets, [`widget${index}`]: true })
		}
	}, [activeIndex])

	// console.log(isbuffer);
	return (
		<>
			{item?.images?.length > 0 && (
				<ImageView
					images={DP}
					imageIndex={0}
					presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
					visible={visible.dp}
					onRequestClose={() => setIsVisible({ ...visible, dp: false })}
				/>
			)}
			<TouchableOpacity
				key={item?._id}
				activeOpacity={0.8}
				onPress={() => navigation.navigate('PostDetails', { item: item, postId: item?._id })}
				style={[index === 0 ? 0 : styles().mt15, styles().pt15, styles().pb15, styles().ph15, styles().br15, styles().boxpeshadowCart]}
			>
				<View style={[styles().flexRow, styles().mb5, styles().justifyBetween, styles().alignCenter]}>
					{item?.author?.photo
						? <TouchableOpacity
								activeOpacity={0.9}
								onPress={() => {
									if (item?.author?._id === user?._id) {
										navigation.navigate('Profile')
									} else {
										navigation.navigate('ViewFriendProfile', {
											userId: item?.author?._id,
											isFriend: true,
										})
									}
								}}
								style={[styles().wh35px, styles().overflowH, styles().br50]}
							>
								<Image
									source={{ uri: item?.author?.photo }}
									style={styles().wh100}
									resizeMode="cover"
								/>
							</TouchableOpacity>
						: <TouchableOpacity
								activeOpacity={0.9}
								onPress={() => {
									if (item?.author?._id === user?._id) {
										navigation.navigate('Profile')
									} else {
										navigation.navigate('ViewFriendProfile', {
											userId: item?.author?._id,
											isFriend: true,
										})
									}
								}}
								style={[
									styles().overflowH,
									styles().justifyCenter,
									styles().alignCenter,
									styles().br50,
									styles().wh35px,
									{ borderWidth: 1, borderColor: currentTheme.themeBackground },
								]}
							>
								<FontAwesome5
									name="user-alt"
									size={15}
									color={currentTheme.themeBackground}
								/>
							</TouchableOpacity>}
					<View style={[styles().flex, styles().ml10]}>
						<View
							style={[
								styles().flexRow,
								styles().alignCenter,
								// {marginBottom: 3},
							]}
						>
							<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>{item?.author?.name}</Text>
							{item?.author?.profileVerified
								? <View style={[styles().wh15px, styles().ml5, styles().overflowH]}>
										<Image
											source={require('../../assets/images/verified-icon.png')}
											style={styles().wh100}
											resizeMode="contain"
										/>
									</View>
								: null}
						</View>

						<View style={[styles().flexRow, styles().alignCenter]}>
							<View style={[styles().wh10px, styles().mr5, styles().overflowH]}>
								<Image
									source={require('../../assets/images/globe.png')}
									style={styles().wh100}
									resizeMode="contain"
								/>
							</View>
							<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.BABDC9 }]}>{moment(item?.createdAt).fromNow()}</Text>
						</View>
					</View>

					<Tooltip
						isVisible={toolTipVisible}
						content={
							<View style={{ flex: 1 }}>
								{item?.author?._id === user?._id
									? <TouchableOpacity
											onPress={() => DeletePost(item?._id)}
											style={[styles().alignCenter, styles().mb5]}
										>
											<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c737373 }]}>{'Delete'}</Text>
										</TouchableOpacity>
									: <TouchableOpacity
											onPress={() => {
												setReport(item)
												setShowReportModal(true)
												settoolTipVisible(false)
											}}
											style={[
												styles().alignCenter,
												// styles().mb5,
											]}
										>
											<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c737373 }]}>{'Report Post'}</Text>
										</TouchableOpacity>}
							</View>
						}
						contentStyle={
							[
								// styles().pt0,
								// styles().w100px,
								// styles().h40px,
							]
						}
						placement="bottom"
						disableShadow={true}
						// showChsildInTooltip={false}
						topAdjustment={Platform.OS === 'android' ? -StatusBar.currentHeight : 0}
						onClose={() => settoolTipVisible(!toolTipVisible)}
					>
						{/* {item?.author?._id === user?._id ? ( */}
						<TouchableOpacity
							style={[styles().wh25px, styles().br50, styles().alignCenter, styles().justifyCenter, { backgroundColor: currentTheme.EFF2F7 }]}
							onPress={() => {
								// settoolTipVisible(props.index);
								settoolTipVisible(!toolTipVisible)
							}}
						>
							<FontAwesome
								name="ellipsis-h"
								size={16}
								color={currentTheme.D9D9D9}
							/>
						</TouchableOpacity>
						{/* ) : null} */}
					</Tooltip>
				</View>
				<View>
					<View>
						<RenderTextWithLinks
							text={item?.content}
							readmore={readmore}
						/>
						{readmore && item?.content?.length > 100
							? <TouchableOpacity onPress={() => setReadmore(undefined)}>
									<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.blackish }]}>read more</Text>
								</TouchableOpacity>
							: null}
					</View>
					{item?.images?.length > 0
						? <TouchableOpacity
								activeOpacity={0.7}
								onPress={() => setIsVisible({ ...visible, dp: true })}
								style={[
									styles().w100,
									styles().mt15,
									styles().overflowH,
									{
										borderRadius: 0,
										backgroundColor: currentTheme.F3F0E4,
										// aspectRatio: 1,
										height: height * 0.5,
									},
								]}
							>
								<Image
									source={{ uri: item?.images[0] }}
									style={{ width: '100%', height: '100%' }}
									resizeMode="cover"
								/>
								<FullScreenIcon />
							</TouchableOpacity>
						: null}
					{item?.videos?.length > 0
						? <TouchableOpacity
								activeOpacity={0.9}
								onPress={() => {
									LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
									isSetVideoWidgets({
										...isVideoWidgets,
										[`widget${index}`]: true,
									})
								}}
								style={[
									// StyleSheet.absoluteFill,
									{ aspectRatio: 1 },
									styles().w100,
									styles().mt15,
									styles().overflowH,
									styles().br15,
								]}
							>
								<Video
									useNativeControls
									// posterResizeMode="cover"
									autoplay={false}
									// resizeMode="contain"
									ref={videoRef}
									source={{ uri: item?.videos[0] }}
									isLooping
									style={{
										height: '100%',
										width: '100%',
									}}
									paused={pause[`pause${index}`]}
									muted={mute[`mute${index}`] === undefined ? true : mute[`mute${index}`]}
									// onLoadStart={() =>
									//   setIsbuffer({
									//     ...isbuffer,
									//     [`buffer${index}`]: true,
									//   })
									// }
									// onBuffer={() =>
									//   setIsbuffer({
									//     ...isbuffer,
									//     [`buffer${index}`]: true,
									//   })
									// }
									// onLoad={() =>
									//   setIsbuffer({
									//     ...isbuffer,
									//     [`buffer${index}`]: false,
									//   })
									// }
									onEnd={() => onVideoEnd()}
								/>

								{/* <BufferLoader index={index} /> */}
								{isVideoWidgets[`widget${index}`]
									? <>
											<ReplayButton index={index} />
											<MutedButton index={index} />
											<PausePlay index={index} />
										</>
									: null}
							</TouchableOpacity>
						: null}
				</View>
				<View style={[styles().flexRow, styles().mt10, styles().justifyBetween, styles().alignCenter]}>
					<View style={[styles().flexRow]}>
						<TouchableOpacity
							onPress={() => LikePost(item?._id)}
							style={[styles().wh28px, styles().pall5, styles().mr5, styles().overflowH]}
						>
							<Image
								source={require('../../assets/images/like-img.png')}
								style={styles().wh100}
								resizeMode="contain"
							/>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={() =>
								navigation.navigate('PostDetails', {
									item: item,
									postId: item?._id,
								})
							}
							style={[styles().wh28px, styles().pall5, styles().mr10, styles().overflowH]}
						>
							<Image
								source={require('../../assets/images/comment-img.png')}
								style={styles().wh100}
								resizeMode="contain"
							/>
						</TouchableOpacity>
						{/* <TouchableOpacity style={[styles().wh30px, styles().ph5, styles().mr10, styles().overflowH]}>
              <Image source={require('../../assets/images/share-img.png')} style={styles().wh100} resizeMode='contain' />
            </TouchableOpacity> */}
					</View>
					<View style={[styles().flexRow, styles().alignCenter]}>
						<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c747EA0 }]}>{item?.likes?.length} likes</Text>
						<View style={[styles().wh5px, styles().mh5, styles().br50, { backgroundColor: currentTheme.c747EA0 }]} />
						<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c747EA0 }]}>{item?.comments?.length} comments</Text>
						{/* <Text style={[styles().fs12, styles().fw400, { color: currentTheme.c747EA0 }]}>3 Shares</Text> */}
					</View>
				</View>
				{lastComment
					? <View style={[styles().flexRow, styles().btw1, styles().mt15, styles().pt15, { borderTopColor: currentTheme.c707070 }]}>
							{lastComment?.user?.photo
								? <View style={[styles().wh30px, styles().br50, styles().overflowH]}>
										<Image
											source={{ uri: lastComment?.user?.photo }}
											style={styles().wh100}
											resizeMode="cover"
										/>
									</View>
								: <View
										style={[
											styles().overflowH,
											styles().justifyCenter,
											styles().alignCenter,
											styles().br50,
											styles().wh30px,
											{ borderWidth: 1, borderColor: currentTheme.themeBackground },
										]}
									>
										<FontAwesome5
											name="user-alt"
											size={12}
											color={currentTheme.themeBackground}
										/>
									</View>}
							<View style={[styles().flex, styles().ml5]}>
								<Text
									style={[
										styles().fs12,
										// styles().mb5,
										styles().fontMedium,
										styles().textCapitalize,
										{ color: currentTheme.black },
									]}
								>
									{lastComment?.user?.name}
								</Text>
								<View style={[styles().flexRow, styles().mb5, styles().flexWrap]}>
									<Text
										numberOfLines={2}
										style={[styles().fs11, styles().fontRegular, { color: currentTheme.c7A8FA6 }]}
									>
										{lastComment?.text}
									</Text>
								</View>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() =>
										navigation.navigate('PostDetails', {
											item: item,
											isReply: true,
											lastComment: lastComment,
											postId: item?._id,
										})
									}
									style={[styles().flex]}
								>
									<Text style={[styles().fs11, styles().fw400, { fontStyle: 'italic', color: currentTheme.themeBackground }]}>Reply</Text>
								</TouchableOpacity>
							</View>
						</View>
					: null}
			</TouchableOpacity>
		</>
	)
}

export default React.memo(PostComponent)

const postStyles = StyleSheet.create({
	mute: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 25,
		width: 25,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
	reply: {
		position: 'absolute',
		bottom: 0,
		right: 30,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: 100,
		height: 25,
		width: 25,
		backgroundColor: 'white',
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
	pausePlay: {
		position: 'absolute',
		bottom: 0,
		right: 60,
		margin: 10,
		zIndex: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 25,
		width: 25,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
	buffer: {
		zIndex: 10,

		position: 'absolute',
		top: '45%',
		left: '45%',
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
	fullscreen: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 28,
		width: 28,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
})
