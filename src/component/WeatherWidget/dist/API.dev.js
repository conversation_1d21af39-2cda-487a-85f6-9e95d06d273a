Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.get_city_from_lat_long = exports.getCityID = exports.getCurrentWeather = void 0

var _environment = _interopRequireDefault(require('../../../environment'))

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var _getEnvVars = (0, _environment.default)()
var ACCU_WEATHER_KEY = _getEnvVars.ACCU_WEATHER_KEY
var GOOGLE_API_KEY = _getEnvVars.GOOGLE_API_KEY

var _CITY_ID = ''

var getCurrentWeather = function getCurrentWeather(citykey, details) {
	var url
	var response
	return regeneratorRuntime.async(
		function getCurrentWeather$(_context) {
			while (1) {
				switch ((_context.prev = _context.next)) {
					case 0:
						url = 'https://dataservice.accuweather.com/currentconditions/v1/'.concat(citykey, '?apikey=').concat(ACCU_WEATHER_KEY, '&details=').concat(details)
						_context.prev = 1
						_context.next = 4
						return regeneratorRuntime.awrap(
							fetch(url, {
								method: 'get',
								headers: {
									'Content-Type': 'application/json',
									Accept: 'application/json', // Authorization: `Bearer ${access_token}`,
								},
							})
						)

					case 4:
						response = _context.sent

						if (response.ok) {
							_context.next = 9
							break
						}

						_context.next = 8
						return regeneratorRuntime.awrap(response.json())

					case 8:
						throw _context.sent

					case 9:
						_context.next = 11
						return regeneratorRuntime.awrap(response.json())

					case 11:
						response = _context.sent
						return _context.abrupt('return', response[0])

					case 15:
						_context.prev = 15
						_context.t0 = _context.catch(1)
						console.log('getCurrentWeather Error :', _context.t0.message)

					case 18:
						_context.prev = 18
						return _context.finish(18)

					case 20:
					case 'end':
						return _context.stop()
				}
			}
		},
		null,
		null,
		[[1, 15, 18, 20]]
	)
}

exports.getCurrentWeather = getCurrentWeather

var getCityID = function getCityID(city) {
	var url
	var response
	var result
	return regeneratorRuntime.async(
		function getCityID$(_context2) {
			while (1) {
				switch ((_context2.prev = _context2.next)) {
					case 0:
						url = 'https://dataservice.accuweather.com/locations/v1/cities/search?apikey='.concat(ACCU_WEATHER_KEY, '&q=').concat(city)
						_context2.prev = 1
						_context2.next = 4
						return regeneratorRuntime.awrap(
							fetch(url, {
								method: 'get',
								headers: {
									'Content-Type': 'application/json',
									Accept: 'application/json', // Authorization: `Bearer ${access_token}`,
								},
							})
						)

					case 4:
						response = _context2.sent

						if (response.ok) {
							_context2.next = 9
							break
						}

						_context2.next = 8
						return regeneratorRuntime.awrap(response.json())

					case 8:
						throw _context2.sent

					case 9:
						_context2.next = 11
						return regeneratorRuntime.awrap(response.json())

					case 11:
						response = _context2.sent
						console.log('getCityID res :', response)
						result = response[0].Key // console.log('KEY:', result);

						return _context2.abrupt('return', result)

					case 17:
						_context2.prev = 17
						_context2.t0 = _context2.catch(1)
						console.log('getCityID Error :', _context2.t0.message)

					case 20:
						_context2.prev = 20
						return _context2.finish(20)

					case 22:
					case 'end':
						return _context2.stop()
				}
			}
		},
		null,
		null,
		[[1, 17, 20, 22]]
	)
}

exports.getCityID = getCityID

var get_city_from_lat_long = function get_city_from_lat_long(lat, lng) {
	var url
	var response
	return regeneratorRuntime.async(
		function get_city_from_lat_long$(_context3) {
			while (1) {
				switch ((_context3.prev = _context3.next)) {
					case 0:
						url = 'https://maps.googleapis.com/maps/api/geocode/json?latlng='.concat(lat, ',').concat(lng, '&key=').concat(GOOGLE_API_KEY)
						_context3.prev = 1
						_context3.next = 4
						return regeneratorRuntime.awrap(
							fetch(url, {
								method: 'get',
								headers: {
									'Content-Type': 'application/json',
									Accept: 'application/json', // Authorization: `Bearer ${access_token}`,
								},
							})
						)

					case 4:
						response = _context3.sent

						if (response.ok) {
							_context3.next = 9
							break
						}

						_context3.next = 8
						return regeneratorRuntime.awrap(response.json())

					case 8:
						throw _context3.sent

					case 9:
						_context3.next = 11
						return regeneratorRuntime.awrap(response.json())

					case 11:
						response = _context3.sent
						// const { address_components } = await response.results[0];
						// let result = await address_components?.find(
						//   item => item.types[0] === 'locality',
						// );
						console.log('get_city_from_lat_long res :', response)
						return _context3.abrupt('return', response)

					case 16:
						_context3.prev = 16
						_context3.t0 = _context3.catch(1)
						console.log('get_city_from_lat_long Error :', _context3.t0.message)

					case 19:
						_context3.prev = 19
						return _context3.finish(19)

					case 21:
					case 'end':
						return _context3.stop()
				}
			}
		},
		null,
		null,
		[[1, 16, 19, 21]]
	)
}

exports.get_city_from_lat_long = get_city_from_lat_long
