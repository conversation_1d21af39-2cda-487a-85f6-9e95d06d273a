import { Text, View, Image, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import Feather from '@expo/vector-icons/Feather'
import styles from '../../screens/styles'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { getCityID, getCurrentWeather, get_city_from_lat_long } from './API'
import Lottie from 'lottie-react-native'
import * as Location from 'expo-location'
import { iconImages } from './WeatherIcons'

const WeatherWidget = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [weather, setWeather] = useState('')
	const [weatherErr, setWeatherErr] = useState(false)
	const [location, setLocation] = useState('')
	const _karachiCoord = {
		lat: 24.8607,
		long: 67.0011,
	}

	const getWeather = async () => {
		console.log('Calling Weather')
		try {
			const { status } = await Location.requestForegroundPermissionsAsync()

			if (status !== 'granted') {
				console.log('Location permission not granted, using fallback location')
				const fallbackCity = await get_city_from_lat_long(_karachiCoord.lat, _karachiCoord.long)
				if (fallbackCity) {
					setLocation(fallbackCity.short_name)
					const cityId = await getCityID(fallbackCity.short_name)
					if (cityId) {
						const weatherData = await getCurrentWeather(cityId, true)
						if (weatherData) {
							setWeather(weatherData)
							return
						}
					}
				}
				setWeatherErr(true)
				return
			}

			const location = await Location.getCurrentPositionAsync({
				accuracy: Location.Accuracy.Balanced,
				timeout: 20000,
				maximumAge: 10000,
			})

			const { latitude, longitude } = location.coords
			console.log('Current position:', { latitude, longitude })

			const cityResult = await get_city_from_lat_long(latitude, longitude)
			if (!cityResult) {
				setWeatherErr(true)
				return
			}

			setLocation(cityResult.short_name)

			const cityId = await getCityID(cityResult.short_name)
			if (!cityId) {
				setWeatherErr(true)
				return
			}

			const weatherData = await getCurrentWeather(cityId, true)
			if (!weatherData) {
				setWeatherErr(true)
				return
			}

			setWeather(weatherData)
		} catch (error) {
			console.log('Weather widget location error:', error)
			setWeatherErr(true)
		}
	}

	const DisplayImageBasedOnWeather = ({ responseNumber }) => {
		const imageSource = iconImages[responseNumber]
		return (
			<View style={[styles().wh40px, styles().overflowH, { marginBottom: 10 }]}>
				<Image
					source={imageSource}
					style={styles().wh100}
					resizeMode="contain"
				/>
			</View>
		)
	}

	useEffect(() => {
		getWeather()
	}, [location])

	if (weather === '' && weatherErr === false) {
		return (
			<TouchableOpacity
				onPress={() => props.navigation.navigate('Weather')}
				activeOpacity={0.9}
				style={[
					styles().w120px,
					styles().h50px,
					styles().br50,
					styles().overflowH,
					styles().alignCenter,
					styles().justifyCenter,
					{ backgroundColor: currentTheme.navyBlue },
				]}
			>
				<Lottie
					source={require('./../../assets/images/weather/weatherloader.json')}
					autoPlay={true}
					loop={true}
					style={{ height: 40, width: 40 }}
				/>
			</TouchableOpacity>
		)
	}

	if (weatherErr) {
		return (
			<TouchableOpacity
				onPress={() => props.navigation.navigate('Weather')}
				activeOpacity={0.9}
				style={[
					styles().w120px,
					styles().h50px,
					styles().ph5,
					styles().br50,
					styles().overflowH,
					styles().alignCenter,
					styles().justifyCenter,
					{ backgroundColor: currentTheme.navyBlue },
				]}
			>
				<View style={[styles().wh35px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/weather/err.png')}
						style={[styles().wh100, { tintColor: 'white' }]}
						resizeMode="contain"
					/>
				</View>
			</TouchableOpacity>
		)
	}
	return (
		<TouchableOpacity
			onPress={() => props.navigation.navigate('Weather')}
			activeOpacity={0.9}
			style={[
				// styles().w150px,
				styles().ph5,
				styles().pb5,
				styles().h50px,
				styles().br50,
				styles().overflowH,
				{ backgroundColor: currentTheme.navyBlue },
			]}
		>
			<View style={[styles().alignCenter, styles().flexRow, styles().justifyCenter, { paddingTop: 7 }]}>
				<Feather
					name="map-pin"
					size={10}
					color={currentTheme.c6a6974}
				/>
				<Text style={[styles().fs8, styles().ml5, styles().fw500, { color: currentTheme.c6a6974 }]}>{location}</Text>
			</View>
			<View
				style={[
					styles().flexRow,
					styles().justifyCenter,
					styles().alignCenter,
					styles().flex,
					// {backgroundColor: 'yellow'},
				]}
			>
				<DisplayImageBasedOnWeather responseNumber={weather?.WeatherIcon} />
				<Text style={[styles().fs12, styles().fw400, { color: currentTheme.white }]}>
					{/* {`${weather?.Temperature?.Metric?.Value}°${weather?.Temperature?.Metric?.Unit}`} */}
					{`${weather?.Temperature?.Imperial?.Value}°${weather?.Temperature?.Imperial?.Unit}`}
				</Text>
				<View style={[styles().ml5, styles().mr10]}>
					<Text style={[styles().fs8, styles().fw400, { color: currentTheme.white }]}>{weather?.WeatherText}</Text>
					<Text style={[styles().fs8, styles().fw400, { color: currentTheme.white }]}>
						{/* {`Feels like ${weather?.RealFeelTemperature?.Metric?.Value}°${weather?.RealFeelTemperature?.Metric?.Unit}`} */}
						{`Feels like ${weather?.RealFeelTemperature?.Imperial?.Value}°${weather?.RealFeelTemperature?.Imperial?.Unit}`}
					</Text>
				</View>
			</View>
		</TouchableOpacity>
	)
}

export default React.memo(WeatherWidget)
