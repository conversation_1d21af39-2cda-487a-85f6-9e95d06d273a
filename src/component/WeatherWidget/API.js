import getEnvVars from '../../../environment'

const { ACCU_WEATHER_KEY, GOOGLE_API_KEY } = getEnvVars()
const _CITY_ID = ''

export const getCurrentWeather = async (citykey, details) => {
	const url = `https://dataservice.accuweather.com/currentconditions/v1/${citykey}?apikey=${ACCU_WEATHER_KEY}&details=${details}`
	try {
		let response = await fetch(url, {
			method: 'get',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
				// Authorization: `Bearer ${access_token}`,
			},
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		// console.log('getCurrentWeather res :', JSON.stringify(response[0]));
		return response[0]
	} catch (error) {
		console.log('getCurrentWeather Error :', error.message)
	} finally {
	}
}

export const getCityID = async city => {
	const url = `https://dataservice.accuweather.com/locations/v1/cities/search?apikey=${ACCU_WEATHER_KEY}&q=${city}`
	try {
		let response = await fetch(url, {
			method: 'get',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
				// Authorization: `Bearer ${access_token}`,
			},
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		// console.log('getCityID res :', response);
		const result = response[0]?.Key
		// console.log('KEY:', result);
		return result
	} catch (error) {
		console.log('getCityID Error :', error?.message)
	} finally {
	}
}

export const get_city_from_lat_long = async (lat, lng) => {
	const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_API_KEY}`
	try {
		let response = await fetch(url, {
			method: 'get',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
				// Authorization: `Bearer ${access_token}`,
			},
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		// console.log('get_city_from_lat_long res :', response);
		const { address_components } = await response.results[0]
		const result = await address_components?.find(item => item.types[0] === 'locality')
		return result
	} catch (error) {
		console.log('get_city_from_lat_long Error :', error.message)
	} finally {
	}
}
