import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, ScrollView, Modal, Platform, StyleSheet } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { reportPost } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import FlashMessage from '../FlashMessage/FlashMessage'
import TextField from '../../component/FloatTextField/FloatTextField'
import Spinner from '../Spinner/Spinner'
import Entypo from '@expo/vector-icons/Entypo'
import AsyncStorage from '@react-native-async-storage/async-storage'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

const ReportModal = ({ modalVisible, onClose, report, updateReport, setReport }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [selectedType, setSelectedType] = useState('')
	const [content, setContent] = useState('')
	const [Loader, setLoader] = useState(false)
	const [error, setError] = useState('')
	//   console.log('>>˘', report);

	const reportTypes = [
		'Inappropriate Content',
		'Harassment or Bullying',
		'False Information or Spam',
		'Hate Speech or Symbol',
		'Self-Harm or Suicide',
		'Violence or Threats',
		'Unauthorized Sales or Promotions',
		'Intellectual Property Infringement',
	]

	const [mutate, _loading] = useMutation(reportPost, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('reportPost res :', res)
			Close()
			updateReport()
			FlashMessage({
				msg: 'Content Reported!',
				type: 'success',
			})
		},
		onError: err => {
			console.log('reportPost Err :', err)
			setLoader(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const handleReport = async () => {
		const userId = await AsyncStorage.getItem('userId')
		console.log('Selected Type:', selectedType)
		console.log('Content:', content)
		if (!content && !selectedType) {
			setError('Enter Reason For Report!')
			return
		}

		const data = {
			reportPostId: report?._id,
			reportInput: {
				reason: content ? content : selectedType,
				user: userId,
			},
		}
		console.log(data)
		setLoader(true)
		setError('')
		await mutate({
			variables: data,
		})
	}

	const Close = () => {
		onClose()
		setLoader(false)
		setContent('')
		setSelectedType('')
	}
	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().alignCenter, styles().justifyCenter, styles().flex, styles().zIndex100]}>
				<TouchableOpacity
					activeOpacity={1}
					style={[
						styles().posAbs,
						styles().wh100,
						{
							backgroundColor: 'rgba(0,0,0,0.5)',
						},
					]}
				/>

				<View
					style={[
						styles().ph20,
						styles().pt20,
						styles().pb20,
						styles().br20,
						{
							width: width * 0.9,
							height: height * 0.75,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<ScrollView contentContainerStyle={{ flexGrow: 1 }}>
						<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().mb10]}>
							<View />
							<Text style={[styles().fs20, styles().fontBold, { color: currentTheme.headingColor }]}>Report</Text>
							<TouchableOpacity
								activeOpacity={0.5}
								onPress={() => Close()}
								style={[styles().flexRow, styles().alignCenter, styles().br100, { backgroundColor: currentTheme.black }]}
							>
								<Entypo
									name={'cross'}
									size={24}
									color={currentTheme.white}
								/>
							</TouchableOpacity>
						</View>

						{reportTypes.map((types, i) => {
							return (
								<TouchableOpacity
									activeOpacity={0.4}
									onPress={() => setSelectedType(prev => (prev === types ? '' : types))}
									key={i}
								>
									<Text
										style={[
											styles().fs14,
											styles().mt15,
											types === selectedType ? styles().fontMedium : styles().fontRegular,
											{
												color: types === selectedType ? currentTheme.themeBackground : currentTheme.black,
											},
										]}
									>
										{`${i + 1}) ${types}`}
									</Text>
								</TouchableOpacity>
							)
						})}
						<View style={[styles().mt20]}>
							<TextField
								keyboardType="default"
								value={content}
								autoCapitalize="none"
								placeholder={'Other...'}
								label={'Other'}
								multiline={true}
								style={[
									styles().bw1,
									styles().br10,
									styles().overflowH,
									{
										borderColor: currentTheme.B7B7B7,
									},
								]}
								stylesInput={[styles().h150px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
								onChangeText={text => {
									setContent(text)
									setSelectedType('')
								}}
							/>
						</View>
						{error ? <Text style={[rbpStyle.error, styles().fontMedium]}>{error}</Text> : null}
						<View style={[styles().flex, styles().justifyEnd]}>
							{Loader
								? <Spinner />
								: <ThemeButton
										Title={'Report'}
										onPress={() => handleReport()}
									/>}
						</View>
					</ScrollView>
				</View>
			</View>
		</Modal>
	)
}

export default ReportModal

const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 15,
		fontSize: 14,
		color: '#B00020',
		alignSelf: 'center',
	},
})
