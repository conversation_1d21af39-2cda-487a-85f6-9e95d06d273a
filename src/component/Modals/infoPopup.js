import { useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Ionicons from '@expo/vector-icons/Ionicons'
import Spinner from '../Spinner/Spinner'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function ModalComponent({
	ModalHeading,
	ModalText,
	ActionBtn1,
	ActionBtn1Loading,
	ActionBtn1Text,
	ModalTextUpper,
	CancelBtn,
	ModalIcon,
	modalVisible,
	onCancel,
}) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<TouchableOpacity
				activeOpacity={1}
				onPress={() => {
					// onCancel();
				}}
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.themeBackground },
					]}
				>
					<View style={[styles().alignCenter]}>
						{ModalIcon
							? <Ionicons
									name="checkmark-circle"
									size={50}
									color={currentTheme.headingColor}
								/>
							: null}
						<Text
							style={[
								styles().fs24,
								styles().mt10,
								styles().textCenter,
								ModalTextUpper ? styles().textUpper : null,
								styles().mb15,
								styles().lh30,
								styles().fontBold,
								{ color: currentTheme.white },
							]}
						>
							{ModalHeading}
						</Text>
						{ModalText && (
							<Text style={[styles().fs14, styles().textCenter, styles().lh20, styles().mb25, styles().fontRegular, { color: currentTheme.white }]}>
								{ModalText}
							</Text>
						)}
					</View>
					<View>
						{!ActionBtn1Loading
							? <ThemeButton
									Title={ActionBtn1Text}
									Style={{
										backgroundColor: currentTheme.headingColor,
										borderColor: currentTheme.headingColor,
									}}
									onPress={ActionBtn1}
								/>
							: <Spinner />}
					</View>
					{CancelBtn
						? <View style={[styles().mt20]}>
								<ThemeButton
									Title={'Cancel'}
									Style={{
										backgroundColor: currentTheme.D9D9D9,
										borderColor: currentTheme.D9D9D9,
									}}
									StyleText={{ color: currentTheme.black }}
									onPress={() => {
										onCancel()
									}}
								/>
							</View>
						: null}
				</View>
			</TouchableOpacity>
		</Modal>
	)
}
