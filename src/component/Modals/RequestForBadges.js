import { useState, useContext } from 'react'
import { View, Text, Image, TouchableOpacity, Dimensions, Modal, FlatList, ActivityIndicator } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Feather from '@expo/vector-icons/Feather'
import Entypo from '@expo/vector-icons/Entypo'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import { useMutation, useQuery } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import FlashMessage from '../FlashMessage/FlashMessage'
import { applyBadge, companies, getMyBadgeRequests, getAvailableCompaniesForBadges } from '../../apollo/server'
import fontStyles from '../../utils/fonts/fontStyles'
import { renderText, useThrottledPress } from '../../utils/Constants'
import navigationService from '../../routes/navigationService'

const { width, height } = Dimensions.get('window')

const RequestForBadges = ({ modalVisible, onCancel }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [page, setPage] = useState(1)
	const [totalPages, setTotalPages] = useState('')
	const [selected, setselected] = useState('')
	const [compamies, setCompamies] = useState([])
	const pageSize = 20

	const { data: getMyBadgeRequestsData, refetch: getMyBadgeRequestsRefetch } = useQuery(getMyBadgeRequests, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		onCompleted: _res => {},
		onError: err => {
			console.log('getCheckRideOffers err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const { data, loading, error, refetch } = useQuery(getAvailableCompaniesForBadges, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		onCompleted: data => {
			console.log('getAvailableCompaniesForBadges res :', JSON.stringify(data?.getAvailableCompaniesForBadges))
			setCompamies(data?.getAvailableCompaniesForBadges || [])
		},
		onError: err => {
			console.log('getAvailableCompaniesForBadges err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refreshData = async () => {
		await refetch()
	}

	const [mutate, { loading: applyBadgeLoader }] = useMutation(applyBadge, {
		onCompleted: async res => {
			console.log('applyBadge res :', res)
			getMyBadgeRequestsRefetch()
			refreshData()
			FlashMessage({
				msg: res?.applyBadge?.message || 'Request for badge has been sent!',
				type: 'success',
			})
			onCancel()
		},
		onError: err => {
			console.log('applyBadge Err :', err)
			let errorMessage = 'Failed to submit badge request. Please try again.'
			
			if (err.message.includes('already exists') || err.message.includes('already requested')) {
				errorMessage = 'You have already requested a badge from this company.'
			} else if (err.message.includes('already have a badge')) {
				errorMessage = 'You already have a badge from this company.'
			} else if (err.message.includes('not available')) {
				errorMessage = 'This badge is not available for request.'
			}
			
			FlashMessage({ msg: errorMessage, type: 'danger' })
		},
	})

	async function RequestForBadge(companyId) {
		setselected(companyId)
		try {
			await mutate({
				variables: {
					companyId: companyId,
				},
			})
		} catch (error) {
			console.log('RequestForBadge error:', error)
		}
	}

	const handleNavigation = item => {
		onCancel()
		navigationService.navigate('CompanyDetails', { item: item })
	}
	const throttledButtonPress = useThrottledPress(handleNavigation)

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph10,
						styles().justifyCenter,
						styles().pt20,
						styles().br15,
						{
							width: width * 0.9,
							maxHeight: height * 0.8,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => onCancel()}
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().posAbs,
							styles().br100,
							styles().mall5,
							{ top: 5, right: 5, backgroundColor: currentTheme.black },
						]}
					>
						<Entypo
							name={'cross'}
							size={20}
							color={currentTheme.white}
						/>
					</TouchableOpacity>
					<View
						style={[
							styles().wh55px,
							styles().alignCenter,
							styles().justifyCenter,
							styles().br100,
							styles().alignSelfCenter,
							styles().pall10,
							{ backgroundColor: currentTheme.themeBackground },
						]}
					>
						<Image
							source={require('../../assets/images/badge.png')}
							style={[styles().wh100]}
							resizeMode="contain"
						/>
					</View>
					<Text style={[styles().fontBold, styles().fs16, styles().alignSelfCenter, styles().mt10, styles().mb15, { color: currentTheme.black }]}>
						REQUEST A BADGE
					</Text>
					<FlatList
						showsVerticalScrollIndicator={false}
						data={compamies}
						renderItem={({ item, index }) => {
							return (
								<View
									key={item?._id}
									style={[
										styles().flexRow,
										styles().pb15,
										styles().pt25,
										styles().ph15,
										styles().mb15,
										styles().mh5,
										index === 0 ? styles().mt20 : null,
										styles().br10,
										styles().br10,
										styles().boxpeshadowCart,
										{ backgroundColor: currentTheme.postBackground },
									]}
								>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => throttledButtonPress(item)}
										style={[
											styles().wh50px,
											styles().br25,
											styles().overflowH,
											styles().justifyCenter,
											styles().alignCenter,
											styles().mr10,
											{
												borderWidth: item?.photo ? 0 : 1,
												borderColor: currentTheme.themeBackground,
											},
										]}
									>
										{item?.photo
											? <Image
													source={{ uri: item?.photo }}
													resizeMode="cover"
													style={styles().wh100}
												/>
											: <MaterialCommunityIcons
													name="city-variant"
													size={25}
													color={currentTheme.themeBackground}
												/>}
									</TouchableOpacity>
									<View style={[styles().flex]}>
										<Text style={[styles().fs12, styles().fontBold, styles().lh20, { color: currentTheme.headingColor }]}>{item?.name}</Text>
										<View
											style={[
												styles().flexRow,
												styles().alignCenter,
												styles().mt5,
												styles().mb5,
												styles().flex,
												styles().flexWrap,
												// {backgroundColor: 'teal'},
											]}
										>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mb5]}>
												<Feather
													name="map-pin"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
													{item?.city ? item?.city : 'N/A'}
												</Text>
											</View>
										</View>
										<TouchableOpacity
											activeOpacity={0.5}
											onPress={() => RequestForBadge(item?._id)}
											style={[
												styles().pv10,
												styles().ph20,
												styles().br100,
												styles().w90,
												styles().alignCenter,
												styles().justifyCenter,
												{
													backgroundColor: currentTheme.black,
												},
											]}
										>
											{selected === item?._id && applyBadgeLoader
												? <ActivityIndicator
														color={currentTheme.white}
														size={'small'}
													/>
												: <View style={[styles().flexRow, styles().alignCenter]}>
														<View style={[styles().wh18px]}>
															<Image
																source={require('../../assets/images/badge2.png')}
																style={[styles().wh100, { tintColor: currentTheme.white }]}
																resizeMode="contain"
															/>
														</View>
														<Text style={[styles().fs12, styles().ml5, styles().fontMedium, { color: currentTheme.white }]}>
															Request A Badge
														</Text>
													</View>}
										</TouchableOpacity>
										{item?.about
											? <View style={[styles().mt10]}>
													<Text
														numberOfLines={2}
														style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
													>
														{renderText(item?.about)}
													</Text>
												</View>
											: null}
									</View>
								</View>
							)
						}}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().mb30]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontFamily: fontStyles.PoppinsRegular,
											fontSize: 14,
										}}
									>
										{loading ? 'Loading...' : 'No companies'}
									</Text>
								</View>
							)
						}}
						ListFooterComponent={<View style={styles().wh20px} />}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default RequestForBadges
