import { useState, useContext } from 'react'
import { View, Text, Dimensions, ScrollView, Modal } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { applyCheckRide } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import FlashMessage from '../FlashMessage/FlashMessage'
import { removetags } from '../../utils/Constants'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function CheckRide({ modalVisible, onCancel, checkRide, companyId }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)
	const [mutate, { client }] = useMutation(applyCheckRide, {
		onCompleted: async data => {
			console.log('applyCheckRide res', data)
			onCancel()
			FlashMessage({ msg: 'Check Ride Accepted.', type: 'success' })
			setLoading(false)
		},
		errorPolicy: 'all',
		onError: async err => {
			console.log('applyCheckRide err  :', err)
			onCancel()
			FlashMessage({ msg: err?.message?.toString(), type: 'danger' })
			setLoading(false)
		},
	})

	async function AcceptCheckRide() {
		setLoading(true)
		const data = {
			companyId: companyId,
		}
		console.log(data)
		await mutate({
			variables: {
				companyId: companyId,
			},
		})
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{
							width: width * 0.9,
							height: height * 0.85,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<View>
						<Text
							style={[
								styles().fs18,
								styles().mt10,
								styles().textCenter,
								styles().mb15,

								styles().lh30,
								styles().fontBold,
								{ color: currentTheme.black },
							]}
						>
							{'Check Ride'}
						</Text>
						<Text style={[styles().fs16, styles().mb10, styles().fw700, { color: currentTheme.black }]}>{`Price: $${checkRide?.price}`}</Text>
						<Text style={[styles().fs14, styles().mb10, styles().fw700, { color: currentTheme.black }]}>
							{`Days: ${checkRide?.hitchMin} - ${checkRide?.hitchMax}`}
							{/* {`Date: ${moment(checkRide?.hitchMin).format('L')} - ${moment(
                checkRide?.hitchMax,
              ).format('L')}`} */}
						</Text>
						<Text style={[styles().fs16, styles().mb10, styles().fw700, { color: currentTheme.themeBackground }]}>Terms and Conditions</Text>
					</View>
					<ScrollView contentContainerStyle={{ flexGrow: 1 }}>
						<Text style={[styles().fs12, styles().lh20, styles().mb25, styles().fontRegular, { color: currentTheme.black }]}>
							{removetags(checkRide?.terms)}
						</Text>
						{/* <RenderHtml
              contentWidth={300}
              source={{html: checkRide?.terms}}
              baseStyle={{
                paddingHorizontal: 0,
                paddingVertical: 0,
              }}
              tagsStyles={{
                p: {
                  marginVertical: 0,
                },
              }}
            /> */}
					</ScrollView>

					<View style={[styles().mt10]}>
						{Loading
							? <View style={[styles().mb5]}>
									<Spinner />
								</View>
							: <ThemeButton
									Title={'Apply'}
									StyleText={{ color: currentTheme.black }}
									onPress={() => AcceptCheckRide()}
								/>}
					</View>
					<ThemeButton
						Title={'Close'}
						Style={{
							backgroundColor: currentTheme.D9D9D9,
							borderColor: currentTheme.D9D9D9,
						}}
						StyleText={{ color: currentTheme.black }}
						onPress={() => {
							onCancel()
							setLoading(false)
						}}
					/>
				</View>
			</View>
		</Modal>
	)
}
