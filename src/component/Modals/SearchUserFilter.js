import { useState, useContext, useLayoutEffect } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal, ScrollView } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { roles } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import Dropdown from '../../component/DropDown/Dropdopwn'
import { removeRoles } from '../../utils/Constants'

const { height, width } = Dimensions.get('window')

export default function SearchUserFilter({ ModalHeading, onClose, modalVisible, filters, clear }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [role, setRole] = useState([])
	const [filter, setFilter] = useState({
		// location: null,
	})

	const { data, loading, error, refetch } = useQuery(roles, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		onCompleted: data => {
			console.log('roles res :', data.roles)
			const filterRoles = data?.roles?.filter(item => !removeRoles?.includes(item))
			setRole(filterRoles)
		},
		onError: err => {
			console.log('roles err :', err)
		},
	})

	const clearFilter = async () => {
		setFilter({
			// ...filter,
			// location: null,
		})
	}

	useLayoutEffect(() => {
		if (clear()) {
			clearFilter()
		}
	}, [clear])

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						onClose()
					}}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{
							width: width * 0.85,
							maxHeight: height * 0.6,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<ScrollView>
						<View style={[styles().alignCenter]}>
							<Text style={[styles().fs18, styles().mt10, styles().textCenter, styles().mb15, styles().lh30, styles().fw700, { color: currentTheme.black }]}>
								{ModalHeading}
							</Text>
						</View>
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.role != null ? (filter.role === 'matePilot' ? 'Mate Pilot' : filter.role) : 'Type/Role'}
								data={role}
								selectedValue={value => setFilter({ ...filter, role: value })}
							/>
						</View>
					</ScrollView>
					<View>
						<ThemeButton
							Title={'Apply'}
							StyleText={{ color: currentTheme.black }}
							onPress={() => {
								console.log(filter.role)
								if (filter?.role) {
									filters(filter)
									onClose()
									setFilter({
										...filter,
									})
								}
							}}
						/>

						<ThemeButton
							Title={'Clear'}
							StyleText={{ color: currentTheme.white }}
							Style={{
								backgroundColor: currentTheme.headingColor,
								borderColor: currentTheme.headingColor,
							}}
							onPress={() => clearFilter()}
						/>
					</View>
				</View>
			</View>
		</Modal>
	)
}
