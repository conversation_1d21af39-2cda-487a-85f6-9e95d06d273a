import { useState, useContext, useEffect } from 'react'
import { View, Text, Image, TouchableOpacity, Dimensions, ScrollView, Modal, Platform, ToastAndroid } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import TextField from '../FloatTextField/FloatTextField'
import { getChatRooms, postMessage } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../FlashMessage/FlashMessage'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Ionicons from '@expo/vector-icons/Ionicons'
import Share from '../../context/Share/Share'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { onShare, urls } from '../../utils/Constants'
import Clipboard from '@react-native-clipboard/clipboard'
const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function SharePopup({ modalVisible }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { isShare, shareData, shareNow, share } = useContext(Share)
	const [search, setSearch] = useState('')
	const [user, setUser] = useState('')
	const [recents, setRecents] = useState([])
	const [searchData, setSearchData] = useState([])
	const [sents, setSents] = useState([])
	const [page, setPage] = useState(1)
	const pageSize = 20
	const completeShareUrl = `${urls.domain}${shareData?.shareType}/${shareData?._id}`
	const { data, loading, error, refetch } = useQuery(getChatRooms, {
		fetchPolicy: 'cache-and-network',
		errorPolicy: 'all',
		variables: {
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'lastMessageAt:desc',
			},
		},
		onCompleted: async res => {
			setRecents(prev => [...prev, ...res?.getChatRooms?.results])
			setSearchData(prev => [...prev, ...res?.getChatRooms?.results])
		},
		onError: err => {
			console.log('getChatRooms err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	async function getuser() {
		const userId = await AsyncStorage.getItem('userId')
		setUser(userId)
	}

	function close() {
		shareNow('')
		setSents([])
		isShare(false)
		setSearchData([])
	}

	const searchFilterFunction = text => {
		if (text) {
			const newData = recents?.filter(item => {
				const filterUser = item?.users?.find(users => {
					return users?._id !== user
				})
				if (item.chatName) {
					const itemData = item?.chatName ? item?.chatName?.toUpperCase() : ''.toUpperCase()
					const textData = text.toUpperCase()
					return itemData.indexOf(textData) > -1
				}
				const itemData = filterUser?.name ? filterUser?.name?.toUpperCase() : ''.toUpperCase()
				const textData = text.toUpperCase()
				return itemData.indexOf(textData) > -1
			})
			// console.log('search result :', newData);
			setSearchData(newData)
			setSearch(text)
		} else {
			setSearchData(recents)
			setSearch(text)
		}
	}

	const copyToClipBoard = async () => {
		const message = 'Copied to clipboard!'
		await Clipboard.setString(completeShareUrl)
		if (Platform.OS !== 'ios') {
			ToastAndroid.showWithGravity(message, ToastAndroid.SHORT, ToastAndroid.CENTER)
		} else {
			FlashMessage({
				msg: message,
				type: 'info',
				position: 'bottom',
				backgroundColor: currentTheme.c737373,
			})
		}
	}

	const shareItem = async () => {
		onShare({
			shareType: shareData?.shareType,
			message: `${shareData?.shareType}/${shareData?._id}`,
		})
	}

	const refresh = async () => {
		setPage(1)
		await refetch({
			page: 1,
			limit: pageSize,
			sortBy: 'lastMessageAt:desc',
		}).then(res => {
			// console.log('refetch recents');
			setRecents(_prev => res?.data?.getChatRooms?.results)
			setSearchData(_prev => res?.data?.getChatRooms?.results)
		})
	}

	const nextPage = async () => {
		if (page < data?.getChatRooms?.totalPages) {
			setPage(old => old + 1)
			await refetch()
		}
	}

	const [mutate, { client }] = useMutation(postMessage, {
		onCompleted: response => {
			console.log('postMessage response :', response)
		},
		onError: error => {
			console.log('postMessage Error :', error)
		},
	})

	async function send(item) {
		const cardData = {
			cardID: shareData?._id,
			title: shareData?.title,
			photo: shareData?.photo,
			description: shareData?.description,
			shareType: shareData?.shareType,
		}
		const shareMsg = shareData?.shareType === 'job' ? 'Shared a job.' : 'Shared a company.'
		const data = {
			authId: user,
			files: [],
			message: shareMsg,
			messageType: 'card',
			roomId: item?._id,
			cardData,
		}
		const _messageSent = await mutate({
			variables: {
				inputMessage: data,
			},
		})
		const arr = []
		const sentcounts = item
		arr.push(sentcounts)
		setSents(prev => [...prev, ...arr])
	}

	useEffect(() => {
		getuser()
		if (share) {
			refresh()
		}
	}, [shareData])

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
				<TouchableOpacity
					onPress={() => close()}
					activeOpacity={1}
					style={[
						styles().posAbs,
						styles().wh100,
						{
							backgroundColor: 'rgba(0,0,0,0.5)',
						},
					]}
				/>

				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt10,
						styles().pb20,
						styles().alignSelfCenter,
						styles().br20,
						// styles().zIndex10,
						{
							width: width * 0.9,
							height: height * 0.75,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<View style={[styles().bbw005, styles().mb10, styles().pv10, { borderBottomColor: currentTheme.B7B7B7 }]}>
						<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
							<Text style={[styles().fs16, styles().fontMedium, { color: currentTheme.headingColor }]}>Refer a Friend</Text>
							{/* <Text
                numberOfLines={1}
                style={[
                  styles().fs11,
                  styles().fontRegular,
                  styles().flex,
                  styles().mr10,
                  {color: currentTheme.headingColor},
                ]}>
                {completeShareUrl}
              </Text> */}
							<View style={[styles().flexRow, styles().alignCenter]}>
								<TouchableOpacity
									onPress={copyToClipBoard}
									activeOpacity={0.5}
									style={[styles().mh10, styles().br100, { backgroundColor: currentTheme.black, padding: 8 }]}
								>
									<Ionicons
										size={18}
										name={'copy'}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
								<TouchableOpacity
									onPress={shareItem}
									activeOpacity={0.5}
									style={[styles().br100, { backgroundColor: currentTheme.black, padding: 7 }]}
								>
									<Ionicons
										size={20}
										name={'share-social'}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
							</View>
						</View>
					</View>
					<Text
						style={[
							styles().fs16,
							// styles().textCenter,
							styles().lh30,
							styles().fontMedium,
							{ color: currentTheme.headingColor },
						]}
					>
						{'Send To'}
					</Text>

					<View style={[styles().mt5]}>
						<TextField
							keyboardType="default"
							value={search}
							// errorText={searchError}
							autoCapitalize="none"
							placeholder={'Search'}
							style={[
								styles().bw1,
								styles().br10,
								// styles().flex,
								styles().overflowH,
								{ borderColor: currentTheme.B7B7B7 },
							]}
							onChangeText={text => searchFilterFunction(text)}
						/>
					</View>

					<View style={[styles().flex]}>
						<ScrollView
							scrollEventThrottle={16}
							showsVerticalScrollIndicator={false}
							onScroll={({ nativeEvent }) => {
								const { layoutMeasurement, contentOffset, contentSize } = nativeEvent
								const isEndReached = layoutMeasurement.height + contentOffset.y >= contentSize.height
								if (isEndReached) {
									nextPage()
								}
							}}
						>
							{searchData?.length === 0
								? <View style={[styles().alignCenter, styles().justifyEnd, { height: 150 }]}>
										<Text
											style={{
												color: currentTheme.E8E8C8,
												fontSize: 14,
											}}
										>
											{loading ? 'Loading...' : 'No recent chats...'}
										</Text>
									</View>
								: searchData?.map((item, i) => {
										const filterUser = item?.users?.find(users => {
											return users?._id !== user
										})
										const isSent = sents?.some(some => some?._id === item?._id)
										return (
											<View
												key={i}
												activeOpacity={0.7}
												style={[
													styles().flexRow,
													styles().mb10,
													i === 0 && styles().mt20,
													styles().alignCenter,
													styles().justifyBetween,
													styles().bbw1,
													styles().pb10,
													{
														borderBottomColor: currentTheme.c707070,
														//   backgroundColor: 'teal',
													},
												]}
											>
												<View
													style={[
														styles().flexRow,
														styles().alignCenter,
														styles().justifyCenter,
														styles().wh40px,
														styles().br100,
														styles().overflowH,
														{
															borderWidth: item?.isAnonymousChat ? 1 : filterUser?.photo ? 0 : 1,
															borderColor: currentTheme.themeBackground,
														},
													]}
												>
													{item?.chatName
														? <FontAwesome5
																name="users"
																size={20}
																color={currentTheme.themeBackground}
															/>
														: item?.isAnonymousChat
															? <FontAwesome5
																	name="user-secret"
																	size={20}
																	color={currentTheme.themeBackground}
																/>
															: filterUser?.photo
																? <Image
																		source={{ uri: filterUser?.photo }}
																		style={styles().wh100}
																		resizeMode="cover"
																	/>
																: <FontAwesome5
																		name="user-alt"
																		size={16}
																		color={currentTheme.themeBackground}
																	/>}
												</View>
												<View
													style={[
														styles().flex,
														styles().ml15,
														//   {backgroundColor: 'pink'},
													]}
												>
													{item?.isAnonymousChat
														? <Text style={[styles().fs12, styles().mb5, styles().fontMedium, { color: currentTheme.black }]}>{'Anonymous User'}</Text>
														: <Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>
																{item?.chatName ? item?.chatName : filterUser?.name}
															</Text>}
													<View>
														<Text style={[styles().fs10, styles().fw400, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>{filterUser?.role}</Text>
													</View>
												</View>
												<ThemeButton
													disabled={!!isSent}
													onPress={() => send(item)}
													Title={isSent ? 'Sent' : 'Send'}
													Style={[
														styles().h30px,
														styles().ph20,
														{
															backgroundColor: isSent ? currentTheme.B7B7B7 : currentTheme.themeBackground,
															borderWidth: 0,
															borderRadius: 7,
														},
													]}
													StyleText={[
														styles().fs12,
														{
															marginTop: 3,
															color: isSent ? currentTheme.black : currentTheme.white,
														},
														styles().fw400,
													]}
												/>
											</View>
										)
									})}
						</ScrollView>
					</View>
				</View>
			</View>
		</Modal>
	)
}
