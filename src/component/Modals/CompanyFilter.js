import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal, Switch, ScrollView } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { getCities, roles } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import Dropdown from '../../component/DropDown/Dropdopwn'
import { removeRoles, switchSize } from '../../utils/Constants'

const { height, width } = Dimensions.get('window')

export default function JobFilter({ ModalHeading, onClose, modalVisible, filters }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [_role, setRole] = useState([])
	const [cities, setCities] = useState([])
	const [filter, setFilter] = useState({
		// location: null,
	})

	const { data, loading, error, refetch } = useQuery(roles, {
		fetchPolicy: 'no-cache',
		onCompleted: data => {
			console.log('roles res :', data.roles)
			const filterRoles = data?.roles?.filter(item => !removeRoles?.includes(item))
			setRole(filterRoles)
		},
		onError: err => {
			console.log('roles err :', err)
		},
	})
	const {} = useQuery(getCities, {
		fetchPolicy: 'no-cache',
		onCompleted: data => {
			// console.log('getCities res :', data.getCities);
			setCities(data.getCities)
		},
		onError: err => {
			console.log('getCities err :', err)
		},
	})

	const clearFilter = async () => {
		setFilter({
			// ...filter,
			// location: null,
		})
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						onClose()
					}}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{
							width: width * 0.85,
							maxHeight: height * 0.6,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<ScrollView>
						<View style={[styles().alignCenter]}>
							<Text style={[styles().fs18, styles().mt10, styles().textCenter, styles().mb15, styles().lh30, styles().fw700, { color: currentTheme.black }]}>
								{ModalHeading}
							</Text>
						</View>
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.city ? filter.city : 'Locations'}
								data={cities}
								selectedValue={value => setFilter({ ...filter, city: value })}
							/>
						</View>
						<View style={[styles().w100, styles().mb15, styles().flexRow, styles().justifyBetween, styles().alignCenter]}>
							<Text style={[styles().fs14, { color: currentTheme.blackish }]}>Check Ride</Text>
							<Switch
								style={{ transform: switchSize }}
								trackColor={{
									false: currentTheme.E2E2E2,
									true: currentTheme.E2E2E2,
								}}
								thumbColor={filter.isCheckRideEnabled ? currentTheme.lightGreen : currentTheme.themeBackground}
								ios_backgroundColor={currentTheme.F4F5F6}
								onValueChange={() =>
									setFilter({
										...filter,
										isCheckRideEnabled: !filter.isCheckRideEnabled,
									})
								}
								value={filter.isCheckRideEnabled}
							/>
						</View>
					</ScrollView>
					<View>
						<ThemeButton
							Title={'Apply'}
							StyleText={{ color: currentTheme.black }}
							onPress={() => {
								filters(filter)
								onClose()
								setFilter({
									...filter,
									isCheckRideEnabled: false,
								})
							}}
						/>

						<ThemeButton
							Title={'Clear'}
							StyleText={{ color: currentTheme.white }}
							Style={{
								backgroundColor: currentTheme.headingColor,
								borderColor: currentTheme.headingColor,
							}}
							onPress={() => clearFilter()}
						/>
					</View>
				</View>
			</View>
		</Modal>
	)
}
