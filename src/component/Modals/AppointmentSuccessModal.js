import { Dimensions, Image, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import navigationService from '../../routes/navigationService'
import { useQuery } from '@apollo/client'
import FlashMessage from '../FlashMessage/FlashMessage'
import { getLicenseBookingById } from '../../apollo/server'
import { handleSocial } from '../../utils/Constants'
import Spinner from '../Spinner/Spinner'

const AppointmentSuccessModal = ({ visible, onClose, booking_data }) => {
	const { width } = Dimensions.get('window')
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [seconds, setSeconds] = useState(10)
	const [paymentLink, setPaymentLink] = useState(null)
	const [fetchPaymentLink, setFetchPaymentLink] = useState(false)
	const [Loader, setLoader] = useState(false)

	const handleGoHome = () => {
		onClose()
		navigationService.ResetNavigationHome()
	}

	const { data, loading, error, refetch } = useQuery(getLicenseBookingById, {
		errorPolicy: 'all',
		variables: {
			licenseBookingId: booking_data?._id,
		},
		skip: !fetchPaymentLink, // Skip the query initially
		onCompleted: data => {
			console.log('getLicenseBookingById res :', JSON.stringify(data?.getLicenseBookingById))
			setPaymentLink(data?.getLicenseBookingById?.payment_link)
			setFetchPaymentLink(false)
			setLoader(false)
		},
		onError: err => {
			console.log('getLicenseBookingById err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoader(false)
			setFetchPaymentLink(false)
		},
	})

	async function getPaymentLink() {
		setLoader(true)
		await refetch()
	}

	useEffect(() => {
		let timerId
		if (visible && seconds > 0) {
			timerId = setInterval(() => {
				setSeconds(prevSeconds => {
					if (prevSeconds <= 1) {
						setFetchPaymentLink(true)
						clearInterval(timerId)
						return 0
					}
					return prevSeconds - 1
				})
			}, 1000)
		}
		if (fetchPaymentLink) {
			// Refetch the query when the timer reaches zero
			getPaymentLink()
		}
		return () => clearInterval(timerId)
	}, [visible, seconds])

	console.log(fetchPaymentLink, seconds, paymentLink, booking_data)

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					// onPress={()=>onClose()}
					activeOpacity={1}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph30,
						styles().justifyCenter,
						styles().alignCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{ width: width * 0.9, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().wh45px, styles().mt15]}>
						<Image
							source={require('../../assets/images/appointment.png')}
							style={[styles().wh100]}
							resizeMode="contain"
						/>
					</View>
					<Text style={[styles().fontMedium, styles().fs16, styles().mb10, styles().mt20, { color: currentTheme.black }]}>Your Appointment is Reserved!</Text>
					<Text style={[styles().fontRegular, styles().fs14, styles().mb20, styles().textCenter, { color: currentTheme.black }]}>
						Your Appointment has been tentatively held for
						<Text style={{ color: currentTheme.red }}> 2 Hours. </Text>Please make the payment to secure your booking
					</Text>
					{Loader
						? <Spinner />
						: <ThemeButton
								onPress={() => {
									if (!paymentLink) {
										setFetchPaymentLink(true)
										getPaymentLink()
										console.log('first')
									}
									if (paymentLink) {
										handleSocial(paymentLink)
										console.log('second')
									}
								}}
								disabled={seconds !== 0}
								Title={
									seconds !== 0 && !paymentLink
										? `Generating Payment Link in ${seconds}`
										: paymentLink && seconds === 0
											? `Pay Now $${Number.parseFloat(booking_data?.booking?.appointmentPrice).toFixed(2)}`
											: 'Re-Generate Payment Link'
								}
								StyleText={[styles().fs14]}
								Style={[
									styles().w100,
									styles().br5,
									{
										borderColor: seconds === 0 ? currentTheme.themeBackground : currentTheme.B7B7B7,
										backgroundColor: seconds === 0 ? currentTheme.themeBackground : currentTheme.B7B7B7,
									},
								]}
							/>}
					<ThemeButton
						Title={'Back To Home'}
						Style={[
							styles().w100,
							styles().br5,
							{
								backgroundColor: currentTheme.B7B7B7,
								borderColor: currentTheme.B7B7B7,
							},
						]}
						onPress={() => handleGoHome()}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default AppointmentSuccessModal
