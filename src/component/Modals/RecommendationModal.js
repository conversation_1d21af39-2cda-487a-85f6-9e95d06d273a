import { useState, useContext } from 'react'
import { View, Text, Modal, TouchableOpacity, Dimensions, Platform } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Ionicons from '@expo/vector-icons/Ionicons'
import { giveRecommendation } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import TextField from '../FloatTextField/FloatTextField'
import FlashMessage from '../FlashMessage/FlashMessage'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function RecommendationModal({ RecommendUser, modalVisible, onCancel }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [recommend, setRecommend] = useState('')
	const [recommendError, setRecommendError] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [mutate, {}] = useMutation(giveRecommendation, {
		errorPolicy: 'all',
		onCompleted: async _res => {
			onCancel()
			setRecommend('')
			setLoading(false)
			FlashMessage({
				msg: `Your recommendation to ${RecommendUser?.name} has been submitted.`,
				type: 'success',
			})
		},
		onError: err => {
			console.log('giveRecommendation Err :', err)
			setLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function SendReccomendation() {
		if (recommend === '') {
			setRecommendError(true)
			return
		}
		setLoading(true)
		await mutate({
			variables: {
				userId: RecommendUser?._id,
				text: recommend,
			},
		})
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View
				onPress={() => {
					// onCancel();
				}}
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<TouchableOpacity
						onPress={() => onCancel()}
						style={[styles().posAbs, styles().pall5, styles().zIndex10, styles().top10, styles().right10]}
					>
						<Ionicons
							name="close-circle-outline"
							size={26}
							color={currentTheme.themeBackground}
							style={{ marginTop: -3 }}
						/>
					</TouchableOpacity>
					<View style={[styles().alignCenter]}>
						<Text style={[styles().fs16, styles().mt10, styles().textCenter, styles().lh30, styles().fw600, { color: currentTheme.headingColor }]}>
							{'Write Recommendation To '}
						</Text>
						<Text
							style={[
								styles().fs16,
								styles().textCenter,
								styles().mb15,
								styles().lh30,
								styles().fontBold,
								styles().textCapitalize,
								{ color: currentTheme.themeBackground },
							]}
						>
							{RecommendUser?.name}
						</Text>
					</View>

					<View style={styles().mb15}>
						<TextField
							keyboardType="default"
							value={recommend}
							errorText={recommendError}
							multiline={true}
							autoCapitalize="none"
							placeholder={'Write your recommendation'}
							onChangeText={text => {
								setRecommendError(false)
								setRecommend(text)
							}}
							style={[styles().bw1, styles().br10, styles().mb15, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							stylesInput={[styles().h150px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
						/>
					</View>

					<View>
						{!Loading
							? <ThemeButton
									Title={'Send'}
									StyleText={{ color: currentTheme.headingColor }}
									onPress={() => SendReccomendation()}
								/>
							: <Spinner />}
					</View>
				</View>
			</View>
		</Modal>
	)
}
