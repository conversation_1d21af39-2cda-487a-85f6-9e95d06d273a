import { useState, useContext, useMemo, useLayoutEffect } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { roles, getJobCities } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import Dropdown from '../../component/DropDown/Dropdopwn'
import { ScrollView } from 'react-native-gesture-handler'
import { removeRoles } from '../../utils/Constants'

const { height, width } = Dimensions.get('window')

export default function JobFilter({ ModalHeading, onClose, modalVisible, filters, clear }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [role, setRole] = useState([])
	const [cities, setCities] = useState([])
	const [filter, setFilter] = useState({})
	const [search, setSearch] = useState('')
	const jobTypes = ['hireOn', 'trip']
	const _qualification = ['Matriculation', 'High School', "Bachelor's", "Master's"]
	const _ratetype = ['Hourly', 'Daily']
	const experiances = ['1+ Year', '2+ Year', '3+ Year', '4+ Year', '5+ Year', '6+ Year', 'More than 10 Years']

	const { data, loading, error, refetch } = useQuery(roles, {
		fetchPolicy: 'no-cache',
		onCompleted: data => {
			console.log('roles res :', data.roles)
			const filterRoles = data?.roles?.filter(item => !removeRoles?.includes(item))
			setRole(filterRoles)
		},
		onError: err => {
			console.log('roles err :', err)
		},
	})

	const { refetch: getJobCitiesRefetch } = useQuery(getJobCities, {
		fetchPolicy: 'no-cache',
		variables: {
			text: search,
		},
		onCompleted: data => {
			// console.log('getJobCities res :', data.getJobCities);
			setCities(data.getJobCities)
		},
		onError: err => {
			console.log('getJobCities err :', err)
		},
	})

	const clearFilter = async () => {
		setFilter({})
		setSearch('')
	}

	useLayoutEffect(() => {
		if (clear()) {
			clearFilter()
		}
	}, [clear])

	useMemo(() => {
		if (filter?.city?.length > 2) {
			const timer = setTimeout(async () => {
				const result = await getJobCitiesRefetch({ text: filter?.city }).then(res => {
					setCities(res?.data?.getJobCities)
				})
				return result
			}, 1500)
			return () => clearTimeout(timer)
		}
	}, [filter?.city])

	// console.log('in filter :', filter);
	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						onClose()
					}}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{
							width: width * 0.85,
							height: height * 0.7,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<ScrollView
						showsVerticalScrollIndicator={false}
						nestedScrollEnabled={true}
						contentContainerStyle={{ flexGrow: 1 }}
					>
						<View style={[styles().alignCenter]}>
							<Text style={[styles().fs18, styles().mt10, styles().textCenter, styles().mb15, styles().lh30, styles().fw700, { color: currentTheme.black }]}>
								{ModalHeading}
							</Text>
						</View>
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.type != null ? filter.type : 'Type/Role'}
								data={role}
								selectedValue={value => setFilter({ ...filter, type: value?.toLowerCase() })}
							/>
						</View>
						{/* <View style={[styles().w100, styles().mb15]}>
            <Dropdown
              placeholder={filter.ratesType ? filter.ratesType : 'Select Type'}
              data={ratetype}
              selectedValue={value => setFilter({...filter, ratesType: value})}
            />
          </View> */}
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.jobType ? filter.jobType : 'Job Type'}
								data={jobTypes}
								selectedValue={value => setFilter({ ...filter, jobType: value })}
							/>
						</View>
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.experience ? filter.experience : 'Experience'}
								data={experiances}
								selectedValue={value => setFilter({ ...filter, experience: value })}
							/>
						</View>
						{/* <View style={[styles().w100, styles().mb15]}>
              <Dropdown
                placeholder={
                  filter.qualification ? filter.qualification : 'Qualification'
                }
                data={qualification}
                selectedValue={value =>
                  setFilter({...filter, qualification: value})
                }
              />
            </View> */}
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								onChangeText={text => setFilter({ ...filter, city: text?.toLowerCase() })}
								value={filter.city}
								search={true}
								placeholder={filter.city ? filter.city : 'Locations'}
								data={cities}
								selectedValue={value => setFilter({ ...filter, city: value?.toLowerCase() })}
							/>
						</View>
						{/* <View style={[styles().w100, styles().mb15]}>
            <TextField
              keyboardType="default"
              value={location}
              autoCapitalize="none"
              placeholder={'Location'}
              style={[
                styles().bw1,
                styles().br10,
                styles().overflowH,
                {borderColor: currentTheme.C3C3C3},
              ]}
              onChangeText={text => {
                setLocation(text);
              }}
              placeholderTextColor={currentTheme.C3C3C3}
            />
          </View> */}
					</ScrollView>
					<View>
						<ThemeButton
							Title={'Apply'}
							StyleText={{ color: currentTheme.black }}
							onPress={() => {
								filters(filter)
								onClose()
							}}
						/>

						<ThemeButton
							Title={'Clear'}
							StyleText={{ color: currentTheme.white }}
							Style={{
								backgroundColor: currentTheme.headingColor,
								borderColor: currentTheme.headingColor,
							}}
							onPress={() => clearFilter()}
						/>
					</View>
				</View>
			</View>
		</Modal>
	)
}
