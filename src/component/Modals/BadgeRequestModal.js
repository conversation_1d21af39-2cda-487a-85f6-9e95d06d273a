import { useContext, useState } from 'react'
import { View, Text, Image, TouchableOpacity, Dimensions, Modal, FlatList } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import JobsComponent from '../JobsComponent/JobsComponent'
import FlashMessage from '../FlashMessage/FlashMessage'
import { applyBadge } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import MyCheckRideComponent from '../MyCheckRideComponent/MyCheckRideComponent'
import fontStyles from '../../utils/fonts/fontStyles'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function BadgeRequestModal({ modalVisible, onCancel, navigation, jobs, company_id, checkRides, callback }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)

	const [mutate, {}] = useMutation(applyBadge, {
		onCompleted: async res => {
			console.log('applyBadge res :', res)
			onCancel()
			callback(true)
			setLoading(false)
			FlashMessage({ 
				msg: res?.applyBadge?.message || 'Request for badge has been sent!', 
				type: 'success' 
			})
		},
		onError: err => {
			console.log('applyBadge Err :', err)
			setLoading(false)
			let errorMessage = 'Failed to submit badge request. Please try again.'
			
			if (err.message.includes('already exists') || err.message.includes('already requested')) {
				errorMessage = 'You have already requested a badge from this company.'
			} else if (err.message.includes('already have a badge')) {
				errorMessage = 'You already have a badge from this company.'
			} else if (err.message.includes('not available')) {
				errorMessage = 'This badge is not available for request.'
			}
			
			FlashMessage({ msg: errorMessage, type: 'danger' })
		},
	})

	async function RequestForBadge() {
		setLoading(true)
		try {
			await mutate({
				variables: {
					companyId: company_id,
				},
			})
		} catch (error) {
			console.log('RequestForBadge error:', error)
			setLoading(false)
		}
	}

	const EmpyComponent = ({ text }) => {
		return (
			<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
				<Text
					style={{
						color: currentTheme.E8E8C8,
						fontFamily: fontStyles.PoppinsRegular,
						fontSize: 14,
					}}
				>
					{text}
				</Text>
			</View>
		)
	}

	return (
		<Modal
			animationType="slide"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyEnd]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={onCancel}
					style={[
						// styles().flex,
						styles().posAbs,
						styles().wh100,
						{
							// backgroundColor: 'rgba(0,0,0,0.2)',
							// backgroundColor: 'rgba(',
						},
					]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().boxpeshadow,
						{
							borderTopRightRadius: 40,
							borderTopLeftRadius: 40,
							width: width,
							// flex:1,
							height: height * 0.7,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<TouchableOpacity
						activeOpacity={1}
						onPress={onCancel}
						style={[
							styles().wh65px,
							styles().alignSelfCenter,
							styles().posAbs,
							styles().bw1,
							styles().pall15,
							{
								top: -40,
								backgroundColor: currentTheme.lightGold,
								borderColor: currentTheme.white,
							},
							styles().br50,
							styles().overflowH,
						]}
					>
						<Image
							source={require('../../assets/images/badge.png')}
							style={styles().wh100}
							resizeMode="contain"
						/>
					</TouchableOpacity>

					<Text style={[styles().fs18, styles().mt10, styles().fontBold, { color: currentTheme.headingColor }]}>{'Job History'}</Text>

					<View style={[styles().flex]}>
						<FlatList
							data={jobs}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ flexGrow: 1 }}
							renderItem={({ item, index }) => {
								return (
									<JobsComponent
										item={item}
										index={index}
										navigation={navigation}
										// alreadyApplied={true}
									/>
								)
							}}
							ListEmptyComponent={() => {
								return <EmpyComponent text={'No Jobs'} />
							}}
							ListFooterComponent={() => {
								if (checkRides?.length > 0) {
									return (
										<>
											<Text
												style={[
													styles().fs14,
													styles().fw600,
													{
														color: currentTheme.black,
														textDecorationLine: 'underline',
													},
												]}
											>
												Check Rides:
											</Text>
											<FlatList
												data={checkRides}
												renderItem={({ item: checkItem, index: i }) => {
													return (
														<MyCheckRideComponent
															item={checkItem}
															index={i}
															// navigation={navigation}
															checkrideID={checkItem?.checkRideId}
															company={checkItem?.company}
														/>
													)
												}}
												ListEmptyComponent={() => {
													return <EmpyComponent text={'No Jobs'} />
												}}
											/>
										</>
									)
								}
							}}
							keyExtractor={(_item, index) => index.toString()}
							// ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>
					<View>
						{Loading
							? <Spinner />
							: <ThemeButton
									Title={'Request For A Badge'}
									Style={{
										backgroundColor: currentTheme.headingColor,
										borderColor: currentTheme.headingColor,
									}}
									onPress={() => RequestForBadge()}
								/>}
					</View>
				</View>
			</View>
		</Modal>
	)
}
