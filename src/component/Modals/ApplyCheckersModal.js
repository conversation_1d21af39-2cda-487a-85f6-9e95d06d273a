import { Dimensions, Image, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import styles from '../../screens/styles'
import FlashMessage from '../FlashMessage/FlashMessage'
import Spinner from '../Spinner/Spinner'
import { openLink } from '../../utils/InAppBrowser'
import { checkers_onBoarding } from '../../checkers/checkers'
import { updateUserCheckrInfo } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Eligible from '../../context/EligibleContext/EligibleContext'
import AsyncStorage from '@react-native-async-storage/async-storage'
import navigationService from '../../routes/navigationService'

const ApplyCheckersModal = ({ modalVisible }) => {
	const { width } = Dimensions.get('window')
	const themeContext = useContext(ThemeContext)
	const [user, setUser] = useState('')
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)
	const { setCheckrEligible } = useContext(Eligible)
	const [mutate, { client }] = useMutation(updateUserCheckrInfo, {
		errorPolicy: 'all',
		onCompleted: data => {
			setLoading(false)
			console.log('updateUserCheckrInfo res :', data)
		},
		onError: err => {
			console.log('updateUserCheckrInfo Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	const onClose = () => {
		setCheckrEligible(false)
		setLoading(false)
	}

	async function _ApplyCheckers() {
		const userAsync = await AsyncStorage.getItem('user')
		const user = JSON.parse(userAsync)
		console.log('check', user)
		if (user?.checkrInviteUrl?.toString()) {
			console.log('already checkr url')
			openLink(user?.checkrInviteUrl.toString())
		} else {
			console.log('data in checkr_onboarding :', user?.role?.toString(), user?.email?.toString())
			setLoading(true)
			await checkers_onBoarding(user?.role?.toString(), user?.email?.toString()).then(async res => {
				setLoading(false)
				if (res.invitation_url) {
					openLink(res.invitation_url)
					await mutate({
						variables: {
							updateCheckrInput: {
								checkrInviteUrl: res.invitation_url,
								checkr_candidate_id: res.candidate_id,
							},
						},
					})
				} else {
					FlashMessage({ msg: res.message, type: 'warning' })
				}
			})
		}
	}

	const isRBPVerification =
		user?.checkr_decision === 'PENDING' ? false : user?.checkr_decision === 'COMPLETED' ? true : user?.checkr_decision === 'NEED_REVIEW' ? false : false

	const isRBPVerificationText =
		user?.checkr_decision === 'PENDING'
			? 'Verification Under Process'
			: user?.checkr_decision === 'COMPLETED'
				? 'RBP Verified'
				: user?.checkr_decision === 'NEED_REVIEW'
					? 'Review Your Application'
					: 'RBP Verification'

	async function RBPVerification() {
		onClose()
		if (!isRBPVerification) {
			navigationService.navigate('RBPVerification')
		}
	}

	async function getUserFromAsync() {
		const userAsync = await AsyncStorage.getItem('user')
		const parseUser = JSON.parse(userAsync)
		setUser(parseUser)
	}

	useEffect(() => {
		getUserFromAsync()
	}, [setCheckrEligible])

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					//   onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br15,
						{
							width: width * 0.85,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<View style={[styles().alignCenter]}>
						<View style={[styles().wh65px, styles().mb15]}>
							<Image
								source={require('../../assets/images/home-logo.png')}
								resizeMode="contain"
								style={[styles().wh100]}
							/>
						</View>
						<Text style={[styles().fs12, styles().fontRegular, styles().mb20, styles().textCenter, styles().w90, { color: currentTheme.blackish }]}>
							We need to verify your account before applying for jobs and app core features.
						</Text>
					</View>

					{Loading
						? <Spinner size={25} />
						: // <ThemeButton
							//   Title={'Apply'}
							//   onPress={() => ApplyCheckers()}
							//   Style={[styles().br5, styles().h40px]}
							//   StyleText={[{fontSize: 14}]}
							// />
							<TouchableOpacity
								// onPress={() => ApplyCheckers()}
								onPress={() => RBPVerification()}
								activeOpacity={0.7}
								style={[
									styles().flexRow,
									styles().alignCenter,
									styles().justifyCenter,
									styles().ph10,
									styles().boxpeshadowCart,
									styles().br5,
									styles().h40px,
									{
										backgroundColor:
											user?.checkr_decision === 'PENDING'
												? currentTheme.starColor
												: user?.checkr_decision === 'COMPLETED'
													? currentTheme.lightGreen
													: user?.checkr_decision === 'NEED_REVIEW'
														? currentTheme.themeBackground
														: currentTheme.F5F9FC,
									},
								]}
							>
								<View style={[styles().wh20px, styles().mb5]}>
									<Image
										style={[
											styles().wh100,
											{
												// borderRadius: 3,
												tintColor:
													user?.checkr_decision === 'PENDING'
														? currentTheme.white
														: user?.checkr_decision === 'COMPLETED'
															? currentTheme.white
															: user?.checkr_decision === 'NEED_REVIEW'
																? currentTheme.white
																: currentTheme.themeBackground,
											},
										]}
										resizeMode="contain"
										// source={require('../../assets/images/checkr.png')}
										source={require('../../assets/images/verifyuser.png')}
									/>
								</View>
								<Text
									style={[
										styles().fs14,
										styles().ml10,
										styles().fw400,
										{
											color:
												user?.checkr_decision === 'PENDING'
													? currentTheme.white
													: user?.checkr_decision === 'COMPLETED'
														? currentTheme.white
														: user?.checkr_decision === 'NEED_REVIEW'
															? currentTheme.white
															: currentTheme.c444D6E,
										},
									]}
								>
									{/* Checkr Verification */}
									{isRBPVerificationText}
								</Text>
							</TouchableOpacity>}
					<TouchableOpacity
						onPress={() => onClose()}
						activeOpacity={0.7}
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().justifyCenter,
							styles().ph10,
							styles().boxpeshadowCart,
							styles().br5,
							styles().h40px,
							styles().mt10,
							{ backgroundColor: currentTheme.F5F9FC },
						]}
					>
						<Text style={[styles().fs14, styles().ml10, styles().fw400, { color: currentTheme.c444D6E }]}>Close</Text>
					</TouchableOpacity>
				</View>
			</View>
		</Modal>
	)
}

export default ApplyCheckersModal
