import { Dimensions, Image, Modal, Platform, Text, TouchableOpacity, UIManager, View } from 'react-native'
import { useContext } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import styles from '../../screens/styles'
import FlashMessage from '../FlashMessage/FlashMessage'
import { openLink } from '../../utils/InAppBrowser'
import { createStripeAccount } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Eligible from '../../context/EligibleContext/EligibleContext'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const StripeEligibleModal = ({ modalVisible }) => {
	const { width } = Dimensions.get('window')
	const { setStripeEligible } = useContext(Eligible)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [mutate, { client }] = useMutation(createStripeAccount, {
		errorPolicy: 'all',
		onCompleted: ({ createStripeAccount }) => {
			console.log('createStripeAccount res :', createStripeAccount)
			openLink(createStripeAccount?.url)
		},
		onError: err => {
			console.log('createStripeAccount Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const onClose = () => {
		setStripeEligible(false)
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br15,
						{ width: width * 0.85, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<View style={[styles().wh65px, styles().mb15]}>
							<Image
								source={require('../../assets/images/home-logo.png')}
								resizeMode="contain"
								style={[styles().wh100]}
							/>
						</View>
						<Text style={[styles().fs12, styles().fontRegular, styles().mb20, styles().textCenter, { color: currentTheme.blackish }]}>
							You need to connect your Stripe account first, then you will be able to apply for the jobs and get paid.
						</Text>
					</View>
					<View
						style={
							[
								// styles().flex,
								// styles().alignCenter,
							]
						}
					>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={async () => await mutate()}
							style={[
								// styles().w48,
								styles().justifyCenter,
								styles().flexRow,
								styles().alignCenter,
								styles().boxpeshadowCart,
								styles().ph10,
								styles().br5,
								styles().h40px,
								{ backgroundColor: currentTheme.F5F9FC },
							]}
						>
							<View style={[styles().wh20px]}>
								<Image
									style={[styles().wh100, { borderRadius: 3 }]}
									resizeMode="contain"
									source={require('../../assets/images/stripe.png')}
								/>
							</View>
							<Text style={[styles().fs14, styles().ml10, styles().fw400, { color: currentTheme.c444D6E }]}>Connect Stripe</Text>
						</TouchableOpacity>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() => onClose()}
							style={[
								// styles().w48,
								styles().justifyCenter,
								styles().flexRow,
								styles().alignCenter,
								styles().boxpeshadowCart,
								styles().ph10,
								styles().br5,
								styles().mt10,
								styles().h40px,
								{ backgroundColor: currentTheme.F5F9FC },
							]}
						>
							<Text style={[styles().fs14, styles().ml10, styles().fw400, { color: currentTheme.c444D6E }]}>Close</Text>
						</TouchableOpacity>
					</View>
				</View>
			</View>
		</Modal>
	)
}

export default StripeEligibleModal
