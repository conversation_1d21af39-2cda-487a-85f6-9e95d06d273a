import { Dimensions, LayoutAnimation, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import Ionicons from '@expo/vector-icons/Ionicons'
import FlashMessage from '../FlashMessage/FlashMessage'
import { DeleteAccount } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import UserContext from '../../context/User/User'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { CommonActions } from '@react-navigation/native'
import { AuthContext } from '../../context/Auth/auth'

const AccountDelete = ({ visible, onClose, props }) => {
	const { width } = Dimensions.get('window')
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)
	const { logout } = useContext(AuthContext)

	async function Logout() {
		try {
			await AsyncStorage.removeItem('token')
			await AsyncStorage.removeItem('user')
			await AsyncStorage.removeItem('userId')
			await AsyncStorage.removeItem('isProfileCompleted')
			logout()
			console.log('async clear - logout!'),
				props.navigation.dispatch(
					CommonActions.reset({
						index: 0,
						routes: [{ name: 'Auth' }],
					})
				)
		} catch (error) {
			console.log('logout Err :', error)
		}
	}

	async function Delete() {
		setLoading(true)
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		mutate({
			variables: {
				email: user?.email,
			},
		})
	}

	const [mutate, { client }] = useMutation(DeleteAccount, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('DeleteAccount res', data)
			onClose()
			FlashMessage({ msg: data?.deleteAccount?.message, type: 'success' })
			setLoading(false)
			await Logout()
		} catch (e) {
			console.log(e)
			setLoading(false)
			onClose()
		} finally {
			setLoading(false)
			onClose()
		}
	}

	function onError(error) {
		onClose()
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('DeleteAccount error  :', error)
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					//   onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<Ionicons
							name="warning"
							size={50}
							color={currentTheme.red}
						/>
						<Text style={[styles().fs14, styles().fw600, styles().mb20, styles().textCenter, { color: currentTheme.black }]}>
							Are your sure you want to Delete your Account? Your data will be removed permanently.
						</Text>
					</View>

					{Loading
						? <Spinner size={25} />
						: <ThemeButton
								Title={'Delete Account'}
								onPress={() => Delete()}
								Style={[
									styles().br5,
									styles().h40px,
									{
										backgroundColor: currentTheme.red,
										borderColor: currentTheme.red,
									},
								]}
								StyleText={[{ fontSize: 14 }]}
							/>}
					<ThemeButton
						Title={'Cancel'}
						onPress={() => {
							onClose()
							setLoading(false)
						}}
						Style={[
							styles().br5,
							styles().h40px,
							{
								backgroundColor: currentTheme.B7B7B7,
								borderColor: currentTheme.B7B7B7,
							},
						]}
						StyleText={[{ color: currentTheme.black, fontSize: 14 }]}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default AccountDelete
