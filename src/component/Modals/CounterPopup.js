import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import TextField from '../FloatTextField/FloatTextField'
import { createOffer } from '../../apollo/server'
import UserContext from '../../context/User/User'
import FlashMessage from '../FlashMessage/FlashMessage'

const { width } = Dimensions.get('window')

export default function CounterPopup({ onClose, modalCounterVisible, applicationID }) {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [counter, setCounter] = useState('')
	const [counterError, setCounterError] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [mutate, { client }] = useMutation(createOffer, {
		onCompleted,
		onError,
	})

	async function onCompleted(_res) {
		try {
			// console.log('createOffer res', res);
			FlashMessage({ msg: 'Your offer has been sent!', type: 'success' })
			setLoading(false)
			onClose()
		} catch (e) {
			console.log(e)
			setLoading(false)
		} finally {
			setLoading(false)
		}
	}

	function onError(error) {
		console.log('createOffer error  :', error)
		FlashMessage({ msg: error.message, type: 'danger' })
		setLoading(false)
	}

	function Offer() {
		let status = true
		if (counter === '') {
			setCounterError(true)
			status = false
			return
		}
		if (status) {
			setLoading(true)
			mutate({
				variables: {
					applicationId: applicationID,
					inputOffer: {
						by: user?._id,
						rates: counter,
					},
				},
			})
		}
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalCounterVisible}
		>
			<TouchableOpacity
				activeOpacity={1}
				onPress={() => onClose()}
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt35,
						styles().pb30,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<Text style={[styles().fs16, styles().fw600, styles().alignSelfCenter, styles().mb15, { color: currentTheme.black }]}>{'Counter Offer'}</Text>
					<TextField
						keyboardType="numeric"
						value={counter}
						errorText={counterError}
						autoCapitalize="none"
						placeholder={'$'}
						onChangeText={text => {
							setCounterError(false)
							setCounter(text)
						}}
						style={[styles().bw1, styles().br10, styles().mb15, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
					<View>
						{Loading
							? <Spinner />
							: <ThemeButton
									Title={'Send'}
									StyleText={{ color: currentTheme.black }}
									onPress={() => Offer()}
								/>}
					</View>
				</View>
			</TouchableOpacity>
		</Modal>
	)
}
