import { Dimensions, Image, LayoutAnimation, Modal, Platform, Text, TouchableOpacity, View, Keyboard } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import TextField from '../FloatTextField/FloatTextField'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import Fontisto from 'react-native-vector-icons/Fontisto'
import FlashMessage from '../FlashMessage/FlashMessage'
import { createApplication } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import UserContext from '../../context/User/User'

const ApplyJobPopup = ({ visible, onClose, job, isApplied }) => {
	const { width } = Dimensions.get('window')
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [fullname, setFullname] = useState('')
	const [fullNameError, setFullnameError] = useState(false)
	const [coverletterLength, _setCoverletterLength] = useState(1000)
	const [email, setEmail] = useState('')
	const [emailError, setEmailError] = useState(false)

	const [rate, setRate] = useState('')
	const [rateError, setRateError] = useState(false)

	const [coverLetter, setCoverLetter] = useState('')
	const [coverLetterError, setCoverLetterError] = useState(false)

	const [Loading, setLoading] = useState(false)

	async function Apply() {
		Keyboard.dismiss()
		let status = true
		if (fullname === '') {
			setFullnameError(true)
			status = false
			return
		}
		if (email === '') {
			setEmailError(true)
			status = false
			return
		}
		if (rate === '') {
			setRateError(true)
			status = false
			return
		}
		if (coverLetter === '') {
			setCoverLetterError(true)
			status = false
			return
		}
		if (status) {
			setLoading(true)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			mutate({
				variables: {
					inputApplication: {
						applicant: user?._id,
						company: job?.company?._id,
						job: job?._id,
						coverletter: coverLetter,
						hourlyRates: rate,
					},
				},
			})
		}
	}

	const [mutate, { client }] = useMutation(createApplication, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(_data) {
		try {
			// console.log('createApplication res', data);
			isApplied(true)
			onClose()
			FlashMessage({ msg: 'Job Application Submitted.', type: 'success' })
			setLoading(false)
		} catch (e) {
			console.log(e)
			setLoading(false)
			onClose()
		} finally {
			setLoading(false)
			onClose()
		}
	}

	function onError(error) {
		onClose()
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('createApplication error  :', error)
	}

	useEffect(() => {
		setFullname(user.name)
		setEmail(user.email)
	}, [])

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<View
							style={[
								styles().wh65px,
								styles().br50,
								styles().overflowH,
								styles().justifyCenter,
								styles().alignCenter,
								styles().mr10,
								styles().boxpeshadow,
								{
									borderWidth: job?.company?.photo ? 0 : 1,
									borderColor: currentTheme.themeBackground,
								},
							]}
						>
							{job?.company?.photo
								? <Image
										source={{ uri: job?.company?.photo }}
										resizeMode="cover"
										style={styles().wh100}
									/>
								: <Fontisto
										size={35}
										color={currentTheme.themeBackground}
										name={'sait-boat'}
									/>}
						</View>

						<View style={[styles().mt10, styles().mb10, styles().w90]}>
							<Text style={[styles().fs14, styles().fontBold, styles().lh20, styles().textCenter, { color: currentTheme.headingColor }]}>{job?.title}</Text>
						</View>
					</View>

					<View style={[styles().mb20]}>
						<TextField
							editable={false}
							keyboardType="default"
							value={fullname}
							errorText={fullNameError}
							autoCapitalize="none"
							placeholder={'Full Name'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => {
								setFullnameError(false)
								setFullname(text)
							}}
						/>
					</View>

					<View style={[styles().mb20]}>
						<TextField
							editable={false}
							keyboardType="default"
							value={email}
							errorText={emailError}
							autoCapitalize="none"
							placeholder={'Email'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => {
								setEmailError(false)
								setEmail(text)
							}}
						/>
					</View>

					<View style={[styles().mb20]}>
						<TextField
							keyboardType="numeric"
							value={rate}
							errorText={rateError}
							autoCapitalize="none"
							placeholder={'Enter Your Daily Price'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => {
								setRateError(false)
								setRate(text)
							}}
						/>
					</View>

					<View style={[styles().mb5]}>
						<TextField
							keyboardType="default"
							value={coverLetter}
							maxLength={coverletterLength}
							errorText={coverLetterError}
							autoCapitalize="none"
							placeholder={'Enter Cover Letter'}
							multiline={true}
							style={[
								styles().bw1,
								styles().br10,
								styles().overflowH,
								{
									borderColor: currentTheme.B7B7B7,
								},
							]}
							stylesInput={[styles().h150px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
							onChangeText={text => {
								setCoverLetterError(false)
								setCoverLetter(text)
							}}
						/>
					</View>
					<Text
						style={{
							fontSize: 10,
							alignSelf: 'flex-end',
							color: currentTheme.c737373,
							marginBottom: 15,
						}}
					>{`${coverletterLength - coverLetter?.length}/${coverletterLength}`}</Text>
					{Loading
						? <Spinner size={25} />
						: <ThemeButton
								Title={'Apply'}
								onPress={() => Apply()}
								Style={[styles().br5, styles().mr10]}
								StyleText={[{ color: currentTheme.black }]}
							/>}
				</View>
			</View>
		</Modal>
	)
}

export default ApplyJobPopup
