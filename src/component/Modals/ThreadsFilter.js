import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Dropdown from '../../component/DropDown/Dropdopwn'
import { ScrollView } from 'react-native-gesture-handler'

const { height, width } = Dimensions.get('window')

export default function ThreadsFilter({ ModalHeading, onClose, modalVisible, filters }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [filter, setFilter] = useState({})
	const type = ['jobs', 'crewmember', 'company', 'checkride', 'mobileapp', 'general']
	const status = ['open', 'close']

	const clearFilter = async () => {
		setFilter({})
	}

	// console.log('in filter :', filter);
	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						onClose()
					}}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{
							width: width * 0.85,
							height: height * 0.65,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<ScrollView>
						<View style={[styles().alignCenter]}>
							<Text style={[styles().fs18, styles().mt10, styles().textCenter, styles().mb15, styles().lh30, styles().fw700, { color: currentTheme.black }]}>
								{ModalHeading}
							</Text>
						</View>
						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.status != null ? filter.status : 'Status'}
								data={status}
								selectedValue={value => setFilter({ ...filter, status: value?.toLowerCase() })}
							/>
						</View>

						<View style={[styles().w100, styles().mb15]}>
							<Dropdown
								placeholder={filter.type ? filter.type : 'Type'}
								data={type}
								selectedValue={value => setFilter({ ...filter, type: value })}
							/>
						</View>
					</ScrollView>
					<View>
						<ThemeButton
							Title={'Apply'}
							StyleText={{ color: currentTheme.black }}
							onPress={() => {
								filters(filter)
								onClose()
							}}
						/>

						<ThemeButton
							Title={'Clear'}
							StyleText={{ color: currentTheme.white }}
							Style={{
								backgroundColor: currentTheme.headingColor,
								borderColor: currentTheme.headingColor,
							}}
							onPress={() => clearFilter()}
						/>
					</View>
				</View>
			</View>
		</Modal>
	)
}
