import { Dimensions, LayoutAnimation, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import FlashMessage from '../FlashMessage/FlashMessage'
import { reqLicensing } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import UserContext from '../../context/User/User'

const LicensingPopup = ({ visible, onClose, props, callBack }) => {
	const { width } = Dimensions.get('window')
	const _user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)

	const [mutate, { client }] = useMutation(reqLicensing, {
		onCompleted: data => {
			console.log('reqLicensing res :', data?.reqLicensing)
			callBack(true)
			onClose()
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('reqLicensing error  :', error)
		},
	})

	const ApplyLicencing = async () => {
		setLoading(true)
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		await mutate()
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					//   onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<Text style={[styles().fs16, styles().fw600, styles().mb20, styles().textCenter, styles().mt15, { color: currentTheme.black }]}>
							Help About Licensing
						</Text>
						<Text style={[styles().fs14, styles().fw300, styles().mb20, styles().textCenter, { color: currentTheme.black }]}>
							This is the paid service, you will be charged for this.{'\n'}Are you sure you need help about licensing?
						</Text>
					</View>

					{Loading
						? <Spinner size={20} />
						: <ThemeButton
								Title={'Yes'}
								onPress={() => ApplyLicencing()}
								Style={[styles().br5, styles().h40px]}
								StyleText={[{ fontSize: 14 }]}
							/>}
					<ThemeButton
						Title={'No'}
						onPress={() => {
							onClose()
							setLoading(false)
						}}
						Style={[
							styles().br5,
							styles().h40px,
							{
								backgroundColor: currentTheme.B7B7B7,
								borderColor: currentTheme.B7B7B7,
							},
						]}
						StyleText={[{ color: currentTheme.black, fontSize: 14 }]}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default LicensingPopup
