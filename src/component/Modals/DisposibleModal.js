import { Dimensions, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import Ionicons from '@expo/vector-icons/Ionicons'
import Spinner from '../Spinner/Spinner'

const DisposibleEmailModal = ({ visible, onClose, callBack }) => {
	const { width } = Dimensions.get('window')
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					//   onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<Ionicons
							name="warning"
							size={50}
							color={currentTheme.B7B7B7}
						/>
						<Text style={[styles().fs14, styles().fw600, styles().mv20, styles().fontMedium, styles().textCenter, { color: currentTheme.black }]}>
							Like a sandcastle by the sea, this test account build with your dummy email and will vanish in 7 days. Enjoy the waves!
						</Text>
					</View>

					{Loading
						? <Spinner size={25} />
						: <ThemeButton
								onPress={() => {
									callBack(true)
									onClose()
									setLoading(false)
								}}
								Title={'Continue'}
								Style={[styles().br5, styles().h40px]}
								StyleText={[{ fontSize: 14 }]}
							/>}
					<ThemeButton
						Title={'Change Email'}
						onPress={() => {
							callBack(false)
							onClose()
							setLoading(false)
						}}
						Style={[
							styles().br5,
							styles().h40px,
							{
								backgroundColor: currentTheme.B7B7B7,
								borderColor: currentTheme.B7B7B7,
							},
						]}
						StyleText={[{ color: currentTheme.blackish, fontSize: 14 }]}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default DisposibleEmailModal
