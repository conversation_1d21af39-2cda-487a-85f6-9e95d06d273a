import { Dimensions, Image, Modal, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import { useContext } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import styles from '../../screens/styles'

const BadgeModal = ({ visible, onClose, badge, currentUser }) => {
	const { width, height } = Dimensions.get('window')
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{
							width: width * 0.8,
							maxHeight: height * 0.8,
							backgroundColor: currentTheme.white,
						},
					]}
				>
					<View style={[styles().mv20]}>
						<ScrollView
							showsVerticalScrollIndicator={false}
							contentContainerStyle={[styles().alignCenter]}
						>
							<View style={{ aspectRatio: 1, width: 50 }}>
								<Image
									source={require('../../assets/images/badge_success.png')}
									style={{ height: '100%', width: '100%' }}
									resizeMode="contain"
								/>
							</View>
							<Text style={[styles().fs18, styles().fontMedium, styles().mv10, styles().textCenter, { color: currentTheme.black }]}>Badge Assigned</Text>
							<Text style={[styles().fs14, styles().w90, styles().fontRegular, styles().mb20, styles().textCenter, { color: currentTheme.black }]}>
								{`${badge?.company?.name} has awarded ${currentUser} a badge on this waterway.`}
							</Text>
							{badge?.waterwaysId?.map((item, i) => {
								return (
									<Text
										key={i}
										style={[
											styles().fs14,
											styles().fontRegular,
											styles().mt10,
											styles().textCenter,
											styles().textDecorationUnderline,
											{ color: currentTheme.themeBackground },
										]}
									>
										{item?.name}
									</Text>
								)
							})}
						</ScrollView>
					</View>
				</View>
			</View>
		</Modal>
	)
}

export default BadgeModal
