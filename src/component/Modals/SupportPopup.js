import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, Modal, Platform } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import Spinner from '../Spinner/Spinner'
import TextField from '../FloatTextField/FloatTextField'
import { postContactUs } from '../../apollo/server'
import UserContext from '../../context/User/User'
import FlashMessage from '../FlashMessage/FlashMessage'

const { width, height } = Dimensions.get('window')

export default function SupportPopup({ onClose, modalVisible, navigation }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [subject, setSubject] = useState('')
	const [subjectError, setSubjectError] = useState(false)
	const [message, setMessage] = useState('')
	const [messageError, setMessageError] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [mutate, {}] = useMutation(postContactUs, {
		onCompleted: ({ postContactUs }) => {
			console.log('postContactUs res :', postContactUs)
			FlashMessage({ msg: 'Your query has been sent.', type: 'success' })
			setLoading(false)
			onClose()
			// navigation.navigate('Home');
		},
		onError: err => {
			console.log('postContactUs err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
			onClose()
		},
	})

	async function ContactUs() {
		setLoading(true)
		await mutate({
			variables: {
				inputContactUs: {
					message: message,
					subject: subject,
					user: user?._id,
				},
			},
		})
	}

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={modalVisible}
		>
			<TouchableOpacity
				activeOpacity={1}
				onPress={() => onClose()}
				style={[
					styles().flex,
					styles().alignCenter,
					styles().justifyCenter,
					{
						backgroundColor: 'rgba(0,0,0,0.6)',
					},
				]}
			>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb30,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<Text style={[styles().fs16, styles().mt10, styles().textCenter, styles().mb15, styles().lh30, styles().fontBold, { color: currentTheme.black }]}>
							{'Need Any Help?'}
						</Text>
					</View>
					<View style={[styles().mb20]}>
						<TextField
							keyboardType="default"
							value={subject}
							errorText={subjectError}
							autoCapitalize="none"
							placeholder={'Subject'}
							onChangeText={text => {
								setSubjectError(false)
								setSubject(text)
							}}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
						/>
					</View>

					<View style={[styles().mb20]}>
						<TextField
							keyboardType="default"
							value={message}
							errorText={messageError}
							autoCapitalize="none"
							placeholder={'Message'}
							multiline={true}
							onChangeText={text => {
								setMessageError(false)
								setMessage(text)
							}}
							style={[
								styles().bw1,
								styles().h100px,
								styles().br10,
								styles().overflowH,
								{
									textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
									borderColor: currentTheme.B7B7B7,
								},
							]}
							stylesInput={[styles().h100px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
						/>
					</View>

					<View>
						{Loading
							? <View style={[styles().mt10]}>
									<Spinner size={25} />
								</View>
							: <ThemeButton
									Title={'Send'}
									StyleText={{ color: currentTheme.black }}
									onPress={() => ContactUs()}
								/>}
					</View>
				</View>
			</TouchableOpacity>
		</Modal>
	)
}
