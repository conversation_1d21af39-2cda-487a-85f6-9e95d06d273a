import { Dimensions, Modal, Text, TouchableOpacity, View } from 'react-native'
import { useContext } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import ThemeButton from '../ThemeButton/ThemeButton'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'

const VideoConfirmModal = ({ visible, onClose, callBack }) => {
	const { width } = Dimensions.get('window')
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<Modal
			animationType="fade"
			transparent={true}
			visible={visible}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				<TouchableOpacity
					activeOpacity={1}
					//   onPress={() => onClose()}
					style={[styles().posAbs, styles().top0, styles().bottom0, styles().left0, styles().right0, { backgroundColor: 'rgba(0,0,0,0.6)' }]}
				/>
				<View
					style={[
						styles().ph20,
						styles().justifyCenter,
						styles().pt20,
						styles().pb20,
						styles().br10,
						{ width: width * 0.8, backgroundColor: currentTheme.white },
					]}
				>
					<View style={[styles().alignCenter]}>
						<View
							style={[
								styles().alignCenter,
								styles().justifyCenter,
								styles().pall20,
								styles().bw2,
								styles().br100,
								{ borderColor: currentTheme.themeBackground },
							]}
						>
							<FontAwesome
								name="video-camera"
								size={30}
								color={currentTheme.themeBackground}
							/>
						</View>
						<Text style={[styles().fs14, styles().fw600, styles().mv20, styles().fontMedium, styles().textCenter, { color: currentTheme.black }]}>
							Are you sure you want to upload this video?
						</Text>
					</View>

					<ThemeButton
						onPress={() => {
							callBack(true)
							onClose()
						}}
						Title={'Upload'}
						Style={[styles().br5, styles().h40px]}
						StyleText={[{ fontSize: 14 }]}
					/>
					<ThemeButton
						Title={'Cancel'}
						onPress={() => {
							callBack(false)
							onClose()
						}}
						Style={[
							styles().br5,
							styles().h40px,
							{
								backgroundColor: currentTheme.B7B7B7,
								borderColor: currentTheme.B7B7B7,
							},
						]}
						StyleText={[{ color: currentTheme.blackish, fontSize: 14 }]}
					/>
				</View>
			</View>
		</Modal>
	)
}

export default VideoConfirmModal
