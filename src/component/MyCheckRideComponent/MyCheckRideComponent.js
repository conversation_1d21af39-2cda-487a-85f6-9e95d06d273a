import { Text, View, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import moment from 'moment'
import { removetags } from '../../utils/Constants'

const MyCheckRideComponent = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const { item, index, navigation, company, checkRideData, checkrideID } = props
	console.log('hello', item)
	return (
		<TouchableOpacity
			onPress={() => {
				if (navigation) {
					// console.log(item);
					navigation.navigate('CheckRideStatus', {
						offer: item,
						checkrideID: checkrideID,
						checkRideData: checkRideData,
						company: company,
					})
				}
			}}
			activeOpacity={0.9}
			style={[
				styles().flexRow,
				styles().pb15,
				styles().pt20,
				styles().ph15,
				item?.status === 'pending' || item?.status === 'expired' || item?.status === 'rejected' ? styles().mb30 : styles().mb15,
				index === 0 ? styles().mt20 : null,
				//   styles().alignCenter,
				//   styles().flex,
				styles().bw1,
				styles().br10,
				{
					borderColor: item?.status === 'expired' ? currentTheme.FFE5E5 : currentTheme.CFCFCF,
				},
			]}
		>
			{item?.status === 'pending' || item?.status === 'expired' || item?.status === 'rejected'
				? <View
						style={[
							styles().posAbs,
							styles().flexRow,
							styles().alignCenter,
							styles().pv5,
							styles().br50,
							styles().ph15,
							styles().left10,
							{
								top: -12,
								backgroundColor:
									item?.status === 'expired'
										? currentTheme.FFE5E5
										: item?.status === 'pending'
											? currentTheme.starColorBG
											: item?.status === 'rejected'
												? currentTheme.FFE5E5
												: currentTheme.E6FFEA,
							},
						]}
					>
						<Text
							style={[
								styles().fs9,
								styles().fw600,
								styles().textCapitalize,
								{
									color:
										item?.status === 'expired'
											? currentTheme.red
											: item?.status === 'pending'
												? currentTheme.starColor
												: item?.status === 'rejected'
													? currentTheme.red
													: currentTheme.green,
								},
							]}
						>
							{item?.status}
						</Text>
					</View>
				: null}
			<View style={[styles().posAbs, styles().flexRow, styles().alignCenter, styles().top10, styles().right10]}>
				{/* <TouchableOpacity>
          <MaterialCommunityIcons
            name="comment-text-outline"
            size={18}
            color={currentTheme.E8E8C8}
          />
        </TouchableOpacity> */}
				{/* <TouchableOpacity
          // onPress={() => share(item)}
          activeOpacity={0.5}
          style={[styles().ph5]}>
          <FontAwesome name="share" size={14} color={currentTheme.E8E8C8} />
        </TouchableOpacity> */}
			</View>
			<View
				style={[
					styles().wh50px,
					styles().br25,
					styles().overflowH,
					styles().justifyCenter,
					styles().alignCenter,
					styles().mr10,
					{
						borderWidth: company?.photo ? 0 : 1,
						borderColor: currentTheme.themeBackground,
					},
				]}
			>
				{company?.photo
					? <Image
							source={{ uri: company?.photo }}
							resizeMode="cover"
							style={styles().wh100}
						/>
					: <MaterialCommunityIcons
							name="city-variant"
							size={25}
							color={currentTheme.themeBackground}
						/>}
			</View>
			<View style={[styles().flex]}>
				<Text
					numberOfLines={1}
					style={[styles().fs12, styles().fontBold, styles().lh20, styles().textCapitalize, { color: currentTheme.black }]}
				>
					{`Check Ride Offer - ${company?.name}`}
				</Text>
				<View
					style={[
						styles().flexRow,
						styles().alignCenter,
						//   styles().mb10,
						styles().flex,
						styles().flexWrap,
						// {backgroundColor: 'teal'},
					]}
				>
					<View
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().mr10,
							// styles().mb10,
						]}
					>
						<Feather
							name="calendar"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs9, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{moment(item?.startDate).format('LL')}</Text>
					</View>
					<View
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().mr10,
							// styles().mb10,
						]}
					>
						<Ionicons
							name="cash-outline"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs9, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{`$${item?.price}`}</Text>
					</View>
					{item?.status === 'pending' || item?.status === 'expired'
						? // ||item?.status === 'rejected'
							<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
								<Feather
									name="clock"
									size={12}
									color={currentTheme.red}
								/>
								<Text style={[styles().fs9, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.red }]}>
									{` ${item.status === 'pending' ? 'expired' : 'expired'} ${moment(item?.expireAt).endOf('day').fromNow()}`}
								</Text>
							</View>
						: null}
				</View>

				<View style={[styles().mt10]}>
					<Text
						numberOfLines={2}
						style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
					>
						{removetags(item?.company?.checkRideData?.terms)}
					</Text>
				</View>
			</View>
		</TouchableOpacity>
	)
}

export default React.memo(MyCheckRideComponent)
