import AsyncStorage from '@react-native-async-storage/async-storage'
import navigationService from '../../routes/navigationService'
import FlashMessage from '../FlashMessage/FlashMessage'

export const ErrorHandler = async (error, _props) => {
	try {
		// const {isLoader} = useContext(Loading);
		console.log('ErrorHandler :', error)
		if (error === 'Please authenticated') {
			const keys = await AsyncStorage.getAllKeys()
			await AsyncStorage.multiRemove(keys)
			// isLoader(false);
			await AsyncStorage.clear().then(() => {
				console.log('async clear from Error Handler')
			})
			// FlashMessage({msg: 'Session Expire', type: 'danger'});
			navigationService.ResetNavigation()
		}
		if (error === 'Network request failed') {
			navigationService.ResetNavigationTo()
		} else {
			FlashMessage({ msg: error, type: 'danger' })
		}
	} catch (error) {
		console.log('Error handler catch :', error)
	}
}
