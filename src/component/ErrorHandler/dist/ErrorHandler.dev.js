Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.ErrorHandler = void 0

var _react = require('react')

var _native = require('@react-navigation/native')

var _routers = require('@react-navigation/routers')

var _asyncStorage = _interopRequireDefault(require('@react-native-async-storage/async-storage'))

var _navigationService = _interopRequireDefault(require('../../routes/navigationService'))

var _FlashMessage = _interopRequireDefault(require('../FlashMessage/FlashMessage'))

var _Loading = _interopRequireDefault(require('../../context/Loading/Loading'))

var _Socket = require('../../socket/Socket')

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var ErrorHandler = function ErrorHandler(error, _props) {
	var keys
	return regeneratorRuntime.async(
		function Error<PERSON>andler$(_context) {
			while (1) {
				switch ((_context.prev = _context.next)) {
					case 0:
						_context.prev = 0
						// const {isLoader} = useContext(Loading);
						console.log('ErrorHandler :', error)

						if (!(error === 'Please authenticated')) {
							_context.next = 11
							break
						}

						_context.next = 5
						return regeneratorRuntime.awrap(_asyncStorage.default.getAllKeys())

					case 5:
						keys = _context.sent
						_context.next = 8
						return regeneratorRuntime.awrap(_asyncStorage.default.multiRemove(keys))

					case 8:
						_context.next = 10
						return regeneratorRuntime.awrap(
							_asyncStorage.default.clear().then(() => {
								console.log('async clear from Error Handler')
							})
						)

					case 10:
						// FlashMessage({msg: 'Session Expire', type: 'danger'});
						_navigationService.default.ResetNavigation()

					case 11:
						if (error === 'Network request failed') {
							_navigationService.default.ResetNavigationTo()
						} else {
							;(0, _FlashMessage.default)({
								msg: error,
								type: 'danger',
							})
						}

						_context.next = 17
						break

					case 14:
						_context.prev = 14
						_context.t0 = _context.catch(0)
						console.log('Error handler catch :', _context.t0)

					case 17:
					case 'end':
						return _context.stop()
				}
			}
		},
		null,
		null,
		[[0, 14]]
	)
}

exports.ErrorHandler = ErrorHandler
