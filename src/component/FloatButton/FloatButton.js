import { useContext } from 'react'
import { View, TouchableOpacity } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Entypo from '@expo/vector-icons/Entypo'

const FloatButton = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<View style={[styles().alignEnd]}>
			<TouchableOpacity
				onPress={props.onPress}
				activeOpacity={0.7}
				style={[styles().wh40px, styles().br100, styles().alignCenter, styles().justifyCenter, { backgroundColor: currentTheme.headingColor }]}
			>
				<Entypo
					name={'plus'}
					color={currentTheme.white}
					size={25}
				/>
			</TouchableOpacity>
		</View>
	)
}

export default FloatButton
