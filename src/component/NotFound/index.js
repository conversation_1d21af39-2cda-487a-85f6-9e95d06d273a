import { View, Text, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const NotFound = ({ loading, navigation, message }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<View style={[styles().flex]}>
			<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().mt50, styles().ph20]}>
				<TouchableOpacity
					onPress={() => navigation()}
					style={[styles().alignSelfStart, styles().justifyCenter, styles().h50px, styles().w25px]}
				>
					<FontAwesome
						name="angle-left"
						size={30}
						color={currentTheme.blackish}
					/>
				</TouchableOpacity>
			</View>
			<View style={[styles().flex, styles().ph20, styles().justifyCenter, styles().alignCenter]}>
				{!loading && <Text style={[styles().fs16, styles().fontMedium, styles().textCenter, styles().w90, { color: currentTheme.c444D6E }]}>{message}</Text>}
			</View>
		</View>
	)
}

export default NotFound
