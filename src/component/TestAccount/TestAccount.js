import { Text, View } from 'react-native'
import { useContext } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import UserContext from '../../context/User/User'
import styles from '../../screens/styles'

const TestAccount = ({}) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	// Get the creation date timestamp
	const creationDate = new Date(user?.createdAt).getTime()
	const expireInDay = 7

	// Calculate the timestamp 7 days later
	const expirationDate = new Date(creationDate + expireInDay * 24 * 60 * 60 * 1000).getTime()

	// Calculate the current timestamp
	const currentDate = Date.now()

	// Calculate the difference in milliseconds
	const differenceInMilliseconds = expirationDate - currentDate

	// Convert the difference to days
	const differenceInDays = Math.floor(differenceInMilliseconds / (1000 * 60 * 60 * 24))

	if (user.disposable !== 'true') {
		return null
	}
	return (
		<View
			style={[
				styles().w90,
				styles().alignCenter,
				styles().alignSelfCenter,
				styles().br100,
				styles().justifyCenter,
				styles().pv10,
				styles().mv10,
				{ backgroundColor: currentTheme.FFE5E5 },
			]}
		>
			<Text style={[styles().fontMedium, styles().fs12, { color: currentTheme.red }]}>
				{differenceInDays >= 0
					? `You're in Test Mode, Expire in ${differenceInDays} ${differenceInDays === 1 ? 'day' : 'days'}`
					: 'Your Test Mode has expired.'}
			</Text>
		</View>
	)
}

export default TestAccount
