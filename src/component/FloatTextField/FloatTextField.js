import { useEffect, useRef, useState, useContext } from 'react'
import { Text, TextInput, StyleSheet, View, Animated, Easing } from 'react-native'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

import Styles from '../../screens/styles'
const TextField = props => {
	const {
		label,
		errorText,
		value,
		style,
		onBlur,
		onFocus,
		stylesInput,
		childrenPassword,
		placeholder,
		FieldIcon,
		editable,
		keyboardType,
		nonEdit,
		maxLength,
		returnKeyType,

		...restOfProps
	} = props
	const [isFocused, setIsFocused] = useState(false)

	const inputRef = useRef(null)
	const focusAnim = useRef(new Animated.Value(0)).current

	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	useEffect(() => {
		Animated.timing(focusAnim, {
			toValue: isFocused || !!value ? 1 : 0,
			duration: 150,
			easing: Easing.bezier(0.4, 0, 0.2, 1),
			useNativeDriver: true,
		}).start()
	}, [focusAnim, isFocused, value])

	let _color = isFocused ? currentTheme.c969AA8 : currentTheme.c969AA8
	if (errorText) {
		_color = '#B00020'
	}

	return (
		// <View style={{flex: 1}}>
		//   {label ? (
		//     <Text
		//       style={[
		//         Styles().fs14,
		//         Styles().fontMedium,
		//         Styles().mb5,
		//         {color: currentTheme.black},
		//       ]}>
		//       {label}
		//     </Text>
		//   ) : null}
		<View
			style={[
				Styles().flexRow,
				Styles().alignCenter,
				// Styles().justifyCenter,
				// Styles().ph20,
				// Styles().flex,
				Styles().bbw1,
				{ borderBottomColor: currentTheme.B7B7B7 },
				style,
			]}
		>
			{FieldIcon}
			<TextInput
				maxLength={maxLength}
				keyboardType={keyboardType}
				returnKeyType={returnKeyType ? returnKeyType : 'done'}
				style={[
					// Styles().h35px,
					// Styles().justifyCenter,
					// Styles().alignCenter,
					Styles().fs14,
					// Styles().bw1, {borderColor:'red'},
					Styles().flex,
					// Styles().lh30,
					Styles().fontRegular,
					Styles().ph10,
					Styles().alignSelfStart,
					Styles().h50px,
					// Styles().mt10,
					// Styles().bw1,

					{
						// borderColor: currentTheme.EFEFEF,
						color: currentTheme.black,
						backgroundColor: editable === undefined ? currentTheme.white : currentTheme.D9D9D9,
					},
					stylesInput,
				]}
				editable={editable}
				placeholderTextColor={errorText === true ? '#B00020' : '#737373'}
				ref={inputRef}
				placeholder={errorText === true ? `${placeholder}*` : placeholder}
				{...restOfProps}
				value={value}
				onBlur={event => {
					setIsFocused(false)
					onBlur?.(event)
				}}
				onFocus={event => {
					setIsFocused(true)
					onFocus?.(event)
				}}
			/>
			{/* <TouchableWithoutFeedback onPress={() => inputRef.current?.focus()}>
        <Animated.View
          style={[
            styles.labelContainer,
            {paddingHorizontal:10 },
            nonEdit && {backgroundColor:currentTheme.E6E6E6, borderRadius:5} ,
            isFocused && {},
            {
              transform: [
                {
                  scale: focusAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1],
                    // outputRange: [1, 1],
                  }),
                },
                {
                  translateY: focusAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [15, 15],
                  }),
                },
                {
                  translateX: focusAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [30, 30],
                  }),
                },
              ],
            },
          ]}
        >
          <Text
            style={[
              styles.label,
              {
                color,
              },
              // isFocused && {
              //   fontSize : 12
              // }
            ]}
          >
            {label}
            {errorText ? "*" : ""}
          </Text>
        </Animated.View>
      </TouchableWithoutFeedback> */}

			{childrenPassword}
			{!!errorText && <Text style={styles.error}>{errorText}</Text>}
		</View>
		// </View>
	)
}

const styles = StyleSheet.create({
	input: {
		// padding: 18,
		// height : 50,
		// justifyContent :'center',
		// alignContent :'center',
		// borderRadius: 4,
		fontSize: 14,
		// borderBottomWidth : 1
	},
	labelContainer: {
		position: 'absolute',
		top: -5,
		// paddingHorizontal: 10,
	},
	label: {
		// fontSize: 16,
		fontSize: 12,
	},
	error: {
		// marginTop: 4,
		marginLeft: 12,
		fontSize: 12,
		color: '#B00020',
	},
})

export default TextField
