import { useState, useContext } from 'react'
import { View, Text, TouchableOpacity, Dimensions, LayoutAnimation, TextInput, ScrollView } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import AntDesign from '@expo/vector-icons/AntDesign'

const { height, width } = Dimensions.get('window')

export default function JobFilter({ placeholder, data, selectedValue, style, search, onChangeText, value }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_selected, setSelected] = useState('')
	const [filteropen, setFilterOpen] = useState(false)

	return (
		<TouchableOpacity
			activeOpacity={0.7}
			onPress={() => {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				setFilterOpen(!filteropen)
			}}
			style={[
				styles().bw1,
				styles().br10,
				// styles().justifyCenter,
				{ borderColor: currentTheme.C3C3C3, paddingBottom: 5 },
				style,
			]}
		>
			<View style={[styles().h45px, styles().justifyCenter, styles().ph15]}>
				<View style={[styles().flexRow, styles().justifyBetween, styles().alignCenter]}>
					{search
						? <>
								<AntDesign
									name={'search1'}
									size={16}
									color={currentTheme.c737373}
								/>
								<TextInput
									value={value}
									placeholderTextColor={currentTheme.c737373}
									placeholder={'Search/Select City'}
									onChangeText={onChangeText}
									style={[styles().fs14, styles().h50px, styles().fw600, styles().textCapitalize, { color: currentTheme.c737373, flex: 1, marginLeft: 3 }]}
								/>
							</>
						: <Text style={[styles().fs14, styles().fontMedium, styles().textCapitalize, { color: currentTheme.c737373 }]}>
								{placeholder === 'hireOn' ? 'Hire On' : placeholder}
							</Text>}

					<FontAwesome
						name={filteropen ? 'angle-up' : 'angle-down'}
						size={20}
						color={currentTheme.c737373}
					/>
				</View>
			</View>
			{filteropen
				? <View style={{ maxHeight: 200 }}>
						<ScrollView
							nestedScrollEnabled={true}
							contentContainerStyle={{ flexGrow: 1 }}
						>
							{data?.length !== 0
								? data?.map((item, i) => {
										const key = item?.language ? item?.language : item
										return (
											<TouchableOpacity
												activeOpacity={0.7}
												key={i}
												onPress={() => {
													setSelected(key)
													selectedValue(key)
													setFilterOpen(false)
													LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
												}}
												style={[styles().ph15, styles().pt5, styles().pb5]}
											>
												<Text style={[styles().fs14, styles().textCapitalize, styles().fontRegular, { color: currentTheme.C3C3C3 }]}>
													{key === 'matePilot'
														? 'Mate Pilot'
														: key === 'crewmember'
															? 'Crew member'
															: key === 'checkride'
																? 'Check Ride'
																: key === 'hireOn'
																	? 'Hire On'
																	: key === 'mobileapp'
																		? 'Mobile App'
																		: key === 'onlyMe'
																			? 'Only me'
																			: key}
												</Text>
											</TouchableOpacity>
										)
									})
								: <Text style={[styles().fs12, styles().fw400, styles().ph15, styles().mb10, { color: currentTheme.C3C3C3 }]}>{`No ${placeholder}`}</Text>}
						</ScrollView>
					</View>
				: null}
		</TouchableOpacity>
	)
}
