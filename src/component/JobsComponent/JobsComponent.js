import { Text, View, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Ionicons from '@expo/vector-icons/Ionicons'
import Feather from '@expo/vector-icons/Feather'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import { useMutation } from '@apollo/client'
import { removeSavedJob, saveJob } from '../../apollo/server'
import moment from 'moment'
import FlashMessage from '../FlashMessage/FlashMessage'
import { removetags, useThrottledPress } from '../../utils/Constants'
import UserContext from '../../context/User/User'
import Share from '../../context/Share/Share'

const JobsComponent = ({ item, navigation, index, alreadyApplied, isSave, InProgressJobs, reload, others }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const { isShare, shareNow } = useContext(Share)
	const [mutate, { client }] = useMutation(isSave === false ? removeSavedJob : saveJob, {
		onCompleted,
		onError,
	})

	async function onCompleted(res) {
		try {
			console.log('saveJob res', res)
			if (isSave === false) {
				await reload()
				// FlashMessage({
				//   msg: res.removeSavedJob.message,
				//   type: 'warning',
				// });
			} else {
				// FlashMessage({
				//   msg: res.saveJob.message,
				//   type: 'success',
				// });
			}
		} catch (e) {
			console.log(e)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		console.log('saveJob error  :', error)
	}

	const save = async item => {
		const save = {
			saveJobId: item?._id,
		}

		const unsave = {
			removeSavedJobId: item?._id,
		}
		await mutate({
			variables: isSave === false ? unsave : save,
		})
	}

	function share(item) {
		isShare(true)
		shareNow({
			title: item?.title,
			_id: item?._id,
			photo: item?.company.photo,
			description: item?.description,
			shareType: 'job',
		})
	}

	const handleNavigation = () => {
		navigation.navigate('JobDetails', {
			item: item,
			jobId: item?._id,
			alreadyApplied: !!(isApplied || item?.status === 'applied' || item?.status === 'pending'),
		})
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)

	const isApplied = user?.appliedJobs?.some(applied => item?._id === applied)
	// console.log('item?.status :',item?.status)

	return (
		<TouchableOpacity
			key={index}
			onPress={() => throttledButtonPress()}
			activeOpacity={0.9}
			style={[
				styles().flexRow,
				styles().pb20,
				styles().pt30,
				styles().ph15,
				styles().mb20,
				index === 0 ? styles().mt20 : null,
				styles().bw1,
				styles().br10,
				{ borderColor: currentTheme.CFCFCF },
			]}
		>
			<View style={[styles().posAbs, styles().flexRow, styles().alignCenter, styles().top10, styles().right10, styles().zIndex100]}>
				<TouchableOpacity
					onPress={() => share(item)}
					activeOpacity={0.5}
					style={[styles().pall8, styles().br5, styles().mh5, { backgroundColor: currentTheme.lighterGold }]}
				>
					<FontAwesome
						name="share"
						size={12}
						color={currentTheme.white}
					/>
				</TouchableOpacity>
				<TouchableOpacity
					onPress={() => save(item)}
					activeOpacity={0.5}
					style={[styles().pall8, styles().br5, { backgroundColor: currentTheme.lighterGold }]}
				>
					<FontAwesome
						name={isSave === false ? 'bookmark' : 'bookmark-o'}
						size={12}
						color={currentTheme.white}
					/>
				</TouchableOpacity>
			</View>
			{/* {true && isSave === false ? ( */}
			{isSave === false && isApplied
				? <View
						style={[
							styles().posAbs,
							styles().left15,
							styles().pv5,
							styles().br20,
							{
								top: -10,
								backgroundColor: others ? currentTheme.themeBackgroundLight : currentTheme.CFCFCF,
								paddingHorizontal: 15,
							},
						]}
					>
						<Text
							style={[
								styles().fs9,
								styles().fontRegular,
								styles().textCapitalize,
								{
									color: others ? currentTheme.themeBackground : currentTheme.white,
								},
							]}
						>
							{others ? (item?.status === 'applied' ? 'Already Applied' : item?.status) : 'Already Applied'}
						</Text>
					</View>
				: null}
			<View
				style={[
					// {backgroundColor: 'teal'},
					styles().mr10,
					styles().alignCenter,
				]}
			>
				<View
					style={[
						styles().wh50px,
						styles().br25,
						styles().overflowH,
						styles().justifyCenter,
						styles().alignCenter,
						{
							borderWidth: item?.company?.photo ? 0 : 1,
							borderColor: currentTheme.themeBackground,
						},
					]}
				>
					{item?.company?.photo
						? <Image
								source={{ uri: item?.company?.photo }}
								resizeMode="cover"
								style={styles().wh100}
							/>
						: <Ionicons
								name="md-briefcase"
								size={22}
								color={currentTheme.themeBackground}
							/>}
				</View>
				<View
					style={[
						styles().ph10,
						styles().pv5,
						styles().br5,
						styles().alignCenter,
						styles().mt10,
						styles().justifyCenter,
						{ backgroundColor: currentTheme.EEE8D5 },
					]}
				>
					<Text style={[styles().fs10, styles().fontMedium, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
						{item?.jobType === 'hireOn' ? 'Hire On' : item?.jobType}
					</Text>
				</View>
			</View>
			<View style={[styles().flex, { marginTop: 10 }]}>
				<Text
					numberOfLines={2}
					style={[
						styles().fs12,
						styles().fontBold,
						styles().lh20,

						{ color: currentTheme.headingColor },
					]}
				>
					{`${item?.title}`}
				</Text>
				<View style={[styles().flexRow, styles().alignCenter, styles().mt5, styles().flex, styles().flexWrap]}>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mb10]}>
						<FontAwesome
							name="briefcase"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
							{item?.type === 'matePilot' ? 'Mate Pilot' : item?.type}
						</Text>
					</View>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mb10]}>
						<FontAwesome
							name="map-pin"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item?.city ? item?.city : 'N/A'}</Text>
					</View>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mb10]}>
						<Feather
							name="clock"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
							{InProgressJobs ? moment(item?.createdAt).fromNow('') : moment(item?.expireAt).fromNow('')}
						</Text>
					</View>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
						<Ionicons
							name="ios-cash-outline"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{`$${item?.rates}/Day`}</Text>
					</View>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mt5]}>
						<FontAwesome5
							name="ship"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
							{item?.name_of_vessel ? item?.name_of_vessel : 'N/A'}
						</Text>
					</View>
					{/* <View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
            <MaterialCommunityIcons
              name="format-list-bulleted-type"
              size={12}
              color={currentTheme.E8E8C8}
            />
            <Text
              style={[
                styles().fs7,
                styles().ml3,
                styles().fontRegular,
                styles().textCapitalize,
                {color: currentTheme.E8E8C8},
              ]}>
              {item?.jobType}
            </Text>
          </View> */}
				</View>
				<View style={[styles().mt10]}>
					<Text
						numberOfLines={3}
						style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
					>
						{removetags(item?.description)}
					</Text>
				</View>
			</View>
		</TouchableOpacity>
	)
}

export default React.memo(JobsComponent)
