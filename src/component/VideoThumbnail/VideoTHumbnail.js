import { Dimensions, LayoutAnimation, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext, useMemo, useRef, useState } from 'react'
import Video from 'react-native-video'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'

const { height, width } = Dimensions.get('window')

const VideoThumbnail = ({ video, isFocus }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [mute, setMute] = useState(true)
	const [pause, setPause] = useState(true)
	const [isVideoWidgets, setIsVideoWidgets] = useState(true)
	const videoRef = useRef(null)

	const PausePlay = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => {
					setPause(!pause)
					setMute(false)
					setTimeout(() => {
						if (pause) {
							LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
							setIsVideoWidgets(false)
						}
					}, 2000)
				}}
				style={[videotStyles.pausePlay, { left: Dimensions.get('window').width / 2 - 35 }]}
			>
				<FontAwesome5
					name={pause ? 'play' : 'pause'}
					color={currentTheme.lightGold}
					size={20}
					style={{ marginLeft: pause ? 3 : 0 }}
				/>
			</TouchableOpacity>
		)
	}

	useMemo(() => {
		if (!isFocus) {
			setPause(true)
			setMute(true)
			setIsVideoWidgets(true)
		}
	}, [isFocus])

	return (
		<TouchableOpacity
			activeOpacity={0.9}
			onPress={() => {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				setIsVideoWidgets(true)
			}}
			style={[styles().w100, styles().mt10, styles().overflowH, styles().br5, { aspectRatio: 0.7 }]}
		>
			<Video
				ref={videoRef}
				useNativeControls
				resizeMode="cover"
				autoplay={false}
				source={{ uri: video }}
				isLooping={false}
				style={{
					width: '100%',
					height: '100%',
				}}
				paused={pause}
				muted={mute}
				onEnd={() => {
					videoRef.current.seek(0)
					setPause(true)
					setMute(true)
					setIsVideoWidgets(true)
				}}
			/>
			{isVideoWidgets ? <PausePlay /> : null}
		</TouchableOpacity>
	)
}

export default VideoThumbnail

const videotStyles = StyleSheet.create({
	pausePlay: {
		position: 'absolute',
		top: height * 0.3,
		zIndex: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 45,
		width: 45,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
})
