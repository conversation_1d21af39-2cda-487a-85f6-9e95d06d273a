import { View, Image, TouchableOpacity } from 'react-native'
import { useContext, useEffect, useRef, useState } from 'react'
import SignatureScreen from 'react-native-signature-canvas'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import AntDesign from '@expo/vector-icons/AntDesign'
import Ionicons from '@expo/vector-icons/Ionicons'

const Signature = ({ descriptionText, onOK, value }) => {
	const ref = useRef()
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [signature, setSignature] = useState('')
	const [showSignature, setShowSignature] = useState(true)
	// Called after ref.current.readSignature() reads a non-empty base64 string
	const handleOK = signature => {
		setSignature(signature)
		// onOK(signature); // Callback from Component props
	}

	const handleOKConfirm = () => {
		setShowSignature(false)
		onOK(signature) // Callback from Component props
	}

	// Called after ref.current.readSignature() reads an empty string
	const handleEmpty = () => {
		console.log('Empty')
	}

	// Called after ref.current.clearSignature()
	const handleClear = () => {
		console.log('clear success!')
	}

	// Called after end of stroke
	const handleEnd = () => {
		ref?.current?.readSignature()
	}

	// Called after ref.current.getData()
	const handleData = data => {
		console.log('handleData :', data)
	}

	const handleRedo = () => {
		// if (ref?.current) {
		// ref?.current?.redo();
		handleClear()
		setSignature('')
		setShowSignature(true)
		onOK('')
		// }
	}

	useEffect(() => {
		if (value) {
			setSignature(value)
			setShowSignature(false)
		}
	}, [value])
	return (
		<View style={[styles().flex, styles().overflowH]}>
			{!showSignature
				? <View style={[]}>
						<Image
							source={{ uri: signature }}
							style={[
								styles().w100,
								styles().h200px,
								styles().alignCenter,
								styles().justifyCenter,
								styles().bw1,
								styles().br5,
								{
									borderColor: currentTheme.themeBackground,
								},
							]}
							resizeMode="contain"
						/>
						<View style={[styles().flexRow, styles().mt10, styles().alignCenter, styles().justifyBetween]}>
							<TouchableOpacity
								onPress={() => handleRedo()}
								activeOpacity={0.5}
							>
								<Ionicons
									name={'reload-circle'}
									size={35}
									color={currentTheme.themeBackground}
								/>
							</TouchableOpacity>
						</View>
					</View>
				: <View style={[styles().flex]}>
						<SignatureScreen
							style={[styles().w100, styles().h200px, styles().bw1, styles().overflowH, styles().br10, { borderColor: currentTheme.themeBackground }]}
							ref={ref}
							nestedScrollEnabled
							onEnd={handleEnd}
							onOK={handleOK}
							onEmpty={handleEmpty}
							onClear={handleClear}
							penColor={currentTheme.themeBackground}
							onGetData={handleData}
							// autoClear={false}
							descriptionText={descriptionText}
						/>

						<View style={[styles().flexRow, styles().mt10, styles().ph5, styles().alignCenter, styles().justifyBetween]}>
							{!signature && !showSignature
								? <TouchableOpacity
										onPress={() => handleRedo()}
										activeOpacity={0.5}
									>
										<Ionicons
											name={'reload-circle'}
											size={35}
											color={currentTheme.themeBackground}
										/>
									</TouchableOpacity>
								: <View />}
							<TouchableOpacity
								onPress={() => handleOKConfirm()}
								activeOpacity={0.5}
							>
								<AntDesign
									name={'checkcircle'}
									size={28}
									color={currentTheme.lightGreen}
								/>
							</TouchableOpacity>
						</View>
					</View>}
		</View>
	)
}

export default Signature
