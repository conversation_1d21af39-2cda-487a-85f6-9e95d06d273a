import { useContext, useRef } from 'react'
import { View, Image, Platform, TouchableOpacity, Animated, Easing } from 'react-native'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import WeatherWidget from '../WeatherWidget/WeatherWidget'
import { MessageContext } from '../../context/Notification/Notification'

export default function HeaderHome(props) {
	const themeContext = useContext(ThemeContext)
	const { newMessage, isNewMessage } = useContext(MessageContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { StyleHead, navigation } = props
	const spinValue = useRef(new Animated.Value(0)).current
	const androidSafeTop = useAndroidSafeTop()

	const startSpinAnimation = () => {
		Animated.timing(spinValue, {
			toValue: 1,
			duration: 1000,
			easing: Easing.linear,
			useNativeDriver: true,
		}).start(() => {
			spinValue.setValue(0)
		})
	}

	const spin = spinValue.interpolate({
		inputRange: [0, 1],
		outputRange: ['0deg', '360deg'],
	})

	return (
		<View
			style={[
				styles().flexRow,
				styles().alignCenter,
				styles().justifyBetween,
				Platform.OS === 'android' && styles().mt35,
				styles().ph20,
				styles().pb15,
				styles().zIndex1,
				styles().posRel,
				StyleHead,
				props.HeaderStyle,
				{ paddingTop: androidSafeTop }
			]}
		>
			<TouchableOpacity
				onPress={startSpinAnimation}
				activeOpacity={1}
				style={[styles().wh50px, styles().overflowH]}
			>
				<Animated.Image
					source={require('../../assets/images/home-logo.png')}
					style={[styles().wh100, { transform: [{ rotate: spin }] }]}
					resizeMode="contain"
				/>
			</TouchableOpacity>

			<WeatherWidget navigation={navigation} />

			<View style={[styles().flexRow, styles().alignCenter, styles().justifyEnd]}>
				<TouchableOpacity
					activeOpacity={0.7}
					onPress={() => props.navigation.navigate('HomeSearch')}
					style={[styles().wh35px, styles().alignCenter, styles().justifyCenter, styles().br50, styles().mr10, { backgroundColor: currentTheme.EEE8D5 }]}
				>
					<FontAwesome
						name="search"
						size={16}
						color={currentTheme.themeBackground}
					/>
				</TouchableOpacity>

				{newMessage
					? <View
							style={{
								position: 'absolute',
								height: 13,
								width: 13,
								borderRadius: 20,
								backgroundColor: currentTheme.red,
								zIndex: 100,
								top: 2,
								right: -3,
								borderWidth: 1.5,
								borderColor: currentTheme.white,
							}}
						/>
					: null}
				<TouchableOpacity
					activeOpacity={0.7}
					onPress={() => {
						navigation.navigate('Chats')
						isNewMessage(false)
					}}
					style={[
						styles().wh35px,
						styles().alignCenter,
						styles().justifyCenter,
						styles().br50,
						styles().pall10,
						styles().overflowH,
						{ backgroundColor: currentTheme.EEE8D5 },
					]}
				>
					<Image
						source={require('../../assets/images/messages.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</TouchableOpacity>
			</View>
		</View>
	)
}
