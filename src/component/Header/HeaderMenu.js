import { useContext } from 'react'
import { View, Text, Image, Platform, TouchableOpacity } from 'react-native'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'

import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import { MessageContext } from '../../context/Notification/Notification'

export default function HeaderMenu(props) {
	// const user = useContext(UserContext);

	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { newMessage, isNewMessage } = useContext(MessageContext)
	const { StyleHead, navigation } = props
	const androidSafeTop = useAndroidSafeTop()

	function Back() {
		return (
			<TouchableOpacity
				onPress={() => props.navigation.goBack()}
				style={[styles().alignCenter, styles().justifyCenter, styles().h50px, styles().w25px]}
			>
				<FontAwesome
					name="angle-left"
					size={30}
					color={currentTheme.blackish}
				/>
			</TouchableOpacity>
		)
	}

	return (
		<View
			style={[
				styles().flexRow,
				styles().alignCenter,
				styles().justifyBetween,
				Platform.OS === 'android' && styles().h100px,
				// styles().ph20,
				styles().zIndex1,
				styles().posRel,
				StyleHead,
				props.HeaderStyle,
				{ paddingTop: androidSafeTop }
			]}
		>
			{props.LeftIcon ? <Back /> : <View style={[styles().h50px, styles().w25px]} />}

			<Text
				numberOfLines={1}
				style={[
					styles().fs18,
					styles().fontBold,
					styles().flex,
					styles().mh15,
					styles().textCenter,
					{
						color: currentTheme.blackish,
					},
				]}
			>
				{props.pagetitle}
			</Text>

			<View style={[styles().flexRow, styles().alignCenter, styles().justifyEnd]}>
				{/* <TouchableOpacity
          activeOpacity={0.7}
          style={[
            styles().wh35px,
            styles().alignCenter,
            styles().justifyCenter,
            styles().br50,
            styles().mr10,
            {backgroundColor: currentTheme.EEE8D5},
          ]}>
          <FontAwesome
            name="search"
            size={16}
            color={currentTheme.themeBackground}
          />
        </TouchableOpacity> */}

				{newMessage
					? <View
							style={{
								position: 'absolute',
								height: 13,
								width: 13,
								borderRadius: 20,
								backgroundColor: currentTheme.red,
								zIndex: 100,
								top: 2,
								right: -3,
								borderWidth: 1.5,
								borderColor: currentTheme.white,
							}}
						/>
					: null}
				<TouchableOpacity
					onPress={() => {
						navigation.navigate('Chats')
						isNewMessage(false)
					}}
					activeOpacity={0.7}
					style={[
						styles().wh35px,
						styles().alignCenter,
						styles().justifyCenter,
						styles().br50,
						styles().pall10,
						styles().overflowH,
						{ backgroundColor: currentTheme.EEE8D5 },
					]}
				>
					<Image
						source={require('../../assets/images/messages.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</TouchableOpacity>
			</View>
		</View>
	)
}
