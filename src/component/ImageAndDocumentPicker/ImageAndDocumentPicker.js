import { useContext, useState } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Platform } from 'react-native'
import { launchCamera, launchImageLibrary } from 'react-native-image-picker'
import Icon from '@expo/vector-icons/MaterialIcons'
import Icon2 from '@expo/vector-icons/MaterialCommunityIcons'
import Icon3 from '@expo/vector-icons/Ionicons'
// import DocumentPicker from 'react-native-document-picker';
import * as DocumentPicker from 'expo-document-picker'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { uploadImageKitProgress, uploadImageToImageKit } from '../Cloudupload/CloudUpload'
import * as FileSystem from 'expo-file-system'
import ImageCropPicker from 'react-native-image-crop-picker'
import ProgressLoader from '../../ProgressLoader/ProgressLoader'
import styles from '../../screens/styles'

export default function ImagePickerToaster({ isImagePicker, setIsImagePicker, setImage, isPdf, isVideo, isCrop, progress, isImage }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { width, height } = Dimensions.get('window')
	const [disbleButtons, setDisbleButtons] = useState(false)
	const [uploadProgress, setUploadProgress] = useState('')
	const [showProgress, setShowProgress] = useState(false)

	const style = StyleSheet.create({
		buttonContainer: {
			backgroundColor: currentTheme.themeBackground,
			borderRadius: 32,
			padding: 6,
			flexDirection: 'row',
			alignItems: 'center',
			justifyContent: 'center',
			width: 100,
			height: 40,
			marginBottom: 10,
			marginRight: 10,
		},
		text: {
			color: currentTheme.white,
			marginHorizontal: 4,
			fontSize: 12,
		},
		mainContainer: {
			position: 'absolute',
			height: '100%',
			width: '100%',
			alignItems: 'flex-end',
			justifyContent: 'flex-end',
			zIndex: 5,
			backgroundColor: 'rgba(0,0,0,0.6)',
		},
	})

	async function VideoPick() {
		await launchImageLibrary(
			{
				title: 'Select video',
				mediaType: 'video',
				videoQuality: 'low',
				// durationLimit: 60,
				allowsEditing: true,
				thumbnail: true,

				// includeBase64: true,
			},
			async res => {
				const fileSize = res.assets[0].fileSize
				const type = res.assets[0]?.type?.split('/')
				const mimeType = type[1]
				if (!res.didCancel) {
					setDisbleButtons(true)
					setShowProgress(true)
					const file = await FileSystem.readAsStringAsync(res.assets[0].uri, {
						encoding: FileSystem.EncodingType.Base64,
					})
					const documentBase64 = res.assets[0].uri ? `data:video/mp4;base64,${file}` : null
					// uploadImageToImageKit(documentBase64).then(res => {
					//   setImage(res.url);
					//   setIsImagePicker(false);
					//   setDisbleButtons(false);
					// });
					try {
						const response = await uploadImageKitProgress(documentBase64, mimeType, fileSize, percentage => {
							// console.log('Upload progress:', percentage);
							progress(percentage)
							setUploadProgress(percentage)
							// Update your UI with the upload progress if needed
						})
						if (response?.url) {
							setUploadProgress(0)
							setImage(response.url)
							setIsImagePicker(false)
							setDisbleButtons(false)
						}
					} catch (error) {
						console.error('Upload failed:', error)
						setUploadProgress(0)
						setIsImagePicker(false)
						setDisbleButtons(false)
						// Handle the error
					}
				}
			}
		)
	}

	async function DocumentPick() {
		// Pick multiple files
		try {
			// const results = await DocumentPicker.pick({
			//   type: [DocumentPicker.types.pdf],
			// });
			const document = await DocumentPicker.getDocumentAsync({
				type: 'application/pdf',
			})
			console.log('DocumnetPicker android:', document)
			console.log('DocumnetPicker ios:', document?.assets[0]?.uri)
			setDisbleButtons(true)
			setShowProgress(false)
			const file = await FileSystem.readAsStringAsync(document?.assets[0]?.uri, {
				encoding: FileSystem.EncodingType.Base64,
			})
			const documentBase64 = document ? `data:application/pdf;base64,${file}` : null
			await uploadImageToImageKit(documentBase64, 'pdf').then(res => {
				setImage(res.url)
				setIsImagePicker(false)
				setDisbleButtons(false)
			})
		} catch (err) {
			if (DocumentPicker.isCancel(err)) {
				setDisbleButtons(false)
				// User cancelled the picker, exit any dialogs or menus and move on
			} else {
				setDisbleButtons(false)
				throw err
			}
		}
	}

	async function CameraPick() {
		try {
			// Permission check skipped
			const result = 'granted'
			if (result === 'granted') {
				console.log('Camera permission granted!')
				await launchCamera(
					{
						mediaType: 'photo',
						quality: 0.5,
						// includeBase64: true,
					},
					async res => {
						console.log('testfile type camera ==========>', res)
						const type = res.assets[0]?.type?.split('/')
						const mimeType = type[1]
						const result = res.assets[0].uri
						var cropPick = ''
						if (!res.didCancel) {
							setDisbleButtons(true)
							setShowProgress(false)
							if (isCrop) {
								cropPick = await ImageCropPicker.openCropper({
									path: result,
									cropperCircleOverlay: true,
									// Other options as needed
								})
							}
							const file = await FileSystem.readAsStringAsync(isCrop ? cropPick?.path : result, {
								encoding: FileSystem.EncodingType.Base64,
							})
							const documentBase64 = cropPick?.path || result ? `data:image/png;base64,${file}` : null
							uploadImageToImageKit(documentBase64, mimeType).then(res => {
								setImage(res.url)
								setIsImagePicker(false)
								setDisbleButtons(false)
							})
						}
					}
				)
			} else {
				console.log('Camera permission denied.')
				// Handle the case where permission is denied
			}
		} catch (error) {
			console.error('Error requesting camera permission:', error)
			// Handle the error if any
		}
	}

	// async function CameraPick() {
	//   await launchCamera(
	//     {
	//       mediaType: 'photo',
	//       quality: 0.5,
	//       // includeBase64: true,
	//     },
	//     async res => {
	//       console.log('testfile type camera ==========>', res);
	//       let type = res.assets[0]?.type?.split('/');
	//       let mimeType = type[1];
	//       let result = res.assets[0].uri;
	//       var cropPick = '';
	//       if (!res.didCancel) {
	//         setDisbleButtons(true);
	//         if (isCrop) {
	//           cropPick = await ImageCropPicker.openCropper({
	//             path: result,
	//             cropperCircleOverlay: true,
	//             // Other options as needed
	//           });
	//         }
	//         const file = await FileSystem.readAsStringAsync(
	//           isCrop ? cropPick?.path : result,
	//           {
	//             encoding: FileSystem.EncodingType.Base64,
	//           },
	//         );
	//         let documentBase64 =
	//           cropPick?.path || result ? `data:image/png;base64,${file}` : null;
	//         uploadImageToImageKit(documentBase64, mimeType).then(res => {
	//           setImage(res.url);
	//           setIsImagePicker(false);
	//           setDisbleButtons(false);
	//         });
	//       }
	//     },
	//   );
	// }

	async function LibraryPick() {
		try {
			await launchImageLibrary(
				{
					mediaType: 'photo',
					quality: 0.5,
					// includeBase64: true,
				},
				async res => {
					console.log('testfile type camera ==========>', res)
					const type = res.assets[0]?.type?.split('/')
					const mimeType = type[1]
					const result = res.assets[0].uri
					var cropPick
					if (!res.didCancel) {
						setDisbleButtons(true)
						setShowProgress(false)
						if (isCrop) {
							cropPick = await ImageCropPicker.openCropper({
								path: result,
								cropperCircleOverlay: true,
								// Other options as needed
							})
						}
						console.log('cropPick:', cropPick)
						const file = await FileSystem.readAsStringAsync(isCrop ? cropPick?.path : result, {
							encoding: FileSystem.EncodingType.Base64,
						})
						const documentBase64 = cropPick?.path || result ? `data:image/png;base64,${file}` : null

						uploadImageToImageKit(documentBase64, mimeType).then(res => {
							setImage(res.url)
							setIsImagePicker(false)
							setDisbleButtons(false)
						})
					}
				}
			)
		} catch (error) {
			console.log('LibraryPick catch :', error)
			setDisbleButtons(false)
		}
	}

	if (disbleButtons) {
		return (
			<View
				style={{
					...style.mainContainer,
					alignItems: 'center',
					justifyContent: 'center',
					backgroundColor: 'rgba(0,0,0,0.7)',
				}}
			>
				{showProgress
					? <ProgressLoader progress={uploadProgress} />
					: <Text style={[styles().fs16, styles().fontMedium, { color: currentTheme.themeBackground }]}>Uploading...</Text>}
			</View>
		)
	}

	async function Remove() {
		setImage('')
		setIsImagePicker(false)
		setDisbleButtons(false)
	}
	return (
		isImagePicker && (
			<TouchableOpacity
				activeOpacity={1}
				onPress={() => setIsImagePicker(false)}
				style={style.mainContainer}
			>
				{!isVideo && isImage
					? <TouchableOpacity
							activeOpacity={0.8}
							onPress={() => Remove()}
						>
							<View style={{ ...style.buttonContainer, backgroundColor: currentTheme.red }}>
								<Icon
									name="cancel"
									size={22}
									color={currentTheme.white}
								/>
								<Text style={style.text}>Remove</Text>
							</View>
						</TouchableOpacity>
					: null}
				{!isVideo && (
					<TouchableOpacity
						activeOpacity={0.8}
						onPress={() => CameraPick()}
					>
						<View style={style.buttonContainer}>
							<Icon
								name="camera"
								size={22}
								color={currentTheme.white}
							/>
							<Text style={style.text}>Camera</Text>
						</View>
					</TouchableOpacity>
				)}
				{!isVideo && (
					<TouchableOpacity
						activeOpacity={0.8}
						onPress={() => LibraryPick()}
					>
						<View style={style.buttonContainer}>
							<Icon2
								name="file-image"
								size={22}
								color={currentTheme.white}
							/>
							<Text style={style.text}>Gallery</Text>
						</View>
					</TouchableOpacity>
				)}

				{isVideo && (
					<TouchableOpacity
						activeOpacity={0.8}
						onPress={() => VideoPick()}
					>
						<View style={style.buttonContainer}>
							<Icon3
								name="videocam"
								size={20}
								color={currentTheme.white}
							/>
							<Text style={style.text}>Video</Text>
						</View>
					</TouchableOpacity>
				)}

				{isPdf && (
					<TouchableOpacity
						activeOpacity={0.8}
						onPress={() => DocumentPick()}
					>
						<View style={style.buttonContainer}>
							<Icon3
								name="document-attach"
								size={20}
								color={currentTheme.white}
							/>
							<Text style={style.text}>PDF</Text>
						</View>
					</TouchableOpacity>
				)}
			</TouchableOpacity>
		)
	)
}
