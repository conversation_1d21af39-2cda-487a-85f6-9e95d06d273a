import { useContext, useEffect, useRef } from 'react'
import { View, Text, StyleSheet, Animated } from 'react-native'
import { AnimatedCircularProgress } from 'react-native-circular-progress'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const NeonCircularProgress = ({ progress }) => {
	const animatedValue = useRef(new Animated.Value(0)).current
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	useEffect(() => {
		animateProgress()
	}, [progress])

	const animateProgress = () => {
		Animated.timing(animatedValue, {
			toValue: progress,
			duration: (progress / 100) * 500, // Adjust the duration as needed
			useNativeDriver: false,
		}).start()
	}

	return (
		<View style={styles.container}>
			<AnimatedCircularProgress
				size={100}
				width={8}
				fill={animatedValue}
				tintColor="#A79441"
				backgroundColor={currentTheme.F3F3F3}
			>
				{fill => <Text style={styles.progressText}>{`${Math.round(fill)}%`}</Text>}
			</AnimatedCircularProgress>
		</View>
	)
}

const styles = StyleSheet.create({
	container: {
		alignItems: 'center',
		justifyContent: 'center',
	},
	progressText: {
		fontFamily: 'Poppins-Medium',
		fontSize: 14,
		color: '#A79441', // Neon color
	},
})

export default NeonCircularProgress
