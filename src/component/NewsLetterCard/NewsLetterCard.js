import { View, Text, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import moment from 'moment'
import { openLink } from '../../utils/InAppBrowser'
import { pdfViewerUrl } from '../../utils/Constants'

const NewsLetterCard = ({ item, index }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const handleNewsLetter = item => {
		const newsletter = pdfViewerUrl(item?.pdf_link)
		console.log('newsletter', newsletter)
		if (newsletter) {
			openLink(newsletter)
		} else {
			console.log('no link found')
		}
	}

	return (
		<TouchableOpacity
			activeOpacity={0.8}
			onPress={() => handleNewsLetter(item)}
			key={index}
			style={[
				styles().bw1,
				styles().w100,
				styles().ph15,
				styles().pv15,
				styles().mb10,
				styles().br10,
				styles().flexRow,
				styles().alignCenter,
				styles().justifyStart,
				{ borderColor: currentTheme.C3C3C3 },
			]}
		>
			<View style={[styles().br5, styles().wh50px, styles().alignCenter, styles().justifyCenter, { backgroundColor: currentTheme.themeBackground }]}>
				<FontAwesome5
					solid
					name={'file-pdf'}
					size={30}
					color={currentTheme.white}
				/>
			</View>
			<View style={[styles().mh10, styles().alignStart, styles().justifyStart]}>
				<Text
					numberOfLines={2}
					style={[
						styles().fs14,
						styles().fontMedium,
						{
							color: currentTheme.black,
						},
					]}
				>
					{item?.title}
				</Text>
				<Text
					style={[
						styles().fs12,
						styles().mt5,
						styles().fontMedium,
						{
							color: currentTheme.C3C3C3,
						},
					]}
				>
					{`Upload on ${moment(new Date(item?.createdAt)).format('LL')}`}
				</Text>
			</View>
		</TouchableOpacity>
	)
}

export default NewsLetterCard
