import { TouchableOpacity, View } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const CheckBox = ({ value, setValue }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_isCheck, _setIsCheck] = useState(false)

	return (
		<TouchableOpacity
			activeOpacity={0.7}
			onPress={() => {
				setValue(!value)
			}}
			style={[styles().flexRow, styles().alignCenter]}
		>
			<View
				style={[
					styles().wh20px,
					styles().alignCenter,
					styles().justifyCenter,
					styles().br5,
					{
						backgroundColor: value ? currentTheme.themeBackground : 'transparent',
						borderWidth: 1,
						borderColor: value ? currentTheme.themeBackground : currentTheme.C3C3C3,
					},
				]}
			>
				{value
					? <FontAwesome
							name="check"
							size={14}
							color={value ? currentTheme.white : currentTheme.themeBackground}
						/>
					: null}
			</View>
		</TouchableOpacity>
	)
}

export default CheckBox
