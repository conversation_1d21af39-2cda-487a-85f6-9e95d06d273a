import { useState, useEffect, useContext } from 'react'
import { Text, View } from 'react-native'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const CountdownTimer = ({ targetTime }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [timer, setTimer] = useState('')

	useEffect(() => {
		const ALLOWED_TIME = 2 * 60 * 60 * 1000 // 2 hours in milliseconds
		const targetDate = new Date(targetTime).getTime()
		const endDate = targetDate + ALLOWED_TIME // Add 2 hours to target time

		const updateTimer = () => {
			const now = Date.now()
			const distance = endDate - now

			if (distance <= 0) {
				// Timer has reached zero
				setTimer('00:00:00')
				clearInterval(timerId)
				return
			}

			const hours = String(Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))).padStart(2, '0')
			const minutes = String(Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))).padStart(2, '0')
			const seconds = String(Math.floor((distance % (1000 * 60)) / 1000)).padStart(2, '0')

			setTimer(`${hours}:${minutes}:${seconds}`)
		}

		// Initial call to set the timer immediately
		updateTimer()

		// Update the timer every second
		const timerId = setInterval(updateTimer, 1000)

		return () => clearInterval(timerId)
	}, [targetTime])

	if (timer === '00:00:00') {
		return null
	}
	return (
		<View
			style={[styles().br100, styles().mb10, styles().ph15, styles().pv5, styles().flexRow, styles().alignCenter, { backgroundColor: currentTheme.FFE5E5 }]}
		>
			{targetTime
				? <>
						<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.red }]}>{'Booking Expire in '}</Text>
						<Text style={[styles().fs10, styles().fontMedium, { color: currentTheme.red }]}>{timer}</Text>
					</>
				: null}
		</View>
	)
}

export default CountdownTimer
