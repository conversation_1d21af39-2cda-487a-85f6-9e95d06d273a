import { ActivityIndicator } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

export default function Spinner(props) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<ActivityIndicator
			size={props.size ? props.size : 30}
			color={props.color ? props.color : currentTheme.themeBackground}
		/>
	)
}
