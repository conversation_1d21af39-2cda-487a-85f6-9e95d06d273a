import { Text, View, TouchableOpacity, Image, Share } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Ionicons from '@expo/vector-icons/Ionicons'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import { renderText } from '../../utils/Constants'

const CheckRideComponent = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, index, checkride, navigation } = props

	const share = async item => {
		if (item?.company?.website !== null) {
			try {
				const result = await Share.share({
					message: item?.company?.website, //shared item
					url: item?.company?.website, //ios
					title: item?.company?.name, //android
				})
				if (result.action === Share.sharedAction) {
					if (result.activityType) {
						// shared with activity type of result.activityType
					} else {
						// shared
					}
				} else if (result.action === Share.dismissedAction) {
					// dismissed
				}
			} catch (error) {
				// FlashMessage({msg: error.message, type: 'danger'});
				console.log('share catch :', error)
			}
		} else {
			// FlashMessage({ msg: "", type: "danger" });
		}
		console.log('share :', item?.company?.website)
	}

	// console.log(item?._id)

	const pending = item?.offers?.find(item => item.status === 'pending')
	const _accepted = item?.offers?.find(item => item.status === 'accepted')
	const _active = item?.offers?.find(item => item.status === 'active')

	return (
		<TouchableOpacity
			onPress={() => {
				navigation.navigate('MyCheckRides', { checkride: item, activeTab: pending ? 0 : 1 })
			}}
			activeOpacity={0.8}
			style={[
				styles().flexRow,
				styles().pb15,
				styles().pt20,
				styles().ph15,
				styles().mb20,
				index === 0 ? styles().mt20 : null,
				styles().bw1,
				styles().br10,
				{
					borderColor: pending ? currentTheme.themeBackground : currentTheme.CFCFCF,
				},
			]}
		>
			{pending
				? <View
						style={[
							styles().posAbs,
							styles().flexRow,
							styles().alignCenter,
							styles().pv5,
							styles().br50,
							styles().ph15,
							styles().left10,
							{ top: -12, backgroundColor: currentTheme.themeBackground },
						]}
					>
						<Text style={[styles().fs9, styles().fw600, { color: currentTheme.white }]}>{'New Offer'}</Text>
					</View>
				: null}
			<View style={[styles().posAbs, styles().flexRow, styles().alignCenter, styles().top10, styles().right10]}>
				{/* {item.offers.length !== 0 ? (
          <TouchableOpacity activeOpacity={0.7}>
            <MaterialCommunityIcons
              name="comment-text-outline"
              size={18}
              color={currentTheme.E8E8C8}
            />
          </TouchableOpacity>
        ) : null} */}
				<TouchableOpacity
					onPress={() => share(item)}
					activeOpacity={0.5}
					style={[styles().ph5]}
				>
					<FontAwesome
						name="share"
						size={14}
						color={currentTheme.E8E8C8}
					/>
				</TouchableOpacity>
			</View>
			<View
				style={[
					styles().wh50px,
					styles().br25,
					styles().overflowH,
					styles().justifyCenter,
					styles().alignCenter,
					styles().mr10,
					{
						borderWidth: 0.5,
						borderColor: currentTheme.themeBackground,
					},
				]}
			>
				{item?.company?.photo
					? <Image
							source={{ uri: item?.company?.photo }}
							resizeMode="cover"
							style={styles().wh100}
						/>
					: <MaterialCommunityIcons
							name="city-variant"
							size={25}
							color={currentTheme.themeBackground}
						/>}
			</View>
			<View style={[styles().flex]}>
				<Text style={[styles().fs12, styles().fontBold, styles().lh20, styles().textCapitalize, { color: currentTheme.black }]}>{item?.company?.name}</Text>
				<View
					style={[
						styles().flexRow,
						styles().alignCenter,
						//   styles().mb10,
						styles().flex,
						styles().flexWrap,
						// {backgroundColor: 'teal'},
					]}
				>
					<View
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().mr10,
							// styles().mb10,
						]}
					>
						<Ionicons
							name="cash-outline"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs9, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{`$${item?.company?.checkRideData?.price}`}</Text>
					</View>
				</View>

				<View style={[styles().mt10]}>
					<Text
						numberOfLines={2}
						style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
					>
						{renderText(item?.company?.checkRideData?.terms)}
					</Text>
				</View>
			</View>
		</TouchableOpacity>
	)
}

export default React.memo(CheckRideComponent)
