import React, { useContext } from 'react'
import { ActivityIndicator, View, StyleSheet } from 'react-native'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

function Loader(props) {
	if (!props.loading) {
		return null
	}
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<View style={style.mainContainer}>
			<View style={style.background} />
			<ActivityIndicator
				size="large"
				color={currentTheme.themeBackground}
			/>
			{/* <Lottie
        source={require('./../../assets/other/loader2.json')}
        autoPlay={true}
        loop={true}
        style={{height: 50, width: 50}}
      /> */}
		</View>
	)
}

export default React.memo(Loader)

const style = StyleSheet.create({
	mainContainer: {
		width: '100%',
		height: '100%',
		alignItems: 'center',
		justifyContent: 'center',
		position: 'absolute',
		// backgroundColor: "red",
		alignSelf: 'center',
		elevation: 10,
		zIndex: 190000,
	},
	text: {
		fontSize: 16,
		// fontWeight: '',
		color: 'black',
		bottom: 14,
		left: 5,
	},
	loaderContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'center',
		height: 90,
		backgroundColor: 'white',
		width: '80%',
		borderRadius: 4,
	},
	background: {
		width: '100%',
		height: '100%',
		position: 'absolute',
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'rgba(17, 17, 17, 0.5)',
	},
})
