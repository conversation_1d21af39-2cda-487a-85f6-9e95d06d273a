import { Text, View, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'

const Badges = ({ badge, navigation, index, callback }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const a = badge?.company?.name?.split(' ') || []
	const b = a[0] || ''
	const c = a[1] || ''
	let initials = ''
	if (c !== '') {
		initials = (b[0]?.toUpperCase() || '') + (c[0]?.toUpperCase() || '')
	} else {
		initials = b[0]?.toUpperCase() || ''
	}
	// initials = b[0]?.toUpperCase() + c[0]?.toUpperCase();
	// console.log(badge?.company?.badge ? 'Badge' : badge?.company?.photo ? 'photo' : initials);
	// console.log(badge)

	const ShowBadge = () => {
		return (
			<TouchableOpacity
				activeOpacity={1}
				// onPress={() => callback(badge)}
				key={index}
				style={[
					styles().wh50px,
					styles().overflowH,
					styles().br100,
					styles().mr15,
					styles().alignCenter,
					styles().justifyCenter,
					{
						borderWidth: (badge?.company?.badge && badge.company.badge.trim() !== '') ? 0 : (badge?.company?.photo && badge.company.photo.trim() !== '') ? 0 : 1,
						borderColor: currentTheme.themeBackground,
					},
				]}
			>
				{badge?.company?.badge && badge.company.badge.trim() !== ''
					? <Image
							source={{ uri: badge.company.badge }}
							style={styles().wh100}
							resizeMode="cover"
						/>
					: badge?.company?.photo && badge.company.photo.trim() !== ''
						? <Image
								source={{ uri: badge.company.photo }}
								style={styles().wh100}
								resizeMode="cover"
							/>
						: <Text style={[styles().fontMedium, styles().fs18, { color: currentTheme.themeBackground }]}>{initials}</Text>}
			</TouchableOpacity>
		)
	}

	const BadgeCard = () => {
		return (
			<View
				style={[
					styles().br10,
					styles().bw1,
					styles().pv15,
					styles().mb10,
					styles().ph10,
					styles().mall5,
					styles().flexRow,
					{
						borderColor: currentTheme.C3C3C3,
					},
				]}
			>
				<ShowBadge />
				<View style={[styles().flex]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.themeBackground }]}>{badge?.company?.name}</Text>
					{badge?.waterwaysId && Array.isArray(badge.waterwaysId) && badge.waterwaysId.map((item, i) => {
						return (
							<Text
								key={i}
								style={[styles().fs12, styles().fontRegular, styles().flex, styles().mt5, { color: currentTheme.textColor }]}
							>
								{`● ${item?.name || 'Unknown'}`}
							</Text>
						)
					})}
				</View>
			</View>
		)
	}

	return <BadgeCard />
}

export default React.memo(Badges)
