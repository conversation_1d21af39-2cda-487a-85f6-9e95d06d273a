import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete'
import { GOOGLE_API_KEY } from '../../utils/fonts/Constants'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { useContext, useRef, useState } from 'react'
import { theme } from '../../context/ThemeContext/ThemeColor'

const AutoCompleteSearch = ({ result }) => {
	const searchRef = useRef(null)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const handleSelect = (data, details) => {
		// console.log("address_components :", JSON.stringify(details));
		// console.log("data_components :", JSON.stringify(data));
		const state = details?.address_components?.find(component => component?.types?.includes('administrative_area_level_1'))
		const zipCode = details?.address_components?.find(component => component?.types?.includes('postal_code'))
		const city = details?.address_components.find(component => component?.types?.includes('locality'))
		console.log('Selected State:', state?.long_name)
		console.log('ZIP Code:', zipCode)
		result({
			state: state?.long_name,
			address: data?.description,
			zipCode: zipCode?.long_name,
			city: city?.long_name,
		})
	}

	const [isFocused, setIsFocused] = useState(false)

	const handleFocus = () => {
		setIsFocused(true)
	}

	const handleBlur = () => {
		setIsFocused(false)
	}

	return (
		<GooglePlacesAutocomplete
			listViewDisplayed={false}
			ref={searchRef}
			placeholder="Address Line 1"
			onPress={handleSelect}
			styles={{
				textInputContainer: {
					// backgroundColor: theme.Red.themeBackground,
					borderRadius: 15,
					borderWidth: 1,
					borderColor: isFocused ? currentTheme.themeBackground : currentTheme.authInputBg,
					height: 50,
				},
				// container: { height: 70 },
				textInput: {
					fontSize: 14,
					fontWeight: '500',
					//   height: 45,
					borderRadius: 15,
				},
				poweredContainer: {
					height: 0,
					width: 0,
					backgroundColor: 'transparent',
				},
				powered: { height: 0, width: 0 },
			}}
			//   GooglePlacesDetailsQuery={{ fields: "geometry" }}
			query={{
				key: GOOGLE_API_KEY,
				language: 'en',
				// types: ["(regions)"],
				types: ['address'],
				// components: "country:us",
			}}
			onFocus={handleFocus}
			onBlur={handleBlur}
			fetchDetails={true}
			enablePoweredByContainer={false}
		/>
	)
}

export default AutoCompleteSearch
