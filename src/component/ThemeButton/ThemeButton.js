import { useContext } from 'react'
import { Text, TouchableOpacity } from 'react-native'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'

export default function ThemeButton({
	onPress,
	Title,
	Style,

	StyleText,
	withoutBg,
	icon,
	disabled,
}) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<TouchableOpacity
			disabled={disabled}
			activeOpacity={0.7}
			onPress={onPress}
			style={[
				styles(currentTheme).justifyCenter,
				styles(currentTheme).alignCenter,
				styles().h50px,
				styles().overflowH,
				styles().ph25,
				styles().br10,
				styles().bw1,
				styles().mt10,
				icon && styles().flexRow,

				{
					backgroundColor: withoutBg ? currentTheme.cE5E5E5 : currentTheme.themeBackground,
					borderColor: withoutBg ? currentTheme.cE5E5E5 : currentTheme.themeBackground,
				},
				Style,
			]}
		>
			<Text
				numberOfLines={1}
				style={[{ color: withoutBg ? currentTheme.borderColor : currentTheme.white }, styles(currentTheme).fs16, styles().fontMedium, StyleText]}
			>
				{Title}
			</Text>
			{icon && icon}
		</TouchableOpacity>
	)
}
