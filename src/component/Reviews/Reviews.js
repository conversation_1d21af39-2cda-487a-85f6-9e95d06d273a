import { Text, View, TouchableOpacity, Image } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import StarRating from 'react-native-star-rating-widget'
import moment from 'moment'

const ReviewsComponent = ({ item }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [readmore, setReadmore] = useState(2)
	const _starCount = 3

	const Stars = ({ star }) => {
		return (
			<StarRating
				onChange={() => {}}
				enableHalfStar={true}
				starSize={18}
				starStyle={styles().ml5}
				maxStars={5}
				rating={star}
				color={currentTheme.starColor}
				emptyColor={currentTheme.e6e6e6}
			/>
		)
	}

	return (
		<View style={[styles().boxpeshadowCart, styles().mb15, styles().mt5, styles().br10, styles().pv15, styles().overflowH]}>
			<View style={[styles().flexRow, styles().ph15, styles().mb10, styles().justifyBetween, styles().alignCenter]}>
				<View style={[styles().flexRow, styles().alignCenter]}>
					<View
						style={[
							styles().wh45px,
							styles().br100,
							styles().overflowH,
							styles().alignCenter,
							styles().justifyCenter,
							{ borderWidth: 1, borderColor: currentTheme.themeBackground },
						]}
					>
						{item?.by?.photo
							? <Image
									source={{ uri: item?.by?.photo }}
									style={styles().wh100}
									resizeMode="cover"
								/>
							: <MaterialCommunityIcons
									name="city-variant"
									size={25}
									color={currentTheme.themeBackground}
								/>}
					</View>
					<View style={[styles().ml10]}>
						<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>{item?.by?.name ? item?.by?.name : 'N/A'}</Text>
						<Text style={[styles().fs11, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).fromNow()}</Text>
					</View>
				</View>
				<View style={[styles().flexRow, styles().alignCenter]}>
					<FontAwesome
						name="star"
						size={20}
						color={currentTheme.starColor}
					/>
					<Text style={[styles().fs20, styles().ml5, styles().fontMedium, { color: currentTheme.headingColor }]}>{item?.avgRating?.toFixed(1)}</Text>
				</View>
			</View>

			<View style={[styles().ph15]}>
				<View style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>Performance</Text>
					<View style={[styles().flexRow, styles().justifyBetween]}>
						<Stars star={item.performance} />
					</View>
				</View>
				<View style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>Punctaulity</Text>
					<View style={[styles().flexRow, styles().justifyBetween]}>
						<Stars star={item?.punctuality} />
					</View>
				</View>
				<View style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>Productivity</Text>
					<View style={[styles().flexRow, styles().justifyBetween]}>
						<Stars star={item?.productivity} />
					</View>
				</View>
				<View style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>Reliability</Text>
					<View style={[styles().flexRow, styles().justifyBetween]}>
						<Stars star={item?.reliability} />
					</View>
				</View>
				<View style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.headingColor }]}>Professional</Text>
					<View style={[styles().flexRow, styles().justifyBetween]}>
						<Stars star={item?.professional_development} />
					</View>
				</View>
			</View>
			{item?.review
				? <View style={[styles().btw1, styles().pt10, styles().mt10, styles().ph10, { borderTopColor: currentTheme.e6e6e6 }]}>
						<View style={[styles().alignStart]}>
							<Text
								numberOfLines={readmore}
								style={[styles().fs12, styles().fontRegular, { color: currentTheme.B7B7B7 }]}
							>
								{item?.review}
							</Text>
							{readmore && item?.review?.length > 100
								? <TouchableOpacity
										activeOpacity={0.5}
										onPress={() => setReadmore(undefined)}
									>
										<Text style={[styles().fs12, { color: currentTheme.c737373 }]}>read more</Text>
									</TouchableOpacity>
								: null}
						</View>
					</View>
				: null}
		</View>
	)
}

export default ReviewsComponent
