import { Platform, StyleSheet } from 'react-native'
import { showMessage } from 'react-native-flash-message'
import fontStyles from '../../utils/fonts/fontStyles'

export const flashstyle = StyleSheet.create({
	style: Platform.OS !== 'ios' && {
		position: 'absolute',
		top: 40,
		left: 15,
		right: 15,
		borderRadius: 10,
		zIndex: 9999999,
		justifyContent: 'center',
		alignContent: 'center',
	},
})

export default function FlashMessage({ desc, msg, type, position, icon, backgroundColor, duration }) {
	return showMessage({
		description: desc,
		message: msg,
		type: type,
		position: position ? position : 'top',
		icon: icon ? icon : 'auto',
		// animated: true,
		backgroundColor: backgroundColor,
		style: flashstyle.style,
		titleStyle: { fontFamily: fontStyles.PoppinsMedium },
		textStyle: { fontFamily: fontStyles.PoppinsRegular },
		// animationDuration: 400,
		height: Platform.OS === 'android' ? 0.2 : 0.025,
		duration: duration ? duration : 4000,
	})
}
