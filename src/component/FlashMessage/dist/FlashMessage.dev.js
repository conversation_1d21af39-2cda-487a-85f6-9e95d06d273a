Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.default = FlashMessage
exports.flashstyle = void 0

var _react = _interopRequireDefault(require('react'))

var _reactNative = require('react-native')

var _reactNativeFlashMessage = require('react-native-flash-message')

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var flashstyle = _reactNative.StyleSheet.create({
	style: _reactNative.Platform.OS === 'android' && {
		position: 'absolute',
		top: 40,
		left: 15,
		right: 15,
		borderRadius: 10,
		zIndex: 9999999,
		justifyContent: 'center',
		alignContent: 'center',
	},
})

exports.flashstyle = flashstyle

function FlashMessage(_ref) {
	var msg = _ref.msg
	var type = _ref.type
	return (0, _reactNativeFlashMessage.showMessage)({
		message: msg,
		type: type,
		position: 'top',
		style: flashstyle.style,
		height: _reactNative.Platform.OS === 'android' ? 0.2 : 0.025,
		duration: 4000,
	})
}
