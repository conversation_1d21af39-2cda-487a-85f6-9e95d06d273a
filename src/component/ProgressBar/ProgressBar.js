import { Dimensions, Text, View } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'

const ProgressBar = ({ active, done }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { width, height } = Dimensions.get('window')
	const bar = [
		{ id: 1, title: '' },
		{ id: 2, title: '' },
		{ id: 3, title: '' },
		// {id: 4, title: ''},
		// {id: 5, title: ''},
		// {id: 6, title: ''},
	]

	return (
		<View
			style={[
				styles().flexRow,
				styles().alignCenter,
				styles().justifyCenter,
				styles().justifyBetween,
				// {backgroundColor: 'teal'},
			]}
		>
			<View
				style={[
					styles().mt20,
					// styles().bw1,
					styles().w100,
					styles().posAbs,
					styles().top0,
					styles().alignSelfCenter,
					// {borderColor: currentTheme.themeBackground,}
				]}
			/>
			{bar?.map((item, i) => {
				return (
					<View
						key={i}
						style={[
							styles().justifyBetween,
							{
								// width: '15%',
								// flex: 1,
								// backgroundColor:'red'
								// alignItems:
								//   i === 0 ? 'flex-start' : i === 5 ? 'flex-end' : 'center',
								// marginRight: i === 1 ? 10 : 0,
								// marginLeft: i === 2 ? 10 : i === 3 ? 20 : 0,
							},
						]}
					>
						{i === bar.length - 1
							? null
							: <View
									style={[
										styles().posAbs,
										// styles().w100,
										// styles().flex,
										styles().top15,
										styles().bw1,

										{
											// width: '30%',
											width: width / 2.2,
											backgroundColor: 'yellow',
											borderColor:
												i === 0
													? currentTheme.themeBackground
													: active === item.id
														? currentTheme.themeBackground
														: done.includes(item.id)
															? currentTheme.themeBackground
															: currentTheme.C3C3C3,
											// right:
											//   i === 0
											//     ? '-35%'
											//     : i === 2
											//     ? '-65%'
											//     : active === item.id
											//     ? '-55%'
											//     : '-65%',
										},
									]}
								/>}
						<View
							style={[
								styles().wh30px,
								styles().br20,
								styles().alignCenter,
								styles().justifyCenter,
								{
									backgroundColor:
										active === item.id ? currentTheme.themeBackground : done.includes(item.id) ? currentTheme.themeBackground : currentTheme.white,
									borderWidth: active === item.id ? 0 : 1,
									borderColor:
										i === 0
											? currentTheme.themeBackground
											: active === item.id
												? currentTheme.themeBackground
												: done.includes(item.id)
													? currentTheme.themeBackground
													: currentTheme.C3C3C3,
								},
							]}
						>
							<Text
								style={[
									styles().fw700,
									{
										fontWeight: 'bold',
										fontSize: 14,
										color: active === item.id ? 'white' : done.includes(item.id) ? currentTheme.white : currentTheme.C3C3C3,
									},
								]}
							>
								{item.id}
							</Text>
						</View>
					</View>
				)
			})}
		</View>
	)
}

export default ProgressBar
