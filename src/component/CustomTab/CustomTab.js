import React, { useContext } from 'react'
import { Image, View, Dimensions, StyleSheet, TouchableOpacity, Platform } from 'react-native'
import { useIOSSafeBottom } from '../../utils/SafeAreaUtils'

import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Ionicons from '@expo/vector-icons/Ionicons'
import Foundation from '@expo/vector-icons/Foundation'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'

import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

// import fontStyles from "../../utils/fonts/fontStyles";
import styless from '../../screens/styles'
import NotificationContext from '../../context/Notification/Notification'

const { width, height } = Dimensions.get('screen')

function CustomTabs(props) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const notifications = useContext(NotificationContext)
	const iosSafeBottom = useIOSSafeBottom()

	const { state, descriptors, navigation, fill } = props

	const routeName = props.state.routes[props.state.index]?.state?.routes[props?.state?.routes[props.state.index]?.state?.index]?.name
	// console.log('routeName', props.state.routes[props.state.index]);
	// console.log('routeName', routeName);
	const _parentName = props.state.routes[props.state.index]?.name

	if (
		routeName === 'Chat' ||
		routeName === 'Chats' ||
		routeName === 'JobDetails' ||
		routeName === 'CompanyDetails' ||
		routeName === 'JobDetails' ||
		routeName === 'Settings' ||
		routeName === 'CheckRideStatus' ||
		routeName === 'MyCheckRides' ||
		routeName === 'Help' ||
		routeName === 'EditEducation' ||
		routeName === 'EditWork' ||
		routeName === 'EditOtherDoc' ||
		routeName === 'EditTWIC' ||
		routeName === 'HomeSearch' ||
		routeName === 'ViewFriendProfile' ||
		routeName === 'ReportIssue' ||
		routeName === 'AllIssues' ||
		routeName === 'PostDetails' ||
		routeName === 'ThreadDetails' ||
		routeName === 'AddBankAccount' ||
		routeName === 'Recommendation' ||
		routeName === 'Recovery' ||
		routeName === 'RecoveryVerification' ||
		routeName === 'Licensing' ||
		routeName === 'LicenseQuestAns' ||
		routeName === 'LicenseQuestAnsDetail' ||
		routeName === 'NearbyCaptain' ||
		routeName === 'ApplyForVerify' ||
		routeName === 'NearbyCaptain' ||
		routeName === 'EditBio' ||
		routeName === 'ChangePassword' ||
		routeName === 'Payments' ||
		routeName === 'PaymentDetails' ||
		routeName === 'ToeBoater' ||
		routeName === 'RBPVerification' ||
		routeName === 'RBPForm' ||
		routeName === 'AppointmentBooking' ||
		routeName === 'Appointments' ||
		routeName === 'AppointmenetDetails' ||
		routeName === 'Weather' ||
		routeName === 'LearnWaterWaysDetail' ||
		routeName === 'EditRoutes' ||
		routeName === 'NewsLetter' ||
		routeName === 'AllBadges'
	) {
		return null
	}

	return (
		<View style={styles(currentTheme).footerContainer}>
			<View style={[styles(currentTheme).footerArea, { paddingBottom: iosSafeBottom }]}>
				{state.routes.length > 0 &&
					state.routes.map((d, index) => {
						// console.log(d.name);
						let icon
						if (d.name === 'Jobs') {
							icon = (
								<Ionicons
									name="briefcase"
									size={24}
									color={state.index === index ? currentTheme.themeBackground : currentTheme.D9D9D9}
								/>
							)
						} else if (d.name === 'Videos') {
							icon = (
								<FontAwesome5
									name="user-friends"
									size={22}
									color={state.index === index ? currentTheme.themeBackground : currentTheme.D9D9D9}
								/>
							)
						} else if (d.name === 'Home') {
							icon = (
								<Foundation
									name="home"
									size={34}
									color={state.index === index ? currentTheme.themeBackground : currentTheme.D9D9D9}
								/>
							)
						} else if (d.name === 'NotificationStack') {
							icon = (
								<MaterialCommunityIcons
									name="bell"
									size={24}
									color={state.index === index ? currentTheme.themeBackground : currentTheme.D9D9D9}
								/>
							)
						} else if (d.name === 'Menu') {
							icon = (
								<Image
									source={require('../../assets/images/menuicon.png')}
									style={{
										height: 24,
										width: 24,
										tintColor: state.index === index ? currentTheme.themeBackground : currentTheme.D9D9D9,
									}}
									resizeMode="contain"
								/>
							)
						}
						//  else if (d.name === "Tickets") {
						//   icon = state.index === index ? <TicketActive /> : <Ticket />;
						// } else if (d.name === "Notifications") {
						//   icon =
						//     state.index === index ? (
						//       <NotificationsActive />
						//     ) : (
						//       <Notifications />
						//     );
						// } else if (d.name === "Draws") {
						//   icon = state.index === index ? <DrawsActive /> : <Draws />;
						// } else if (d.name === "Cart") {
						//   icon = state.index === index ? <CartActive /> : <Cart />;
						// }

						return (
							<TouchableOpacity
								key={index}
								style={[styles(currentTheme).footer, { padding: 4 }]}
								onPress={() => {
									navigation.navigate(d.name, { screen: d.name })
								}}
							>
								{d.name === 'NotificationStack' && notifications?.unreads > 0
									? <View
											style={{
												position: 'absolute',
												height: 11,
												width: 11,
												borderRadius: 20,
												backgroundColor: currentTheme.red,
												zIndex: 10,
												top: 6,
												left: 18,
												borderWidth: 1,
												borderColor: currentTheme.white,
											}}
										/>
									: null}
								<View style={[styless().alignCenter, styless().justifyCenter]}>{icon}</View>
							</TouchableOpacity>
						)
					})}
			</View>
		</View>
	)
}
export default React.memo(CustomTabs)

const styles = (props = null) =>
	StyleSheet.create({
		footerContainer: {
			backgroundColor: 'transparent',
		},
		footerArea: {
			// height: 0,
			paddingVertical: 10,
			paddingHorizontal: 20,
			backgroundColor: props !== null ? props.white : 'transparent',
			borderTopLeftRadius: 20,
			borderTopRightRadius: 20,
			// position:'absolute',
			width: '100%',
			flexDirection: 'row',
			alignItems: 'center',
			justifyContent: 'space-between',
			shadowColor: '#000',
			shadowOffset: {
				width: 0,
				height: 2,
			},
			shadowOpacity: 0.25,
			shadowRadius: 2.58,
			elevation: 4,
		},
		footerText: {
			fontSize: 20,
			lineHeight: 24,
			color: '#FFFFFF',
			paddingHorizontal: 10,
		},
		footer: {
			justifyContent: 'center',
			alignItems: 'center',
		},
		addButtonFooter: {
			// backgroundColor:props !== null ? props.themeBackground : 'transparent',
			borderRadius: 10,
			width: 35,
			height: 35,
			alignItems: 'center',
			justifyContent: 'center',
		},
		linearGradient: {
			width: 35,
			height: 35,
			borderRadius: 10,
			overflow: 'hidden',
			alignItems: 'center',
			justifyContent: 'center',
		},
	})
