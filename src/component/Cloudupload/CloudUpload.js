import ImageKit from 'imagekit-javascript'
import getEnvVars from '../../../environment'
const { IMAGEKIT_AuthenticationEndpoint, IMAGEKIT_PUBLIC_KEY, IMAGEKIT_URL_ENDPOINT } = getEnvVars()

const CLOUD_NAME = 'dvb3q9dsq'
const CLOUDINARY_PRESET = 'mnh4qyn1'
const CLOUDINARY_URL = `https://api.cloudinary.com/v1_1/${CLOUD_NAME}/image/upload`

var imagekit = new ImageKit({
	publicKey: IMAGEKIT_PUBLIC_KEY,
	urlEndpoint: IMAGEKIT_URL_ENDPOINT,
	authenticationEndpoint: `${IMAGEKIT_AuthenticationEndpoint}`,
})

async function uploadImageToCloudinary(image) {
	if (image === '') {
		return image
	}

	// const CLOUDINARY_URL = "https://node.hostingladz.com:3033/v1/user/upload";

	const apiUrl = CLOUDINARY_URL
	const data = {
		file: image,
		upload_preset: CLOUDINARY_PRESET,
	}
	try {
		const result = await fetch(apiUrl, {
			body: JSON.stringify(data),
			headers: {
				'content-type': 'application/json',
			},
			method: 'POST',
		})
		const cloudData = await result.json()
		console.log('Cloudinary res :', cloudData)
		return cloudData.secure_url
	} catch (e) {
		console.log(e)
	}
}

async function uploadToImageKit(file, base64) {
	return new Promise((resolve, reject) => {
		imagekit.upload(
			{
				file: base64,
				fileName: file.name,
			},
			(err, result) => {
				if (err) reject(err)
				resolve(result)
			}
		)
	})
		.then(async newdata => {
			console.log('imageKit result:', newdata)
			const _url = imagekit.url({
				src: newdata.url,
				// transformation: [{ height: 300, width: 400 }],
			})

			return newdata
		})
		.catch(err => {
			console.log('imageKit err:', err)
			return ''
		})
}

async function uploadImageToImageKit(file, mimeType) {
	// console.log('file data :', file);
	return new Promise((resolve, reject) => {
		imagekit.upload(
			{
				file: file,
				fileName: `file.${mimeType}`,

				//you can change this and generate your own name if required
				// tags: ["tag-1", "tag-2"], //change this or remove it if you want
			},
			(err, result) => {
				if (err) reject('error here :', err)
				resolve(result)
			}
		)
	})
		.then(async newdata => {
			console.log('imageKit result:', newdata)
			const _url = imagekit.url({
				src: newdata.url,
				// transformation: [{ height: 300, width: 400 }],
			})

			return newdata
		})
		.catch(err => {
			console.log('imageKit err:', err)
			return ''
		})
}

async function uploadImageKitProgress(file, mimeType, fileSize, onUploadProgress) {
	try {
		const customXHR = new XMLHttpRequest()
		customXHR.upload.addEventListener('progress', e => {
			if (e.loaded <= fileSize) {
				const percent = Math.round((e.loaded / fileSize) * 100) //in bytes
				onUploadProgress(percent)
			}
			if (e.loaded === e.total) {
				console.log('Upload done')
			}
		})

		const result = await imagekit.upload({
			xhr: customXHR,
			file: file,
			fileName: `abc1.${mimeType}`,
		})

		console.log('uploadImageKitProgress result:', result)
		return result
	} catch (error) {
		console.error('uploadImageKitProgress error:', error)
		throw error // Rethrow the error to be caught by the calling function
	}
}

export { uploadImageToCloudinary, uploadToImageKit, uploadImageToImageKit, uploadImageKitProgress }
