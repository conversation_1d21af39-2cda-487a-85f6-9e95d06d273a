Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.uploadImageToCloudinary = uploadImageToCloudinary
exports.uploadToImageKit = uploadToImageKit
exports.uploadImageToImageKit = uploadImageToImageKit

var _imagekitJavascript = _interopRequireDefault(require('imagekit-javascript'))

var _environment = _interopRequireDefault(require('../../../environment'))

function _interopRequireDefault(obj) {
	return obj?.__esModule ? obj : { default: obj }
}

var _getEnvVars = (0, _environment.default)()
var IMAGEKIT_AuthenticationEndpoint = _getEnvVars.IMAGEKIT_AuthenticationEndpoint
var IMAGEKIT_PUBLIC_KEY = _getEnvVars.IMAGEKIT_PUBLIC_KEY
var IMAGEKIT_URL_ENDPOINT = _getEnvVars.IMAGEKIT_URL_ENDPOINT

var CLOUD_NAME = 'dvb3q9dsq'
var CLOUDINARY_PRESET = 'mnh4qyn1'
var CLOUDINARY_URL = 'https://api.cloudinary.com/v1_1/'.concat(CLOUD_NAME, '/image/upload')
var imagekit = new _imagekitJavascript.default({
	publicKey: IMAGEKIT_PUBLIC_KEY,
	urlEndpoint: IMAGEKIT_URL_ENDPOINT,
	authenticationEndpoint: ''.concat(IMAGEKIT_AuthenticationEndpoint),
})

function uploadImageToCloudinary(image) {
	var apiUrl
	var data
	var result
	var cloudData
	return regeneratorRuntime.async(
		function uploadImageToCloudinary$(_context) {
			while (1) {
				switch ((_context.prev = _context.next)) {
					case 0:
						if (!(image === '')) {
							_context.next = 2
							break
						}

						return _context.abrupt('return', image)

					case 2:
						// const CLOUDINARY_URL = "https://node.hostingladz.com:3033/v1/user/upload";
						apiUrl = CLOUDINARY_URL
						data = {
							file: image,
							upload_preset: CLOUDINARY_PRESET,
						}
						_context.prev = 4
						_context.next = 7
						return regeneratorRuntime.awrap(
							fetch(apiUrl, {
								body: JSON.stringify(data),
								headers: {
									'content-type': 'application/json',
								},
								method: 'POST',
							})
						)

					case 7:
						result = _context.sent
						_context.next = 10
						return regeneratorRuntime.awrap(result.json())

					case 10:
						cloudData = _context.sent
						console.log('Cloudinary res :', cloudData)
						return _context.abrupt('return', cloudData.secure_url)

					case 15:
						_context.prev = 15
						_context.t0 = _context.catch(4)
						console.log(_context.t0)

					case 18:
					case 'end':
						return _context.stop()
				}
			}
		},
		null,
		null,
		[[4, 15]]
	)
}

function uploadToImageKit(file, base64) {
	return regeneratorRuntime.async(function uploadToImageKit$(_context3) {
		while (1) {
			switch ((_context3.prev = _context3.next)) {
				case 0:
					return _context3.abrupt(
						'return',
						new Promise((resolve, reject) => {
							imagekit.upload(
								{
									file: base64,
									fileName: file.name,
								},
								(err, result) => {
									if (err) reject(err)
									resolve(result)
								}
							)
						})
							.then(function _callee(newdata) {
								var _url
								return regeneratorRuntime.async(function _callee$(_context2) {
									while (1) {
										switch ((_context2.prev = _context2.next)) {
											case 0:
												console.log('imageKit result:', newdata)
												_url = imagekit.url({
													src: newdata.url, // transformation: [{ height: 300, width: 400 }],
												})
												return _context2.abrupt('return', newdata)

											case 3:
											case 'end':
												return _context2.stop()
										}
									}
								})
							})
							.catch(err => {
								console.log('imageKit err:', err)
								return ''
							})
					)

				case 1:
				case 'end':
					return _context3.stop()
			}
		}
	})
}

function uploadImageToImageKit(file, mimeType) {
	return regeneratorRuntime.async(function uploadImageToImageKit$(_context5) {
		while (1) {
			switch ((_context5.prev = _context5.next)) {
				case 0:
					return _context5.abrupt(
						'return',
						new Promise((resolve, reject) => {
							imagekit.upload(
								{
									file: file,
									fileName: 'file.'.concat(mimeType), //you can change this and generate your own name if required
									// tags: ["tag-1", "tag-2"], //change this or remove it if you want
								},
								(err, result) => {
									if (err) reject('error here :', err)
									resolve(result)
								}
							)
						})
							.then(function _callee2(newdata) {
								var _url
								return regeneratorRuntime.async(function _callee2$(_context4) {
									while (1) {
										switch ((_context4.prev = _context4.next)) {
											case 0:
												console.log('imageKit result:', newdata)
												_url = imagekit.url({
													src: newdata.url, // transformation: [{ height: 300, width: 400 }],
												})
												return _context4.abrupt('return', newdata)

											case 3:
											case 'end':
												return _context4.stop()
										}
									}
								})
							})
							.catch(err => {
								console.log('imageKit err:', err)
								return ''
							})
					)

				case 1:
				case 'end':
					return _context5.stop()
			}
		}
	})
}
