import { View } from 'react-native'
import { useContext } from 'react'
import styles from '../../screens/styles'
import { theme } from '../../context/ThemeContext/ThemeColor'
import ThemeContext from '../../context/ThemeContext/ThemeContext'

const ProgressLine = ({ progress }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<View
			style={[
				styles().br10,
				{
					height: 8,
					width: `${progress}%`,
					backgroundColor: currentTheme.starBg,
				},
			]}
		/>
	)
}

export default ProgressLine
