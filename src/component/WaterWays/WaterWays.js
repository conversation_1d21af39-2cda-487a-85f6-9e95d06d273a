import { Text, View, TouchableOpacity, LayoutAnimation, FlatList, UIManager, Platform } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import Entypo from '@expo/vector-icons/Entypo'
import FontAwesome from '@expo/vector-icons/FontAwesome'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const WaterWays = ({ item, index, selected }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [selectedRoutes, setSelectedRoutes] = useState([])
	const [show, setShow] = useState(false)

	return (
		<>
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => {
					setShow(!show)
					LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				}}
				style={[
					styles().flexRow,
					styles().mt25,
					styles().pb10,
					styles().mb10,
					styles().alignCenter,
					styles().justifyBetween,
					{ borderBottomColor: currentTheme.C3C3C3, borderBottomWidth: 0.5 },
				]}
			>
				<Text
					numberOfLines={1}
					style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor }]}
				>
					{item.name}
				</Text>
				<Entypo
					name={show ? 'chevron-with-circle-up' : 'chevron-with-circle-down'}
					size={20}
					color={currentTheme.blackish}
				/>
			</TouchableOpacity>
			{show
				? <View style={[styles().ph20, styles().boxpeshadow, styles().br10, { marginHorizontal: 2 }]}>
						<FlatList
							data={item.subWaterWays}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ flexGrow: 1 }}
							renderItem={({ item, index }) => {
								return (
									<TouchableOpacity
										activeOpacity={0.5}
										style={[
											styles().flexRow,
											styles().pb10,
											styles().pt20,
											styles().alignCenter,
											styles().justifyBetween,
											{
												borderTopWidth: index === 0 ? 0 : 1,
												borderTopColor: currentTheme.C3C3C3,
												backgroundColor: currentTheme.white,
											},
										]}
										onPress={() => {
											const x = selectedRoutes.indexOf(item._id)
											if (x === -1) {
												setSelectedRoutes(prev => [...prev, item._id])
											} else {
												selectedRoutes.splice(x, 1)
												setSelectedRoutes(prev => [...prev])
											}
										}}
									>
										<View style={[styles().flexRow, styles().alignCenter]}>
											<View
												style={[
													styles().wh20px,
													styles().alignCenter,
													styles().justifyCenter,
													styles().mr10,
													styles().bw1,
													styles().br5,
													{
														backgroundColor: selectedRoutes.includes(item._id) ? currentTheme.themeBackground : currentTheme.white,
														borderColor: currentTheme.themeBackground,
													},
												]}
											>
												{selectedRoutes.includes(item._id) && (
													<FontAwesome
														name="check"
														size={16}
														color={currentTheme.white}
													/>
												)}
											</View>
											<Text
												numberOfLines={2}
												style={[styles().fs12, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
											>
												{item.name}
											</Text>
										</View>
									</TouchableOpacity>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>
				: null}
		</>
	)
}

export default WaterWays
