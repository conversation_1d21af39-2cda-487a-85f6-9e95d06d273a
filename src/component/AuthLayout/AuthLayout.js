import { useContext } from 'react'
import { View, ImageBackground, Dimensions, Platform, ScrollView, StatusBar, SafeAreaView, KeyboardAvoidingView } from 'react-native'

import styles from '../../screens/styles'

import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Header from '../AuthHeader/AuthHeader'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function AuthLayout({ containerStyle, LeftIcon, withBg, pagetitle, headerShown, children, navigation, withoutScroll }) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<View
			style={[
				styles().flex,
				styles().posRel,
				styles().zIndex1,
				containerStyle,

				{ backgroundColor: currentTheme.white },
			]}
		>
			<SafeAreaView
				style={[
					styles().flex,
					{
						// paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
					},
				]}
			>
				{withBg
					? <KeyboardAvoidingView
							behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
							keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
							style={[styles().flex]}
						>
							<ImageBackground
								source={require('../../assets/images/verify-bg.png')}
								resizeMode="contain"
								style={[
									styles().flex,
									{
										paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
									},
								]}
							>
								{headerShown
									? <Header
											navigation={navigation}
											LeftIcon={LeftIcon}
											pagetitle={pagetitle}
										/>
									: null}

								{withoutScroll
									? <View style={{ flexGrow: 1 }}>{children}</View>
									: <ScrollView
											contentContainerStyle={{ flexGrow: 1 }}
											showsVerticalScrollIndicator={false}
											keyboardShouldPersistTaps={'handled'}
										>
											{children}
										</ScrollView>}
							</ImageBackground>
						</KeyboardAvoidingView>
					: <KeyboardAvoidingView
							behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
							keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
							style={[styles().flex]}
						>
							{headerShown
								? <Header
										navigation={navigation}
										LeftIcon={LeftIcon}
										pagetitle={pagetitle}
									/>
								: null}
							{withoutScroll
								? <View style={{ flexGrow: 1 }}>{children}</View>
								: <ScrollView
										contentContainerStyle={{ flexGrow: 1 }}
										showsVerticalScrollIndicator={false}
										keyboardShouldPersistTaps={'handled'}
									>
										{children}
									</ScrollView>}
						</KeyboardAvoidingView>}
			</SafeAreaView>
		</View>
	)
}
