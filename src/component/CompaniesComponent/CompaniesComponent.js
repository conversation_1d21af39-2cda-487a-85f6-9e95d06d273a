import { Text, View, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Feather from '@expo/vector-icons/Feather'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import { renderText, useThrottledPress } from '../../utils/Constants'
import Share from '../../context/Share/Share'

const CompaniesComponent = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, index, checkride, navigation } = props
	const { isShare, shareNow } = useContext(Share)

	function share(item) {
		isShare(true)
		shareNow({
			title: item.name,
			_id: item._id,
			photo: item.photo,
			description: item.about,
			shareType: 'company',
		})
	}

	const handleNavigation = item => {
		navigation.navigate('CompanyDetails', { item: item, companyId: item?._id })
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)
	// console.log(item?._id)

	return (
		<TouchableOpacity
			key={item?._id}
			onPress={() => throttledButtonPress(item)}
			activeOpacity={0.9}
			style={[
				styles().flexRow,
				styles().pb20,
				styles().pt30,
				styles().ph15,
				styles().mb15,
				index === 0 ? styles().mt20 : null,
				//   styles().alignCenter,
				//   styles().flex,
				styles().bw1,
				styles().br10,
				{ borderColor: currentTheme.CFCFCF },
			]}
		>
			<View style={[styles().posAbs, styles().flexRow, styles().alignCenter, styles().top10, styles().right10, styles().zIndex100]}>
				<TouchableOpacity
					onPress={() => share(item)}
					activeOpacity={0.5}
					style={[styles().pall8, styles().br5, styles().mh5, { backgroundColor: currentTheme.lighterGold }]}
				>
					<FontAwesome
						name="share"
						size={12}
						color={currentTheme.white}
					/>
				</TouchableOpacity>
			</View>
			<View
				style={[
					styles().wh50px,
					styles().br25,
					styles().overflowH,
					styles().justifyCenter,
					styles().alignCenter,
					styles().mr10,
					{
						borderWidth: item?.photo ? 0 : 1,
						borderColor: currentTheme.themeBackground,
					},
				]}
			>
				{item?.photo
					? <Image
							source={{ uri: item?.photo }}
							resizeMode="cover"
							style={styles().wh100}
						/>
					: <MaterialCommunityIcons
							name="city-variant"
							size={25}
							color={currentTheme.themeBackground}
						/>}
			</View>
			<View style={[styles().flex]}>
				<Text style={[styles().fs12, styles().fontBold, styles().lh20, { color: currentTheme.headingColor }]}>{item?.name}</Text>
				<View
					style={[
						styles().flexRow,
						styles().alignCenter,
						styles().mt5,
						styles().mb5,
						styles().flex,
						styles().flexWrap,
						// {backgroundColor: 'teal'},
					]}
				>
					<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mb5]}>
						<Feather
							name="map-pin"
							size={12}
							color={currentTheme.E8E8C8}
						/>
						<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item?.city ? item?.city : 'N/A'}</Text>
					</View>
				</View>
				{checkride
					? <View style={[styles().alignSelfStart, styles().pv5, styles().ph10, styles().br20, { backgroundColor: currentTheme.F3F0E4 }]}>
							<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.themeBackground }]}>{'Check Rides'}</Text>
						</View>
					: <View style={[styles().alignSelfStart, styles().pv5, styles().ph10, styles().br20, { backgroundColor: currentTheme.lightBlue }]}>
							<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.darkBlue }]}>{`Open Jobs - ${item.openJobs}`}</Text>
						</View>}
				{item?.about
					? <View style={[styles().mt10]}>
							<Text
								numberOfLines={2}
								style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
							>
								{renderText(item?.about)}
							</Text>
						</View>
					: null}
			</View>
		</TouchableOpacity>
	)
}

export default React.memo(CompaniesComponent)
