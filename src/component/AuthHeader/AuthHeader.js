import { useContext } from 'react'
import { View, Text, Platform, TouchableOpacity } from 'react-native'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'

import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'

export default function AuthHeader(props) {
	// const user = useContext(UserContext);

	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { StyleHead } = props
	const androidSafeTop = useAndroidSafeTop()

	function Back() {
		return (
			<TouchableOpacity
				onPress={props.LeftIconFunc ? props.LeftIconFunc : () => props.navigation.goBack()}
				style={[
					styles().alignCenter,
					styles().justifyCenter,
					styles().br100,
					styles().h50px,
					styles().w40px,
					// styles().pall15,
				]}
			>
				<FontAwesome
					name="angle-left"
					size={30}
					color={currentTheme.blackish}
				/>
			</TouchableOpacity>
		)
	}

	return (
		<View
			style={[
				styles().flexRow,
				styles().alignCenter,
				styles().justifyBetween,
				Platform.OS === 'android' && styles().h100px,
				styles().ph10,
				styles().zIndex1,
				styles().posRel,
				StyleHead,
				props.HeaderStyle,
				{ paddingTop: androidSafeTop }
			]}
		>
			{props.LeftIcon ? <Back /> : <View style={[styles().h50px, styles().w25px]} />}

			<Text
				numberOfLines={1}
				style={[
					styles().fs18,
					styles().fontBold,
					styles().flex,
					styles().mh15,
					styles().textCenter,
					// styles().textCapitalize,
					{
						color: currentTheme.blackish,
					},
				]}
			>
				{props.pagetitle}
			</Text>

			<View style={[styles().w25px]} />
		</View>
	)
}
