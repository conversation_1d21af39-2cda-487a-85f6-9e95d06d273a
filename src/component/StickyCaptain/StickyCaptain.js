import { Text, TouchableOpacity, Image, UIManager, Platform, LayoutAnimation } from 'react-native'
import React, { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import fontStyles from '../../utils/fonts/fontStyles'
import UserContext from '../../context/User/User'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const StickyCaptain = ({ navigation }) => {
	const themeContext = useContext(ThemeContext)
	const _user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [show, setShow] = useState(false)

	return (
		<TouchableOpacity
			activeOpacity={1}
			onPress={() => {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				setShow(!show)
			}}
			style={{
				backgroundColor: currentTheme.themeBackground,
				alignSelf: 'flex-end',
				borderTopLeftRadius: 100,
				borderBottomLeftRadius: 100,
				paddingLeft: 10,
				paddingRight: 10,
				paddingVertical: show ? 10 : 10,
				position: 'absolute',
				top: 170,
				zIndex: 100,
			}}
		>
			{show
				? <TouchableOpacity
						onPress={() => {
							navigation.navigate('NearbyCaptain')
							setShow(false)
						}}
						style={{ flexDirection: 'row', alignItems: 'center' }}
					>
						<MaterialIcons
							name={'location-history'}
							size={25}
							color={currentTheme.white}
						/>
						<Text
							style={{
								fontSize: 12,
								color: currentTheme.white,
								marginLeft: 3,
								fontFamily: fontStyles.PoppinsMedium,
							}}
						>
							Nearby Captains
						</Text>
					</TouchableOpacity>
				: <Image
						source={require('../../assets/images/captains.png')}
						style={{ height: 25, width: 25, tintColor: 'white' }}
						resizeMode="contain"
					/>}
		</TouchableOpacity>
	)
}

export default React.memo(StickyCaptain)
