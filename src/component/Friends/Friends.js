import { Text, View, TouchableOpacity, Image } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import UserContext from '../../context/User/User'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import { useThrottledPress } from '../../utils/Constants'

const FriendsComponent = ({ item, navigation, index }) => {
	const user = useContext(UserContext)
	const filterUser = item?.users?.find(users => {
		return users?._id !== user?._id
	})
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const handleNavigation = item => {
		navigation.navigate('Chat', {
			item: item,
			chatUser: filterUser,
			roomId: item?._id,
			isAnonymousChat: item?.isAnonymousChat,
		})
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)

	return (
		<TouchableOpacity
			activeOpacity={0.7}
			onPress={() => throttledButtonPress(item)}
			style={[styles().alignCenter, styles().mr10, styles().justifyCenter, index === 0 && styles().ml10, { width: 70 }]}
		>
			<View
				style={[
					styles().wh65px,
					styles().overflowH,
					styles().br100,
					styles().alignCenter,
					styles().justifyCenter,
					{
						borderWidth: item?.isAnonymousChat ? 1 : !filterUser?.photo ? 1 : item?.chatName ? 1 : 0,
						borderColor: currentTheme.themeBackground,
					},
				]}
			>
				{item?.chatName
					? <FontAwesome5
							name="users"
							size={25}
							color={currentTheme.themeBackground}
						/>
					: item?.isAnonymousChat
						? <FontAwesome5
								name="user-secret"
								size={25}
								color={currentTheme.themeBackground}
							/>
						: filterUser?.photo
							? <Image
									source={{ uri: filterUser?.photo }}
									style={styles().wh100}
									resizeMode="cover"
								/>
							: <FontAwesome5
									name="user-alt"
									size={25}
									color={currentTheme.themeBackground}
								/>}
			</View>
			{item?.isAnonymousChat
				? <Text
						numberOfLines={1}
						style={[styles().fs12, styles().fontRegular, styles().textCapitalize, styles().textCenter, { color: currentTheme.lighterBlue, marginTop: 3 }]}
					>
						{'Anonymous User'}
					</Text>
				: <Text
						numberOfLines={1}
						style={[styles().fs12, styles().fontRegular, { color: currentTheme.lighterBlue, marginTop: 3 }]}
					>
						{item?.chatName ? item?.chatName : filterUser?.name}
					</Text>}
		</TouchableOpacity>
	)
}

export default React.memo(FriendsComponent)
