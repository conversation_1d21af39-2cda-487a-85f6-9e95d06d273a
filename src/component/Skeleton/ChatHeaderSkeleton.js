import { useContext } from 'react'
import SkeletonPlaceholder from 'react-native-skeleton-placeholder'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { View } from 'react-native'

const ChatHeaderSkeleton = () => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	return (
		<View>
			<SkeletonPlaceholder
				highlightColor={currentTheme.skeletonHightlight}
				borderRadius={4}
			>
				{/* Single user row view */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					padding={10}
				>
					{/* User Image */}
					<SkeletonPlaceholder.Item
						width={50}
						height={50}
						borderRadius={50}
					/>

					{/* User Name and Online Status */}
					<SkeletonPlaceholder.Item
						marginLeft={10}
						justifyContent="center"
					>
						<SkeletonPlaceholder.Item
							width={120}
							height={20} // Name Placeholder
							borderRadius={5}
						/>
						<SkeletonPlaceholder.Item
							marginTop={5}
							width={80}
							height={15} // Online Status Placeholder
							borderRadius={5}
						/>
					</SkeletonPlaceholder.Item>
				</SkeletonPlaceholder.Item>
			</SkeletonPlaceholder>
		</View>
	)
}

export default ChatHeaderSkeleton
