import { useContext } from 'react'
import { Dimensions, View } from 'react-native'
import SkeletonPlaceholder from 'react-native-skeleton-placeholder'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const ChatSkeleton = () => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { width, height } = Dimensions.get('window')
	const messageRadius = 5
	const avatarSize = 35
	const marginBottom = 5
	const paddingHorizontal = 10

	return (
		<View>
			<SkeletonPlaceholder highlightColor={currentTheme.skeletonHightlight}>
				{/* Receiver's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
						marginTop={30}
					/>
					<SkeletonPlaceholder.Item marginLeft={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.7} // Longer chat bubble for the receiver
							height={60}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
				</SkeletonPlaceholder.Item>

				{/* Sender's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					justifyContent="flex-end" // Align sender's chat bubble to the right
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item marginRight={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.7} // Shorter chat bubble for the sender
							height={50}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
						marginTop={20}
					/>
				</SkeletonPlaceholder.Item>

				{/* Receiver's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
					/>
					<SkeletonPlaceholder.Item marginLeft={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.6}
							height={30}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
				</SkeletonPlaceholder.Item>

				{/* Sender's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					justifyContent="flex-end"
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item marginRight={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.6}
							height={35}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
						marginTop={5}
					/>
				</SkeletonPlaceholder.Item>

				{/* Receiver's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
						// marginBottom={marginBottom}
					/>
					<SkeletonPlaceholder.Item marginLeft={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.7}
							height={50}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
				</SkeletonPlaceholder.Item>
				{/* Receiver's message */}
				<SkeletonPlaceholder.Item
					flexDirection="row"
					alignItems="center"
					marginBottom={marginBottom}
					paddingHorizontal={paddingHorizontal}
				>
					<SkeletonPlaceholder.Item
						width={avatarSize}
						height={avatarSize}
						borderRadius={avatarSize}
						// marginBottom={marginBottom}
					/>
					<SkeletonPlaceholder.Item marginLeft={10}>
						<SkeletonPlaceholder.Item
							width={width * 0.7}
							height={30}
							borderRadius={messageRadius}
						/>
					</SkeletonPlaceholder.Item>
				</SkeletonPlaceholder.Item>
			</SkeletonPlaceholder>
		</View>
	)
}

export default ChatSkeleton
