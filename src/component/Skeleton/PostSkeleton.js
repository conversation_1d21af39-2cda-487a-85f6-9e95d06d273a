import { useContext } from 'react'
import SkeletonPlaceholder from 'react-native-skeleton-placeholder'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const PostSkeleton = ({ data }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<>
			<SkeletonPlaceholder
				highlightColor={currentTheme.skeletonHightlight}
				borderRadius={4}
			>
				<SkeletonPlaceholder.Item
					marginBottom={30}
					paddingHorizontal={20}
				>
					<SkeletonPlaceholder.Item
						flexDirection="row"
						alignItems="center"
					>
						<SkeletonPlaceholder.Item
							width={35}
							height={35}
							borderRadius={35}
						/>
						<SkeletonPlaceholder.Item marginLeft={10}>
							<SkeletonPlaceholder.Item
								width={100}
								height={10}
							/>
							<SkeletonPlaceholder.Item
								marginTop={6}
								width={80}
								height={10}
							/>
						</SkeletonPlaceholder.Item>
					</SkeletonPlaceholder.Item>
					<SkeletonPlaceholder.Item
						marginTop={10}
						width={'50%'}
						height={20}
					/>
					<SkeletonPlaceholder.Item
						marginTop={10}
						width={'100%'}
						height={100}
					/>
					<SkeletonPlaceholder.Item
						marginTop={10}
						width={'100%'}
						height={40}
					/>
				</SkeletonPlaceholder.Item>
			</SkeletonPlaceholder>
			<SkeletonPlaceholder
				highlightColor={currentTheme.skeletonHightlight}
				borderRadius={4}
			>
				<SkeletonPlaceholder.Item
					marginBottom={30}
					paddingHorizontal={20}
				>
					<SkeletonPlaceholder.Item
						flexDirection="row"
						alignItems="center"
					>
						<SkeletonPlaceholder.Item
							width={35}
							height={35}
							borderRadius={35}
						/>
						<SkeletonPlaceholder.Item marginLeft={10}>
							<SkeletonPlaceholder.Item
								width={100}
								height={10}
							/>
							<SkeletonPlaceholder.Item
								marginTop={6}
								width={80}
								height={10}
							/>
						</SkeletonPlaceholder.Item>
					</SkeletonPlaceholder.Item>
					<SkeletonPlaceholder.Item
						marginTop={10}
						width={'50%'}
						height={20}
					/>
					<SkeletonPlaceholder.Item
						marginTop={10}
						width={'100%'}
						height={40}
					/>
				</SkeletonPlaceholder.Item>
			</SkeletonPlaceholder>
		</>
	)
}
export default PostSkeleton
