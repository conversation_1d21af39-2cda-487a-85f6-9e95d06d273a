import { useContext } from 'react'
import { View, Dimensions, Platform, ScrollView, SafeAreaView, KeyboardAvoidingView } from 'react-native'

import styles from '../../screens/styles'

import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Header from '../AuthHeader/AuthHeader'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'

const { height } = Dimensions.get('window')
const { width } = Dimensions.get('window')

export default function Layout({
	containerStyle,
	LeftIcon,
	pagetitle,
	headerShown,
	children,
	navigation,
	withoutScroll,
	HeaderStyle,
	LeftIconFunc,
	ContentArea,
	keyBoardArea,
	refreshControl,
	scrollRef,
}) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const androidSafeTop = useAndroidSafeTop()

	return (
		<View
			style={[
				styles().flex,

				{ backgroundColor: currentTheme.white },
				containerStyle,
			]}
		>
			<SafeAreaView
				style={[
					styles().flex,
					{
						paddingTop: androidSafeTop,
					},
				]}
			>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : keyBoardArea ? keyBoardArea : 35}
					style={[styles().flex]}
				>
					{headerShown
						? <Header
								navigation={navigation}
								LeftIconFunc={LeftIconFunc}
								LeftIcon={LeftIcon}
								pagetitle={pagetitle}
								HeaderStyle={HeaderStyle}
							/>
						: null}
					{withoutScroll
						? <View style={[ContentArea, { flexGrow: 1 }]}>{children}</View>
						: <ScrollView
								nestedScrollEnabled={true}
								ref={scrollRef}
								refreshControl={refreshControl}
								contentContainerStyle={[ContentArea, { flexGrow: 1 }]}
								showsVerticalScrollIndicator={false}
								keyboardShouldPersistTaps={'handled'}
							>
								{children}
							</ScrollView>}
				</KeyboardAvoidingView>
			</SafeAreaView>
		</View>
	)
}
