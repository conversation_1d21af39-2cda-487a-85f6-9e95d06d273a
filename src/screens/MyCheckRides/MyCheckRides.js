import { Text, View, FlatList, useWindowDimensions } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { createChatRoom, getCheckRideOffers } from '../../apollo/server'
import Layout from '../../component/Layout/Layout'
import { TabView, SceneMap, TabBar } from 'react-native-tab-view'
import MyCheckRideComponent from '../../component/MyCheckRideComponent/MyCheckRideComponent'
import UserContext from '../../context/User/User'
import Loading from '../../context/Loading/Loading'
import useCheckRideWebSocket from '../../hooks/useCheckRideWebSocket'

const MyCheckRides = props => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const activeTab = props?.route?.params?.activeTab
	const _checkride = props?.route?.params?.checkride
	// let { company } = checkride;
	const { isLoader } = useContext(Loading)
	// let { checkRideData } = company;
	const [tab1, setTab1] = useState([])
	const [tab2, setTab2] = useState([])
	const [tab3, setTab3] = useState([])
	const [tab4, setTab4] = useState([])
	const [_chatLoader, setChatLoader] = useState(false)
	// const [Loading, setLoading] = useState(false);
	const layout = useWindowDimensions()
	const [index, setIndex] = React.useState(activeTab ? activeTab : 0)
	// console.log('activeTab', activeTab)
	const [page, _setPage] = useState(1)
	const pageSize = 1000

	// WebSocket for real-time updates (optional - gracefully degrades if server unavailable)
	const { isConnected } = useCheckRideWebSocket(user?._id, null, (data) => {
		// Handle real-time check ride updates
		if (data && data.type && (
		    data.type === 'CHECKRIDE_OFFER_ACCEPTED' || 
		    data.type === 'CHECKRIDE_OFFER_REJECTED' ||
		    data.type === 'NEW_CHECKRIDE_OFFER' ||
		    data.type === 'CHECKRIDE_STATUS_CHANGED')) {
			// Refetch the check rides data to get latest updates
			refetch()
		}
	})

	// useEffect(() => {

	// }, [checkride]);

	const [routes] = React.useState([
		{ key: 'first', title: 'Offers' },
		{ key: 'second', title: 'In Progress' },
		{ key: 'third', title: 'Completed Rides' },
		{ key: 'fourth', title: 'Others' },
	])

	const { data, loading, error, refetch } = useQuery(getCheckRideOffers, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filters: {
				user: user?._id,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: async res => {
			isLoader(false)
			console.log('getCheckRideOffers res :', JSON.stringify(data?.getCheckRideOffers))
			const offers = res?.getCheckRideOffers?.results
			const accepted = offers?.filter(item => item.status === 'accepted')
			const pending = offers?.filter(item => item.status === 'pending')
			const active = offers?.filter(item => item.status === 'active')
			const rejected = offers?.filter(item => item.status === 'rejected')
			const expired = offers?.filter(item => item.status === 'expired')
			const completed = offers?.filter(item => item.status === 'completed')
			// let tab1 = [...pending, ...expired, ...rejected].sort((a, b) =>
			//   a?.status?.localeCompare(b.status),
			// );
			const tab1 = [...pending]
			const tab2 = [...accepted, ...active]
			const tab3 = [...completed]
			const tab4 = [...expired, ...rejected]
			setTab1(tab1)
			setTab2(tab2)
			setTab3(tab3)
			setTab4(tab4)
			if (tab1?.length > 0) {
				setIndex(0)
			} else {
				setIndex(1)
			}
		},
		onError: err => {
			console.log('getCheckRideOffers err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [createChatroomMutate, {}] = useMutation(createChatRoom, {
		onCompleted: async ({ createChatRoom }) => {
			console.log('createChatRoom res :', createChatRoom)
			const filterUser = await createChatRoom?.users?.find(users => {
				return users?._id !== user?._id
			})
			props.navigation.navigate('Chat', {
				item: createChatRoom,
				chatUser: filterUser,
				roomId: createChatRoom?._id,
				isAnonymousChat: createChatRoom?.isAnonymousChat,
			})
			setChatLoader(false)
		},
		onError: err => {
			console.log('createChatRoom err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setChatLoader(false)
		},
	})

	async function _Chat() {
		setChatLoader(true)
		await createChatroomMutate({
			variables: {
				inputRoom: {
					users: [user._id, company?._id],
				},
			},
		})
	}

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	const Offers = () => (
		<View style={[styles().flex, styles().ph10]}>
			<FlatList
				data={tab1}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{ flexGrow: 1 }}
				renderItem={({ item, index }) => {
					return (
						<MyCheckRideComponent
							item={item}
							index={index}
							navigation={props.navigation}
							checkrideID={item?.checkRideId}
							company={item?.company}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{'No Offers'}
							</Text>
						</View>
					)
				}}
			/>
		</View>
	)

	const InProgress = () => (
		<View style={[styles().flex, styles().ph10]}>
			<FlatList
				data={tab2}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{ flexGrow: 1 }}
				renderItem={({ item, index }) => {
					return (
						<MyCheckRideComponent
							item={item}
							index={index}
							navigation={props.navigation}
							checkrideID={item?.checkRideId}
							company={item?.company}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{'No Check Rides'}
							</Text>
						</View>
					)
				}}
			/>
		</View>
	)

	const CompletedJobs = () => (
		<View style={[styles().flex, styles().ph10]}>
			<FlatList
				data={tab3}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{ flexGrow: 1 }}
				renderItem={({ item, index }) => {
					return (
						<MyCheckRideComponent
							item={item}
							index={index}
							navigation={props.navigation}
							checkrideID={item?.checkRideId}
							company={item?.company}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{'No Check Rides'}
							</Text>
						</View>
					)
				}}
			/>
		</View>
	)

	const Others = () => (
		<View style={[styles().flex, styles().ph10]}>
			<FlatList
				data={tab4}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{ flexGrow: 1 }}
				renderItem={({ item, index }) => {
					return (
						<MyCheckRideComponent
							item={item}
							index={index}
							navigation={props.navigation}
							checkrideID={item?.checkRideId}
							company={item?.company}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{'No Check Rides'}
							</Text>
						</View>
					)
				}}
			/>
		</View>
	)

	const renderScene = SceneMap({
		first: Offers,
		second: InProgress,
		third: CompletedJobs,
		fourth: Others,
	})

	const renderTabBar = props => (
		<TabBar
			{...props}
			indicatorStyle={[{ marginBottom: 10, width: '100%', height: 2 }]}
			indicatorContainerStyle={{ width: 0 }}
			//   indicatorContainerStyle={{marginRight:10}}
			style={[{ backgroundColor: currentTheme.white, elevation: 0 }]}
			activeColor={currentTheme.black}
			inactiveColor={currentTheme.blackish}
			scrollEnabled={true}
			pressOpacity={1}
			pressColor={currentTheme.white}
			contentContainerStyle={{ width: 'auto' }}
			tabStyle={[styles().alignStart, styles().mr10, { elevation: 0, width: 'auto', overflow: 'hidden' }]}
			renderLabel={({ route, focused, color }) => (
				<>
					<View style={{ flexDirection: 'row', alignItems: 'center' }}>
						<Text
							style={[
								styles().fs18,
								styles().fw700,
								{ textTransform: 'capitalize' },
								{
									color: focused ? currentTheme.themeBackground : currentTheme.C3C3C3,
								},
							]}
						>
							{route?.title}
						</Text>
						{tab1?.length !== 0 && route?.title === 'Offers'
							? <View
									style={{
										backgroundColor: 'white',
										padding: 2,
										left: 2,
										alignItems: 'center',
										justifyContent: 'center',
										borderRadius: 100,
										bottom: 7,
										shadowColor: '#000',
										shadowOffset: {
											width: 0,
											height: 3,
										},
										shadowOpacity: 0.27,
										shadowRadius: 4.65,

										elevation: 6,
									}}
								>
									<View
										style={{
											height: 8,
											width: 8,
											borderRadius: 100,
											backgroundColor: 'red',
										}}
									/>
								</View>
							: null}
					</View>
					{focused
						? <View
								style={[
									styles().w70px,
									styles().mt5,
									{
										borderBottomWidth: 2,
										borderBottomColor: currentTheme.themeBackground,
									},
								]}
							/>
						: null}
				</>
			)}
		/>
	)

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			ContentArea={styles().ph0}
			// HeaderStyle={styles().h100px}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Check Rides'}
		>
			{/* <View
          style={[
            styles().pb20,
            styles().alignCenter,
            styles().ph20,
            styles().pt25,
            styles().mb20,
            styles().overflowH,
            {
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              backgroundColor: currentTheme.F3F0E4,
            },
          ]}>
          <View
            style={[
              styles().wh65px,
              styles().br50,
              styles().overflowH,
              styles().justifyCenter,
              styles().alignCenter,
              //   styles().mr10,
              {
                borderWidth: 1,
                borderColor: currentTheme.themeBackground,
              },
            ]}>
            {company?.photo ? (
              <Image
                source={{ uri: company?.photo }}
                resizeMode="cover"
                style={styles().wh100}
              />
            ) : (
              <MaterialCommunityIcons
                name="city-variant"
                size={35}
                color={currentTheme.themeBackground}
              />
            )}
          </View>

          <View style={[styles().mt10, styles().mb10]}>
            <Text
              style={[
                styles().fs12,
                styles().fontBold,
                styles().lh20,
                styles().textCapitalize,
                { color: currentTheme.headingColor },
              ]}>
              {company?.name}
            </Text>
          </View>
          <View
            style={[
              styles().flexRow,
              styles().alignCenter,
              styles().flexWrap,
              styles().justifyCenter,
            ]}>
            <View
              style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
              <Feather name="map-pin" size={12} color={currentTheme.E8E8C8} />
              <Text
                style={[
                  styles().fs10,
                  styles().ml5,
                  styles().fontRegular,
                  styles().textCapitalize,
                  { color: currentTheme.E8E8C8 },
                ]}>
                {company?.city ? company?.city : 'N/A'}
              </Text>
            </View>
            {company?.companySize ? (
              <View
                style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
                <FontAwesome5
                  name="user-tie"
                  size={12}
                  color={currentTheme.E8E8C8}
                />
                <Text
                  style={[
                    styles().fs10,
                    styles().ml5,
                    styles().fontRegular,
                    { color: currentTheme.E8E8C8 },
                  ]}>
                  {company?.companySize + ' Employees'}
                </Text>
              </View>
            ) : null}

            <View
              style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
              <Ionicons
                name="ios-cash-outline"
                size={12}
                color={currentTheme.E8E8C8}
              />
              <Text
                style={[
                  styles().fs10,
                  styles().ml5,
                  styles().fontRegular,
                  { color: currentTheme.E8E8C8 },
                ]}>
                {` $${checkRideData?.price}/Hour`}
              </Text>
            </View>
          </View>
          
          <View style={[styles().w80, styles().mt10, styles().alignCenter]}>
            <Text
              numberOfLines={3}
              style={[
                styles().fs10,
                styles().fw400,
                styles().textCenter,
                styles().lh20,
                {color: currentTheme.headingColor},
              ]}>
              {company?.about}
            </Text>
          </View>
          {chatLoader ? (
            <ActivityIndicator
              size={'small'}
              color={currentTheme.themeBackground}
              style={{ marginTop: 5 }}
            />
          ) : (
            <TouchableOpacity
              onPress={() => Chat()}
              activeOpacity={0.5}
              style={[
                styles().h35px,
                styles().alignCenter,
                styles().justifyCenter,
                styles().br5,
                styles().mt10,
                styles().flexRow,
                styles().w120px,
                { backgroundColor: currentTheme.themeBackground },
              ]}>
              <MaterialCommunityIcons
                name="comment-text-outline"
                size={18}
                color={currentTheme.white}
              />
              <Text
                style={[
                  styles().fs12,
                  styles().fw400,
                  { color: currentTheme.white, marginLeft: 3, marginBottom: 3 },
                ]}>
                Chat Now
              </Text>
            </TouchableOpacity>
          )}
        </View> */}
			<View style={[styles().flex, styles().ph5]}>
				<TabView
					navigationState={{ index, routes }}
					renderScene={renderScene}
					onIndexChange={setIndex}
					renderTabBar={renderTabBar}
					initialLayout={{ width: layout.width }}
				/>
			</View>
		</Layout>
	)
}

export default MyCheckRides
