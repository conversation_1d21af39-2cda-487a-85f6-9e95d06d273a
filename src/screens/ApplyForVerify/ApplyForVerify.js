import { Text, View, LayoutAnimation, Platform, UIManager } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation, useQuery } from '@apollo/client'
import { getKeyByFilter, updateUser } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import UserContext from '../../context/User/User'
import { ScrollView } from 'react-native-gesture-handler'
import Loading from '../../context/Loading/Loading'
import moment from 'moment'
import { removetags } from '../../utils/Constants'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const ApplyForVerify = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loader, setLoader] = useState(false)
	const { isLoader } = useContext(Loading)

	const [mutate, { client }] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
			setLoader(false)
			FlashMessage({
				msg: 'Your application has been submitted',
				type: 'success',
			})
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} catch (e) {
			setLoader(false)
			console.log('catch updateUser:', e)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoader(false)
		console.log('updateUser error  :', error)
	}

	const Apply = async () => {
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		setLoader(true)
		await mutate({
			variables: {
				updateUserInput: {
					profileVerficationRequested: true,
				},
			},
		})
	}

	const { data, loading, refetch } = useQuery(getKeyByFilter, {
		fetchPolicy: 'no-cache',
		variables: {
			filters: {
				key: 'crewmember_verification_terms',
				specific_for: 'crewmember',
			},
		},
		onCompleted: data => {
			console.log('getKeyByFilter res :', JSON.stringify(data?.getKeyByFilter))
		},
		onError: err => {
			console.log('getKeyByFilter err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Apply for Verified Badge'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<ScrollView>
					<View style={styles().flex}>
						<Text style={[styles().fs16, styles().mb5, styles().fw600, { color: currentTheme.themeBackground }]}>Terms and Conditions</Text>
						<View style={styles().mb10}>
							<Text style={[styles().fs12, styles().mb20, styles().lh22, styles().fw400, { color: currentTheme.E8E8C8 }]}>
								{moment(new Date()).format('LL')}
							</Text>
							<Text style={[styles().fs14, styles().mb10, styles().lh22, styles().fw400, { color: currentTheme.E8E8C8 }]}>
								{removetags(data?.getKeyByFilter?.value)}
							</Text>
						</View>
					</View>
					<View style={styles().wh20px} />
				</ScrollView>
			</View>
			{user?.profileVerficationRequested
				? <ThemeButton
						onPress={() => props.navigation.goBack()}
						Title={'Already Applied, Go Back'}
						Style={[
							styles().h50px,
							{
								backgroundColor: currentTheme.E8E8C8,
								marginTop: 0,
								marginBottom: 20,
								borderWidth: 0,
							},
						]}
						StyleText={{ color: currentTheme.white, fontSize: 14 }}
					/>
				: <View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
						{Loader
							? <View style={[styles().w47]}>
									<Spinner />
								</View>
							: <ThemeButton
									onPress={() => Apply()}
									Title={'Apply'}
									Style={[styles().w47, styles().h45px]}
									StyleText={{ color: currentTheme.black }}
								/>}
						<ThemeButton
							onPress={() => props.navigation.goBack()}
							Title={'Cancel'}
							Style={[
								styles().w47,
								styles().h45px,
								{
									backgroundColor: currentTheme.C3C3C3,
									borderColor: currentTheme.C3C3C3,
								},
							]}
							StyleText={{ color: currentTheme.black }}
						/>
					</View>}
		</Layout>
	)
}

export default ApplyForVerify
