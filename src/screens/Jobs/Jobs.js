import { Text, View, TouchableOpacity, Image, FlatList, Platform, RefreshControl, UIManager } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Ionicons from '@expo/vector-icons/Ionicons'
import Feather from '@expo/vector-icons/Feather'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Entypo from '@expo/vector-icons/Entypo'
import TextField from '../../component/FloatTextField/FloatTextField'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import LayoutView from '../../component/Layout/Layout'
import { removeSavedJob, saveJob, jobs, getAppliedJobsIds } from '../../apollo/server'
import moment from 'moment'
import UserContext from '../../context/User/User'
import { useIsFocused } from '@react-navigation/native'
import JobFilter from '../../component/Modals/JobFilter'
import { renderText, useThrottledPress } from '../../utils/Constants'
import Share from '../../context/Share/Share'
import fontStyles from '../../utils/fonts/fontStyles'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Jobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const { isShare, shareNow } = useContext(Share)
	const isFocus = useIsFocused()
	const [search, setSearch] = useState('')
	const [Jobs, setJobs] = useState([])
	const [searchData, setSearchData] = useState([])
	const [page, setPage] = useState(1)
	const [appliedpage, setAppliedPage] = useState(1)
	const [appliedJobs, setAppliedJobs] = useState([])
	const [filter, setFilter] = useState(null)
	const [Loading, setLoading] = useState(false)
	const [showclearfilter, setShowclearfilter] = useState(false)
	const [isFilter, setIsFilter] = useState(false)
	// const [date, setDate] = useState('');
	const [status, setStatus] = useState('')
	const [savedJobs, setSavedJobs] = useState(user?.savedJobs)
	const [_throttle, _setThrottle] = useState(false)
	const pageSize = 30
	const appliedPageSize = 1000

	// const my_saved_jobs = user?.savedJobs;
	// console.log(my_saved_jobs);

	const {
		data: appliedData,
		loading: loadingApplied,
		error: errorApplied,
		refetch: refetchApplied,
	} = useQuery(getAppliedJobsIds, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			options: {
				page: appliedpage,
				limit: appliedPageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('📦 Jobs Screen - Applied Jobs IDs loaded:', data?.getAppliedJobs?.results?.length || 0)
			console.log('📦 Jobs Screen - Applied Jobs response:', JSON.stringify(data?.getAppliedJobs, null, 2))
			setAppliedJobs(prev => [...prev, ...data?.getAppliedJobs?.results])
		},
		onError: err => {
			console.log('❌ Jobs Screen - getAppliedJobs error:', err)
			console.log('❌ Jobs Screen - Error details:', JSON.stringify(err, null, 2))
			setAppliedJobs([])
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})
	console.log({ ...filter, ...status })
	const { data, loading, error, refetch } = useQuery(jobs, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filters: { ...filter, ...status },
			// filters: filter,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('📦 Jobs Screen - All Jobs loaded:', data?.jobs?.results?.length || 0)
			console.log('📦 Jobs Screen - Jobs response:', JSON.stringify(data?.jobs, null, 2))
			console.log('📦 Jobs Screen - Total pages:', data?.jobs?.totalPages)
			console.log('📦 Jobs Screen - Current page:', page)
			setLoading(false)
			setJobs(prev => [...prev, ...data?.jobs?.results])
			setSearchData(prev => [...prev, ...data?.jobs?.results])
		},
		onError: err => {
			console.log('❌ Jobs Screen - jobs query error:', err)
			console.log('❌ Jobs Screen - Error message:', err.message)
			console.log('❌ Jobs Screen - Error stack:', err.stack)
			setLoading(false)
		},
	})

	const searchFilterFunction = text => {
		if (text) {
			const newData = Jobs.filter(item => {
				const itemData = item.title ? item.title.toUpperCase() : ''.toUpperCase()
				const textData = text.toUpperCase()
				return itemData.indexOf(textData) > -1
			})
			console.log('search result :', newData)
			setSearchData(newData)
			setSearch(text)
		} else {
			setSearchData(Jobs)
			setSearch(text)
		}
	}

	const [mutate, { client }] = useMutation(saveJob, {
		onCompleted: async res => {
			console.log('saveJob res', res)
			// FlashMessage({msg: res.saveJob.message, type: 'success'});
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('saveJob error  :', error)
		},
	})

	const save = async item => {
		const arr = [...savedJobs]
		arr.push(item._id)
		setSavedJobs(arr)
		await mutate({
			variables: {
				saveJobId: item?._id,
			},
		})
	}

	function share(item) {
		isShare(true)
		shareNow({
			title: item.title,
			_id: item._id,
			photo: item.company.photo,
			description: item.description,
			shareType: 'job',
		})
	}

	const refresh = async () => {
		setShowclearfilter(false)
		setLoading(true)
		setPage(1)
		setAppliedPage(1)
		setFilter(null)
		setSearchData([])
		setJobs([])
		setAppliedJobs([])
		setSearch('')
		// await user?.refresh();
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setJobs(_prev => res?.data?.jobs?.results)
			setSearchData(_prev => res?.data?.jobs?.results)
			setLoading(false)
			console.log('jobs refreshed')
		})
		await refetchApplied({
			options: {
				page: 1,
				limit: appliedPageSize,
			},
		}).then(res => {
			setAppliedJobs(_prev => res?.data?.getAppliedJobs?.results)
		})
	}

	const nextPage = async () => {
		if (page < data?.jobs?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			// await refetch();
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
				filters: null,
			})
			await refetchApplied({
				options: {
					page: appliedpage,
					limit: appliedPageSize,
					sortBy: 'createdAt:desc',
				},
			})
			console.log('jobs page :', page)
			// setSearch('');
		}
		if (appliedpage < appliedData?.getAppliedJobs?.totalPages) {
			setAppliedPage(old => old + 1)
			await refetchApplied({
				options: {
					page: appliedpage,
					limit: appliedPageSize,
					sortBy: 'createdAt:desc',
				},
			})
			console.log('applied page :', appliedpage)
			// setSearch('');
		}
	}

	const onFilter = async filter => {
		setFilter(filter)
		setShowclearfilter(true)
		console.log('filter values :', filter, status)
		setLoading(true)
		setSearch('')
		setSearchData([])
		setPage(1)
		setJobs([])
		console.log('filter page :', page)
		await refetch({
			filters: { ...filter, ...status },
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			console.log('filter data =======>', JSON.stringify(res?.data?.jobs))
			setJobs(res?.data?.jobs?.results)
			setSearchData(res?.data?.jobs?.results)
			setLoading(false)
		})
		await refetchApplied({
			options: {
				page: appliedpage,
				limit: appliedPageSize,
			},
		})
	}

	const clearFiter = async () => {
		setLoading(true)
		setPage(1)
		setSearch('')
		setShowclearfilter(false)
		setSearchData([])
		setFilter(null)
		setAppliedPage(1)
		setJobs([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
			filters: filter,
		})
		await refetchApplied({
			options: null,
		})
	}

	const [removeSave, {}] = useMutation(removeSavedJob, {
		onCompleted: async res => {
			console.log('removeSavedJob res  :', res)
			// await refetch();
			// FlashMessage({
			//   msg: res.removeSavedJob.message,
			//   type: 'warning',
			// });
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('removeSavedJob error  :', error)
		},
	})

	const remove = async item => {
		const update = savedJobs?.filter(job => job !== item?._id)
		setSavedJobs(update)
		await removeSave({
			variables: {
				removeSavedJobId: item?._id,
			},
		})
	}

	const handleNavigation = async item => {
		// if (user_eligible()) {
		const check = appliedJobs?.find(applied => item?._id === applied?._id)
		props.navigation.push('JobDetails', {
			item: item,
			jobId: item?._id,
			alreadyApplied: !!check,
		})
		// }
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)

	useEffect(() => {
		setLoading(true)
		// setDate({expireAt: new Date()});
		setStatus({ status: 'active' })
	}, [])

	useEffect(() => {
		setPage(1)
	}, [isFocus])

	useEffect(() => {
		refetchApplied({
			options: {
				page: appliedpage,
				limit: appliedPageSize,
			},
		}).then(res => {
			setAppliedJobs(prev => [...prev, ...res?.data?.getAppliedJobs?.results])
		})
	}, [isFocus])

	// console.log('total jobs =========>', Jobs.length);
	// console.log('in job filter =>', { ...filter, ...date });

	return (
		<LayoutView
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Jobs'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
					<View style={[styles().flex]}>
						<TextField
							keyboardType="default"
							value={search}
							// errorText={searchError}
							autoCapitalize="none"
							placeholder={'Search'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => searchFilterFunction(text)}
						/>
					</View>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => setIsFilter(true)}
						style={[styles().wh35px, styles().ml15, styles().alignCenter, styles().justifyCenter, styles().br20, { backgroundColor: currentTheme.EEE8D5 }]}
					>
						<FontAwesome
							name="filter"
							size={20}
							color={currentTheme.themeBackground}
						/>
					</TouchableOpacity>
				</View>
				{showclearfilter
					? <TouchableOpacity
							onPress={() => clearFiter()}
							style={[styles().flexRow, styles().alignCenter, styles().mb15, { width: 130 }]}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black, marginTop: 1 }]}>Clear Filters</Text>
							<Entypo
								name={'cross'}
								size={20}
							/>
						</TouchableOpacity>
					: null}
				<View style={{ flex: 1 }}>
					<FlatList
						data={searchData}
						onEndReached={() => nextPage()}
						onEndReachedThreshold={0.5}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={{ flexGrow: 1 }}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={Loading}
							/>
						}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontFamily: fontStyles.PoppinsRegular,
											fontSize: 14,
										}}
									>
										{Loading || loadingApplied ? 'Loading...' : 'No jobs'}
									</Text>
								</View>
							)
						}}
						renderItem={({ item, index }) => {
							const isSave = savedJobs?.find(savejob => savejob === item?._id)
							return (
								<TouchableOpacity
									key={item._id}
									onPress={() => throttledButtonPress(item)}
									activeOpacity={0.8}
									style={[
										styles().flexRow,
										styles().pb20,
										styles().pt30,
										styles().ph15,
										styles().mb20,
										index === 0 ? styles().mt10 : null,
										//   styles().alignCenter,
										//   styles().flex,
										styles().bw1,
										styles().br10,
										{
											borderColor: currentTheme.CFCFCF,
										},
									]}
								>
									<View style={[styles().posAbs, styles().flexRow, styles().alignCenter, styles().top10, styles().right10, styles().zIndex100]}>
										<TouchableOpacity
											onPress={() => share(item)}
											style={[styles().pall8, styles().br5, styles().mh5, { backgroundColor: currentTheme.lighterGold }]}
										>
											<FontAwesome
												name="share"
												size={12}
												color={currentTheme.white}
											/>
										</TouchableOpacity>
										<TouchableOpacity
											onPress={() => {
												isSave ? remove(item) : save(item)
											}}
											style={[styles().pall8, styles().br5, { backgroundColor: currentTheme.lighterGold }]}
										>
											<FontAwesome
												name={isSave ? 'bookmark' : 'bookmark-o'}
												size={12}
												color={currentTheme.white}
											/>
										</TouchableOpacity>
									</View>
									{appliedJobs?.length !== 0 &&
										appliedJobs
											?.filter(applied => item?._id === applied?._id)
											?.map(_apply => {
												return (
													<View
														style={[
															styles().posAbs,
															styles().left15,
															styles().pv5,
															styles().ph10,
															styles().br20,
															{
																top: -10,
																backgroundColor: currentTheme.CFCFCF,
															},
														]}
													>
														<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.white }]}>{'Already Applied'}</Text>
													</View>
												)
											})}
									<View
										style={[
											// {backgroundColor: 'teal'},
											styles().mr10,
											styles().alignCenter,
										]}
									>
										<View
											style={[
												styles().wh50px,
												styles().br25,
												styles().overflowH,
												styles().justifyCenter,
												styles().alignCenter,

												{
													borderWidth: item?.company?.photo ? 0 : 1,
													borderColor: currentTheme.themeBackground,
												},
											]}
										>
											{item?.company?.photo
												? <Image
														source={{ uri: item?.company?.photo }}
														resizeMode="cover"
														style={styles().wh100}
													/>
												: <Ionicons
														name="md-briefcase"
														size={22}
														color={currentTheme.themeBackground}
													/>}
										</View>
										<View
											style={[
												styles().ph10,
												styles().pv5,
												styles().br5,
												styles().alignCenter,
												styles().mt10,
												styles().justifyCenter,
												{ backgroundColor: currentTheme.EEE8D5 },
											]}
										>
											<Text style={[styles().fs10, styles().fontMedium, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
												{item?.jobType === 'hireOn' ? 'Hire On' : item?.jobType}
											</Text>
										</View>
									</View>
									<View style={[styles().flex, { marginTop: 10 }]}>
										<Text
											numberOfLines={2}
											style={[
												styles().fs12,
												styles().fontBold,
												styles().lh20,
												{
													color: currentTheme.headingColor,
												},
											]}
										>
											{`${item?.title}`}
										</Text>
										<View style={[styles().flexRow, styles().alignCenter, styles().flexWrap, styles().flex, styles().mt5]}>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
												<FontAwesome
													name="briefcase"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
													{item?.type === 'matePilot' ? 'Mate Pilot' : item?.type}
												</Text>
											</View>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
												<FontAwesome
													name="map-pin"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item.city ? item.city : 'N/A'}</Text>
											</View>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
												<Feather
													name="clock"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
													{moment(item?.createdAt).fromNow()}
													{/* {moment(item.expireAt).fromNow('')} */}
												</Text>
											</View>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
												<Ionicons
													name="ios-cash-outline"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
													{/* {`$${item.rates}/${item.ratesType}`} */}
													{`$${item?.rates}/Day`}
												</Text>
											</View>
											<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mt5]}>
												<FontAwesome5
													name="ship"
													size={12}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
													{item?.name_of_vessel ? item?.name_of_vessel : 'N/A'}
												</Text>
											</View>
										</View>
										<View style={[styles().mt10]}>
											<Text
												numberOfLines={3}
												style={[styles().fs10, styles().fontRegular, { color: currentTheme.headingColor }]}
											>
												{renderText(item?.description)}
											</Text>
										</View>
									</View>
								</TouchableOpacity>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={<View style={styles().wh30px} />}
					/>
				</View>
			</View>
			<JobFilter
				ModalHeading={'Filters'}
				modalVisible={isFilter}
				onClose={() => setIsFilter(false)}
				filters={filters => onFilter(filters)}
				clear={() => {
					if (showclearfilter === false) {
						return true
					}
				}}
			/>
		</LayoutView>
	)
}

export default React.memo(Jobs)
