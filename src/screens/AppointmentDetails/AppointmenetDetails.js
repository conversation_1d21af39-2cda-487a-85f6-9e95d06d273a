import { Text, View, TouchableOpacity, Platform, UIManager, LayoutAnimation, ImageBackground, ScrollView } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { handleSocial, removetags } from '../../utils/Constants'
import { useQuery } from '@apollo/client'
import { getLicenseBookingById } from '../../apollo/server'
import Loading from '../../context/Loading/Loading'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const AppointmenetDetails = props => {
	const { appointmenetParams, appointmentId } = props?.route?.params
	const { isLoader } = useContext(Loading)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [show, setshow] = useState('eye-slash')
	console.log('appointmentId :', appointmentId)

	function toggleShow() {
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		if (show === 'eye') {
			setshow('eye-slash')
		} else {
			setshow('eye')
		}
	}
	function formatNumberWithSpaces(number) {
		// Convert number to string
		const numStr = number.toString()
		// Check the length of the string and format accordingly
		if (numStr.length >= 8 && numStr.length <= 11) {
			// For lengths between 8 and 11 digits, format as required
			return numStr.replace(/(.{3})(?=.)/g, '$1 ')
		}
		// Return the number as is if it's not in the desired length range
		return numStr
	}

	const { data, loading, error, refetch } = useQuery(getLicenseBookingById, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			licenseBookingId: appointmentId,
		},
		onCompleted: _data => {
			// console.log('getLicenseBookingById res :', data.getLicenseBookingById);
		},
		onError: err => {
			console.log('getLicenseBookingById err :', err)
		},
	})

	const ALLOWED_TIME = 2 * 60 * 60 * 1000 // 2 hours in milliseconds
	const targetDate = new Date(data?.getLicenseBookingById?.createdAt).getTime()
	const endDate = targetDate + ALLOWED_TIME //
	const now = Date.now()
	const distance = endDate - now

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Appointment Detail'}
			ContentArea={[styles().ph0]}
		>
			<ImageBackground
				resizeMode="cover"
				source={require('../../assets/images/verify-bg.png')}
				style={[styles().ph20, styles().flex]}
			>
				<ScrollView contentContainerStyle={{ flexGrow: 1 }}>
					<View style={[styles().mb20, styles().flexRow, styles().justifyBetween]}>
						<Text style={[styles().fs16, styles().fontMedium, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
							{data?.getLicenseBookingById?.booking?.question ? data?.getLicenseBookingById?.booking?.question : 'N/A'}
						</Text>
					</View>
					<View style={[styles().mb20]}>
						<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>
							{data?.getLicenseBookingById?.booking?.answer ? removetags(data?.getLicenseBookingById?.booking?.answer) : 'N/A'}
						</Text>
					</View>

					<View style={[styles().flex, styles().justifyEnd, styles().mb20]}>
						<View style={[styles().flexRow, styles().alignStart, styles().mb5]}>
							<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Meeting Date:</Text>
							<Text style={[styles().fs14, styles().ph10, styles().fontRegular, { color: currentTheme.blackish }]}>
								{data?.getLicenseBookingById?.meeting_date ? data?.getLicenseBookingById?.meeting_date : 'N/A'}
							</Text>
						</View>
						<View style={[styles().flexRow, styles().alignStart, styles().mb5]}>
							<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Time Slot:</Text>
							<Text style={[styles().fs14, styles().ph10, styles().fontRegular, { color: currentTheme.blackish }]}>
								{data?.getLicenseBookingById?.meeting_time_slot ? data?.getLicenseBookingById?.meeting_time_slot : 'N/A'}
							</Text>
						</View>
						<View style={[styles().flexRow, styles().alignStart, styles().mb5]}>
							<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Fee:</Text>
							<Text style={[styles().fs14, styles().ph10, styles().fontRegular, { color: currentTheme.blackish }]}>
								{`$${Number.parseFloat(
									data?.getLicenseBookingById?.booking?.appointmentPrice ? data?.getLicenseBookingById?.booking?.appointmentPrice : 0
								).toFixed(2)}`}
							</Text>
						</View>
						{data?.getLicenseBookingById?.status === 'confirmed' && (
							<>
								<View style={[styles().flexRow, styles().alignStart, styles().mb5]}>
									<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Meeting Link:</Text>
									<TouchableOpacity
										activeOpacity={0.5}
										style={[styles().flex]}
										onPress={() => handleSocial(data?.getLicenseBookingById?.meeting_link)}
									>
										<Text
											style={[
												styles().fs12,
												styles().fontRegular,
												styles().ph10,
												{
													color: currentTheme.darkBlue,
													textDecorationLine: 'underline',
												},
											]}
										>
											{data?.getLicenseBookingById?.meeting_link}
										</Text>
									</TouchableOpacity>
								</View>
								<View style={[styles().flexRow, styles().alignStart, styles().mb5]}>
									<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Meeting ID:</Text>
									<TouchableOpacity onPress={() => handleSocial(data?.getLicenseBookingById?.payment_link)}>
										<Text style={[styles().fs14, styles().ph10, styles().fontRegular, { color: currentTheme.blackish }]}>
											{formatNumberWithSpaces(data?.getLicenseBookingById?.meeting_id)}
										</Text>
									</TouchableOpacity>
								</View>
								<View style={[styles().flexRow, styles().alignCenter, styles().mb20]}>
									<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Password:</Text>
									<Text style={[styles().fs14, styles().ph10, styles().w100px, styles().fontRegular, { color: currentTheme.blackish }]}>
										{show !== 'eye' ? '*'?.repeat(data?.getLicenseBookingById?.meeting_password?.length) : data?.getLicenseBookingById?.meeting_password}
									</Text>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => toggleShow()}
									>
										<FontAwesome5
											name={show}
											size={16}
											color={show === 'eye' ? currentTheme.themeBackground : currentTheme.B7B7B7}
										/>
									</TouchableOpacity>
								</View>
							</>
						)}
						{!loading &&
							(distance <= 0 && data?.getLicenseBookingById?.status !== 'confirmed'
								? null
								: <ThemeButton
										Title={
											data?.getLicenseBookingById?.status === 'pending'
												? `Pay Now $${Number.parseFloat(data?.getLicenseBookingById?.booking?.appointmentPrice)?.toFixed(2)}`
												: data?.getLicenseBookingById?.status === 'confirmed'
													? 'Join Meeting'
													: 'Expired'
										}
										onPress={() => {
											if (data?.getLicenseBookingById?.status === 'confirmed') {
												handleSocial(data?.getLicenseBookingById?.meeting_link)
											}
											if (data?.getLicenseBookingById?.status === 'pending') {
												handleSocial(data?.getLicenseBookingById?.payment_link)
											}
										}}
									/>)}
					</View>
				</ScrollView>
			</ImageBackground>
		</Layout>
	)
}

export default AppointmenetDetails
