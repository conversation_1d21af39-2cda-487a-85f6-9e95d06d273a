import { Text, View, Keyboard } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import TextField from '../../component/FloatTextField/FloatTextField'
import { useMutation } from '@apollo/client'
import { sendRecoveryEmailVerification } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Spinner from '../../component/Spinner/Spinner'

const Recovery = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [email, setEmail] = useState('')
	const [emailError, setEmailError] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [mutate, { client }] = useMutation(sendRecoveryEmailVerification, {
		errorPolicy: 'all',
		onCompleted: async data => {
			console.log('sendRecoveryEmailVerification res', data)
			FlashMessage({ msg: `Email has been sent to ${email}`, type: 'success' })
			setLoading(false)
			props.navigation.navigate('RecoveryVerification', {
				email: email.trim().toLowerCase(),
			})
		},
		onError: async err => {
			console.log('sendRecoveryEmailVerification err  :', err)
			FlashMessage({ msg: err?.message?.toString(), type: 'danger' })
			setLoading(false)
		},
	})

	async function sendRecoveryEmail() {
		Keyboard.dismiss()
		const emailregex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
		let _status = true
		if (email === '') {
			setEmailError(true)
			_status = false
			return
		}
		if (!emailregex.test(email.trim().toLowerCase())) {
			FlashMessage({ msg: 'Invalid Email Address', type: 'warning' })
			setEmailError(true)
			_status = false
			return
		}

		setLoading(true)
		const data = {
			email: email.trim().toLowerCase(),
		}
		console.log(data)
		await mutate({
			variables: data,
		})
	}

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			keyBoardArea={-10}
			pagetitle={'Recovery Information'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View>
					<Text style={[styles().alignSelfCenter, styles().fs14, styles().lh22, styles().mb20, styles().fontRegular, { color: currentTheme.headingColor }]}>
						Set a recovery email address so we can reach you in case we detect unusual activity in your Account or you accidentally get locked.
					</Text>
				</View>

				<View style={[styles().mb20]}>
					<View style={[styles().mb10]}>
						<Text style={[styles().fs16, styles().fw400, { color: currentTheme.black }]}>Email Address</Text>
					</View>
					<TextField
						keyboardType="default"
						value={email}
						errorText={emailError}
						autoCapitalize="none"
						placeholder={'Email'}
						onChangeText={text => {
							setEmailError(false)
							setEmail(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>

				<View style={[styles().justifyEnd, styles().flex, styles().mb30]}>
					{Loading
						? <Spinner />
						: <ThemeButton
								Title={'Continue'}
								onPress={() => sendRecoveryEmail()}
								// onPress={() => setAccountDeactivate(true)}
							/>}
				</View>
			</View>
		</Layout>
	)
}

export default Recovery
