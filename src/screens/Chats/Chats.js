import { Text, View, TouchableOpacity, Platform, Image, UIManager, FlatList, RefreshControl } from 'react-native'
import React, { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import TextField from '../../component/FloatTextField/FloatTextField'
import { getChatRooms } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import UserContext from '../../context/User/User'
import moment from 'moment'
import { useIsFocused } from '@react-navigation/native'
import { useThrottledPress } from '../../utils/Constants'
import fontStyles from '../../utils/fonts/fontStyles'
import Animated, { Easing, useSharedValue, useAnimatedStyle, withTiming, withDelay } from 'react-native-reanimated'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Chats = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [Search, setSearch] = useState('')
	const [searchData, setSearchData] = useState([])
	const [Chats, setChats] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 50

	const { data, loading, error, refetch } = useQuery(getChatRooms, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filter: {},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'lastMessageAt:desc',
			},
		},
		onCompleted: async res => {
			setChats(prev => [...prev, ...res?.getChatRooms?.results])
			setSearchData(prev => [...prev, ...res?.getChatRooms?.results])
			setLoading(false)
		},
		onError: err => {
			setLoading(false)
			console.log('getChatRooms err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const searchFilterFunction = text => {
		if (text) {
			const newData = Chats?.filter(item => {
				const filterUser = item?.users?.find(users => {
					return users?._id !== user?._id
				})
				const itemData = item.chatName ? item.chatName?.toUpperCase() : filterUser?.name?.toUpperCase() || ''
				const textData = text.toUpperCase()
				return itemData.indexOf(textData) > -1
			})
			setSearchData(newData)
			setSearch(text)
		} else {
			setSearchData(Chats)
			setSearch(text)
		}
	}

	const refresh = async () => {
		setPage(1)
		setChats([])
		setSearchData([])
		setLoading(true)
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'lastMessageAt:desc',
			},
		}).then(res => {
			setChats(res?.data?.getChatRooms?.results || [])
			setSearchData(res?.data?.getChatRooms?.results || [])
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.getChatRooms?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	const handleNavigation = (item, filterUser) => {
		props.navigation.navigate('Chat', {
			item: item,
			chatUser: filterUser,
			roomId: item?._id,
			isAnonymousChat: item?.isAnonymousChat,
		})
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)

	const isFocus = useIsFocused()

	useEffect(() => {
		refetch({
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'lastMessageAt:desc',
			},
		}).then(res => {
			setChats(res?.data?.getChatRooms?.results || [])
			setSearchData(res?.data?.getChatRooms?.results || [])
			setLoading(false)
		})
		setLoading(true)
	}, [isFocus])

	// Animated item component
	const AnimatedItem = ({ item, index }) => {
		// Set up animated values
		const opacity = useSharedValue(0)
		const translateY = useSharedValue(20) // Start a bit lower

		// Create an animated style
		const animatedStyle = useAnimatedStyle(() => {
			return {
				opacity: opacity.value,
				transform: [{ translateY: translateY.value }],
			}
		})

		// Trigger animation on mount
		useEffect(() => {
			opacity.value = withDelay(index * 100, withTiming(1, { duration: 300, easing: Easing.out(Easing.exp) }))
			translateY.value = withDelay(index * 100, withTiming(0, { duration: 300, easing: Easing.out(Easing.exp) }))
		}, [opacity, translateY])

		const filterUser = item?.users?.find(users => {
			return users?._id !== user?._id
		})

		const seen = item?.lastMessage?.status === 'readed'

		return (
			<Animated.View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().mb10, animatedStyle]}>
				<TouchableOpacity
					activeOpacity={0.9}
					onPress={() => throttledButtonPress(item, filterUser)}
					style={[
						styles().flexRow,
						styles().alignCenter,
						styles().justifyBetween,
						styles().pt5,
						styles().pb10,
						styles().bbw1,
						{ borderBottomColor: currentTheme.c707070 },
					]}
				>
					<View>
						<View
							style={[
								styles().wh45px,
								styles().overflowH,
								styles().alignCenter,
								styles().justifyCenter,
								styles().br25,
								{
									borderWidth: item?.isAnonymousChat ? 1 : filterUser?.photo ? 0 : 1,
									borderColor: currentTheme.themeBackground,
								},
							]}
						>
							{item?.chatName
								? <FontAwesome5
										name="users"
										size={20}
										color={currentTheme.themeBackground}
									/>
								: item?.isAnonymousChat
									? <FontAwesome5
											name="user-secret"
											size={20}
											color={currentTheme.themeBackground}
										/>
									: filterUser?.photo
										? <Image
												source={{ uri: filterUser?.photo }}
												style={styles().wh100}
												resizeMode="cover"
											/>
										: <FontAwesome5
												name="user-alt"
												size={18}
												color={currentTheme.themeBackground}
											/>}
						</View>
						<View
							style={[
								styles().wh15px,
								styles().posAbs,
								styles().br10,
								{
									bottom: -2,
									borderWidth: 3,
									backgroundColor: filterUser?.isOnline ? currentTheme.green : currentTheme.D5D4D4,
									borderColor: currentTheme.white,
								},
							]}
						/>
					</View>
					<View style={[styles().flex, styles().ml15]}>
						{item?.isAnonymousChat
							? <Text style={[styles().fs12, styles().mb5, styles().fontMedium, { color: currentTheme.black }]}>{'Anonymous User'}</Text>
							: <Text
									numberOfLines={1}
									style={[styles().fs14, styles().mb5, styles().fontMedium, { color: currentTheme.black }]}
								>
									{item?.chatName ? item?.chatName : filterUser?.name}
								</Text>}
						<Text
							numberOfLines={2}
							style={[
								styles().fs12,
								styles().fontRegular,
								{
									color: !seen ? currentTheme.black : currentTheme.c9F9F9F,
								},
							]}
						>
							{item?.lastMessage?.message}
						</Text>
					</View>
					<View style={[styles().justifyBetween, { paddingLeft: 5 }]}>
						<Text style={[styles().fs10, styles().mb10, styles().mr10, styles().fontMedium, { color: currentTheme.c9F9F9F }]}>
							{moment(item?.lastMessage?.at).fromNow()}
						</Text>
						{item.unseenCount
							? <View
									style={[
										styles().w25px,
										styles().alignSelfEnd,
										styles().alignCenter,
										styles().justifyCenter,
										styles().h17px,
										styles().br10,
										{ backgroundColor: currentTheme.themeBackground },
									]}
								>
									<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.white }]}>{item.unseenCount}</Text>
								</View>
							: null}
					</View>
				</TouchableOpacity>
			</Animated.View>
		)
	}

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Chats'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={styles().flex}>
					<View style={[styles().mb20]}>
						<TextField
							keyboardType="default"
							value={Search}
							autoCapitalize="none"
							placeholder={'Search'}
							onChangeText={text => searchFilterFunction(text)}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							placeholderTextColor={currentTheme.C3C3C3}
							stylesInput={[styles().fs16]}
						/>
					</View>
					<FlatList
						data={searchData}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={Loading}
							/>
						}
						onEndReached={() => nextPage()}
						ListEmptyComponent={() => (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
										fontFamily: fontStyles.PoppinsRegular,
									}}
								>
									{Loading ? 'Loading...' : Search ? 'Not Found...' : 'No Chats'}
								</Text>
							</View>
						)}
						showsVerticalScrollIndicator={false}
						onEndReachedThreshold={0.75}
						contentContainerStyle={{ padding: 3, flexGrow: 1 }}
						renderItem={({ item, index }) => (
							<AnimatedItem
								item={item}
								index={index}
							/>
						)}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={<View style={styles().wh20px} />}
					/>
				</View>
			</View>
		</Layout>
	)
}

export default React.memo(Chats)
