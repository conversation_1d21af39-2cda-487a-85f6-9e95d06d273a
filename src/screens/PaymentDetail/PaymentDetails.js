import { View, Text, TouchableOpacity, Image, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { getInvoiceById } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import moment from 'moment'
import Feather from '@expo/vector-icons/Feather'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Ionicons from '@expo/vector-icons/Ionicons'
import Loading from '../../context/Loading/Loading'

const PaymentDetail = props => {
	const { invoice, invoiceId } = props?.route?.params
	const { isLoader } = useContext(Loading)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [loaderRefesh, setLoaderRefesh] = useState(false)
	console.log('invoice id:', invoiceId)

	const {
		data: invoice_data,
		loading: invoice_loading,
		refetch: invoice_refetch,
	} = useQuery(getInvoiceById, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			invoiceId: invoiceId,
		},

		onCompleted: ({ getInvoiceById }) => {
			console.log('getInvoiceById res :', JSON.stringify(getInvoiceById))
		},
		onError: err => {
			console.log('getInvoiceById err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	// const {
	//   data: transaction_data,
	//   loading: transaction_loading,
	//   refetch: transaction_refetch,
	// } = useQuery(getInvoiceTransaction, {
	//   fetchPolicy: 'no-cache',
	//   variables: {
	//     invoiceId: invoiceId,
	//   },
	//   onCompleted: ({getInvoiceTransaction}) => {
	//     console.log(
	//       'getInvoiceTransaction res :',
	//       JSON.stringify(getInvoiceTransaction),
	//     );
	//   },
	//   onError: err => {
	//     console.log('getInvoiceTransaction err :', err);
	//     // FlashMessage({msg: err.message, type: 'danger'});
	//   },
	// });

	// let transactions = [
	//   {
	//     title: 'Current Update',
	//     desc: transaction_data?.getInvoiceTransaction?.current_update,
	//   },
	//   {
	//     title: 'Description',
	//     desc: transaction_data?.getInvoiceTransaction?.description,
	//   },
	//   {
	//     title: 'Status',
	//     desc: transaction_data?.getInvoiceTransaction?.status,
	//   },
	//   {
	//     title: 'Action',
	//     desc: transaction_data?.getInvoiceTransaction?.action,
	//   },
	//   {
	//     title: 'Receipt Url',
	//     desc: transaction_data?.getInvoiceTransaction?.receipt_url,
	//   },
	// ];

	const refresh = async () => {
		setLoaderRefesh(true)
		await invoice_refetch({
			invoiceId: invoiceId,
		}).then(_res => {
			// console.log(res, 'hello here');
			setLoaderRefesh(false)
		})
		await transaction_refetch({
			invoiceId: invoiceId,
		}).then(_res => {
			// console.log(res, 'hello here');
			setLoaderRefesh(false)
		})
	}

	useEffect(() => {
		isLoader(invoice_loading)
	}, [invoice_loading])

	// console.log('transaction_data?.getInvoiceTransaction :', transaction_data);

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			ContentArea={styles().ph0}
			pagetitle={'Payment Details'}
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => refresh()}
					refreshing={loaderRefesh}
				/>
			}
		>
			{invoice_loading
				? null
				: <View style={[styles().flex]}>
						<View
							style={[
								styles().pb20,
								styles().alignCenter,
								styles().ph20,
								styles().mt10,
								styles().overflowH,
								styles().pt20,
								{
									borderTopLeftRadius: 30,
									borderTopRightRadius: 30,
									backgroundColor: currentTheme.F3F0E4,
								},
							]}
						>
							<TouchableOpacity
								activeOpacity={1}
								style={[
									styles().wh65px,
									styles().br50,
									styles().overflowH,
									styles().justifyCenter,
									styles().alignCenter,
									styles().mr10,
									// styles().boxpeshadow,
									{
										// borderWidth: 0.5,
										borderColor: currentTheme.themeBackground,
									},
								]}
							>
								{invoice_data?.getInvoiceById.paymentOf === 'job' && invoice_data?.getInvoiceById?.application?.company?.photo
									? <Image
											source={{
												uri: invoice_data?.getInvoiceById?.application?.company?.photo,
											}}
											resizeMode="cover"
											style={styles().wh100}
										/>
									: invoice_data?.getInvoiceById.paymentOf === 'checkride' && invoice_data?.getInvoiceById?.checkRide?.company?.photo
										? <Image
												source={{
													uri: invoice_data?.getInvoiceById?.checkRide?.company?.photo,
												}}
												resizeMode="cover"
												style={styles().wh100}
											/>
										: <Ionicons
												name="md-briefcase"
												size={30}
												color={currentTheme.themeBackground}
											/>}
							</TouchableOpacity>

							<View style={[styles().mt10, styles().mb10]}>
								<Text style={[styles().fs12, styles().fontBold, styles().lh20, styles().textCapitalize, { color: currentTheme.headingColor }]}>
									{invoice_data?.getInvoiceById.paymentOf === 'job' ? invoice_data?.getInvoiceById?.application?.job?.title : 'Check Ride'}
								</Text>
							</View>
							<View style={[styles().flexRow, styles().alignCenter, styles().flexWrap, styles().justifyCenter]}>
								{invoice_data?.getInvoiceById.paymentOf === 'job'
									? <View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
											<FontAwesome
												name="briefcase"
												size={12}
												color={currentTheme.E8E8C8}
											/>
											<Text style={[styles().fs10, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
												{invoice_data?.getInvoiceById?.application?.job?.type === 'matePilot'
													? 'Mate Pilot'
													: invoice_data?.getInvoiceById?.application?.job?.type}
											</Text>
										</View>
									: null}
								<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
									<FontAwesome
										name="map-pin"
										size={12}
										color={currentTheme.E8E8C8}
									/>
									{invoice_data?.getInvoiceById?.paymentOf === 'job'
										? <Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
												{invoice_data?.getInvoiceById?.application?.job?.city ? invoice_data?.getInvoiceById?.application?.job?.city : 'N/A'}
											</Text>
										: <Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
												{invoice_data?.getInvoiceById?.checkRide?.company?.city ? invoice_data?.getInvoiceById?.checkRide?.company?.city : 'N/A'}
											</Text>}
								</View>

								<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
									<Feather
										name="clock"
										size={12}
										color={currentTheme.E8E8C8}
									/>
									<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
										{moment(invoice_data?.getInvoiceById?.createdAt).fromNow()}
									</Text>
								</View>

								{invoice_data?.getInvoiceById.paymentOf === 'job'
									? <View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
											<Ionicons
												name="ios-cash-outline"
												size={12}
												color={currentTheme.E8E8C8}
											/>
											<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
												{invoice_data?.getInvoiceById?.amount ? `$${invoice_data?.getInvoiceById?.amount / invoice_data?.getInvoiceById?.jobDays}/Day` : 'N/A'}
												{/* {transaction_data?.getInvoiceTransaction?.amount
                    ? `$${transaction_data?.getInvoiceTransaction?.amount}/Day`
                    : 'N/A'} */}
											</Text>
										</View>
									: null}
							</View>
							<View style={[styles().flexRow, styles().mt20, styles().alignCenter, styles().justifyCenter]}>
								<View
									style={[
										styles().h40px,
										styles().br100,
										styles().alignCenter,
										styles().ph20,
										styles().justifyCenter,
										styles().mr10,
										{
											backgroundColor:
												invoice_data?.getInvoiceById?.status === 'paid'
													? currentTheme.E6FFEA
													: invoice_data?.getInvoiceById?.status === 'unpaid'
														? currentTheme.starColorBG
														: currentTheme.FFE5E5,
										},
									]}
								>
									<Text
										style={[
											styles().fs12,
											styles().textCapitalize,
											styles().fw500,

											{
												letterSpacing: 0.5,
												color:
													invoice_data?.getInvoiceById?.status === 'paid'
														? currentTheme.green
														: invoice_data?.getInvoiceById?.status === 'unpaid'
															? currentTheme.starColor
															: currentTheme.red,
											},
										]}
									>
										{invoice_data?.getInvoiceById?.status}
									</Text>
								</View>
							</View>
						</View>
						<View style={[styles().ph20, styles().pt15, styles().pb35]}>
							<Text style={[styles().fs18, styles().mb5, styles().fw700, { color: currentTheme.themeBackground }]}>Payment Detail</Text>
							<View style={[styles().pv15, styles().mt10, styles().ph20, styles().br10, { backgroundColor: currentTheme.F5F9FC, marginBottom: 25 }]}>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Amount:</Text>
									{invoice_data?.getInvoiceById?.paymentOf === 'job'
										? <Text
												numberOfLines={1}
												style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.c444D6E, textAlign: 'right' }]}
											>
												{invoice_data?.getInvoiceById?.amount ? `$${invoice_data?.getInvoiceById?.amount}` : 'N/A'}
											</Text>
										: <Text
												numberOfLines={1}
												style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.c444D6E, textAlign: 'right' }]}
											>
												{invoice_data?.getInvoiceById?.amount ? `$${invoice_data?.getInvoiceById?.amount}` : 'N/A'}
											</Text>}
								</View>
								{/* {transaction_data?.getInvoiceTransaction?.negative_balance ? (
                <View
                  style={[
                    styles().flexRow,
                    styles().mb10,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs14,
                      styles().fw700,
                      styles().w60,
                      {color: currentTheme.headingColor},
                    ]}>
                    Amount Deduction:
                  </Text>
                  <Text
                    numberOfLines={1}
                    style={[
                      styles().fs14,
                      styles().fw400,
                      styles().w40,
                      {color: currentTheme.c444D6E, textAlign: 'right'},
                    ]}>
                    {`$${
                      transaction_data?.getInvoiceTransaction?.negative_balance
                        ?.amount
                        ? transaction_data?.getInvoiceTransaction
                            ?.negative_balance?.amount
                        : 'N/A'
                    }`}
                  </Text>
                </View>
              ) : null} */}
								{invoice_data?.getInvoiceById?.checkRide
									? <View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
											<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Hitch Days:</Text>
											<Text
												numberOfLines={1}
												style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.c444D6E, textAlign: 'right' }]}
											>
												{`${invoice_data?.getInvoiceById?.checkRide?.checkRideData?.hitchMin}` +
													' - ' +
													`${invoice_data?.getInvoiceById?.checkRide?.checkRideData?.hitchMax}`}
											</Text>
										</View>
									: null}
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Payment Date:</Text>
									<Text
										numberOfLines={1}
										style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.c444D6E, textAlign: 'right' }]}
									>
										{moment(invoice_data?.getInvoiceById?.updatedAt).format('LL')}
									</Text>
								</View>
							</View>
							{/* <Text
              style={[
                styles().fs18,
                styles().mb5,
                styles().fw700,
                {color: currentTheme.themeBackground},
              ]}>
              Transaction Detail
            </Text> */}
							{/* <View
              style={[
                styles().pv15,
                styles().mt10,
                styles().ph20,
                styles().br10,
                {backgroundColor: currentTheme.F5F9FC, marginBottom: 25},
              ]}>
              {transactions?.map((item, i) => {
                return (
                  <View
                    style={[
                      // styles().flexRow,
                      styles().mb10,
                      styles().alignStart,
                      // styles().justifyBetween,
                    ]}>
                    <Text
                      style={[
                        styles().fs14,
                        styles().fw700,
                        styles().mb5,
                        {color: currentTheme.headingColor},
                      ]}>
                      {`${item?.title}:`}
                    </Text>
                    <Text
                      style={[
                        styles().fs12,
                        styles().fw400,
                        {color: currentTheme.c444D6E},
                      ]}>
                      {item?.desc ? item?.desc : 'N/A'}
                    </Text>
                  </View>
                );
              })}
            </View> */}
							{/* {transaction_data?.getInvoiceTransaction?.money_movement?.length >
            0 ? (
              <Text
                style={[
                  styles().fs18,
                  styles().mb15,
                  styles().fw700,
                  {color: currentTheme.themeBackground},
                ]}>
                Payment Track
              </Text>
            ) : null} */}
							{/* {transaction_data?.getInvoiceTransaction?.money_movement?.map(
              (item, i) => {
                return (
                  <View
                    key={i}
                    style={[
                      styles().pall10,
                      styles().bw05,
                      styles().br5,
                      styles().mb5,
                      {borderColor: currentTheme.C3C3C3},
                    ]}>
                    <Text
                      style={[
                        styles().fs12,
                        {color: currentTheme.headingColor, marginBottom: 3},
                      ]}>
                      {item?.title}
                    </Text>
                    <Text
                      style={[styles().fs10, {color: currentTheme.c9E9E9E}]}>
                      {item?.description}
                    </Text>
                  </View>
                );
              },
            )} */}
						</View>
					</View>}
		</Layout>
	)
}

export default PaymentDetail
