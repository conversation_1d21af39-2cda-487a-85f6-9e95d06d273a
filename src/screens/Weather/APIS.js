import getEnvVars from '../../../environment'
const { TOMMOROW_IO_API_KEY } = getEnvVars()

export const getForecast = async (location, units) => {
	const url = `https://api.tomorrow.io/v4/weather/forecast?location=${location}&apikey=${TOMMOROW_IO_API_KEY}&units=${units}`
	try {
		let response = await fetch(url, {
			method: 'get',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
			},
		})
		if (!response.ok) {
			throw await response.json()
		}
		response = await response.json()
		// console.log(`getForecast res :`, JSON.stringify(response?.timelines));
		const status = { success: true }
		return { ...response?.timelines, ...status }
	} catch (error) {
		const status = { success: false }
		console.log('getForecast Err :', error)
		return { ...error, ...status }
	}
}
