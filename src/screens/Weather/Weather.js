import { Text, View, Image, Animated, Easing } from 'react-native'
import { useContext, useEffect, useRef, useState } from 'react'
import Layout from '../../component/Layout/Layout'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { iconImages, iconImagesCommon } from './Tommorow.io-imageSet'
import moment from 'moment'
import LinearGradient from 'react-native-linear-gradient'
import GeolocationService from 'react-native-geolocation-service'
import { getForecast } from './APIS'
import { weatherCodeDay, weatherCodeNight } from './Tommorow.io-WeatherCode'
import { get_city_from_lat_long } from '../../component/WeatherWidget/API'
import Lottie from 'lottie-react-native'

const Weather = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	// metric for C and imperial for F
	// wind unit in metric m/s and in imperial kph
	const units = 'imperial'
	const temperatureUnit = units === 'imperial' ? 'F' : 'C'
	// const {isLoader, loading} = useContext(Loading);
	const [Loader, setLoader] = useState(false)
	var isDay = new Date().getHours() >= 6 && new Date().getHours() < 18
	const [weatherErr, setWeatherErr] = useState('Unable to Find Weather Updates, Try Again Later')
	const [city, setCity] = useState('')
	const [todayForecast, setTodayForecast] = useState({})
	const [forecast, setForecast] = useState([])

	const todayWeatherCode = `${todayForecast?.values?.weatherCodeMax}`?.concat(isDay ? 0 : 1)

	const weatherInfo = [
		{
			image: require('../../assets/images/tommorow.io/wind.png'),
			value: `${Number.parseInt(todayForecast?.values?.windSpeedMax)}`, //in metrics m/s
			type: 'Wind',
			unit: units === 'imperial' ? 'mph' : 'm/s',
		},
		{
			image: require('../../assets/images/tommorow.io/humidity.png'),
			value: `${Number.parseInt(todayForecast?.values?.humidityAvg)}`,
			type: 'Humidity',
			unit: '%',
		},
		{
			image: require('../../assets/images/tommorow.io/rain.png'),
			value: `${Number.parseInt(todayForecast?.values?.precipitationProbabilityMax)}`,
			type: 'Rain',
			unit: '%',
		},
	]

	const DisplayImageBasedOnWeather = ({ responseNumber, style, forecast, isAnimated }) => {
		const is_available = Object.hasOwn(iconImagesCommon, responseNumber)
		const _imageSource = forecast ? iconImagesCommon[responseNumber] : iconImages[responseNumber]

		const img = is_available ? iconImagesCommon[responseNumber] : iconImages[`${responseNumber}`?.concat(isDay ? 0 : 1)]

		const swingAnim = useRef(new Animated.Value(0)).current

		useEffect(() => {
			if (isAnimated) {
				const swing = Animated.loop(
					Animated.sequence([
						Animated.timing(swingAnim, {
							toValue: 1,
							duration: 600,
							easing: Easing.inOut(Easing.ease),
							useNativeDriver: true,
						}),

						Animated.timing(swingAnim, {
							toValue: -1,
							duration: 600,
							easing: Easing.inOut(Easing.ease),
							useNativeDriver: true,
						}),
					])
				)

				swing.start()
				// Cleanup the animation on unmount
				return () => swing.stop()
			}
		}, [swingAnim])

		const rotate = swingAnim.interpolate({
			inputRange: [-1, 0, 1],
			outputRange: ['-3deg', '0deg', '3deg'],
		})

		return (
			<View style={[styles().wh80px, styles().overflowH, style]}>
				{/* <Image source={img} style={styles().wh100} resizeMode="contain" /> */}
				<Animated.Image
					source={img}
					style={[
						styles().wh100,
						{
							transform: [{ rotate }],
						},
					]}
					resizeMode="contain"
				/>
			</View>
		)
	}

	const AnimatedBar = ({ percentage }) => {
		const animatedValue = useRef(new Animated.Value(0)).current

		useEffect(() => {
			Animated.timing(animatedValue, {
				toValue: percentage,
				duration: 1000, // Adjust the duration as needed
				useNativeDriver: false, // Set to false because we are animating layout properties
			}).start()
		}, [percentage])

		const animatedWidth = animatedValue.interpolate({
			inputRange: [0, 100],
			outputRange: ['0%', '100%'],
		})

		const animatedRight = animatedValue.interpolate({
			inputRange: [0, 100],
			outputRange: ['100%', '0%'],
		})

		const animatedLeft = animatedValue.interpolate({
			inputRange: [0, 100],
			outputRange: ['0%', '100%'],
		})

		return (
			<Animated.View
				style={[
					{
						width: animatedWidth,
						right: animatedRight,
						left: animatedLeft,
						height: '100%',
						borderRadius: 100,
						backgroundColor: '#A4903C',
					},
				]}
			/>
		)
	}

	const SunPosition = ({ type, time }) => {
		const image = type === 'sunrise' ? require('../../assets/images/tommorow.io/sunrise.png') : require('../../assets/images/tommorow.io/sunset.png')
		return (
			<View style={[styles().alignCenter]}>
				<View style={[styles().flexRow, styles().alignCenter]}>
					<View style={[styles().wh25px, styles().overflowH, styles().mb5]}>
						<Image
							source={image}
							style={styles().wh100}
							resizeMode="contain"
						/>
					</View>
					<Text style={[styles().fs14, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.black }]}>{type}</Text>
				</View>
				<Text style={[styles().fs14, styles().ml5, styles().fontRegular, { color: currentTheme.c737373 }]}>{moment(time).format('LT')}</Text>
			</View>
		)
	}

	async function getWeather() {
		// isLoader(true);
		setLoader(true)
		GeolocationService.getCurrentPosition(
			async position => {
				const { latitude, longitude } = position?.coords
				// const location = `24.8607,67.0011`;
				// console.log(`${latitude},${longitude}`);
				if (!latitude && !longitude) {
					setWeatherErr('Unable To Find Your Location')
					// isLoader(false);
					setLoader(false)
					return
				}
				await get_city_from_lat_long(latitude, longitude).then(async res => {
					if (res !== undefined) {
						setCity(res?.long_name)
					}
				})
				const location = `${latitude},${longitude}`
				const response = await getForecast(location, units)
				if (response?.success) {
					const remove_today = response?.daily?.slice(1)
					setTodayForecast(response?.daily[0])
					setForecast(remove_today)
					setWeatherErr('')
				} else {
					console.log(response)
					setWeatherErr(response?.message)
				}
				// isLoader(false);
				setLoader(false)
			},
			error => {
				// See error code charts below.
				setWeatherErr('Unable To Find Your Location')
				console.log('GeolocationService in Weather Err :', error)
			},
			{ enableHighAccuracy: true, timeout: 20000, maximumAge: 10000 }
		)
	}

	useEffect(() => {
		getWeather()
	}, [])

	if (Loader) {
		return (
			<Layout
				navigation={props.navigation}
				LeftIcon={true}
				headerShown={true}
				withoutScroll={false}
				pagetitle={'Weather'}
				ContentArea={[styles().alignCenter, styles().justifyCenter]}
			>
				<Lottie
					source={require('./../../assets/images/weather/weatherloader.json')}
					autoPlay={true}
					loop={true}
					style={{ height: 120, width: 120 }}
				/>
			</Layout>
		)
	}
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Weather'}
			ContentArea={[styles().ph20]}
		>
			{weatherErr
				? <View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
						<View style={[styles().wh100px, styles().overflowH, styles().mb15]}>
							<Image
								source={require('../../assets/images/weather/err.png')}
								style={[styles().wh100, { tintColor: currentTheme.navyBlue }]}
								resizeMode="contain"
							/>
						</View>
						<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.navyBlue }]}>{weatherErr}</Text>
					</View>
				: <View style={[styles().flex, styles().pb30]}>
						<View style={[styles().mb5, styles().alignSelfStart, styles().pb5]}>
							<Text style={[styles().fs18, styles().fontMedium, { color: currentTheme.black }]}>{city}</Text>
							<View
								style={[
									styles().bbw1,
									styles().mv5,
									{
										borderBottomColor: currentTheme.B7B7B7,
									},
								]}
							/>
							<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.blackish }]}>{moment(new Date()).format('LLL')}</Text>
						</View>

						<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
							<Text style={[styles().fontBold, { fontSize: 55, color: currentTheme.black }]}>
								{Number.parseInt(todayForecast?.values?.temperatureApparentAvg)}
							</Text>
							<Text style={[styles().fontBold, styles().mr20, { fontSize: 20, bottom: 12, color: currentTheme.black }]}>°{temperatureUnit}</Text>

							<Text style={[styles().fs16, styles().flex, styles().textCenter, styles().fontMedium, { color: currentTheme.black }]}>
								{isDay ? weatherCodeDay[todayWeatherCode] : weatherCodeNight[todayWeatherCode]}
							</Text>
							<DisplayImageBasedOnWeather
								responseNumber={todayForecast?.values?.weatherCodeMax}
								forecast={false}
								isAnimated={true}
							/>
						</View>

						<View
							style={[
								styles().br10,
								styles().mt20,
								styles().pv10,
								styles().ph30,
								styles().flexRow,
								styles().alignEnd,
								styles().justifyBetween,
								{ backgroundColor: currentTheme.navyBlue },
							]}
						>
							{weatherInfo?.map((item, i) => {
								return (
									<View
										key={i}
										style={[styles().alignCenter]}
									>
										<View style={[i === 1 ? styles().wh30px : styles().wh35px, styles().overflowH, styles().mb5]}>
											<Image
												source={item?.image}
												style={styles().wh100}
												resizeMode="contain"
											/>
										</View>
										<Text style={[styles().fs14, styles().fontBold, { color: currentTheme.white }]}>{`${item?.value} ${item?.unit}`}</Text>
										<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.white }]}>{item?.type}</Text>
									</View>
								)
							})}
						</View>

						<View style={[styles().flexRow, styles().mt30, styles().alignCenter, styles().justifyBetween]}>
							<SunPosition
								time={todayForecast.values?.sunriseTime}
								type={'sunrise'}
							/>
							<View style={[styles().overflowH, styles().mb5, { flex: 1, height: 50 }]}>
								<Image
									source={require('../../assets/images/tommorow.io/sun_status.png')}
									style={styles().wh100}
									resizeMode="contain"
								/>
							</View>
							<SunPosition
								time={todayForecast.values?.sunsetTime}
								type={'sunset'}
							/>
						</View>
						<View style={[styles().overflowH, styles().mv10, { height: 1 }]}>
							<View style={[styles().bw1, { borderColor: currentTheme.B7B7B7, borderStyle: 'dashed' }]} />
						</View>

						<View style={[styles().flexRow, styles().justifyBetween, styles().alignCenter, styles().mt10]}>
							<Text style={[styles().fs18, styles().fontMedium, { color: currentTheme.themeBackground }]}>Forecast</Text>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.themeBackground }]}>{`Next ${forecast?.length} Days`}</Text>
						</View>
						{forecast?.map((item, i) => {
							const range_of_intereast = item?.values?.temperatureApparentMax - item?.values?.temperatureApparentMin
							const progress = (item?.values?.temperatureApparentAvg - item?.values?.temperatureApparentMin) / range_of_intereast
							const percentage = progress * 100
							// console.log(Math.abs(percentage));
							return (
								<View
									key={i}
									style={[styles().flexRow, styles().justifyCenter, styles().mt15, styles().alignCenter]}
								>
									<Text
										numberOfLines={1}
										style={[
											styles().fs12,
											styles().w80px,
											styles().fontMedium,
											{ color: currentTheme.black },
											// {backgroundColor: 'teal'},
										]}
									>
										{moment(item?.time).format('dddd')}
									</Text>
									<View
										style={[
											styles().flexRow,
											styles().alignCenter,
											styles().justifyCenter,
											styles().flex,
											// styles().ph20,
											// {backgroundColor: 'teal'},
										]}
									>
										<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black }]}>{Number.parseInt(item?.values?.temperatureMin)}°</Text>
										<LinearGradient
											colors={['#beb389', '#EEE8D5']}
											style={{
												height: 10,
												flex: 1,
												borderRadius: 100,
												overflow: 'hidden',
												marginHorizontal: 10,
											}}
										>
											<AnimatedBar percentage={percentage} />
										</LinearGradient>
										<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black }]}>{Number.parseInt(item?.values?.temperatureMax)}°</Text>
									</View>
									<View style={[styles().w50px, styles().alignEnd]}>
										<DisplayImageBasedOnWeather
											responseNumber={item?.values?.weatherCodeMax}
											style={[styles().wh35px]}
											forecast={true}
										/>
									</View>
								</View>
							)
						})}
					</View>}
		</Layout>
	)
}

export default Weather
