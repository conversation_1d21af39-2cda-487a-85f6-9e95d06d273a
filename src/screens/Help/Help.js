import { Text, View, TouchableOpacity, LayoutAnimation, FlatList } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Feather from '@expo/vector-icons/Feather'
import LayoutView from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import LinearGradient from 'react-native-linear-gradient'
import SupportPopup from '../../component/Modals/SupportPopup'
import UserContext from '../../context/User/User'

const Help = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [faqShow, setFaqShow] = useState(false)
	const [helpModal, setHelpModal] = useState(false)
	const user = useContext(UserContext)
	const check_disposable = user?.disposable

	const faqList = [
		{
			id: 0,
			title: 'Who is the Riverbank Professionals App for?',
			content:
				'The Riverbank Professionals App is for crewmembers and marine companies. The former can look for work on their days off, while the latter can keep their boat crewed at all times. And the app is completely free.',
		},
		{
			id: 1,
			title: 'Is my data secure on the app?',
			content:
				'Your privacy is important to us, and we have implemented highly advanced security measures to protect your data. However, you must also play your part by not sharing your login credentials with anyone, including friends and family.',
		},
		{
			id: 2,
			title: 'How can I verify my account?',
			content:
				'We’ve made it simple and hassle-free for users to verify their accounts. Simply create an account by providing authentic and up-to-date information, after which the Admin will check it. If everything is how it should be, you’ll see a blue mark on your profile, meaning it’s verified.',
		},
		{
			id: 3,
			title: 'Do you provide license renewal consultancy on the app?',
			content:
				'Yes, we provide Coast Guard License Renewal consultancy on the app. Once you submit the required documents, we’ll review them and schedule a Coast Guard Physical. We will also notify you when the license is about to expire, so you can renew it on time.',
		},
		{
			id: 4,
			title: 'How will I get the payment after completing the job?',
			content:
				'As of now, we don’t have a payment method integrated into our app. However, we’re working day and night to add that feature. So, for now, the Admin of our app is responsible for sending the payments on time.',
		},
		// {
		//   id: 5,
		//   title: 'How can I make sure I’m hiring the right crewmembers?',
		//   content:
		//     'The crewmembers have to go through a thorough vetting process before we display their profiles on our apps. Therefore, when you’re hiring one for your boat, you can rest assured that they’re highly qualified and skilled.',
		// },
	]

	return (
		<LayoutView
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Help and Support'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={styles().flex}>
					<Text style={[styles().fs16, styles().mb10, styles().fontMedium, styles().mt10, { color: currentTheme.themeBackground }]}>
						Frequently Asked Questions
					</Text>
					<FlatList
						data={faqList}
						showsVerticalScrollIndicator={false}
						onEndReachedThreshold={0.75}
						contentContainerStyle={{ padding: 3, flexGrow: 1 }}
						renderItem={({ item, index }) => {
							return (
								<View style={[styles().boxpeshadowProduct, styles().mb15, styles().br10, styles().pv15, styles().ph20]}>
									<TouchableOpacity
										activeOpacity={0.7}
										onPress={() => {
											setFaqShow(prevIndex => (index === prevIndex ? '' : index))
											LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
										}}
										style={[styles().flexRow, styles().flex, { flexGrow: 1 }, styles().alignCenter, styles().justifyBetween]}
									>
										<Text style={[styles().fs14, styles().flex, styles().pr10, styles().fontMedium, { color: currentTheme.headingColor }]}>{item.title}</Text>
										<Feather
											name={faqShow === index ? 'minus' : 'plus'}
											size={20}
											color={currentTheme.B7B7B7}
										/>
									</TouchableOpacity>
									{faqShow === index
										? <View style={[styles().mt5]}>
												<Text style={[styles().fs12, styles().lh20, styles().fontRegular, { color: currentTheme.c444D6E }]}>{item.content}</Text>
											</View>
										: null}
								</View>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
					/>
					<View
						style={{
							flex: 1,
							paddingBottom: 20,
							justifyContent: 'flex-end',
						}}
					>
						{check_disposable === 'false' && (
							<ThemeButton
								Title={'Report An Issue'}
								onPress={() => props.navigation.navigate('ReportIssue')}
								Style={[
									styles().br10,
									styles().mt30,
									{
										backgroundColor: currentTheme.B7B7B7,
										borderColor: currentTheme.B7B7B7,
									},
								]}
								StyleText={[styles().fs16, { color: currentTheme.black }]}
							/>
						)}
						<TouchableOpacity
							onPress={() => props.navigation.navigate('TermsAndConditions')}
							style={[styles().mt15]}
							activeOpacity={0.7}
						>
							<LinearGradient
								colors={['#B3A36E', '#AA9750', '#A4903C']}
								style={[
									styles().h50px,
									styles().alignCenter,
									styles().flexRow,
									styles().justifyCenter,
									styles().br10,
									styles().flex,
									styles().pv15,
									styles().ph25,
								]}
							>
								<Text style={[styles().fs16, styles().fontMedium, { color: currentTheme.white }]}>Terms & Conditions</Text>
							</LinearGradient>
						</TouchableOpacity>
					</View>
				</View>
			</View>
			<SupportPopup
				modalVisible={helpModal}
				onClose={() => setHelpModal(false)}
				navigation={props.navigation}
			/>
		</LayoutView>
	)
}

export default Help
