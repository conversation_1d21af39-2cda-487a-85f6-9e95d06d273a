import { Text, View, Image } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import { profile } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import styles from '../styles'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Spinner from '../../component/Spinner/Spinner'
import { CommonActions } from '@react-navigation/native'
import Loading from '../../context/Loading/Loading'

const NetworkStatus = props => {
	const themeContext = useContext(ThemeContext)
	const { isLoader } = useContext(Loading)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loader, setLoader] = useState(false)

	const { data, loading, error, refetch } = useQuery(profile, {
		fetchPolicy: 'cache-and-network',
		errorPolicy: 'all',
		onCompleted: async data => {
			console.log('Profile res  in network:', JSON.stringify(data?.profile))
			setLoader(false)
			if (data?.profile?._id) {
				navigateDrawer()
			} else {
				navigateAuth()
			}
		},
		onError: async err => {
			console.log('Profile err in network:', err.message)
			setLoader(false)
			if (err.message === 'Please authenticated') {
				navigateAuth()
			}
		},
	})

	async function reload() {
		setLoader(true)
		await refetch()
	}

	const navigateDrawer = () => {
		props.navigation.dispatch(
			CommonActions.reset({
				index: 0,
				routes: [{ name: 'noDrawer' }],
			})
		)
	}

	const navigateAuth = () => {
		props.navigation.dispatch(
			CommonActions.reset({
				index: 0,
				routes: [{ name: 'Auth' }],
			})
		)
	}

	useEffect(() => {
		isLoader(false)
	}, [])

	return (
		<View style={[styles().ph20, styles().flex, { alignItems: 'center', justifyContent: 'center' }]}>
			<View style={[styles().h250px, { width: '70%' }]}>
				<Image
					source={require('../../assets/images/no-internet.png')}
					resizeMode="contain"
					style={[styles().wh100]}
				/>
			</View>
			<Text style={[styles().fontBold, styles().mv10, { fontSize: 24, color: currentTheme.black }]}>Whoops!!</Text>
			<Text style={[styles().fontRegular, styles().w80, styles().textCenter, styles().mb10, { fontSize: 14, color: currentTheme.black }]}>
				No internet connection found, Check your internet connection or try again.
			</Text>
			{Loader ? (
				<View style={[styles().mt20]}>
					<Spinner />
				</View>
			) : (
				<ThemeButton
					Title={'Try Again'}
					Style={[styles().w80]}
					onPress={async () => await reload()}
				/>
			)}
		</View>
	)
}

export default NetworkStatus
