import {
	Text,
	View,
	TouchableOpacity,
	Image,
	FlatList,
	SafeAreaView,
	ActivityIndicator,
	ScrollView,
	KeyboardAvoidingView,
	StatusBar,
	Dimensions,
	RefreshControl,
	Platform,
	LayoutAnimation,
	ImageBackground,
	UIManager,
} from 'react-native'
import React, { useContext, useEffect, useState, useRef } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import AntDesign from '@expo/vector-icons/AntDesign'
import Ionicons from '@expo/vector-icons/Ionicons'
import TextField from '../../component/FloatTextField/FloatTextField'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { Ratings, createPost, myFriendships, posts, updateUser } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import ImageView from 'react-native-image-viewing'
import UserContext from '../../context/User/User'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import { useIsFocused } from '@react-navigation/native'
import { formatPhoneNumber, useThrottledPress } from '../../utils/Constants'
import PostComponent from '../../component/PostComponent/PostComponent'
import Video from 'react-native-video'
import Spinner from '../../component/Spinner/Spinner'
import PostSkeleton from '../../component/Skeleton/PostSkeleton'
import AsyncStorage from '@react-native-async-storage/async-storage'
import VideoThumbnail from '../../component/VideoThumbnail/VideoTHumbnail'
import fontStyles from '../../utils/fonts/fontStyles'
import RequestForBadges from '../../component/Modals/RequestForBadges'
import { ErrorHandler } from '../../component/ErrorHandler/ErrorHandler'
import LinearGradient from 'react-native-linear-gradient'

const { width } = Dimensions.get('window')
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Profile = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const user = useContext(UserContext)
	const [asyncUser, setAsyncUser] = useState('')
	const _checkers = user?.checkr_decision !== 'ENGAGE' || user?.checkr_decision !== 'PENDING'
	const isLearn = user?.waterways_learning_enabled
	const [post, setPost] = useState({
		author: user?._id,
		images: '',
		videos: '',
		content: '',
	})

	const [ispostPicker, setIspostPicker] = useState({
		image: false,
		video: false,
	})
	const [isProfilePicker, setProfilePicker] = useState(false)
	const [loader1, setLoader1] = useState(false)
	const [photo, setPhoto] = useState('')
	const [isCoverPicker, setCoverPicker] = useState(false)
	const [loader2, setLoader2] = useState(false)
	const [coverPhoto, setCoverPhoto] = useState('')
	const [postLoader, setPostLoader] = useState(false)
	const [page, _setPage] = useState(1)
	const pageSize = 7
	const [visible, setIsVisible] = useState({
		dp: false,
		cp: false,
	})
	const [postPage, setPostPage] = useState(1)
	const postPageSize = 30
	const [getPosts, setGetPosts] = useState([])
	const [Loading, setLoading] = useState(false)
	const [enablePost, setEnablePost] = useState(true)
	const [badgeModal, setBadgeModal] = useState(false)
	const [ratings, setRatings] = useState([
		{ percentage: 0, stars: 5 },
		{ percentage: 0, stars: 4 },
		{ percentage: 0, stars: 3 },
		{ percentage: 0, stars: 2 },
		{ percentage: 0, stars: 1 },
	])

	const changeProfilePicture = async data => {
		setPhoto(data)
		setLoader1(false)
		await mutate({
			variables: {
				updateUserInput: {
					photo: data,
				},
			},
		})
	}

	const changeCoverPicture = async data => {
		setCoverPhoto(data)
		setLoader2(false)
		await mutate({
			variables: {
				updateUserInput: {
					coverImage: data,
				},
			},
		})
	}

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Profile Updated!',
				type: 'success',
			})
		} catch (e) {
			console.log('catch updateUser:', e)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		ErrorHandler(error.message)
		console.log('updateUser error  :', error)
	}

	const {
		data: ratingData,
		loading: ratingLoading,
		refetch: refetchRatings,
	} = useQuery(Ratings, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		variables: {
			filters: {
				user: user?._id,
			},
			options: {
				page: 1,
				limit: 100000,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			try {
				// console.log('Ratings res :', JSON.stringify(data?.Ratings));
				const updatedRatings = [...ratings] // Create a copy of the original ratings array
				data.Ratings?.results?.forEach(result => {
					const avgRating = result.avgRating

					// Find the index of the rating in the ratings array that corresponds to the average rating
					const index = updatedRatings.findIndex(rating => rating.stars === Math.round(avgRating))
					if (index !== -1) {
						// Update the percentage based on the calculation
						updatedRatings[index].percentage += 100 / data.Ratings?.results?.length
					}
				})

				// Set the updated ratings state
				setRatings(updatedRatings)
			} catch (error) {
				console.log(error)
			}
		},
		onError: err => {
			console.log('Ratings err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const { data, loading, error, refetch } = useQuery(myFriendships, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			userId: asyncUser._id ? asyncUser._id : user?._id,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: _data => {
			// console.log('myFriendships res :', JSON.stringify(data?.myFriendships));
		},
		onError: err => {
			console.log('myFriendships err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: postData,
		loading: postLoading,
		refetch: postRefetch,
	} = useQuery(posts, {
		fetchPolicy: 'no-cache',
		variables: {
			filters: {
				author: user?._id,
			},
			options: {
				page: postPage,
				limit: postPageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			// console.log('posts res :', res);
			setGetPosts(prev => [...prev, ...res?.Posts?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('posts err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPostPage(1)
		setGetPosts([])
		// await user?.refetch()
		await postRefetch({
			options: {
				page: 1,
				limit: postPageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setGetPosts(res?.data?.Posts?.results)
			setLoading(false)
			// console.log('refresh');
		})
		await refetch()
	}

	const nextPage = async () => {
		if (postPage < postData?.Posts?.totalPages) {
			setPostPage(old => old + 1)
			setLoading(true)
			await postRefetch()
		}
	}

	const removePost = async _id => {
		const removePost = await getPosts?.filter(obj => obj?._id !== _id)
		setGetPosts(removePost)
	}

	const likePost = async _id => {
		const updateLikes = getPosts?.map(post => {
			if (post._id === _id) {
				const isLiked = post?.likes?.includes(user?._id)
				return {
					...post,
					likes: isLiked ? post?.likes?.filter(id => id !== user?._id) : [...post?.likes, user?._id],
				}
			}
			return post
		})

		if (updateLikes) {
			setGetPosts(updateLikes)
		}
	}

	const [postMutate, {}] = useMutation(createPost, {
		onCompleted: async ({ createPost }) => {
			// console.log('createPost res :', createPost);
			setPost({
				...post,
				images: '',
				videos: '',
				content: '',
				author: user?._id,
			})
			setPostLoader(false)
			setEnablePost(true)
			if (createPost?.status === 'active') {
				const newPost = []
				newPost.push(createPost)
				setGetPosts(prev => [...newPost, ...prev])
			} else {
				FlashMessage({
					msg: 'Post Submited, Will be Approved By Admin',
					type: 'warning',
					duration: 7000,
				})
			}
		},
		onError: err => {
			console.log('createPost err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setPostLoader(false)
		},
	})

	const handleInputChange = text => {
		setPost({ ...post, content: text })
		setEnablePost(!(text.trim().length > 0))
	}

	async function Post() {
		if (post.images === '' && post.videos === '' && post.content === '') {
			// FlashMessage({msg: 'Post data requied', type: 'info'});
			setEnablePost(false)
			return
		}
		let data
		if (post.videos !== '') {
			data = {
				author: user?._id,
				videos: post.videos,
				content: post.content?.trim(),
			}
		}
		if (post.images !== '') {
			data = {
				author: user?._id,
				images: post.images,
				content: post.content?.trim(),
			}
		}
		if (post.images === '' && post.videos === '') {
			data = {
				author: user?._id,
				content: post.content?.trim(),
			}
		}
		console.log(data)
		setPostLoader(true)
		await postMutate({
			variables: {
				inputPost: data,
			},
		})
	}

	const scrollViewRef = useRef()
	const [_visibleItemIndex, setVisibleItemIndex] = useState(0)

	const _handleScroll = nativeEvent => {
		const contentOffsetY = nativeEvent.contentOffset.y
		const screenHeight = Dimensions.get('window').height

		// Calculate the index of the visible item based on scroll position and item height
		const newIndex = Math.floor(contentOffsetY / screenHeight)
		setVisibleItemIndex(newIndex)
	}

	// console.log('visibleItemIndex======>', visibleItemIndex);

	const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
		const paddingToBottom = 20
		return layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
	}

	const handleNavigation = item => {
		props.navigation.navigate('ViewFriendProfile', {
			userId: item?._id,
			isFriend: true,
		})
	}
	const throttledButtonPress = useThrottledPress(handleNavigation)

	const getuser = async () => {
		const userAsync = await AsyncStorage.getItem('user')
		const user = JSON.parse(userAsync)
		setAsyncUser(user)
	}

	useEffect(() => {
		setPhoto(user?.photo)
		setCoverPhoto(user?.coverImage)
		getuser()
		// refresh()
	}, [isFocus])

	const DP = [{ uri: photo }]
	const CP = [{ uri: coverPhoto }]

	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			{photo
				? <ImageView
						images={DP}
						imageIndex={0}
						presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
						visible={visible.dp}
						onRequestClose={() => setIsVisible({ ...visible, dp: false })}
					/>
				: null}
			{coverPhoto
				? <ImageView
						images={CP}
						imageIndex={0}
						presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
						visible={visible.cp}
						onRequestClose={() => setIsVisible({ ...visible, cp: false })}
					/>
				: null}
			<SafeAreaView style={[styles().flex, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
					style={[styles().flex]}
				>
					<ScrollView
						ref={scrollViewRef}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => {
									refresh()
								}}
								refreshing={Loading}
							/>
						}
						onScroll={({ nativeEvent }) => {
							// handleScroll(nativeEvent)
							if (isCloseToBottom(nativeEvent)) {
								nextPage()
							}
						}}
						scrollEventThrottle={400}
						contentContainerStyle={[{ flexGrow: 1 }]}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps={'handled'}
					>
						<TouchableOpacity
							activeOpacity={0.9}
							onPress={() => setIsVisible({ ...visible, cp: true })}
						>
							<ImageBackground
								style={[
									styles().pb30,
									styles().alignCenter,
									styles().ph20,
									styles().mt10,
									styles().h220px,
									styles().flexRow,
									styles().alignCenter,
									styles().justifyBetween,
									styles().overflowH,
									{
										borderTopLeftRadius: 30,
										borderTopRightRadius: 30,
										backgroundColor: currentTheme.F3F0E4,
									},
								]}
								source={{ uri: coverPhoto }}
							>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => props.navigation.goBack()}
									style={[
										styles().alignSelfStart,
										styles().justifyCenter,
										styles().alignCenter,
										styles().h50px,
										styles().w50px,
										{ zIndex: 300, right: 20, top: 10 },
									]}
								>
									<FontAwesome
										name="angle-left"
										size={30}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
								<ActivityIndicator
									animating={loader2}
									style={[styles().absoulteCenter, styles().posAbs, styles().zIndex100]}
									size={'large'}
									color={currentTheme.themeBackground}
								/>
								<TouchableOpacity
									onPress={() => {
										setCoverPicker(true)
										setLoader2(true)
									}}
									activeOpacity={1}
									style={[
										styles().wh40px,
										styles().alignSelfStart,
										styles().mt20,
										styles().br25,
										styles().alignCenter,
										styles().justifyCenter,
										{ backgroundColor: currentTheme.C3C3C3, zIndex: 200 },
									]}
								>
									<Ionicons
										name="camera-outline"
										size={26}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
							</ImageBackground>
						</TouchableOpacity>

						<View style={[styles().mtminus75, styles().alignCenter]}>
							<View>
								<LinearGradient
									start={{ x: 0, y: 0 }}
									colors={isLearn ? currentTheme.learningGradient : currentTheme.whiteGradient}
									style={{
										padding: 8,
										borderRadius: 100,
										alignItems: 'center',
										justifyContent: 'center',
									}}
								>
									<TouchableOpacity
										activeOpacity={1}
										onPress={() => setIsVisible({ ...visible, dp: true })}
										style={[
											styles().wh130px,
											styles().br100,
											styles().overflowH,
											// styles().mr10,
											styles().alignCenter,
											styles().justifyCenter,
											{
												borderWidth: isLearn ? 2 : 0,
												borderColor: currentTheme.white,
												backgroundColor: isLearn ? currentTheme.white : currentTheme.F3F0E4,
											},
										]}
									>
										<ActivityIndicator
											animating={loader1}
											style={[styles().absoulteCenter, styles().posAbs, styles().zIndex100]}
											color={currentTheme.themeBackground}
											size={'small'}
										/>
										{photo
											? <Image
													source={{ uri: photo }}
													resizeMode="cover"
													style={[styles().wh100, styles().br100]}
												/>
											: <FontAwesome5
													name="user-alt"
													size={50}
													color={isLearn ? currentTheme.lightGreen : currentTheme.themeBackground}
												/>}
									</TouchableOpacity>
								</LinearGradient>
								<TouchableOpacity
									onPress={() => {
										setProfilePicker(true)
										setLoader1(true)
									}}
									activeOpacity={1}
									style={[
										styles().wh40px,
										styles().posAbs,
										styles().right10,
										styles().bottom5,
										styles().alignSelfStart,
										styles().mt20,
										styles().br25,
										styles().alignCenter,
										styles().justifyCenter,
										{ backgroundColor: currentTheme.C3C3C3 },
									]}
								>
									<Ionicons
										name="camera-outline"
										size={26}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
							</View>

							<View style={[styles().mt15, styles().alignCenter, styles().mb15]}>
								<View style={[styles().flexRow, styles().alignStart, styles().w80, styles().mb5]}>
									<Text
										// numberOfLines={}
										style={[styles().fs22, styles().fontBold, styles().textCenter, { color: currentTheme.lightGold }]}
									>
										{user?.name ? user?.name : asyncUser?.name ? asyncUser?.name : 'N/A'}
									</Text>
									{user?.profileVerified
										? <View style={[styles().wh20px, styles().ml10, styles().overflowH, { marginTop: 7 }]}>
												<Image
													source={require('../../assets/images/verified-icon.png')}
													style={styles().wh100}
													resizeMode="contain"
												/>
											</View>
										: null}
								</View>
								<View style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}>
									<Text style={[styles().fs16, styles().mr5, styles().fontMedium, styles().textCapitalize, { color: currentTheme.headingColor }]}>
										{user?.role ? (user?.role === 'matePilot' ? 'Mate Pilot' : user?.role) : 'N/A'}
									</Text>
									{ratingData
										? <TouchableOpacity
												activeOpacity={0.5}
												onPress={() =>
													props.navigation.navigate('Reviews', {
														user: user,
														overallRatings: ratings,
													})
												}
												style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
											>
												<View style={[styles().mb5]}>
													<FontAwesome
														name="star"
														color={currentTheme.starColor}
														size={16}
													/>
												</View>
												<Text style={[styles().fs12, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.headingColor }]}>
													{`${user?.ratings ? user?.ratings?.toFixed(1) : 0} (${user?.ratingsCount ? user?.ratingsCount : 0} reviews)`}
												</Text>
											</TouchableOpacity>
										: null}
								</View>
							</View>
							<View style={[styles().flexRow, styles().flex, styles().alignCenter]}>
								<TouchableOpacity
									activeOpacity={0.7}
									onPress={() => props.navigation.navigate('EditProfile', { edit: true })}
									style={[
										styles().br20,
										styles().bw1,
										styles().h40px,
										styles().ph15,
										styles().alignCenter,
										styles().flexRow,
										styles().justifyCenter,
										{ borderColor: currentTheme.lightGold },
									]}
								>
									<Ionicons
										name="eye-sharp"
										size={18}
										color={currentTheme.lightGold}
									/>
									<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.lightGold }]}>{'View Profile'}</Text>
								</TouchableOpacity>
								<TouchableOpacity
									activeOpacity={0.7}
									onPress={() => setBadgeModal(!badgeModal)}
									style={[
										styles().br20,
										styles().bw1,
										styles().ml10,
										styles().h40px,
										styles().ph15,
										styles().alignCenter,
										styles().flexRow,
										styles().justifyCenter,
										{ borderColor: currentTheme.lightGold },
									]}
								>
									<View style={[styles().wh18px]}>
										<Image
											source={require('../../assets/images/badge2.png')}
											style={[styles().wh100]}
											resizeMode="contain"
										/>
									</View>
									<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.lightGold }]}>{'Request A Badge'}</Text>
								</TouchableOpacity>
							</View>
						</View>

						<View
							style={[
								styles().alignCenter,
								styles().mt20,
								styles().mb25,
								styles().ml20,
								{
									borderBottomWidth: 1,
									width: width * 1 - 40,
									borderBottomColor: currentTheme.c707070,
								},
							]}
						/>

						<View style={[styles().ph20, styles().mb15]}>
							<View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
								<Ionicons
									name="md-briefcase"
									size={20}
									color={currentTheme.themeBackground}
								/>
								<Text
									numberOfLines={2}
									style={[styles().fs14, styles().fontRegular, styles().ml10, styles().flex, { color: currentTheme.headingColor }]}
								>
									{user?.companyWorkFor ? `Work at ${user?.companyWorkFor}` : 'Add work'}
								</Text>
							</View>
							<View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
								<FontAwesome
									name="envelope"
									size={18}
									color={currentTheme.themeBackground}
								/>
								<Text
									numberOfLines={1}
									style={[styles().fs14, styles().ml10, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
								>
									{user?.email ? user?.email : 'N/A'}
								</Text>
							</View>
							<View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
								<FontAwesome
									name="phone"
									size={20}
									color={currentTheme.themeBackground}
								/>
								<Text
									numberOfLines={1}
									style={[styles().fs14, styles().ml10, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
								>
									{user?.phone ? formatPhoneNumber(user?.phone) : 'Add phone'}
								</Text>
							</View>
							{user?.hourlyRate
								? <View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
										<Image
											style={{ height: 20, width: 23 }}
											resizeMode="contain"
											source={require('../../assets/images/cash.png')}
										/>
										<Text
											numberOfLines={1}
											style={[styles().fs14, styles().fontRegular, styles().ml10, styles().flex, { color: currentTheme.headingColor }]}
										>
											{`$${user?.hourlyRate}/day`}
										</Text>
									</View>
								: null}
						</View>
						{user?.role === 'deckhand'
							? <>
									<View
										style={[
											styles().alignCenter,
											styles().mb10,
											styles().ml20,
											{
												borderBottomWidth: 1,
												width: width * 1 - 40,
												borderBottomColor: currentTheme.c707070,
											},
										]}
									/>
									<View style={[styles().ph15, styles().flex, styles().pt15, styles().pb15]}>
										<Text style={[styles().fs18, styles().fw700, { color: currentTheme.black }]}>I Wanna Be A Toe Boater</Text>

										{user?.deckhand_video
											? <VideoThumbnail
													video={user?.deckhand_video}
													isFocus={isFocus}
												/>
											: <TouchableOpacity
													onPress={() => props.navigation.navigate('ToeBoater')}
													activeOpacity={0.9}
													style={[
														styles().w90,
														styles().mt20,
														styles().bw1,
														styles().alignSelfCenter,
														styles().h80px,
														styles().br10,
														styles().alignCenter,
														styles().justifyCenter,
														{ borderColor: currentTheme.line, borderStyle: 'dashed' },
													]}
												>
													<Text style={[styles().fontRegular, styles().fs14, { color: currentTheme.black }]}>Upload a Video</Text>
												</TouchableOpacity>}
									</View>
								</>
							: null}
						<View
							style={[
								styles().alignCenter,
								styles().mb10,
								styles().ml20,
								{
									borderBottomWidth: 1,
									width: width * 1 - 40,
									borderBottomColor: currentTheme.c707070,
								},
							]}
						/>
						<View style={[styles().ph15, styles().flex]}>
							<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
								<View style={[styles().flexRow, styles().mb10, styles().pr20, styles().alignCenter]}>
									<Text style={[styles().fs18, styles().fw700, { color: currentTheme.black }]}>{'Friends '}</Text>
									<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
										{`(${data?.myFriendships?.totalResults ? data?.myFriendships?.totalResults : 0} ${
											data?.myFriendships?.totalResults > 1 ? 'friends' : 'friend'
										})`}
									</Text>
								</View>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => props.navigation.navigate('FriendsNavigator')}
								>
									<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.lightGold }]}>See all</Text>
								</TouchableOpacity>
							</View>
							<View style={[styles().mb25]}>
								<FlatList
									data={data?.myFriendships?.results}
									showsHorizontalScrollIndicator={false}
									horizontal
									contentContainerStyle={{ flexGrow: 1 }}
									renderItem={({ item, index }) => {
										return (
											<TouchableOpacity
												onPress={() => throttledButtonPress(item)}
												activeOpacity={0.7}
												style={[styles().alignCenter, styles().w70px, styles().mr5]}
											>
												<View style={[styles().wh65px, styles().overflowH, styles().br100, styles().alignCenter, styles().justifyCenter]}>
													{item?.photo
														? <Image
																source={{ uri: item?.photo }}
																style={styles().wh100}
																resizeMode="cover"
															/>
														: <View
																style={[
																	styles().overflowH,
																	styles().justifyCenter,
																	styles().alignCenter,
																	styles().br50,
																	styles().wh65px,
																	{
																		borderWidth: 1,
																		borderColor: currentTheme.themeBackground,
																	},
																]}
															>
																<FontAwesome5
																	name="user-alt"
																	size={25}
																	color={currentTheme.themeBackground}
																/>
															</View>}
												</View>
												<Text
													numberOfLines={1}
													style={[styles().fs13, styles().fontRegular, { color: currentTheme.lighterBlue, marginTop: 3 }]}
												>
													{item?.name}
												</Text>
											</TouchableOpacity>
										)
									}}
									ListEmptyComponent={() => {
										return (
											<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
												<Text
													style={{
														color: currentTheme.E8E8C8,
														marginTop: 20,
														fontSize: 14,
														fontFamily: fontStyles.PoppinsRegular,
													}}
												>
													{loading ? 'Loading...' : 'No Friends'}
												</Text>
											</View>
										)
									}}
									keyExtractor={(_item, index) => index.toString()}
								/>
							</View>

							<View
								style={[
									styles().alignCenter,
									styles().mb20,
									{
										borderBottomWidth: 1,
										width: width * 1 - 40,
										borderBottomColor: currentTheme.c707070,
									},
								]}
							/>

							<View
								style={[
									styles().mb10,
									styles().pr20,
									{
										borderBottomWidth: 1,
										borderBottomColor: currentTheme.c707070,
									},
								]}
							>
								<Text style={[styles().fs18, styles().fw700, { color: currentTheme.black }]}>Your Post</Text>
								<View style={[styles().flexRow, styles().alignStart, styles().justifyBetween, styles().mt10]}>
									<View
										style={[
											styles().wh40px,
											styles().overflowH,
											styles().br100,
											{
												// backgroundColor: currentTheme.F3F0E4,
											},
										]}
									>
										{photo
											? <Image
													source={{ uri: photo ? photo : user?.photo }}
													resizeMode="cover"
													style={[styles().wh100, styles().br10]}
												/>
											: <View
													style={[
														styles().overflowH,
														styles().justifyCenter,
														styles().alignCenter,
														styles().br50,
														styles().wh40px,
														{
															borderWidth: 1,
															borderColor: currentTheme.themeBackground,
														},
													]}
												>
													<FontAwesome5
														name="user-alt"
														size={16}
														color={currentTheme.themeBackground}
													/>
												</View>}
									</View>
									<TextField
										keyboardType="default"
										value={post.content}
										multiline={true}
										autoCapitalize="none"
										placeholder={'What are you thinking?'}
										onChangeText={text => handleInputChange(text)}
										style={[styles().overflowH, styles().flex, { borderBottomWidth: 0 }]}
										stylesInput={[
											{
												height: 75,
												textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
											},
										]}
									/>
								</View>
								{post?.images
									? <TouchableOpacity style={[styles().h100px, styles().w200px, styles().br10, styles().mt10]}>
											<TouchableOpacity
												onPress={() => {
													setPost({ ...post, images: '' })
													setEnablePost(true)
												}}
												activeOpacity={0.5}
												style={{
													position: 'absolute',
													// backgroundColor: 'teal',
													zIndex: 10,
													right: -5,
													top: -5,
													backgroundColor: 'white',
													borderRadius: 100,
												}}
											>
												<AntDesign
													name={'closecircle'}
													color={currentTheme.themeBackground}
													size={20}
												/>
											</TouchableOpacity>
											<Image
												style={[styles().wh100, styles().br10]}
												resizeMode="cover"
												source={{ uri: post?.images }}
											/>
										</TouchableOpacity>
									: null}
								{post?.videos
									? <View
											style={{
												borderWidth: 0.5,
												borderColor: currentTheme.c737373,
												// borderRadius: 8,
											}}
										>
											<Video
												autoplay={false}
												source={{ uri: post?.videos }}
												style={{
													height: 200,
													width: '100%',
													// borderRadius: 10,
												}}
												paused={true}
												muted={true}
											/>
											<TouchableOpacity
												onPress={() => {
													setPost({ ...post, videos: '' })
													setEnablePost(true)
												}}
												activeOpacity={0.5}
												style={{
													position: 'absolute',
													zIndex: 10,
													right: -5,
													top: -5,
													backgroundColor: 'white',
													borderRadius: 100,
												}}
											>
												<AntDesign
													name={'closecircle'}
													color={currentTheme.themeBackground}
													size={20}
												/>
											</TouchableOpacity>
										</View>
									: null}
								<View style={[styles().flexRow, styles().mt20, styles().mb35, styles().alignCenter]}>
									<TouchableOpacity
										activeOpacity={0.7}
										onPress={() => setIspostPicker({ ...ispostPicker, image: true })}
										style={[
											styles().br10,
											styles().mr10,
											styles().h35px,
											styles().ph15,
											styles().alignCenter,
											styles().flexRow,
											styles().justifyCenter,
											{ backgroundColor: currentTheme.EEE8D5 },
										]}
									>
										<Ionicons
											name="camera"
											size={20}
											color={currentTheme.themeBackground}
										/>
										<Text style={[styles().fs12, styles().ml5, styles().fontRegular, { color: currentTheme.c444D6E }]}>Photos</Text>
									</TouchableOpacity>
									<TouchableOpacity
										activeOpacity={0.7}
										onPress={() => setIspostPicker({ ...ispostPicker, video: true })}
										style={[
											styles().br10,
											styles().h35px,
											styles().mr10,
											styles().ph15,
											styles().alignCenter,
											styles().flexRow,
											styles().justifyCenter,
											{ backgroundColor: currentTheme.EEE8D5 },
										]}
									>
										<FontAwesome
											name="video-camera"
											size={15}
											color={currentTheme.themeBackground}
										/>
										<Text style={[styles().fs12, styles().ml5, styles().fontRegular, { color: currentTheme.c444D6E }]}>Video</Text>
									</TouchableOpacity>
									{postLoader
										? <Spinner size={25} />
										: <TouchableOpacity
												disabled={enablePost}
												activeOpacity={0.7}
												onPress={() => Post()}
												style={[
													styles().w80px,
													styles().br10,
													styles().alignCenter,
													styles().justifyCenter,
													styles().h35px,
													{
														backgroundColor: enablePost ? currentTheme.CFCFCF : currentTheme.headingColor,
													},
												]}
											>
												<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.white }]}>Post</Text>
											</TouchableOpacity>}
								</View>
							</View>
							{getPosts?.length === 0
								? <View style={[styles().flex]}>
										{Loading || loading
											? Array.from({ length: 2 }).map(item => {
													return <PostSkeleton data={item} />
												})
											: <Text
													style={{
														marginTop: 100,
														marginBottom: 50,
														alignSelf: 'center',
														color: currentTheme.E8E8C8,
														fontSize: 14,
														fontFamily: fontStyles.PoppinsRegular,
													}}
												>
													{'No Posts'}
												</Text>}
									</View>
								: null}
							{getPosts?.map((item, index) => {
								return (
									<PostComponent
										navigation={props.navigation}
										item={item}
										index={index}
										update={(updateType, post_id) => {
											if (updateType === 'deletePost') {
												removePost(post_id)
											}
											if (updateType === 'postLike') {
												likePost(post_id)
											}
										}}
									/>
								)
							})}
							<View style={styles().wh30px} />
						</View>
					</ScrollView>
				</KeyboardAvoidingView>
			</SafeAreaView>
			<RequestForBadges
				modalVisible={badgeModal}
				onCancel={() => setBadgeModal(false)}
			/>
			<ImageAndDocumentPicker
				isImage={photo}
				isCrop={Platform.OS !== 'ios'}
				isImagePicker={isProfilePicker}
				setIsImagePicker={() => {
					setProfilePicker(false)
					setLoader1(false)
				}}
				setImage={data => changeProfilePicture(data)}
			/>
			<ImageAndDocumentPicker
				isImage={coverPhoto}
				isImagePicker={isCoverPicker}
				setIsImagePicker={() => {
					setCoverPicker(false)
					setLoader2(false)
				}}
				setImage={data => changeCoverPicture(data)}
			/>
			<ImageAndDocumentPicker
				isImagePicker={ispostPicker.image}
				setIsImagePicker={() => {
					setIspostPicker({ ...ispostPicker, image: false })
				}}
				setImage={data => {
					setPost({ ...post, images: data, videos: '' })
					setIspostPicker({ ...ispostPicker, image: false })
					setEnablePost(false)
				}}
			/>
			<ImageAndDocumentPicker
				isImagePicker={ispostPicker.video}
				setIsImagePicker={() => {
					setIspostPicker({ ...ispostPicker, video: false })
				}}
				setImage={data => {
					setPost({ ...post, videos: data, images: '' })
					setIspostPicker({ ...ispostPicker, video: false })
					setEnablePost(false)
				}}
				isVideo={true}
				progress={percentage => console.log('video upload percentage :', percentage)}
			/>
		</View>
	)
}

export default React.memo(Profile)
