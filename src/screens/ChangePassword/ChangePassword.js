import { View, TouchableOpacity, Platform, UIManager } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Feather from '@expo/vector-icons/Feather'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation } from '@apollo/client'
import { changepassword } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import UserContext from '../../context/User/User'
import { passwordRegex } from '../../utils/Constants'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const ChangePassword = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [password, setPassword] = useState('')
	const [passwordError, setPasswordError] = useState(false)
	const [Loading, setLoading] = useState(false)
	const [newPassword, setNewPassword] = useState('')
	const [newPasswordError, setNewPasswordError] = useState(false)
	const [confirmPassword, setConfirmPassword] = useState('')
	const [confirmPasswordError, setConfirmPasswordError] = useState(false)

	const [iconEye, seticonEye] = useState('eye-slash')

	const [ConfirmiconEye, setConfirmIconEye] = useState('eye-slash')
	const [NewiconEye, setNewIconEye] = useState('eye-slash')

	function onChangeIcon() {
		if (iconEye === 'eye') {
			seticonEye('eye-slash')
		} else {
			seticonEye('eye')
		}
	}

	function onChangeIconNew() {
		if (NewiconEye === 'eye') {
			setNewIconEye('eye-slash')
		} else {
			setNewIconEye('eye')
		}
	}

	function onChangeIconConfirm() {
		if (ConfirmiconEye === 'eye') {
			setConfirmIconEye('eye-slash')
		} else {
			setConfirmIconEye('eye')
		}
	}

	const [mutate, { client }] = useMutation(changepassword, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('changePassword res :', data.changePassword)
			FlashMessage({ msg: 'Password Changed!', type: 'success' })
			props.navigation.goBack()
		} catch (e) {
			console.log(e)
			setLoading(false)
		} finally {
			// setLoading(false);
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('changePassword error  :', error)
	}

	async function changePassword() {
		let status = true
		if (password === '') {
			FlashMessage({ msg: 'Enter Password', type: 'warning' })
			setPasswordError(true)
			status = false
			return
		}
		if (newPassword === '') {
			FlashMessage({ msg: 'Enter New Password', type: 'warning' })
			setNewPasswordError(true)
			status = false
			return
		}
		if (confirmPassword === '') {
			FlashMessage({ msg: 'Enter Confirm Password', type: 'warning' })
			setConfirmPasswordError(true)
			status = false
			return
		}
		if (!passwordRegex.test(newPassword.trim())) {
			FlashMessage({
				msg: 'Your password must be at least 8 characters long and it must contain one upper case alphabet, one lower case alphabet, one number and one special character',
				type: 'warning',
			})
			setPasswordError(true)
			status = false
			return
		}
		if (confirmPassword !== newPassword) {
			FlashMessage({
				msg: 'New Password & Confirm Password must be same',
				type: 'warning',
			})
			setConfirmPasswordError(true)
			setPasswordError(true)
			status = false
			return
		}
		if (password === newPassword) {
			FlashMessage({
				msg: 'Current Password & New Password cannot be same',
				type: 'warning',
			})
			setPasswordError(true)
			setNewPasswordError(true)
			status = false
			return
		}
		if (status) {
			setLoading(true)
			await mutate({
				variables: {
					password: password,
					newPassword: newPassword,
					email: user?.email,
				},
			})
		}
	}

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			keyBoardArea={50}
			pagetitle={'Change Password'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={password}
						errorText={passwordError}
						autoCapitalize="none"
						placeholder={'Current Password'}
						style
						onChangeText={text => {
							setPasswordError(false)
							setPassword(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={ConfirmiconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIconConfirm.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={ConfirmiconEye}
									size={16}
									color={ConfirmiconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>

				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={newPassword}
						errorText={newPasswordError}
						autoCapitalize="none"
						placeholder={'New Password'}
						style
						onChangeText={text => {
							setNewPasswordError(false)
							setNewPassword(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={NewiconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIconNew.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={NewiconEye}
									size={16}
									color={NewiconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>

				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={confirmPassword}
						errorText={confirmPasswordError}
						autoCapitalize="none"
						placeholder={'Confirm Password'}
						style
						onChangeText={text => {
							setConfirmPasswordError(false)
							setConfirmPassword(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={iconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIcon.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={iconEye}
									size={16}
									color={iconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>
			</View>

			<View style={styles().mb30}>
				{Loading
					? <Spinner />
					: <ThemeButton
							onPress={() => changePassword()}
							Title={'Change Password'}
						/>}
			</View>
		</Layout>
	)
}

export default ChangePassword
