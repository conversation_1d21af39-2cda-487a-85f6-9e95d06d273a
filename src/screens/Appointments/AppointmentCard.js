import { View, Text, TouchableOpacity } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Feather from '@expo/vector-icons/Feather'
import Ionicons from '@expo/vector-icons/Ionicons'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Octicons from '@expo/vector-icons/Octicons'
import Entypo from '@expo/vector-icons/Entypo'
import navigationService from '../../routes/navigationService'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import CountdownTimer from '../../component/Counter/Counter'
import { handleSocial } from '../../utils/Constants'

const AppointmentCard = ({ item, index, isPayNow, join, isDetail }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const duration = Math.floor(item?.meeting_secs / 60)
	const [show, setshow] = useState('eye-slash')

	function toggleShow() {
		if (show === 'eye') {
			setshow('eye-slash')
		} else {
			setshow('eye')
		}
	}

	const ALLOWED_TIME = 2 * 60 * 60 * 1000 // 2 hours in milliseconds
	const targetDate = new Date(item?.createdAt).getTime()
	const endDate = targetDate + ALLOWED_TIME //
	const now = Date.now()
	const distance = endDate - now

	return (
		<TouchableOpacity
			activeOpacity={1}
			onPress={() => {
				console.log(item)
			}}
			style={[
				styles().pv15,
				styles().ph15,
				styles().mb20,
				styles().alignStart,
				index === 0 ? styles().mt20 : null,
				styles().bw1,
				styles().br10,
				{ borderColor: currentTheme.CFCFCF },
			]}
		>
			<View style={[styles().flexRow, styles().w100, styles().alignCenter, styles().mb5]}>
				{item?.status !== 'confirmed' ? <CountdownTimer targetTime={item?.createdAt} /> : null}
				<View style={[styles().alignEnd, styles().flex]}>
					<View style={[styles().ph15, styles().pv5, styles().br100, { backgroundColor: currentTheme.badgeCardBG }]}>
						<Text style={[styles().fs10, styles().alignSelfEnd, styles().fontRegular, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
							{item?.status}
						</Text>
					</View>
				</View>

				{item?.status === 'confirmed' && isDetail && (
					<TouchableOpacity
						onPress={() =>
							navigationService.navigate('AppointmenetDetails', {
								appointmenetParams: item,
								appointmentId: item?._id,
							})
						}
						activeOpacity={0.5}
						style={[styles().alignEnd, styles().ml5]}
					>
						<Feather
							name={'arrow-right-circle'}
							size={20}
							color={currentTheme.themeBackground}
						/>
					</TouchableOpacity>
				)}
			</View>
			<View style={[styles().flexRow, styles().alignStart, styles().mb10]}>
				<Text
					numberOfLines={2}
					style={[styles().fs12, styles().flex, styles().fontRegular, { color: currentTheme.black }]}
				>
					{item?.booking?.question}
				</Text>
			</View>
			<View style={[styles().flexRow, styles().alignCenter, styles().flexWrap, styles().justifyStart]}>
				<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
					<Octicons
						name="calendar"
						size={12}
						color={currentTheme.E8E8C8}
					/>
					<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item?.meeting_date}</Text>
				</View>
				<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
					<Feather
						name="clock"
						size={12}
						color={currentTheme.E8E8C8}
					/>
					<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item?.meeting_time_slot}</Text>
				</View>
				<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
					<Ionicons
						name="ios-cash-outline"
						size={12}
						color={currentTheme.E8E8C8}
					/>
					<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
						{`$${Number.parseFloat(item?.booking?.appointmentPrice)?.toFixed(2)}`}
					</Text>
				</View>
				<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
					<Entypo
						name="time-slot"
						size={12}
						color={currentTheme.E8E8C8}
					/>
					<Text style={[styles().fs7, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{`${duration} mins`}</Text>
				</View>
			</View>

			{item?.status === 'pending' && isPayNow
				? <View style={[styles().flexRow, styles().mt15, styles().alignCenter, styles().w100, styles().justifyBetween]}>
						<ThemeButton
							onPress={() =>
								navigationService.navigate('AppointmenetDetails', {
									appointmenetParams: item,
									appointmentId: item?._id,
								})
							}
							Title={'View Details'}
							StyleText={{ color: currentTheme.themeBackground, fontSize: 12 }}
							Style={[
								styles().h40px,
								styles().br5,
								styles().w48,
								{
									borderColor: currentTheme.themeBackground,
									backgroundColor: 'transparent',
								},
							]}
						/>
						{distance <= 0
							? null
							: <ThemeButton
									onPress={() => handleSocial(item?.payment_link)}
									Style={[styles().h40px, styles().br5, styles().w48]}
									StyleText={{ color: currentTheme.white, fontSize: 12 }}
									Title={`Pay Now $${Number.parseFloat(item?.booking?.appointmentPrice).toFixed(2)}`}
								/>}
					</View>
				: item?.status === 'confirmed' && join && item?.meeting_link
					? <View style={[styles().flexRow, styles().mt15, styles().w100, styles().alignCenter, styles().justifyBetween]}>
							<ThemeButton
								onPress={() => handleSocial(item?.meeting_link)}
								Style={[styles().h40px, styles().br5, styles().w45]}
								StyleText={{ color: currentTheme.white, fontSize: 12 }}
								Title={'Join Meeting'}
							/>
							<View
								style={[
									styles().flexRow,
									styles().alignCenter,
									styles().justifyBetween,
									styles().w45,
									styles().bw1,
									styles().mt10,
									styles().br5,
									styles().h40px,
									styles().ph10,
									{ borderColor: currentTheme.B7B7B7 },
								]}
							>
								<Text style={[show !== 'eye' ? styles().fs16 : styles().fs12, styles().fontRegular, { color: currentTheme.blackish }]}>
									{show !== 'eye' ? '*'?.repeat(item?.meeting_password?.length) : item?.meeting_password}
								</Text>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => toggleShow()}
								>
									<FontAwesome5
										name={show}
										size={16}
										color={show === 'eye' ? currentTheme.themeBackground : currentTheme.B7B7B7}
									/>
								</TouchableOpacity>
							</View>
						</View>
					: null}
		</TouchableOpacity>
	)
}

export default AppointmentCard
