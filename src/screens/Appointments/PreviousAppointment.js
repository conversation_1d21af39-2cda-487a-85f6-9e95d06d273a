import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { LicenseBookings } from '../../apollo/server'
import UserContext from '../../context/User/User'
import AppointmentCard from './AppointmentCard'

const PreviousAppointment = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [appointments, setAppointments] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 30

	const { data, loading, error, refetch } = useQuery(LicenseBookings, {
		errorPolicy: 'all',
		variables: {
			filters: {
				user: user?._id,
				previous: true,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('LicenseBookings res :', JSON.stringify(data?.LicenseBookings))
			setAppointments(prev => [...prev, ...data?.LicenseBookings?.results])
		},
		onError: err => {
			console.log('LicenseBookings err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const nextPage = async () => {
		if (page < data?.LicenseBookings?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}
	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setAppointments([])
		// await refetch();
		await refetch().then(res => {
			setAppointments(_prev => res?.data?.LicenseBookings?.results)
			setLoading(false)
			console.log('refresh upcommings')
		})
	}

	return (
		<View style={styles().flex}>
			<FlatList
				data={appointments}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View
							style={[
								styles().alignCenter,
								styles().justifyCenter,

								styles().flex,
							]}
						>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{loading ? 'Loading...' : 'No Appointments'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<AppointmentCard
							item={item}
							index={index}
							navigation={props.navigation}
							isPayNow={false}
							join={false}
							isDetail={false}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default PreviousAppointment
