import { Text, View, TouchableOpacity, Keyboard, ImageBackground, Dimensions } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Feather from '@expo/vector-icons/Feather'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { getKeyByFilter, sendVerificationEmail, signup } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Spinner from '../../component/Spinner/Spinner'
import { checkDisposibleEmail } from '../../utils/APIS'
import DisposibleEmailModal from '../../component/Modals/DisposibleModal'
import { passwordRegex } from '../../utils/Constants'
import CheckBox from '../../component/CheckBox/CheckBox'

const SignUp = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [fullName, setFullName] = useState('')
	const [fullNameError, setFullNameError] = useState(false)
	const [middleName, setMiddleName] = useState('')
	const [middleNameErr, setMiddleNameErr] = useState(false)
	const [lastName, setLastName] = useState('')
	const [lastNameErr, setLastNameErr] = useState(false)
	const [email, setEmail] = useState('')
	const [emailError, setEmailError] = useState(false)
	const [password, setPassword] = useState('')
	const [passwordError, setPasswordError] = useState(false)
	const [confirmPass, setConfirmPass] = useState('')
	const [confirmPassError, setConfirmPassError] = useState(false)
	const [iconEye, setIconEye] = useState('eye-slash')
	const [Loading, setLoading] = useState(false)
	const [disposibleEmailModal, setDisposibleEmailModal] = useState(false)
	const [isDisposible, setIsDisposible] = useState(false)
	const [suffix, setSuffix] = useState('')
	const [suffixErr, setSuffixErr] = useState(false)
	const [EULAContract, setEULAContract] = useState(false)
	const [_viewTermCondition, _setViewTermCondition] = useState(false)
	const { width } = Dimensions.get('window')

	const { data, loading, refetch } = useQuery(getKeyByFilter, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: {
				key: 'terms_cond_signup',
				specific_for: 'crewmember',
			},
		},
		onCompleted: data => {
			console.log('getKeyByFilter res :', JSON.stringify(data?.getKeyByFilter))
		},
		onError: err => {
			console.log('getKeyByFilter err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	function onChangeIconEye() {
		if (iconEye === 'eye') {
			setIconEye('eye-slash')
		} else {
			setIconEye('eye')
		}
	}

	const [ConfirmiconEye, setConfirmIconEye] = useState('eye-slash')
	function onChangeIconConfirm() {
		if (ConfirmiconEye === 'eye') {
			setConfirmIconEye('eye-slash')
		} else {
			setConfirmIconEye('eye')
		}
	}

	const [email_verify_mutate] = useMutation(sendVerificationEmail, {
		errorPolicy: 'all',
		onCompleted: ({ sendVerificationEmail }) => {
			console.log('sendVerificationEmail res :', sendVerificationEmail)
			setLoading(false)
			FlashMessage({
				msg: 'Email verification code has been send to your email address.',
				type: 'success',
			})
			props.navigation.navigate('Verification', {
				email: email.toLowerCase().trim(),
				verification: true,
			})
		},
		onError: error => {
			console.log('sendVerificationEmail Err :', error)
			FlashMessage({ msg: error.message?.toString(), type: 'warning' })
			setLoading(false)
		},
	})

	const [mutate, { client }] = useMutation(signup, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('Signup res :', data.signup)
			FlashMessage({ msg: 'Register Successfully', type: 'success' })
			await email_verify_mutate({
				variables: {
					email: email.toLowerCase().trim(),
				},
			})
		} catch (e) {
			console.log(e)
			setLoading(false)
		} finally {
			// setLoading(false);
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('signup error  :', error)
	}

	async function Signup() {
		try {
			const emailregex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
			let status = true
			Keyboard.dismiss()

			if (fullName === '') {
				FlashMessage({ msg: 'Enter Full Name', type: 'warning' })
				setFullNameError(true)
				status = false
				return
			}
			if (fullName.length < 3) {
				FlashMessage({
					msg: 'Full name length must be 3 characters.',
					type: 'warning',
				})
				setFullNameError(true)
				status = false
				return
			}
			if (lastName === '') {
				FlashMessage({ msg: 'Enter Last Name', type: 'warning' })
				setLastNameErr(true)
				status = false
				return
			}
			if (email === '') {
				FlashMessage({ msg: 'Enter Email', type: 'warning' })
				setEmailError(true)
				status = false
				return
			}
			if (!emailregex.test(email.trim().toLowerCase())) {
				FlashMessage({ msg: 'Invalid Email Address', type: 'warning' })
				setEmailError(true)
				status = false
				return
			}
			if (password === '') {
				FlashMessage({ msg: 'Enter Password', type: 'warning' })
				setPasswordError(true)
				status = false
				return
			}
			if (!passwordRegex.test(password.trim())) {
				FlashMessage({
					msg: 'Your password must be at least 8 characters long and it must contain one upper case alphabet, one lower case alphabet, one number and one special character',
					type: 'warning',
				})
				setPasswordError(true)
				status = false
				return
			}
			if (confirmPass === '') {
				FlashMessage({ msg: 'Enter Re-type Password', type: 'warning' })
				setConfirmPassError(true)
				status = false
				return
			}
			if (confirmPass !== password) {
				FlashMessage({
					msg: 'Password & Re-type Password must be same',
					type: 'warning',
				})
				setConfirmPassError(true)
				setPasswordError(true)
				status = false
				return
			}
			if (!EULAContract) {
				FlashMessage({
					msg: 'Agree to Terms & Conditions to continue',
					type: 'warning',
				})
				status = false
				return
			}

			if (status) {
				setLoading(true)
				await checkDisposibleEmail(email.trim().toLowerCase()).then(async res => {
					if (res.disposable) {
						setLoading(false)
						setDisposibleEmailModal(true)
						setIsDisposible(true)
					} else {
						createUser()
					}
				})
			}
		} catch (e) {
			console.log('catch signup :', e)
		}
	}
	const createUser = async () => {
		const data = {
			refrence_no: Date.now().toString(), //unix date for unique number
			email: email.toLowerCase().trim(),
			// name: fullName,
			first_name: fullName,
			middle_name: middleName,
			last_name: lastName,
			name_initial: suffix,
			password: password,
			role: 'guest',
			disposable: isDisposible,
		}
		console.log('signup data :', data)
		await mutate({
			variables: {
				userInput: data,
			},
		})
	}

	return (
		<AuthLayout>
			<ImageBackground
				source={require('../../assets/images/login-bg.png')}
				resizeMode="cover"
				style={[styles().h350px, styles().alignCenter, styles().justifyCenter]}
			>
				<Text style={[styles().fs24, styles().fontBold, styles().lh30, styles().textCenter, styles().textUpper, { color: currentTheme.headingColor }]}>
					Create Your {'\n'} Account
				</Text>
				<Text
					style={[
						styles().fs16,
						// styles().fw700,
						styles().textCenter,
						styles().lh30,
						styles().fontMedium,
						styles().mt20,
						{ color: currentTheme.headingColor },
					]}
				>
					Signup as Crew Member
				</Text>
			</ImageBackground>
			<View style={[styles().mt20, styles().flex, styles().ph20]}>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={fullName}
						errorText={fullNameError}
						autoCapitalize="none"
						placeholder={'First Name'}
						style
						onChangeText={text => {
							setFullNameError(false)
							setFullName(text)
						}}
						FieldIcon={
							<Feather
								name="user"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={middleName}
						errorText={middleNameErr}
						autoCapitalize="none"
						placeholder={'Middle Name'}
						style
						onChangeText={text => {
							setMiddleNameErr(false)
							setMiddleName(text)
						}}
						FieldIcon={
							<Feather
								name="user"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={lastName}
						errorText={lastNameErr}
						autoCapitalize="none"
						placeholder={'Last Name'}
						style
						onChangeText={text => {
							setLastNameErr(false)
							setLastName(text)
						}}
						FieldIcon={
							<Feather
								name="user"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={suffix}
						errorText={suffixErr}
						autoCapitalize="none"
						placeholder={'Enter Suffix'}
						style
						onChangeText={text => {
							setSuffixErr(false)
							setSuffix(text)
						}}
						FieldIcon={
							<Feather
								name="user"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={email}
						errorText={emailError}
						autoCapitalize="none"
						placeholder={'Email'}
						style
						onChangeText={text => {
							setEmailError(false)
							setEmail(text)
						}}
						FieldIcon={
							<FontAwesome
								name="envelope-o"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={password}
						errorText={passwordError}
						autoCapitalize="none"
						placeholder={'Password'}
						style
						onChangeText={text => {
							setPasswordError(false)
							setPassword(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={iconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIconEye.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={iconEye}
									size={16}
									color={iconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={confirmPass}
						errorText={confirmPassError}
						autoCapitalize="none"
						placeholder={'Retype Password'}
						style
						onChangeText={text => {
							setConfirmPassError(false)
							setConfirmPass(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={ConfirmiconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIconConfirm.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={ConfirmiconEye}
									size={16}
									color={ConfirmiconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>
				<View style={[styles().flexRow, styles().alignCenter]}>
					<CheckBox
						value={EULAContract}
						setValue={boolean => setEULAContract(boolean)}
					/>
					<TouchableOpacity onPress={() => props.navigation.navigate('TermsAndConditions')}>
						<Text style={[styles().fs14, styles().textDecorationUnderline, styles().ml10, styles().fontMedium]}>Agree to Terms and Conditions</Text>
					</TouchableOpacity>
				</View>

				<View style={[styles().mt20, styles().mb10]}>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => Signup()}
								Title={'Register'}
							/>}
				</View>
				<View style={[styles().mt20, styles().mb30, styles().flexRow, styles().alignCenter, styles().justifyCenter]}>
					<Text
						style={[
							styles().fs16,

							styles().fontRegular,
							{ color: currentTheme.headingColor },
						]}
					>
						Already have an account?{'  '}
					</Text>
					<TouchableOpacity
						onPress={() => {
							props.navigation.navigate('Login')
						}}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.themeBackground }]}>Login</Text>
					</TouchableOpacity>
				</View>
			</View>
			<DisposibleEmailModal
				onClose={() => setDisposibleEmailModal(false)}
				visible={disposibleEmailModal}
				callBack={boolean => {
					setIsDisposible(boolean)
					if (boolean) {
						setLoading(true)
						createUser()
					}
				}}
			/>
		</AuthLayout>
	)
}

export default SignUp
