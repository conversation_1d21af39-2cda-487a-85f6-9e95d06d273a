import { Text, View, Keyboard, TouchableOpacity, LayoutAnimation } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Feather from '@expo/vector-icons/Feather'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { ForgetPasswordChange } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import { passwordRegex } from '../../utils/Constants'

const ForgetPassword = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { email, token } = props?.route?.params
	const [password, setPassword] = useState('')
	const [passwordError, setPasswordError] = useState(false)
	const [password2, setPassword2] = useState('')
	const [passwordError2, setPasswordError2] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [ConfirmiconEye, setConfirmIconEye] = useState('eye-slash')
	function onChangeIconConfirm() {
		if (ConfirmiconEye === 'eye') {
			setConfirmIconEye('eye-slash')
		} else {
			setConfirmIconEye('eye')
		}
	}
	const [ConfirmiconEye2, setConfirmIconEye2] = useState('eye-slash')
	function onChangeIconConfirm2() {
		if (ConfirmiconEye2 === 'eye') {
			setConfirmIconEye2('eye-slash')
		} else {
			setConfirmIconEye2('eye')
		}
	}

	const [mutate, { client }] = useMutation(ForgetPasswordChange, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('ForgetPasswordChange res :', data)
			FlashMessage({
				msg: 'Reset Password Successfully!',
				type: 'success',
			})
			props.navigation.navigate('Login')
		} catch (e) {
			console.log(e)
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('ForgetPasswordChange error  :', error)
	}

	async function ResetPassword() {
		Keyboard.dismiss()
		let status = true
		if (password === '') {
			FlashMessage({ msg: 'Enter Password', type: 'warning' })
			setPasswordError(true)
			status = false
			return
		}
		if (!passwordRegex.test(password.trim())) {
			FlashMessage({
				msg: 'Your password must be at least 8 characters long and it must contain one upper case alphabet, one lower case alphabet, one number and one special character',
				type: 'warning',
			})
			setPasswordError(true)
			status = false
			return
		}
		if (password2 === '') {
			FlashMessage({ msg: 'Enter Re-type Password', type: 'warning' })
			setPasswordError2(true)
			status = false
			return
		}
		if (password2 !== password) {
			FlashMessage({
				msg: 'Password & Re-type Password must be same',
				type: 'warning',
			})
			setPasswordError2(true)
			setPasswordError(true)
			status = false
			return
		}
		if (status) {
			setLoading(true)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			await mutate({
				variables: {
					password: password,
					token: token,
				},
			})
		}
	}

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={true}
			withBg={true}
			pagetitle={'Reset Password'}
			headerShown={true}
			LeftIcon={true}
		>
			<View style={[styles().mt50, styles().flex, styles().ph20]}>
				<View style={[styles().flex]}>
					<View style={[styles().mb20]}>
						<Text style={[styles().fs16, styles().mb15, styles().fw700, { color: currentTheme.headingColor }]}>New Password</Text>
						<Text style={[styles().fs16, styles().fw400, { color: currentTheme.headingColor }]}>
							Your new password must be different from {'\n'} previous used password.
						</Text>
					</View>
					<View style={[styles().w100, styles().alignSelfCenter, styles().mb25]}>
						<View style={[styles().mb20, { height: 60 }]}>
							<TextField
								keyboardType="default"
								value={password}
								errorText={passwordError}
								autoCapitalize="none"
								placeholder={'Password'}
								style
								onChangeText={text => {
									setPasswordError(false)
									setPassword(text)
								}}
								FieldIcon={
									<Feather
										name="lock"
										size={20}
										color={currentTheme.black}
									/>
								}
								secureTextEntry={ConfirmiconEye !== 'eye'}
								childrenPassword={
									<TouchableOpacity
										onPress={onChangeIconConfirm.bind()}
										style={[styles().passEye]}
									>
										<FontAwesome5
											name={ConfirmiconEye}
											size={16}
											color={ConfirmiconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
										/>
									</TouchableOpacity>
								}
							/>
						</View>

						<View style={[styles().mb20, { height: 60 }]}>
							<TextField
								keyboardType="default"
								value={password2}
								errorText={passwordError2}
								autoCapitalize="none"
								placeholder={'Re-type Password'}
								style
								onChangeText={text => {
									setPasswordError2(false)
									setPassword2(text)
								}}
								FieldIcon={
									<Feather
										name="lock"
										size={20}
										color={currentTheme.black}
									/>
								}
								secureTextEntry={ConfirmiconEye2 !== 'eye'}
								childrenPassword={
									<TouchableOpacity
										onPress={onChangeIconConfirm2.bind()}
										style={[styles().passEye]}
									>
										<FontAwesome5
											name={ConfirmiconEye2}
											size={16}
											color={ConfirmiconEye2 === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
										/>
									</TouchableOpacity>
								}
							/>
						</View>
					</View>
					<View style={[styles().mb20]}>
						{Loading
							? <Spinner />
							: <ThemeButton
									onPress={() => ResetPassword()}
									Title={'Save Password'}
									Style={{
										backgroundColor: currentTheme.headingColor,
										borderColor: currentTheme.headingColor,
									}}
								/>}
					</View>
				</View>
			</View>
		</AuthLayout>
	)
}

export default ForgetPassword
