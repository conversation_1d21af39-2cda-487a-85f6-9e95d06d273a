import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import JobsComponent from '../../component/JobsComponent/JobsComponent'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getSavedJobs } from '../../apollo/server'

const SavedJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [SavedJobs, setGetSavedJobs] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 30

	const { data, loading, error, refetch } = useQuery(getSavedJobs, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: null,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			// console.log('getSavedJobs res :', JSON.stringify(res?.getSavedJobs));
			setGetSavedJobs(prev => [...prev, ...res?.getSavedJobs?.results])
			setLoading(false)
		},
		onError: err => {
			console.log('getSavedJobs err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setGetSavedJobs([])
		// await refetch();
		// await refetchApplied();

		await refetch().then(res => {
			setGetSavedJobs(_prev => res?.data.getSavedJobs?.results)
			setLoading(false)
			console.log('refresh saved jobs')
		})
		await refetchApplied()
	}

	const nextPage = async () => {
		if (page < data?.getSavedJobs?.totalPages) {
			console.log('chala ')
			setPage(old => old + 1)
			await refetch()
		}
	}

	const reload = async () => {
		await refetch().then(res => {
			setGetSavedJobs(res.data?.getSavedJobs?.results)
			setLoading(false)
		})
	}

	useEffect(() => {
		setLoading(true)
	}, [])

	return (
		<View style={[styles().flex]}>
			<FlatList
				data={SavedJobs}
				onEndReachedThreshold={0.75}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : 'No jobs'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							isSave={false}
							reload={() => reload()}
							others={true}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default SavedJobs
