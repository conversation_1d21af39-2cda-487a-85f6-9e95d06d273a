import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getRejectedJobs } from '../../apollo/server'
import JobsComponent from '../../component/JobsComponent/JobsComponent'

const RejectedJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [rejected, setRejected] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 30
	const { data, loading, error, refetch } = useQuery(getRejectedJobs, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			// console.log(
			//   'getRejectedJobs res :',
			//   JSON.stringify(data?.getRejectedJobs),
			// );
			setRejected(prev => [...prev, ...res?.getRejectedJobs?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('getRejectedJobs err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setRejected([])
		await refetch().then(res => {
			setRejected(_prev => res?.data?.getRejectedJobs?.results)
			console.log('refresh other jobs')
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.getRejectedJobs?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(true)
	}, [])

	return (
		<View style={styles().flex}>
			<FlatList
				data={rejected}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View
							style={[
								styles().alignCenter,
								styles().justifyCenter,

								styles().flex,
							]}
						>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : 'No Jobs'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							alreadyApplied={true}
							isSave={false}
							others={true}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default RejectedJobs
