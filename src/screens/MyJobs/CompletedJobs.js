import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import JobsComponent from '../../component/JobsComponent/JobsComponent'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getCompletedJobs } from '../../apollo/server'

const CompletedJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [page, setPage] = useState(1)
	const [Loading, setLoading] = useState(false)
	const [completedJobs, setCompletedJobs] = useState([])
	const pageSize = 30

	const { data, loading, error, refetch } = useQuery(getCompletedJobs, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: null,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			// console.log(
			//   'getCompletedJobs res :',
			//   JSON.stringify(data?.getCompletedJobs),
			// );
			setLoading(false)
			setCompletedJobs(prev => [...prev, ...res?.getCompletedJobs?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('getCompletedJobs err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setCompletedJobs([])
		// await refetch();
		await refetch().then(res => {
			setCompletedJobs(_prev => res?.data?.getCompletedJobs?.results)
			setLoading(false)
			console.log('refresh completed jobs')
		})
	}

	const nextPage = async () => {
		if (page < data?.getCompletedJobs?.totalPages) {
			setPage(old => old + 1)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(true)
	}, [])

	return (
		<View style={styles().flex}>
			<FlatList
				data={completedJobs}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : 'No jobs'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							alreadyApplied={true}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default CompletedJobs
