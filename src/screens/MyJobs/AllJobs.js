import { Text, View, FlatList } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import JobsComponent from '../../component/JobsComponent/JobsComponent'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { jobs } from '../../apollo/server'

const AllJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Jobs, setJobs] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const [_date, setDate] = useState('')

	const filter = props?.company
		? {
				company: props?.company,
				// expireAt: date,
				status: 'active',
				isActive: true,
			}
		: {
				type: props?.type,
				// expireAt: date,
				status: 'active',
				isActive: true,
			}

	// console.log(props.company, props.type);
	const { data, loading, error, refetch } = useQuery(jobs, {
		fetchPolicy: 'no-cache',
		variables: {
			filters: filter,
			options: {
				page: 1,
				limit: 10,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('jobs res :', JSON.stringify(data?.jobs))

			const filter = data?.jobs?.results?.filter(obj => obj?._id !== props?.currentJob)
			setJobs(filter)
			setLoading(false)
		},
		onError: err => {
			console.log('in all jobs err :', err)
			setLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const _refresh = async () => {
		setLoading(true)
		setPage(1)
		await refetch()
		// await refetch().then(res => {
		//   setJobs(prev => [...prev, ...res?.data?.jobs?.results]);
		//   setLoading(false);
		//   console.log('refresh all jobs');
		// });
	}

	const nextPage = async () => {
		if (page < data?.jobs?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(loading)
		setDate(new Date())
	}, [loading])

	useEffect(() => {
		refetch()
	}, [loading])
	// console.log('alljobs count :', Jobs.length);
	return (
		<View style={styles().flex}>
			<FlatList
				data={Jobs}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				// refreshControl={
				//   <RefreshControl
				//     colors={[currentTheme.themeBackground, currentTheme.black]}
				//     onRefresh={() => refresh()}
				//     refreshing={Loading}
				//   />
				// }
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().mt50, styles().mb50]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : 'No jobs'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							InProgressJobs={true}
							// alreadyApplied={true}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh30px} />}
			/>
		</View>
	)
}

export default AllJobs
