import { View, useWindowDimensions, Text, ScrollView, Animated, Dimensions, TouchableOpacity } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { TabView, SceneMap, TabBar } from 'react-native-tab-view'
import AppliedJobs from './AppliedJobs'
import CompletedJobs from './CompletedJobs'
import SavedJobs from './SavedJobs'
import RejectedJobs from './RejectedJobs'
import InProgressJobs from './InProgress'

const MyJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const activeTab = props?.route?.params?.active
	const { width } = Dimensions.get('window')

	const layout = useWindowDimensions()

	const [index, setIndex] = React.useState(activeTab ? activeTab : 0)
	const [routes] = React.useState([
		{ key: 'first', title: 'Applied Jobs' },
		{ key: 'second', title: 'Hired' },
		{ key: 'third', title: 'Completed Jobs' },
		{ key: 'fourth', title: 'Saved Jobs' },
		{ key: 'fifth', title: 'Others' },
	])

	const renderScene = SceneMap({
		first: () => <AppliedJobs {...props} />,
		second: () => <InProgressJobs {...props} />,
		third: () => <CompletedJobs {...props} />,
		fourth: () => <SavedJobs {...props} />,
		fifth: () => <RejectedJobs {...props} />,
	})

	const _renderTabBar = props => {
		const inputRange = props.navigationState.routes.map((_x, i) => i)
		return (
			<View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
				<ScrollView
					horizontal
					contentContainerStyle={{ flexGrow: 1 }}
					onScroll={event => {
						handleScroll(event)
					}}
					scrollEventThrottle={16}
				>
					{props.navigationState.routes.map((route, i) => {
						const _opacity = props.position.interpolate({
							inputRange,
							outputRange: inputRange.map(inputIndex => (inputIndex === i ? 1 : 0.5)),
						})

						return (
							<TouchableOpacity
								style={[i === 0 ? styles().ml0 : styles().ml25]}
								onPress={() => setIndex(i)}
							>
								<Animated.Text
									style={[
										styles().fs18,
										styles().fontMedium,
										{
											color: index === i ? currentTheme.themeBackground : currentTheme.C3C3C3,
										},
									]}
								>
									{route.title}
								</Animated.Text>
								<View
									style={[
										styles().w70px,
										styles().mt5,
										{
											borderBottomWidth: index === i ? 2 : 0,
											borderBottomColor: currentTheme.themeBackground,
										},
									]}
								/>
							</TouchableOpacity>
						)
					})}
				</ScrollView>
			</View>
		)
	}

	const renderTabBar = props => (
		<TabBar
			{...props}
			indicatorStyle={[{ marginBottom: 10, width: '100%', height: 2 }]}
			indicatorContainerStyle={{ width: 0 }}
			//   indicatorContainerStyle={{marginRight:10}}
			style={[{ backgroundColor: currentTheme.white, elevation: 0 }]}
			activeColor={currentTheme.black}
			inactiveColor={currentTheme.blackish}
			scrollEnabled={true}
			pressOpacity={1}
			pressColor={currentTheme.white}
			contentContainerStyle={{ width: 'auto' }}
			tabStyle={[styles().alignStart, styles().mr10, { elevation: 0, width: 'auto', overflow: 'hidden' }]}
			renderLabel={({ route, focused, color }) => (
				<>
					<Text
						style={[
							styles().fs18,
							styles().fontMedium,
							{ textTransform: 'capitalize' },
							{
								color: focused ? currentTheme.themeBackground : currentTheme.C3C3C3,
							},
						]}
					>
						{route.title}
					</Text>
					{focused
						? <View
								style={[
									styles().w70px,
									styles().mt5,
									{
										borderBottomWidth: 2,
										borderBottomColor: currentTheme.themeBackground,
									},
								]}
							/>
						: null}
				</>
			)}
		/>
	)

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'My Jobs'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				<TabView
					navigationState={{ index, routes }}
					renderScene={renderScene}
					onIndexChange={setIndex}
					renderTabBar={renderTabBar}
					initialLayout={{ width: layout.width }}
				/>
			</View>
		</Layout>
	)
}

export default React.memo(MyJobs)
