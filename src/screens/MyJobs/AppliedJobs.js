import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import JobsComponent from '../../component/JobsComponent/JobsComponent'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getAppliedJobs } from '../../apollo/server'
import UserContext from '../../context/User/User'

const AppliedJobs = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [appliedJobs, setAppliedJobs] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 30

	console.log('📄 AppliedJobs - GraphQL Query variables:', {
		filters: null,
		options: {
			page: page,
			limit: pageSize,
			sortBy: 'createdAt:desc',
		},
	})

	const { data, loading, error, refetch } = useQuery(getAppliedJobs, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: null,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			console.log('📱 AppliedJobs - onCompleted called')
			console.log('📱 AppliedJobs - Full response:', JSON.stringify(res, null, 2))
			console.log('📱 AppliedJobs - Results array:', res?.getAppliedJobs?.results)
			console.log('📱 AppliedJobs - Results count:', res?.getAppliedJobs?.results?.length || 0)
			console.log('📱 AppliedJobs - Total results:', res?.getAppliedJobs?.totalResults)
			console.log('📱 AppliedJobs - Current page:', page)
			setAppliedJobs(prev => [...prev, ...res?.getAppliedJobs?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('❌ AppliedJobs - Error occurred:', err)
			console.log('❌ AppliedJobs - Error message:', err.message)
			console.log('❌ AppliedJobs - Error details:', JSON.stringify(err, null, 2))
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const reload = async () => {
		console.log('🔄 AppliedJobs - Reload called')
		await refetch({
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			console.log('🔄 AppliedJobs - Reload response:', res.data?.getAppliedJobs)
			setAppliedJobs(res.data?.getAppliedJobs?.results)
			setLoading(false)
		})
	}

	const refresh = async () => {
		console.log('🔄 AppliedJobs - Refresh called')
		setLoading(true)
		setPage(1)
		setAppliedJobs([])
		await refetch().then(res => {
			console.log('🔄 AppliedJobs - Refresh response:', JSON.stringify(res?.data?.getAppliedJobs, null, 2))
			setAppliedJobs(_prev => res?.data?.getAppliedJobs?.results)
			setLoading(false)
		}).catch(err => {
			console.log('❌ AppliedJobs - Refresh error:', err)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.getAppliedJobs?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	useEffect(() => {
		console.log('🚀 AppliedJobs - Component mounted, starting initial load')
		console.log('👤 AppliedJobs - Current user:', user?.email)
		console.log('👤 AppliedJobs - User ID:', user?._id)
		setLoading(true)
	}, [])

	console.log('📊 AppliedJobs - Render state:')
	console.log('- appliedJobs array length:', appliedJobs?.length || 0)
	console.log('- Loading state:', Loading)
	console.log('- GraphQL loading state:', loading)
	console.log('- GraphQL error:', error)
	console.log('- GraphQL data:', data)
	console.log('- Current page:', page)

	return (
		<View style={styles().flex}>
			<FlatList
				data={appliedJobs}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View
							style={[
								styles().alignCenter,
								styles().justifyCenter,

								styles().flex,
							]}
						>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : `No jobs (Total: ${appliedJobs?.length || 0})`}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					const isSave = user?.savedJobs?.find(savejob => savejob === item?._id)
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							alreadyApplied={true}
							isSave={!!isSave}
							reload={async () => await reload()}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default AppliedJobs
