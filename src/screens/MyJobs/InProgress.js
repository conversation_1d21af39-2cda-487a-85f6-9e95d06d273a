import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import JobsComponent from '../../component/JobsComponent/JobsComponent'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getActiveJobs } from '../../apollo/server'
import UserContext from '../../context/User/User'

const InProgressJobs = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [page, setPage] = useState(1)
	const [Loading, setLoading] = useState(false)
	const [InProgressJobs, setInProgressJobs] = useState([])
	const [_checksavedJob, setChecksavedJob] = useState(undefined)

	const pageSize = 20
	const { data, loading, error, refetch } = useQuery(getActiveJobs, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			// console.log('getActiveJobs res :', JSON.stringify(data?.getActiveJobs));
			setLoading(false)
			setInProgressJobs(prev => [...prev, ...res?.getActiveJobs?.results])
			const isSave = user?.savedJobs?.some(_id => _id === res?.getActiveJobs?.results._id)
			setChecksavedJob(isSave)
		},
		onError: err => {
			setLoading(false)
			console.log('getActiveJobs err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setInProgressJobs([])
		// await refetch();
		await refetch().then(res => {
			setInProgressJobs(_prev => res?.data?.getActiveJobs?.results)
			setLoading(false)
			console.log('refresh getActiveJobs')
		})
	}

	const nextPage = async () => {
		if (page < data?.getActiveJobs?.totalPages) {
			setPage(old => old + 1)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(true)
	}, [])
	// console.log('inprogress jobs :', InProgressJobs.length);
	return (
		<View style={styles().flex}>
			<FlatList
				data={InProgressJobs}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				onEndReached={() => nextPage()}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<View
								style={{
									height: 100,
									alignItems: 'center',
									justifyContent: 'center',
									paddingTop: 20,
								}}
							>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No jobs'}
								</Text>
							</View>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<JobsComponent
							item={item}
							index={index}
							navigation={props.navigation}
							alreadyApplied={true}
							InProgressJobs={true}
							// isSave={checksavedJob}
						/>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</View>
	)
}

export default InProgressJobs
