import { View, Text, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AntDesign from '@expo/vector-icons/AntDesign'
import Ionicons from '@expo/vector-icons/Ionicons'
import FontAwesome from '@expo/vector-icons/FontAwesome'

const TypesCard = ({ item, index, verificationInfo, navigation }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const name = item?.step?.replaceAll('-', ' ')
	const checkStatus = item?.status !== 'completed' && item?.status !== 'locked'
	const checkStatus2 = item?.status !== 'completed' && item?.status === 'locked'
	// const pending = item.status === 'pending';
	// const rejected = item?.status === 'rejected';
	const openCompletedBox = item?.status === 'completed' || item?.status !== 'locked'

	return (
		<TouchableOpacity
			activeOpacity={0.5}
			disabled={!openCompletedBox}
			onPress={() => {
				console.log('type :', item)
				if (openCompletedBox) {
					navigation.navigate('RBPForm', {
						step: item,
						title: name?.toUpperCase(),
						verification: verificationInfo,
					})
				}
			}}
			key={index}
			style={[
				styles().br10,
				styles().ph20,
				styles().pv15,
				styles().flexRow,
				styles().justifyBetween,
				styles().alignCenter,
				styles().mt10,
				{
					borderColor:
						item?.status === 'rejected'
							? currentTheme.red
							: checkStatus
								? currentTheme.starColor
								: checkStatus2
									? currentTheme.B7B7B7
									: currentTheme.lightGreen,
					borderWidth: 1,
				},
			]}
		>
			<View style={[styles().flex]}>
				<Text
					style={[
						styles().fs14,
						styles().fontRegular,
						name !== 'twic' && styles().textCapitalize,
						{
							color: checkStatus
								? currentTheme.blackish
								: checkStatus2
									? currentTheme.B7B7B7
									: item?.status === 'rejected'
										? currentTheme.red
										: currentTheme.lightGreen,
						},
					]}
				>
					{name === 'twic' ? 'TWIC' : name}
				</Text>
			</View>
			<View style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}>
				{item?.status === 'locked'
					? <FontAwesome
							name={'lock'}
							size={22}
							color={currentTheme.B7B7B7}
							style={[styles().mr10]}
						/>
					: null}
				{item?.status === 'completed'
					? <AntDesign
							name={'checkcircle'}
							size={18}
							color={currentTheme.lightGreen}
							style={[styles().mr10]}
						/>
					: item?.status === 'pending' || item?.status === 'rejected'
						? <AntDesign
								name={'exclamationcircle'}
								size={18}
								color={item?.user?.checkr_decision === 'NEED_REVIEW' || item?.status === 'rejected' ? currentTheme.red : currentTheme.starColor}
								style={[styles().mr10]}
							/>
						: item?.status === 'inprogress'
							? <AntDesign
									name={'clockcircle'}
									size={18}
									color={currentTheme.starBg}
									style={[styles().mr10]}
								/>
							: null}
				<Ionicons
					name={'arrow-forward-circle'}
					size={24}
					color={checkStatus ? currentTheme.blackish : currentTheme.B7B7B7}
				/>
			</View>
		</TouchableOpacity>
	)
}

export default TypesCard
