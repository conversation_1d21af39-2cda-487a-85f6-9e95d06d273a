import RBPTextInput from './InputTypes/RBPTextInput'
import RBPSignature from './InputTypes/RBPSignature'
import RBPDate from './InputTypes/RBPDate'
import RBPMultiCheck from './InputTypes/RBPMultiCheck'
import RBPLibrary from './InputTypes/RBPLibrary'
import RBPDropDown from './InputTypes/RBPDropDown'
import RBPRadioButton from './InputTypes/RBPRadioButton'
import RBPMultiLibrary from './InputTypes/RBPMultiLibrary'
import RBPGooglePlaces from './InputTypes/RBPGooglePlaces'
import RBPCheck from './InputTypes/RBPCheck'
import RBPTextArea from './InputTypes/RBPTextArea'

const RBPInputs = props => {
	const { item } = props
	const { type } = item

	switch (type) {
		case 'textinput':
			return <RBPTextInput {...props} />
		case 'textarea':
			return <RBPTextArea {...props} />
		case 'googleinput':
			return <RBPGooglePlaces {...props} />
		case 'radiobuttons':
			return <RBPRadioButton {...props} />
		case 'dropdown':
			return <RBPDropDown {...props} />
		case 'library':
			return <RBPLibrary {...props} />
		case 'multicheck':
			return <RBPMultiCheck {...props} />
		case 'checkbox':
			return <RBPCheck {...props} />
		case 'date':
			return <RBPDate {...props} />
		case 'signature':
			return <RBPSignature {...props} />
		case 'multiLibrary':
			return <RBPMultiLibrary {...props} />
		default:
			return null
	}
}

export default RBPInputs
