import { View, FlatList, Platform, Text, Dimensions, TouchableOpacity } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../../styles'
import Layout from '../../../component/Layout/Layout'
import RBPInputs from '../RBPInputs'
import ImageAndDocumentPicker from '../../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import ThemeButton from '../../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../../component/FlashMessage/FlashMessage'
import { getRBPVerificationFieldsByStepName, getRBPVerificationStepData, updateRBPVerificationStep } from '../../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import Spinner from '../../../component/Spinner/Spinner'
import DateTimePicker from '@react-native-community/datetimepicker'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import moment from 'moment'

const RBPForm = props => {
	const { step, title, verification } = props?.route?.params
	const { height } = Dimensions.get('screen')
	const [pickers, setPickers] = useState({})
	const [selectedDate, setSelectedDate] = useState(new Date())
	const [calendarPickers, setCalendarPickers] = useState({})
	const [state, setState] = useState([])
	const [Loading, setLoading] = useState(false)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [details, setDetails] = useState('')

	const setPreviousData = previouseData => {
		const updatedForm = state?.map(field => {
			const apiValue = previouseData[field?.name]
			return { ...field, value: apiValue ? apiValue : '' }
		})
		setState(updatedForm)
	}

	const { data, loading, error, refetch } = useQuery(getRBPVerificationFieldsByStepName, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			stepName: step?.step,
		},
		onCompleted: _res => {
			const fields = data?.getRBPVerificationFieldsByStepName?.fields
			const fieldsWithValue = fields.map(field => ({
				...field,
				value: field?.type === 'multicheck' ? [] : '',
				error: false,
			}))

			setState(fieldsWithValue)
			setDetails(data?.getRBPVerificationFieldsByStepName?.description)
			// console.log(
			//   'getRBPVerificationFieldsByStepName res :',
			//   JSON.stringify(data?.getRBPVerificationFieldsByStepName),
			// );
		},
		onError: err => {
			console.log('getRBPVerificationFieldsByStepName err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: stepData,
		loading: stepLoading,
		error: stepError,
		refetch: stepRefetch,
	} = useQuery(getRBPVerificationStepData, {
		fetchPolicy: 'cache-and-network',
		errorPolicy: 'all',
		variables: {
			verificationId: verification?._id,
			stepId: step?._id,
		},
		onCompleted: res => {
			// console.log(
			//   'getRBPVerificationStepData res :',
			//   JSON.stringify(res?.getRBPVerificationStepData),
			// );
			setPreviousData(res?.getRBPVerificationStepData)
		},
		onError: err => {
			console.log('getRBPVerificationStepData err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [mutate, { client }] = useMutation(updateRBPVerificationStep, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateRBPVerificationStep res :', data?.updateRBPVerificationStep)
			FlashMessage({ msg: 'Form Submited!', type: 'success' })
			setLoading(false)
			props.navigation.goBack()
		} catch (e) {
			console.log(e)
		}
	}

	function onError(error) {
		setLoading(false)
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		console.log('updateRBPVerificationStep error  :', error)
	}

	const handleInputChange = (index, data) => {
		const newState = [...state]
		const currentInput = newState[index]
		if (currentInput) {
			if (currentInput?.type === 'multicheck') {
				const currentValue = [...currentInput?.value] // Create a copy of the value array
				const valueIndex = currentValue?.indexOf(data)
				if (valueIndex === -1) {
					// If option doesn't exist in value array, add it
					currentValue?.push(data)
				} else {
					// If option exists in value array, remove it
					currentValue.splice(valueIndex, 1)
				}
				currentInput.value = currentValue // Update the value array
			} else if (currentInput?.type === 'googleinput') {
				const { address, city, state, zipCode } = data
				currentInput.value = address
				// Update the corresponding city, state, and zipcode fields in the newState
				newState.forEach(item => {
					if (item.name === 'city' && city) {
						item.value = city
					}
					if (item.name === 'state' && state) {
						item.value = state
					}
					if (item.name === 'zipcode' && zipCode) {
						item.value = zipCode
					}
				})
			} else {
				// For other input types, update value directly
				currentInput.value = data
			}
		}
		setState(newState)
	}

	const togglePicker = name => {
		setPickers(prevState => ({
			...prevState,
			[name]: !prevState[name], // Toggle the visibility of the picker
		}))
	}

	const toggleCalendarPicker = name => {
		setCalendarPickers(prevState => ({
			...prevState,
			[name]: !prevState[name], // Toggle the visibility of the picker
		}))
	}

	const handleSubmit = async () => {
		try {
			// Initialize an object to collect values by name
			const valuesToNameObject = {}

			// Track if there are any required fields not filled
			let requiredFieldsEmpty = false
			let regexValidationFailed = false

			// Iterate over the state array and collect values by name
			const newState = state.map(item => {
				valuesToNameObject[item.name] = item.value

				// Check if the field is required and its value is empty
				if (item.required && !item.value) {
					// If yes, set error to true for the required field
					requiredFieldsEmpty = true
					// Set error message without repeating "is required"
					const errorMessage = item?.errorMessage ? item?.errorMessage : `${item.placeholder} is required`
					return {
						...item,
						error: true,
						errorMessage: errorMessage,
					}
				}

				// Check regex validation if validation object exists
				if (item?.validation?.regex && item.required) {
					const regex = new RegExp(item?.validation?.regex)
					if (!regex?.test(item?.value)) {
						regexValidationFailed = true
						return {
							...item,
							error: true,
							errorMessage: item?.validation?.message,
						}
					}
				}

				// Reset the error status when the field is not empty
				return { ...item, error: false, errorMessage: '' }
			})

			// If googleinput type has a value filled with city, state, and zipCode,
			// then set these values in the state array for corresponding fields with empty values

			// Update the state with new error statuses
			setState(newState)

			// If any required field is empty, prevent submission
			if (requiredFieldsEmpty) {
				console.warn('Please fill in all required fields.')
				return // Stop further execution
			}

			// If any field failed regex validation, prevent submission
			if (regexValidationFailed) {
				console.warn('Please correct the errors in the form.')
				return // Stop further execution
			}

			// Now valuesToNameObject object contains values indexed by their corresponding names
			// Example: If you want to append these values into another object
			const otherObject = {
				// existing properties of the other object
				// ...
				...valuesToNameObject, // append form data
			}

			const data = {
				verificationId: verification?._id,
				stepId: step?._id,
				updateRbpVerificationInput: otherObject,
			}
			console.log('submit :', data)
			setLoading(true)
			await mutate({ variables: data })
		} catch (error) {
			console.error('An error occurred during form submission:', error)
		} finally {
			setLoading(false)
		}
	}

	// useEffect(() => {
	//   setPreviousData();
	// }, [stepData]);

	// console.log('state :', state);

	return (
		<>
			<Layout
				LeftIcon={true}
				headerShown={true}
				pagetitle={title}
				ContentArea={[styles().ph20]}
				navigation={props.navigation}
				keyBoardArea={65}
			>
				<View style={[styles().flex, styles().mt15]}>
					<FlatList
						nestedScrollEnabled={true}
						keyboardShouldPersistTaps={'handled'}
						showsVerticalScrollIndicator={false}
						data={state}
						ListHeaderComponent={
							<Text style={[styles().fs14, styles().fontMedium, styles().textLeft, styles().mb20, { color: currentTheme.black }]}>{details}</Text>
						}
						renderItem={({ item, index }) => {
							return (
								<RBPInputs
									item={item}
									onChangeValue={data => handleInputChange(index, data)}
									isPicker={name => togglePicker(name)}
									isCalendar={name => toggleCalendarPicker(name)}
								/>
							)
						}}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().wh100, { height: height / 1.5 }]}>
									<Text
										style={[
											styles().fontRegular,
											{
												color: currentTheme.E8E8C8,
												fontSize: 14,
											},
										]}
									>
										{loading ? 'Loading...' : 'No Field Available'}
									</Text>
								</View>
							)
						}}
					/>
					<View style={[styles().flex, styles().justifyEnd, styles().mv25]}>
						{Loading
							? <Spinner />
							: !loading
								? <ThemeButton
										Title={'Confirm & Next'}
										onPress={() => handleSubmit()}
									/>
								: null}
					</View>
				</View>
			</Layout>

			{Object.keys(calendarPickers).map(pickerId => {
				if (calendarPickers[pickerId]) {
					return (
						<DateTimePicker
							testID="dateTimePicker"
							minimumDate={new Date('1947-08-14')}
							value={selectedDate}
							mode={'date'}
							themeVariant="light"
							textColor={currentTheme.themeBackground}
							accentColor="blue"
							positiveButton={{
								label: 'OK',
								textColor: currentTheme.themeBackground,
							}}
							negativeButton={{
								label: 'Cancel',
								textColor: currentTheme.themeBackground,
							}}
							display={Platform.OS === 'ios' ? 'spinner' : 'default'}
							is24Hour={true}
							onChange={(event, date) => {
								if (event.type === 'dismissed') {
									// Handle cancel button press
									toggleCalendarPicker(pickerId)
									return
								}
								if (Platform.OS !== 'ios') {
									toggleCalendarPicker(pickerId)
								}
								if (event.type === 'set') {
									setSelectedDate(date)
									const selected = moment(date.toString()).format('YYYY-MM-DD')
									handleInputChange(
										state?.findIndex(item => item?.name === pickerId),
										selected
									)
								}
							}}
							style={{
								alignSelf: 'center',
								justifyContent: 'center',
								alignItems: 'flex-start',
							}}
						/>
					)
				}
			})}
			{Object.keys(calendarPickers).map(pickerId => {
				if (calendarPickers[pickerId] && Platform.OS === 'ios') {
					return (
						<View
							style={[
								styles().alignSelfCenter,
								styles().justifyCenter,
								styles().alignCenter,
								styles().mb30,
								styles().bw1,
								styles().br5,
								{ borderColor: currentTheme.themeBackground },
							]}
						>
							<TouchableOpacity
								style={[styles().ph30, styles().pv5]}
								onPress={() => {
									toggleCalendarPicker(pickerId)
								}}
							>
								<Text style={[styles().fontMedium, styles().fs14, { color: currentTheme.themeBackground }]}>Close</Text>
							</TouchableOpacity>
						</View>
					)
				}
			})}
			{Object.keys(pickers).map(pickerId => (
				<ImageAndDocumentPicker
					isImagePicker={pickers[pickerId]} // Determine visibility based on the picker state
					setIsImagePicker={() => togglePicker(pickerId)} // Close the picker
					setImage={data =>
						handleInputChange(
							state.findIndex(item => item?.name === pickerId),
							String(data)
						)
					} // Update the corresponding input item
					isPdf={true}
				/>
			))}
		</>
	)
}

export default RBPForm
