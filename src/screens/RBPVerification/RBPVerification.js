import { View, Text, FlatList, StyleSheet, TouchableOpacity, Dimensions } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import TypesCard from './TypesCard'
import { SubmitVerification, createRBPVerification } from '../../apollo/server'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useIsFocused } from '@react-navigation/native'
import Spinner from '../../component/Spinner/Spinner'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import fontStyles from '../../utils/fonts/fontStyles'

const RBPVerification = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const [Loading, setLoading] = useState(false)
	const { height } = Dimensions.get('window')

	const [mutate, { client, loading, data }] = useMutation(createRBPVerification, {
		errorPolicy: 'all',
		onCompleted: _data => {
			// console.log(
			//   'createRBPVerification res :',
			//   JSON.stringify(data?.createRBPVerification),
			// );
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('createRBPVerification error  :', error)
		},
	})

	const [mutateSubmit, {}] = useMutation(SubmitVerification, {
		errorPolicy: 'all',
		onCompleted: data => {
			console.log('SubmitVerification res :', data?.SubmitVerification)
			setLoading(false)
			props.navigation.goBack()
			FlashMessage({ msg: 'RBP Verification Submitted', type: 'success' })
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			setLoading(false)
			console.log('SubmitVerification error  :', error)
		},
	})

	async function getFormByRole() {
		await mutate()
	}

	const handleSubmit = async () => {
		setLoading(true)
		mutateSubmit({
			variables: {
				submitVerificationId: data?.createRBPVerification?._id,
			},
		})
	}

	useEffect(() => {
		getFormByRole()
	}, [isFocus])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'RBP Verification'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={65}
		>
			<View style={[styles().flex]}>
				<FlatList
					data={data?.createRBPVerification?.verifications}
					keyExtractor={(_item, index) => index.toString()}
					showsVerticalScrollIndicator={false}
					ListFooterComponent={<View style={styles().wh20px} />}
					renderItem={({ index, item }) => {
						return (
							<TypesCard
								item={item}
								verificationInfo={data?.createRBPVerification}
								index={index}
								navigation={props.navigation}
							/>
						)
					}}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().wh100, { height: height / 1.2 }]}>
								<Text
									style={[
										styles().fontRegular,
										{
											color: currentTheme.E8E8C8,
											fontSize: 14,
										},
									]}
								>
									{loading ? 'Loading...' : 'No Verification Fields Available'}
								</Text>
							</View>
						)
					}}
				/>
				<View style={[styles().alignCenter, styles().justifyCenter]}>
					{data?.createRBPVerification?.status === 'verified' && (
						<View style={[styles().mb5, styles().alignCenter]}>
							<Text style={rbpStyle.error}>
								{'Your account has been successfully verified by the Admin. Once the Admin approves it, you will be eligible to apply for a job.'}
							</Text>
							<TouchableOpacity
								onPress={() => props.navigation.navigate('AllIssues')}
								style={[styles().mt10, styles().bw1, styles().br5, styles().ph10, styles().pv5, { borderColor: currentTheme.black }]}
								activeOpacity={0.5}
							>
								<Text style={[styles().fs12, styles().fontMedium]}>Click here to contact Admin</Text>
							</TouchableOpacity>
						</View>
					)}
					{data?.createRBPVerification?.user?.checkr_decision === 'NEED_REVIEW' && (
						<Text style={rbpStyle.error}>
							{data?.createRBPVerification?.reviewComment ? `Review Reason : ${data?.createRBPVerification?.reviewComment}` : null}
						</Text>
					)}
					{data?.createRBPVerification?.rejectReason && (
						<Text style={rbpStyle.error}>{`RBP Rejection Reason: ${data?.createRBPVerification?.rejectReason}`}</Text>
					)}
				</View>
				<View style={[styles().flex, styles().justifyEnd, styles().mv15]}>
					{Loading
						? <Spinner />
						: data
							? <ThemeButton
									Style={{
										backgroundColor:
											data?.createRBPVerification?.status === 'verified'
												? currentTheme?.lightGreen
												: data?.createRBPVerification?.status === 'completed'
													? currentTheme.themeBackground
													: '',
										borderWidth: data?.createRBPVerification?.status === 'verified' ? 0 : data?.createRBPVerification?.status === 'completed' ? 0 : 1,
									}}
									StyleText={{
										color:
											data?.createRBPVerification?.status === 'verified'
												? currentTheme?.white
												: data?.createRBPVerification?.status === 'completed'
													? currentTheme.white
													: currentTheme.black,
									}}
									disabled={data?.createRBPVerification?.status === 'completed' ? false : data?.createRBPVerification?.status === 'verified' ? true : true}
									onPress={() => handleSubmit()}
									Title={
										data?.createRBPVerification?.status === 'verified'
											? 'Verified'
											: data?.createRBPVerification?.status === 'submited'
												? 'Submited'
												: 'Submit RBP Verification'
									}
									withoutBg={data?.createRBPVerification?.status === 'completed' ? false : data?.createRBPVerification?.status !== 'verified'}
								/>
							: null}
				</View>
			</View>
		</Layout>
	)
}

export default RBPVerification

const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 10,
		fontSize: 12,
		textAlign: 'left',
		color: '#B00020',
		fontFamily: fontStyles.PoppinsRegular,
	},
})
