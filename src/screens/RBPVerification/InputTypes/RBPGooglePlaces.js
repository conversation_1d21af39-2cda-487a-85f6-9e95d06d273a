import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete'
import { useContext, useEffect, useRef, useState } from 'react'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import getEnvVars from '../../../../environment'
import { StyleSheet, Text, View } from 'react-native'
import styles from '../../styles'
import fontStyles from '../../../utils/fonts/fontStyles'

const RBPGooglePlaces = props => {
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, title } = item
	const { GOOGLE_API_KEY } = getEnvVars()
	const searchRef = useRef(null)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const handleSelect = (data, details) => {
		// console.log("address_components :", JSON.stringify(details));
		// console.log("data_components :", JSON.stringify(data));
		const state = details?.address_components?.find(component => component?.types?.includes('administrative_area_level_1'))
		const zipCode = details?.address_components?.find(component => component?.types?.includes('postal_code'))
		const city = details?.address_components.find(component => component?.types?.includes('locality'))
		console.log('Selected State:', state?.long_name)
		console.log('ZIP Code:', zipCode)
		onChangeValue({
			state: state?.long_name,
			address: data?.description,
			zipCode: zipCode?.long_name,
			city: city?.long_name,
		})
	}

	const [_isFocused, setIsFocused] = useState(false)

	const handleFocus = () => {
		setIsFocused(true)
	}

	const handleBlur = () => {
		setIsFocused(false)
	}

	useEffect(() => {
		if (searchRef?.current) {
			searchRef?.current?.setAddressText(value)
		}
	}, [value])

	return (
		<View style={[styles().mb20]}>
			{title ? <Text style={[styles().fs12, styles().fontMedium, styles().mb5, { color: currentTheme.black }]}>{title}</Text> : null}
			<GooglePlacesAutocomplete
				listViewDisplayed={false}
				ref={searchRef}
				placeholder={placeholder}
				onPress={handleSelect}
				textInputProps={{
					placeholderTextColor: currentTheme.c737373,
				}}
				styles={{
					textInputContainer: {
						// backgroundColor: theme.Red.themeBackground,
						borderRadius: 10,
						borderWidth: 1,
						borderColor: currentTheme.B7B7B7,
						height: 50,
					},
					// container: { height: 70 },
					textInput: {
						fontSize: 14,
						fontFamily: fontStyles.PoppinsRegular,
						//   height: 45,
						borderRadius: 15,
						color: '#000',
					},
					poweredContainer: {
						height: 0,
						width: 0,
						backgroundColor: 'transparent',
					},
					powered: { height: 0, width: 0 },
				}}
				//   GooglePlacesDetailsQuery={{ fields: "geometry" }}
				query={{
					key: GOOGLE_API_KEY,
					language: 'en',
					// types: ["(regions)"],
					types: ['address'],
					// components: "country:us",
				}}
				onFocus={handleFocus}
				onBlur={handleBlur}
				fetchDetails={true}
				enablePoweredByContainer={false}
			/>
			{error ? <Text style={rbpStyle.error}>{errorMessage}</Text> : null}
		</View>
	)
}

export default RBPGooglePlaces

const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
