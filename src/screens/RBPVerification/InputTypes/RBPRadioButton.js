import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'

const RBPRadioButton = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, title, error, errorMessage, keyboardType } = item
	return (
		<View style={[styles().mb20]}>
			<Text style={[styles().fs14, styles().fontMedium, styles().mb20, { color: currentTheme.black }]}>{title}</Text>
			<View style={[styles().flexRow, styles().alignStart, styles().justifyStart]}>
				{options?.map(rboptions => {
					return (
						<View style={[styles().flexRow, styles().alignCenter, styles().mr50]}>
							<TouchableOpacity
								activeOpacity={0.5}
								onPress={() => {
									item?.value === rboptions ? onChangeValue(null) : onChangeValue(rboptions)
								}}
								style={[
									styles().br100,
									styles().pall5,
									styles().wh20px,
									{
										backgroundColor: item?.value !== rboptions ? currentTheme.white : currentTheme.themeBackground,
										borderWidth: item?.value !== rboptions ? 1 : 0,
										borderColor: currentTheme.B7B7B7,
									},
								]}
							>
								<View style={[styles().br100, styles().flex, { backgroundColor: currentTheme.white }]} />
							</TouchableOpacity>
							<Text style={[styles().fontRegular, styles().fs14, styles().textCapitalize, styles().mh10, { color: currentTheme.c737373 }]}>{rboptions}</Text>
						</View>
					)
				})}
			</View>

			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPRadioButton
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 10,
		fontSize: 12,
		color: '#B00020',
	},
})
