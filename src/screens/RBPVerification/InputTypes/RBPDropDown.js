import { View, Text, StyleSheet } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import Dropdown from '../../../component/DropDown/Dropdopwn'

const RBPDropDown = props => {
	const themeContext = useContext(ThemeContext)
	const _currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType } = item
	return (
		<View style={[styles().mb20]}>
			<Dropdown
				placeholder={value ? value : placeholder}
				data={options}
				selectedValue={selected => onChangeValue(selected)}
			/>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPDropDown
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
