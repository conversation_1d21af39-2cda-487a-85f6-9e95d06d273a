import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../../styles'
import TextField from '../../../component/FloatTextField/FloatTextField'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'

const RBPTextInput = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, limit, visible, title } = item
	const _numericKeyboardTypes = name === 'phoneNo' || name === 'zipcode'
	const isPasswordField = name === 'password' || name === 'confirmPassword'
	const [eye, setEye] = useState('eye-slash')

	function toggleEye() {
		if (eye === 'eye') {
			setEye('eye-slash')
		} else {
			setEye('eye')
		}
	}

	// console.log(error, validation, validationError);
	return (
		<View style={[styles().mb20]}>
			{title ? <Text style={[styles().fs12, styles().fontMedium, styles().mb5, { color: currentTheme.black }]}>{title}</Text> : null}
			<TextField
				keyboardType={keyboardType ? keyboardType : 'default'}
				value={value}
				maxLength={limit ? Number.parseInt(limit) : null}
				autoCapitalize="none"
				errorText={error}
				placeholder={placeholder}
				onChangeText={text => {
					onChangeValue(text)
				}}
				style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
				secureTextEntry={isPasswordField ? eye !== 'eye' : false}
				childrenPassword={
					isPasswordField
						? <TouchableOpacity
								onPress={toggleEye.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={eye}
									size={16}
									color={eye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						: null
				}
			/>
			{error ? <Text style={rbpStyle.error}>{errorMessage}</Text> : null}
		</View>
	)
}

export default RBPTextInput

const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
