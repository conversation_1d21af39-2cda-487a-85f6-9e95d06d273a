import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import Entypo from '@expo/vector-icons/Entypo'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import { get_url_extension } from '../../../utils/Constants'

const RBPLibrary = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType } = item
	let url
	const checkValueType = Array.isArray(item?.value)
	url = checkValueType ? value[0] : value
	const docType = get_url_extension(url)
	const icon =
		docType === 'pdf'
			? <FontAwesome5
					name="file-pdf"
					size={45}
					color={currentTheme.themeBackground}
				/>
			: docType === 'docx' || docType === 'doc'
				? <FontAwesome
						name="file-word-o"
						size={45}
						color={currentTheme.themeBackground}
					/>
				: docType === 'png' || docType === 'jpeg' || docType === 'jpg'
					? <Image
							source={{ uri: url }}
							style={[styles().wh100, styles().br10]}
							resizeMode="cover"
						/>
					: <Entypo
							name="plus"
							size={45}
							color={currentTheme.themeBackground}
						/>

	return (
		<View style={[styles().mb20]}>
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => isPicker(name)}
				style={[
					styles().br10,
					styles().h100px,
					styles().alignCenter,
					styles().justifyCenter,
					styles().bw1,
					{
						borderStyle: 'dashed',
						borderColor: currentTheme.headingColor,
					},
				]}
			>
				{value
					? <View style={[styles().wh100, styles().br8, styles().alignCenter, styles().overflowH, styles().justifyCenter]}>{icon}</View>
					: <Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.black }]}>{placeholder}</Text>}
			</TouchableOpacity>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPLibrary
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
