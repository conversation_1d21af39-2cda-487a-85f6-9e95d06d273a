import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import Octicons from '@expo/vector-icons/Octicons'
import moment from 'moment'

const RBPDate = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, title } = item
	return (
		<View style={[styles().mb20]}>
			{title ? <Text style={[styles().fs12, styles().fontMedium, styles().mb5, { color: currentTheme.black }]}>{title}</Text> : null}
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => isCalendar(name)}
				style={[
					styles().br10,
					styles().h55px,
					styles().w100,
					styles().alignCenter,
					styles().flexRow,
					styles().ph15,
					styles().justifyBetween,
					styles().bw1,
					{
						borderColor: currentTheme.B7B7B7,
					},
				]}
			>
				<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>{value ? moment(value).format('LL') : placeholder}</Text>
				<Octicons
					name={'calendar'}
					size={18}
					color={currentTheme.B7B7B7}
				/>
			</TouchableOpacity>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPDate
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
