import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import Entypo from '@expo/vector-icons/Entypo'
import Ionicons from '@expo/vector-icons/Ionicons'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Feather from '@expo/vector-icons/Feather'
import { getFileNameFromPath, get_url_extension } from '../../../utils/Constants'
import ImagePicker from 'react-native-image-crop-picker'
import * as FileSystem from 'expo-file-system'
import { uploadImageToImageKit } from '../../../component/Cloudupload/CloudUpload'
import Loading from '../../../context/Loading/Loading'
import * as DocumentPicker from 'expo-document-picker'
import FlashMessage from '../../../component/FlashMessage/FlashMessage'

const RBPMultiLibrary = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [files, setFiles] = useState([])
	const { isLoader } = useContext(Loading)

	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, limit } = item

	const MultiLibraryPick = async () => {
		try {
			const Arr = []
			const multiImages = await ImagePicker.openPicker({
				multiple: true,
				mediaType: 'photo',
			})
			const conversionPromises = multiImages.map(async (item, _i) => {
				try {
					const file = await FileSystem.readAsStringAsync(item.path, {
						encoding: FileSystem.EncodingType.Base64,
					})

					if (file) {
						const documentBase64 = `data:${item.mime};base64,${file}`
						const file_name = getFileNameFromPath(item?.path)
						var currentDate = new Date()
						var unixTimestamp = currentDate.getTime()
						Arr.push({
							url: item.path,
							uri: documentBase64,
							type: item.mime,
							name: `${unixTimestamp}_${file_name}`,
						})
					} else {
						console.log('Failed to read file content or file content is empty.')
					}
				} catch (error) {
					console.log('Error while reading file:', error)
				}
			})
			await Promise.all(conversionPromises)

			// Check if setData is defined before calling it
			return Arr
		} catch (error) {
			console.log('LibraryPick catch :', error)
		}
	}

	const _DocumentPick = async () => {
		if (files?.length < limit) {
			try {
				var document = await DocumentPicker.getDocumentAsync({
					type: 'application/pdf',
				})

				if (document) {
					if (document.canceled) {
						return null
					}
					isLoader(true)
					const file = await FileSystem.readAsStringAsync(document?.assets[0]?.uri, {
						encoding: FileSystem.EncodingType.Base64,
					})
					const documentBase64 = document ? `data:application/pdf;base64,${file}` : null
					var currentDate = new Date()
					var unixTimestamp = currentDate.getTime()
					const data = {
						// url: document?.assets[0]?.uri,
						// url: documentBase64,
						uri: documentBase64,
						type: document?.assets[0]?.mimeType,
						name: `doc_${unixTimestamp}.pdf`,
					}
					if (documentBase64) {
						await uploadImageToImageKit(documentBase64, 'pdf').then(res => {
							setFiles(prev => {
								const newFiles = [...prev, res?.url]
								onChangeValue(newFiles) // Update the parent component
								return newFiles
							})
							isLoader(false)
						})
						return data
					}
				} else {
					// Document picker was canceled or encountered an error
					console.log('Document picker was canceled or encountered an error.')
					return null
				}
			} catch (err) {
				console.log('Error while picking document:', err)
				throw err
			}
		} else {
			FlashMessage({
				msg: `You cannot upload more than ${limit} images/documents`,
				type: 'warning',
			})
		}
	}

	const picker = async () => {
		if (files?.length < limit) {
			const response = await MultiLibraryPick()
			if (response) {
				isLoader(true)
				const uploadedFiles = []
				for (const item of response) {
					const getMime = item?.type?.split('/')
					const type = getMime[1]
					try {
						const res = await uploadImageToImageKit(item?.uri, type)
						uploadedFiles.push(res?.url)
					} catch (error) {
						console.error('Upload failed for item:', item, 'Error:', error)
					}
				}

				setFiles(prev => {
					const newFiles = [...prev, ...uploadedFiles]
					onChangeValue(newFiles) // Update the parent component
					return newFiles
				})
				isLoader(false)
			}
		} else {
			FlashMessage({
				msg: `You cannot upload more than ${limit} images/documents`,
				type: 'warning',
			})
		}
	}

	const removeFiles = index => {
		const newArr = [...files]
		newArr.splice(index, 1)
		onChangeValue(newArr)
		setFiles(newArr)
	}

	useEffect(() => {
		if (files?.length === 0 && value && value?.length > 0) {
			setFiles(value)
		}
		// onChangeValue([...files, ...value]);
	}, [value])

	return (
		<View style={[styles().mb20]}>
			<Text style={[styles().mb20, styles().fontRegular, { color: currentTheme.black }]}>{placeholder}</Text>
			<View style={[styles().flex, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
				<TouchableOpacity
					activeOpacity={0.5}
					onPress={() => picker(name)}
					style={[
						styles().br10,
						styles().h80px,
						styles().w100,
						styles().ph10,
						styles().flexRow,
						styles().alignCenter,
						styles().justifyCenter,
						styles().bw1,
						{
							borderStyle: 'dashed',
							borderColor: currentTheme.headingColor,
						},
					]}
				>
					<Feather
						name={'upload'}
						color={currentTheme.black}
						size={24}
					/>
					<Text style={[styles().fs14, styles().fontRegular, styles().ml10, { color: currentTheme.black }]}>{'Upload Images'}</Text>
				</TouchableOpacity>
				{/* <Text
          style={[
            styles().fs12,
            styles().fontRegular,
            {color: currentTheme.c9E9E9E},
          ]}>
          Or
        </Text>
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={() => DocumentPick(name)}
          style={[
            styles().br10,
            styles().w45,
            styles().ph10,
            styles().h80px,
            styles().flexRow,
            styles().alignCenter,
            styles().justifyCenter,
            styles().bw1,
            {
              borderStyle: 'dashed',
              borderColor: currentTheme.headingColor,
            },
          ]}>
          <Feather name={'upload'} color={currentTheme.black} size={20} />
          <Text
            style={[
              styles().fs12,
              styles().ml10,
              styles().fontRegular,
              {color: currentTheme.black},
            ]}>
            {'Upload PDF'}
          </Text>
        </TouchableOpacity> */}
			</View>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
			{files?.length > 0
				? <View style={[styles().flexWrap, styles().flexRow, styles().alignStart, styles().mt30]}>
						{files?.map((val, j) => {
							const fileName = getFileNameFromPath(val)
							const docType = get_url_extension(val)
							const icon =
								docType === 'pdf'
									? <FontAwesome5
											name="file-pdf"
											size={45}
											color={currentTheme.themeBackground}
										/>
									: docType === 'docx' || docType === 'doc'
										? <FontAwesome
												name="file-word-o"
												size={45}
												color={currentTheme.themeBackground}
											/>
										: docType === 'png' || docType === 'jpeg' || docType === 'jpg'
											? <Image
													source={{ uri: val }}
													style={[styles().wh100, styles().br10]}
													resizeMode="cover"
												/>
											: <Entypo
													name="plus"
													size={45}
													color={currentTheme.themeBackground}
												/>

							return (
								<View style={[styles().wh130px, styles().alignCenter, styles().mb10]}>
									<TouchableOpacity
										onPress={() => removeFiles(j)}
										style={[
											styles().posAbs,
											styles().br100,
											styles().pall5,
											styles().zIndex10,
											styles().alignCenter,
											styles().justifyCenter,
											{
												right: 5,
												top: -10,
												backgroundColor: currentTheme.themeBackground,
											},
										]}
									>
										<Ionicons
											name="close"
											size={16}
											color={currentTheme.white}
										/>
									</TouchableOpacity>
									<View
										key={j}
										style={[
											styles().alignCenter,
											styles().wh100px,
											styles().justifyCenter,

											styles().br10,
											{
												borderWidth: 1,
												borderColor: currentTheme.themeBackground,
											},
										]}
									>
										{icon}
									</View>
									<Text style={[styles().fs10, styles().mt5, { color: currentTheme.black }]}>{fileName}</Text>
								</View>
							)
						})}
					</View>
				: null}
		</View>
	)
}

export default RBPMultiLibrary
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
