import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import AntDesign from '@expo/vector-icons/AntDesign'

const RBPMultiCheck = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType } = item
	return (
		<View style={[styles().mb20, styles().flex]}>
			{placeholder ? <Text style={[styles().fs14, styles().fontMedium, styles().mb20, { color: currentTheme.black }]}>{placeholder}</Text> : null}
			<View style={[styles().flexRow, styles().flexWrap, styles().alignStart, styles().justifyStart]}>
				{options?.map(rboptions => {
					const isChecked = value?.includes(rboptions)
					return (
						<View style={[styles().flexRow, styles().mb15, styles().alignStart, styles().mr15]}>
							<TouchableOpacity
								activeOpacity={0.7}
								onPress={() => onChangeValue(rboptions)}
								style={[
									styles().br5,

									styles().mr5,
									styles().wh20px,
									styles().alignCenter,
									styles().bw1,
									styles().justifyCenter,
									{
										backgroundColor: !isChecked ? currentTheme.white : currentTheme.themeBackground,
										borderColor: currentTheme.themeBackground,
									},
								]}
							>
								{isChecked
									? <AntDesign
											name={'check'}
											size={14}
											color={currentTheme.white}
										/>
									: null}
							</TouchableOpacity>
							<Text style={[styles().fontRegular, styles().fs14, styles().textLeft, options?.length === 1 && styles().flex, { color: currentTheme.c737373 }]}>
								{rboptions}
							</Text>
						</View>
					)
				})}
			</View>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPMultiCheck
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
