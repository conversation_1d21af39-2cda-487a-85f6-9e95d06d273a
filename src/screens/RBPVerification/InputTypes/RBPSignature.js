import { View, Text, StyleSheet } from 'react-native'
import { useContext } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import Signature from '../../../component/SignatureScreen/SignatureScreen'

const RBPSignature = props => {
	const themeContext = useContext(ThemeContext)
	const _currentTheme = theme[themeContext.ThemeValue]
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, title } = item
	return (
		<View style={[styles().mb5]}>
			{/* {title ? (
        <Text
          style={[
            styles().fs12,
            styles().fontMedium,
            styles().mb5,
            {color: currentTheme.black},
          ]}>
          {title}
        </Text>
      ) : null} */}
			<Signature
				value={value}
				descriptionText={'Signature'}
				onOK={sign => {
					onChangeValue(sign)
				}}
			/>
			{error && <Text style={rbpStyle.error}>{errorMessage}</Text>}
		</View>
	)
}

export default RBPSignature
const rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
