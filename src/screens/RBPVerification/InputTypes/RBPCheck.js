import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { useContext, useState } from 'react'
import styles from '../../styles'
import ThemeContext from '../../../context/ThemeContext/ThemeContext'
import { theme } from '../../../context/ThemeContext/ThemeColor'
import AntDesign from '@expo/vector-icons/AntDesign'

const RBPCheck = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_isCheck, setIsCheck] = useState(false)
	const { item, validationType, onChangeValue, isPicker, isCalendar } = props
	const { type, value, placeholder, options, name, error, errorMessage, keyboardType, title } = item

	const handlePress = () => {
		setIsCheck(prev => {
			const newValue = !prev
			if (newValue === false) {
				onChangeValue('')
			} else {
				onChangeValue(String(newValue))
				return newValue
			}
		})
	}

	return (
		<View style={[styles().mb20, styles().flex]}>
			{title ? <Text style={[styles().fs12, styles().fontMedium, styles().mb20, { color: currentTheme.black }]}>{title}</Text> : null}
			<View style={[styles().flexRow, styles().flexWrap, styles().alignStart, styles().justifyStart]}>
				<View style={[styles().flexRow, styles().mb15, styles().alignStart, styles().mr15]}>
					<TouchableOpacity
						activeOpacity={0.7}
						onPress={() => handlePress()}
						style={[
							styles().br5,
							styles().mr5,
							styles().wh20px,
							styles().alignCenter,
							styles().bw1,
							styles().justifyCenter,
							{
								backgroundColor: value ? currentTheme.themeBackground : currentTheme.white,
								borderColor: error ? currentTheme.error : currentTheme.themeBackground,
							},
						]}
					>
						{value
							? <AntDesign
									name={'check'}
									size={14}
									color={currentTheme.white}
								/>
							: null}
					</TouchableOpacity>
					<Text
						style={[
							styles().fontRegular,
							styles().fs14,
							styles().textLeft,
							styles().flex,
							options?.length === 1 && styles().flex,
							{ color: error ? currentTheme.error : currentTheme.c737373 },
						]}
					>
						{placeholder}
					</Text>
				</View>
			</View>
			{/* {error && <Text style={rbpStyle.error}>{errorMessage}</Text>} */}
		</View>
	)
}

export default RBPCheck
const _rbpStyle = StyleSheet.create({
	error: {
		marginTop: 5,
		fontSize: 12,
		color: '#B00020',
	},
})
