import { Text, View, FlatList, Image, TouchableOpacity, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Feather from '@expo/vector-icons/Feather'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import moment from 'moment'
import { useQuery } from '@apollo/client'
import { Invoices } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import UserContext from '../../context/User/User'

const PaymentUnpaid = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Loading, setLoading] = useState(false)
	const [unpaidPayments, setUnpaidPayments] = useState([])
	const [page, setPage] = useState(1)
	const pageSize = 30

	const { data, loading, refetch } = useQuery(Invoices, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: {
				status: 'unpaid',
				payee: user?._id,
				// payer: user?._id,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			// console.log('Invoices  Unpaid res :', JSON.stringify(res?.Invoices));
			const invoices = res?.Invoices?.results
			setUnpaidPayments(prev => [...prev, ...invoices])
		},
		onError: err => {
			setLoading(false)
			console.log('Invoices err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setUnpaidPayments([])
		await refetch({
			filters: {
				status: 'unpaid',
				payee: user?._id,
			},
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setUnpaidPayments(_prev => res?.data?.Invoices?.results)
			setLoading(false)
			console.log('refresh')
		})
	}

	const nextPage = async () => {
		if (page < data?.Invoices?.totalPages) {
			setPage(old => old + 1)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(loading)
	}, [loading])
	// console.log(unpaidPayments.length);
	return (
		<View style={styles().flex}>
			<FlatList
				data={unpaidPayments}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.75}
				onEndReached={() => nextPage()}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				contentContainerStyle={{ flexGrow: 1 }}
				renderItem={({ item, index }) => {
					return (
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() =>
								props.navigation.navigate('PaymentDetails', {
									invoice: item,
									invoiceId: item?._id,
								})
							}
							key={index}
							style={[
								styles().flexRow,
								styles().pv15,
								styles().ph20,
								styles().br10,
								styles().bw1,
								{ borderColor: currentTheme.B7B7B7 },
								styles().mt10,
								styles().alignStart,
							]}
						>
							{item?.application?.job?.company?.photo || item?.checkRide?.company?.photo
								? <View style={[styles().wh40px, styles().br50, styles().overflowH, styles().mr10]}>
										<Image
											source={{
												uri: item?.paymentOf === 'job' ? item?.application?.job?.company?.photo : item.checkRide?.company?.photo,
											}}
											style={styles().wh100}
											resizeMode="cover"
										/>
									</View>
								: <View
										style={[
											styles().wh40px,
											styles().alignCenter,
											styles().justifyCenter,
											styles().br50,
											styles().overflowH,
											styles().mr10,
											{ borderWidth: 1, borderColor: currentTheme.themeBackground },
										]}
									>
										<Image
											source={require('../../assets/images/payments-img.png')}
											style={{ height: 20, width: 20 }}
											resizeMode="contain"
										/>
									</View>}
							<View style={[styles().flex]}>
								<Text
									numberOfLines={2}
									style={[styles().fs12, styles().lh18, styles().fw600, styles().textCapitalize, { color: currentTheme.headingColor }]}
								>
									{item.paymentOf === 'job' ? item?.application?.job?.title : item.checkRide?.company?.name}
								</Text>
								<View style={[styles().flexRow, styles().flexWrap, styles().alignCenter]}>
									<View style={[styles().flexRow, styles().alignCenter, styles().mr10, styles().mt10]}>
										<Feather
											name="calendar"
											size={14}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fw400, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).format('ll')}</Text>
									</View>
									<View style={[styles().flexRow, styles().mr10, styles().alignCenter, styles().mt10]}>
										<Feather
											name="clock"
											size={14}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fw400, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).format('LT')}</Text>
									</View>
									<View style={[styles().flexRow, styles().mr10, styles().alignCenter, styles().mt10]}>
										<MaterialIcons
											name="payments"
											size={14}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fw400, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
											{item?.paymentOf}
										</Text>
									</View>
								</View>
							</View>
							<View style={[styles().alignCenter, styles().justifyEnd]}>
								<Text style={[styles().fs14, styles().fw700, { color: currentTheme.themeBackground }]}>{`$${item?.amount}`}</Text>
								<TouchableOpacity
									activeOpacity={0.9}
									style={[
										// styles().bw1,
										styles().mt10,
										styles().h25px,
										styles().alignCenter,
										styles().justifyCenter,
										styles().ph15,
										styles().br50,
										{
											borderColor: item?.status === 'unpaid' ? currentTheme.c707070 : currentTheme.themeBackgroundLight,
											backgroundColor: item?.status === 'unpaid' ? currentTheme.c707070 : currentTheme.themeBackgroundLight,
										},
									]}
								>
									<Text
										style={[
											styles().fs10,
											styles().fw400,
											styles().textCapitalize,
											{
												color: item?.status === 'unpaid' ? currentTheme.E8E8C8 : currentTheme.themeBackground,
											},
										]}
									>
										{item?.status === 'unpaid' ? 'Pending' : 'Paid'}
									</Text>
								</TouchableOpacity>
							</View>
						</TouchableOpacity>
					)
				}}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{Loading ? 'Loading...' : 'No Pending Payments'}
							</Text>
						</View>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh30px} />}
			/>
		</View>
	)
}

export default PaymentUnpaid
