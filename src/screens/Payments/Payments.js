import { View, useWindowDimensions, Text, Dimensions } from 'react-native'
import React, { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { TabView, SceneMap, TabBar } from 'react-native-tab-view'
import PaymentPaid from './PaymentPaid'
import PaymentUnpaid from './PaymentUnpaid'

const Payments = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const activeTab = props?.route?.params?.active
	const { width } = Dimensions.get('window')
	const layout = useWindowDimensions()
	const [index, setIndex] = React.useState(activeTab ? activeTab : 0)
	const [_balance, _setBalance] = useState('')
	const [_Loading, _setLoading] = useState(false)
	const [routes] = React.useState([
		// { key: 'first', title: 'Completed Jobs' },
		{ key: 'first', title: 'Pending Payments' },
		{ key: 'second', title: 'Paid Payments' },
	])

	const renderScene = SceneMap({
		// first: () => <CompletedJobsPayment {...props} />,
		first: () => <PaymentUnpaid {...props} />,
		second: () => <PaymentPaid {...props} />,
	})

	const renderTabBar = props => (
		<TabBar
			{...props}
			indicatorStyle={[{ marginBottom: 10, width: '100%', height: 2 }]}
			indicatorContainerStyle={{ width: 0 }}
			//   indicatorContainerStyle={{marginRight:10}}
			style={[{ backgroundColor: currentTheme.white, elevation: 0 }]}
			activeColor={currentTheme.black}
			inactiveColor={currentTheme.blackish}
			scrollEnabled={true}
			pressOpacity={1}
			pressColor={currentTheme.white}
			contentContainerStyle={{ width: 'auto' }}
			tabStyle={[styles().alignStart, styles().mr10, { elevation: 0, width: 'auto', overflow: 'hidden' }]}
			renderLabel={({ route, focused, color }) => (
				<>
					<Text
						style={[
							styles().fs18,
							styles().fw700,
							{ textTransform: 'capitalize' },
							{
								color: focused ? currentTheme.themeBackground : currentTheme.C3C3C3,
							},
						]}
					>
						{route.title}
					</Text>
					{focused
						? <View
								style={[
									styles().w70px,
									styles().mt5,
									{
										borderBottomWidth: 2,
										borderBottomColor: currentTheme.themeBackground,
									},
								]}
							/>
						: null}
				</>
			)}
		/>
	)

	// const {data, loading, refetch} = useQuery(getAvailableBalance, {
	//   fetchPolicy: 'no-cache',
	//   onCompleted: data => {
	//     console.log(
	//       'getAvailableBalance res :',
	//       JSON.stringify(data?.getAvailableBalance),
	//     );
	//     setBalance(data?.getAvailableBalance);
	//   },
	//   onError: err => {
	//     console.log('getAvailableBalance err :', err);
	//     FlashMessage({msg: err.message, type: 'danger'});
	//     setLoading(false);
	//   },
	// });

	// async function refresh() {
	//   setLoading(true);
	//   refetch().then(res => {
	//     setBalance(res?.data?.getAvailableBalance);
	//     setLoading(false);
	//   });
	// }

	// useEffect(() => {
	//   setLoading(loading);
	// }, [loading]);

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Payments'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				{/* <View
          style={[
            styles().bw1,
            styles().br15,
            styles().mb25,
            styles().h170px,
            styles().overflowH,
            {borderColor: currentTheme.themeBackground},
          ]}>
          <ImageBackground
            style={[
              styles().wh100,
              styles().alignCenter,
              styles().justifyCenter,
            ]}
            resizeMode="contain"
            source={require('../../assets/images/payment-bg.png')}>
            <TouchableOpacity
              activeOpacity={0.5}
              onPress={() => refresh()}
              style={[styles().posAbs, {right: 0, top: 0, padding: 10}]}>
              <Feather
                name={'refresh-ccw'}
                size={18}
                color={currentTheme.themeBackground}
              />
            </TouchableOpacity>
            <Text
              style={[
                styles().fs20,
                styles().fw400,
                {color: currentTheme.C3C3C3},
              ]}>
              Balance
            </Text>
            {Loading ? (
              <View style={[styles().mb10, styles().mt5]}>
                <Spinner size={'small'} />
              </View>
            ) : (
              <View
                style={[
                  styles().flexRow,
                  styles().mb10,
                  styles().mt5,
                  styles().alignEnd,
                  styles().justifyCenter,
                ]}>
                <Text
                  style={[
                    styles().fs30,
                    styles().mr5,
                    styles().fw700,
                    {color: currentTheme.themeBackground},
                  ]}>{`$${balance?.available}`}</Text>
                <Text
                  style={[
                    styles().fs16,
                    styles().fw400,
                    {marginBottom: 3, color: currentTheme.C3C3C3},
                  ]}>
                  USD
                </Text>
              </View>
            )}
            <Text
              style={[
                styles().fs14,
                styles().fw400,
                {color: currentTheme.C3C3C3},
              ]}>
              {moment(new Date()).format('LLL')}
            </Text>
          </ImageBackground>
        </View> */}
				<TabView
					navigationState={{ index, routes }}
					renderScene={renderScene}
					onIndexChange={setIndex}
					renderTabBar={renderTabBar}
					initialLayout={{ width: layout.width }}
				/>
			</View>
		</Layout>
	)
}

export default Payments
