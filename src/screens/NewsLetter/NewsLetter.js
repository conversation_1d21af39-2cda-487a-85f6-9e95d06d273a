import { View, Text, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import Layout from '../../component/Layout/Layout'
import styles from '../styles'
import fontStyles from '../../utils/fonts/fontStyles'
import NewsLetterCard from '../../component/NewsLetterCard/NewsLetterCard'
import { NewsLetters } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'
import { useIsFocused } from '@react-navigation/native'

const NewsLetter = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const companyId = props?.route?.params?.companyId
	const [page, setPage] = useState(1)
	const [totalPages, setTotalPages] = useState('')
	const [Loading, setLoading] = useState(false)
	const [newsLetters, setNewsLetters] = useState([])
	const pageSize = 25
	const isFocus = useIsFocused()

	const { data, loading, refetch } = useQuery(NewsLetters, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		variables: {
			filters: {
				company: companyId,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			console.log('NewsLetters res :', JSON.stringify(res.NewsLetters))
			setNewsLetters(res?.NewsLetters?.results)
			setTotalPages(res?.NewsLetters?.totalPages)
			setLoading(false)
		},
		onError: err => {
			console.log('NewsLetters err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		await refetch({
			filters: {
				company: companyId,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setNewsLetters(res?.data?.NewsLetters?.results)
			setTotalPages(res?.data?.NewsLetters?.totalPages)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch({
				filters: {
					company: companyId,
				},
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
		}
	}

	useEffect(() => {
		setPage(1)
	}, [isFocus])

	useEffect(() => {
		setLoading(loading)
	}, [loading])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'NewsLetter'}
			navigation={props.navigation}
			withoutScroll={true}
			keyBoardArea={55}
		>
			<View style={[styles().ph20, styles().pt20, styles().flex]}>
				<FlatList
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					onEndReached={() => nextPage()}
					onEndReachedThreshold={0.5}
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ flexGrow: 1 }}
					data={newsLetters}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={<View style={styles().wh30px} />}
					renderItem={({ index, item }) => {
						return (
							<NewsLetterCard
								item={item}
								index={index}
							/>
						)
					}}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontFamily: fontStyles.PoppinsRegular,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No Newsletters'}
								</Text>
							</View>
						)
					}}
				/>
			</View>
		</Layout>
	)
}

export default NewsLetter
