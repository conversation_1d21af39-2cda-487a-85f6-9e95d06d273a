import { Text, View, TouchableOpacity, LayoutAnimation, Platform, Image, RefreshControl, UIManager, FlatList } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Feather from '@expo/vector-icons/Feather'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { Threads } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import moment from 'moment'
import ThreadsFilter from '../../component/Modals/ThreadsFilter'
import { useIsFocused } from '@react-navigation/native'
import UserContext from '../../context/User/User'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const AllIssues = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [search, setSearch] = useState('')
	const [searchData, setSearchData] = useState([])
	const [page, setPage] = useState(1)
	const [isFilter, setIsFilter] = useState(false)
	const isFocus = useIsFocused()
	const [showclearfilter, setShowclearfilter] = useState(false)
	const [Loading, setLoading] = useState(false)
	const [threads, setThreads] = useState([])
	const [author, setAuthor] = useState('')
	const pageSize = 30
	const [filter, setFilter] = useState(null)

	const { data, loading, error, refetch } = useQuery(Threads, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filters: { ...filter, ...{ author: user?._id } },
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('Threads res :', JSON.stringify(data))
			setLoading(false)
			setThreads(prev => [...prev, ...data?.Threads?.results])
			setSearchData(prev => [...prev, ...data?.Threads?.results])
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		},
		onError: err => {
			console.log('Threads err :', err)
			setLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const searchFilterFunction = text => {
		if (text) {
			const newData = threads.filter(item => {
				const itemData = item.title ? item.title.toUpperCase() : ''.toUpperCase()
				const textData = text.toUpperCase()
				return itemData.indexOf(textData) > -1
			})
			console.log('search result :', newData)
			setSearchData(newData)
			setSearch(text)
		} else {
			setSearchData(threads)
			setSearch(text)
		}
	}

	const refresh = async () => {
		setShowclearfilter(false)
		setLoading(true)
		setPage(1)
		setFilter(null)
		setSearchData([])
		setFilter({ author: user?._id })
		setThreads([])
		setSearch('')
		console.log('refresh')
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setThreads(_prev => res?.data?.Threads?.results)
			setSearchData(_prev => res?.data?.Threads?.results)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.Threads?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			// await refetch();
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
				filters: { ...filter, ...author },
			})
		}
	}

	const clearFiter = async () => {
		setLoading(true)
		setPage(1)
		setSearch('')
		setShowclearfilter(false)
		setSearchData([])
		setFilter({ author: user?._id })
		setThreads([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
			filters: filter,
		})
	}

	const onFilter = async filter => {
		setFilter(filter)
		setShowclearfilter(true)
		console.log('filter values :', { ...filter })
		setLoading(true)
		setSearch('')
		setSearchData([])
		setPage(1)
		setThreads([])
		console.log('filter page :', page)
		await refetch({
			filters: { ...filter, ...author },
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			console.log('filter data =======>', JSON.stringify(res?.data?.Threads))
			setThreads(res?.data?.Threads?.results)
			setSearchData(res?.data?.Threads?.results)
			setLoading(false)
		})
	}

	useEffect(() => {
		refresh()
		setAuthor({ author: user?._id })
	}, [isFocus])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'All Issues'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
					<View style={[styles().flex]}>
						<TextField
							keyboardType="default"
							value={search}
							// errorText={searchError}
							autoCapitalize="none"
							placeholder={'Search'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => searchFilterFunction(text)}
						/>
					</View>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => setIsFilter(true)}
						style={[styles().wh35px, styles().ml15, styles().alignCenter, styles().justifyCenter, styles().br20, { backgroundColor: currentTheme.EEE8D5 }]}
					>
						<FontAwesome
							name="filter"
							size={20}
							color={currentTheme.themeBackground}
						/>
					</TouchableOpacity>
				</View>
				{showclearfilter
					? <TouchableOpacity
							onPress={() => clearFiter()}
							style={[styles().flexRow, styles().alignCenter, styles().mb15, { width: 130 }]}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black, marginTop: 1 }]}>Clear Filters</Text>
							<Entypo
								name={'cross'}
								size={20}
							/>
						</TouchableOpacity>
					: null}

				<View style={{ flex: 1 }}>
					<FlatList
						data={searchData}
						onEndReached={() => nextPage()}
						onEndReachedThreshold={0.5}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={{ flexGrow: 1 }}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={Loading}
							/>
						}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
										}}
									>
										{Loading || loading ? 'Loading...' : 'No Issues'}
									</Text>
								</View>
							)
						}}
						renderItem={({ item, index }) => {
							return (
								<TouchableOpacity
									key={index}
									activeOpacity={0.7}
									onPress={() =>
										props.navigation.navigate('ThreadDetails', {
											ticketId: item?._id,
										})
									}
									style={[styles().pv10, styles().mb15, styles().flexRow, styles().ph10, styles().bw1, styles().br10, { borderColor: currentTheme.B7B7B7 }]}
								>
									<View>
										<MaterialCommunityIcons
											name="flag"
											size={20}
											color={currentTheme.E8E8C8}
										/>
									</View>
									<View style={[styles().ml10]}>
										<Text style={[styles().fs14, styles().fw600, styles().textCapitalize, { color: currentTheme.headingColor }]}>
											{item?.title ? item?.title : 'N/A'}
										</Text>
										<View style={[styles().flexRow, styles().mt10, styles().alignCenter, styles().flexWrap]}>
											<View style={[styles().flexRow, styles().mr10, styles().alignCenter, { marginBottom: 3 }]}>
												<FontAwesome5
													name="hashtag"
													size={14}
													color={currentTheme.themeBackground}
												/>
												<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.themeBackground }]}>{item?.thread_ref}</Text>
											</View>
											<View style={[styles().flexRow, styles().mr10, styles().alignCenter, { marginBottom: 3 }]}>
												<FontAwesome5
													name="user-alt"
													size={14}
													color={currentTheme.themeBackground}
												/>
												<Text style={[styles().fs10, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
													{item?.author?.name}
												</Text>
											</View>
											<View style={[styles().flexRow, styles().alignCenter, { marginBottom: 3 }]}>
												<View style={[styles().wh15px, styles().overflowH]}>
													<Image
														source={require('../../assets/images/issue-icon.png')}
														style={styles().wh100}
														resizeMode="contain"
													/>
												</View>
												<Text style={[styles().fs10, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
													{item?.type}
												</Text>
											</View>
										</View>
										<View style={[styles().flexRow, styles().alignCenter, styles().mt5]}>
											<View style={[styles().flexRow, styles().alignCenter]}>
												<Feather
													name="clock"
													size={14}
													color={currentTheme.E8E8C8}
												/>
												<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
													{moment(item?.createdAt).fromNow()}
												</Text>
											</View>
											<View style={[styles().wh5px, styles().ml15, styles().br50, { backgroundColor: currentTheme.E8E8C8 }]} />
											<Text style={[styles().fs11, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
												{item.status}
											</Text>
											<View style={[styles().wh5px, styles().ml15, styles().br50, { backgroundColor: currentTheme.E8E8C8 }]} />
											<View style={[styles().flexRow, styles().alignCenter]}>
												<FontAwesome
													name="comment-o"
													size={12}
													color={currentTheme.E8E8C8}
													style={[styles().ml5]}
												/>
												<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{item?.comments?.length}</Text>
											</View>
										</View>
									</View>
								</TouchableOpacity>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={<View style={styles().wh20px} />}
					/>
				</View>
			</View>
			<ThemeButton
				onPress={() => props.navigation.navigate('ReportIssue')}
				Title={'Report a New Issue'}
				Style={styles().mb20}
			/>
			<ThreadsFilter
				ModalHeading={'Filters'}
				modalVisible={isFilter}
				onClose={() => setIsFilter(false)}
				filters={filters => onFilter(filters)}
			/>
		</Layout>
	)
}

export default AllIssues
