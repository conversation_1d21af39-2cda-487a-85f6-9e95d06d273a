import {
	Text,
	View,
	TouchableOpacity,
	Image,
	SafeAreaView,
	ScrollView,
	KeyboardAvoidingView,
	LayoutAnimation,
	StatusBar,
	Dimensions,
	Platform,
	ImageBackground,
	UIManager,
} from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import AntDesign from '@expo/vector-icons/AntDesign'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getMyBadgeRequests, getCheckRideOffers, getCompanyDetail, getCompletedJobs, getMyBadges, NewsLetters } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import AllJobs from '../MyJobs/AllJobs'
import ImageView from 'react-native-image-viewing'
import { formatPhoneNumber, handleSocial, user_eligible } from '../../utils/Constants'
import Loading from '../../context/Loading/Loading'
import CheckRide from '../../component/Modals/CheckRide'
import BadgeRequestModal from '../../component/Modals/BadgeRequestModal'
import UserContext from '../../context/User/User'
import Eligible from '../../context/EligibleContext/EligibleContext'
import NewsLetterCard from '../../component/NewsLetterCard/NewsLetterCard'
import NotFound from '../../component/NotFound'
import Share from '../../context/Share/Share'

const { width } = Dimensions.get('window')

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const CompanyDetails = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { isShare, shareNow } = useContext(Share)
	const { isLoader } = useContext(Loading)
	const { setStripeEligible, setCheckrEligible } = useContext(Eligible)
	const { item, companyId } = props?.route?.params
	// console.log('company id:', companyId);
	const [tab, setTab] = useState('Info')
	const [visible, setIsVisible] = useState(false)
	const [imageIndex, _setImageIndex] = useState(0)
	const [_enableBadge, setEnableBadge] = useState(false)
	const [alreadyAppliedBadge, setAlreadyAppliedBadge] = useState(undefined)
	const [alreadyHaveBadge, setAlreadyHaveBadge] = useState(false)

	function share(item) {
		isShare(true)
		shareNow({
			title: item.name,
			_id: item._id,
			photo: item.photo,
			description: item.about,
			shareType: 'company',
		})
	}

	const { data, loading, error, refetch } = useQuery(getCompanyDetail, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		variables: {
			companyId: companyId,
		},
		onCompleted: _res => {
			// console.log('getCompanyDetail res :', res?.getCompanyDetail);
		},
		onError: err => {
			console.log('getCompanyDetail err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const { data: completedJobs } = useQuery(getCompletedJobs, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		variables: {
			filters: {
				company: companyId,
			},
			options: {
				page: 1,
				limit: 3,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			console.log('getCompletedJobs res :', JSON.stringify(res))
			if (res?.getCompletedJobs?.results?.length > 0) {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
				setEnableBadge(true)
			}
		},
		onError: err => {
			console.log('getCompletedJobs err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const { data: checkRides } = useQuery(getCheckRideOffers, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		variables: {
			filters: {
				company: companyId,
				status: 'completed',
				user: user?._id,
			},
			options: {
				page: 1,
				limit: 3,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			console.log('getCheckRideOffers res :', JSON.stringify(res))
			if (res?.getCheckRideOffers?.results?.length > 0) {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
				setEnableBadge(true)
			}
		},
		onError: err => {
			console.log('getCheckRideOffers err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const { data: newsLetters } = useQuery(NewsLetters, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		variables: {
			filters: {
				company: companyId,
			},
			options: {
				page: 1,
				limit: 4,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: _res => {
			// console.log('NewsLetters res :', JSON.stringify(res));
		},
		onError: err => {
			console.log('NewsLetters err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const {
		data: badgesData,
		loading: badgesLoader,
		refetch: badgesRefetch,
	} = useQuery(getMyBadges, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		onCompleted: data => {
			console.log('getMyBadges res :', JSON.stringify(data?.getMyBadges))
			const checkBadge = data?.getMyBadges?.find(val => val?.name?.toLowerCase() === item?.name?.toLowerCase())
			if (checkBadge) {
				setAlreadyHaveBadge(true)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
			} else {
				setAlreadyHaveBadge(false)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
			}
		},
		onError: err => {
			console.log('getMyBadges err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const { data: getMyBadgeRequestsData, refetch: getMyBadgeRequestsRefetch } = useQuery(getMyBadgeRequests, {
		fetchPolicy: 'network-only',
		errorPolicy: 'all',
		onCompleted: res => {
			console.log('getMyBadgeRequests res :', res.getMyBadgeRequests)
			const check = res.getMyBadgeRequests?.find(val => val === companyId)
			if (check) {
				setAlreadyAppliedBadge(true)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
			} else {
				setAlreadyAppliedBadge(false)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
			}
		},
		onError: err => {
			console.log('getMyBadgeRequests err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const handleCheckride = () => {
		if (user_eligible(user).status) {
			setCheckRide(true)
		} else {
			// if (user_eligible(user).type === 'stripe') {
			//   setStripeEligible(true);
			// }
			if (user_eligible(user).type === 'checkr') {
				setCheckrEligible(true)
			}
		}
	}

	const _website = data?.getJob?.company?.website
	var _schemes_regex = /^(http|https)/

	const galley = data?.getCompanyDetail?.gallery?.map(url => ({
		uri: url,
	}))

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	const [checkRide, setCheckRide] = useState('false')

	const [badgeRequest, setBadgeRequest] = useState(false)
	// console.log('badge case :', enableBadge, alreadyAppliedBadge);

	if (loading || !data?.getCompanyDetail || data?.getCompanyDetail?.deleted) {
		return (
			<NotFound
				navigation={() => props.navigation.goBack()}
				loading={loading}
				message={'This company no longer exists or could not be found. Please verify the details or explore other options.'}
			/>
		)
	}
	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			<ImageView
				images={galley}
				imageIndex={imageIndex}
				presentationStyle={'fullScreen'}
				visible={visible}
				onRequestClose={() => setIsVisible(false)}
			/>
			<SafeAreaView style={[styles().flex, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
					style={[styles().flex]}
				>
					<ScrollView
						contentContainerStyle={[{ flexGrow: 1 }]}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps={'handled'}
					>
						<ImageBackground
							style={[
								styles().pb30,
								// styles().alignCenter,
								styles().ph20,
								styles().mt10,
								styles().h220px,
								styles().overflowH,
								{
									borderTopLeftRadius: 30,
									borderTopRightRadius: 30,
									backgroundColor: currentTheme.F3F0E4,
								},
							]}
							source={{ uri: data?.getCompanyDetail?.coverImage }}
						>
							<View style={[styles().flexRow, styles().alignCenter, styles().mt10, styles().justifyBetween]}>
								<TouchableOpacity
									onPress={() => props.navigation.goBack()}
									style={[styles().justifyCenter, styles().h50px, styles().w25px]}
								>
									<FontAwesome
										name="angle-left"
										size={30}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
								<TouchableOpacity
									onPress={() => share(item)}
									activeOpacity={0.5}
									style={[styles().pall8, styles().br5, { backgroundColor: currentTheme.lighterGold }]}
								>
									<FontAwesome
										name="share"
										size={16}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
							</View>
						</ImageBackground>

						<View style={[styles().mtminus75, styles().alignCenter]}>
							<View
								style={[
									styles().wh150px,
									styles().br100,
									styles().overflowH,
									styles().alignCenter,
									styles().justifyCenter,
									styles().mr10,
									{
										borderWidth: 8,
										borderColor: currentTheme.white,
										backgroundColor: currentTheme.themeBackground,
									},
								]}
							>
								{data?.getCompanyDetail?.photo
									? <Image
											source={{ uri: data?.getCompanyDetail?.photo }}
											resizeMode="cover"
											style={styles().wh100}
										/>
									: <MaterialCommunityIcons
											name="city-variant"
											size={70}
											color={currentTheme.white}
										/>}
							</View>

							<View style={[styles().mt5, styles().mb10]}>
								<Text style={[styles().fs20, styles().fontBold, styles().textCapitalize, { color: currentTheme.lightGold }]}>
									{data?.getCompanyDetail?.name}
								</Text>
							</View>
							<View style={[styles().flexRow, styles().alignCenter]}>
								<View style={[styles().pv10, styles().ph25, styles().br20, { backgroundColor: currentTheme.lightBlue }]}>
									<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.darkBlue }]}>
										{`Open Jobs - ${data?.getCompanyDetail?.openJobs ? data?.getCompanyDetail?.openJobs : 0}`}
									</Text>
								</View>
								{user?.disposable === 'false' && user?.role !== 'deckhand' && data?.getCompanyDetail?.isCheckRideEnabled === true
									? <TouchableOpacity
											activeOpacity={0.5}
											onPress={() => handleCheckride()}
											style={[styles().pv10, styles().ph25, styles().br20, styles().ml15, { backgroundColor: currentTheme.F3F0E4 }]}
										>
											<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.themeBackground }]}>{'Check Ride'}</Text>
										</TouchableOpacity>
									: null}
							</View>
						</View>

						<View style={[styles().ph20, styles().mt35]}>
							<View style={[styles().mb35]}>
								<Text style={[styles().fs18, styles().fontBold, { color: currentTheme.lighterGold }]}>About Company</Text>
								<Text style={[styles().fs14, styles().lh24, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
									{data?.getCompanyDetail?.about ? data?.getCompanyDetail?.about?.trim() : 'N/A'}
								</Text>
							</View>
							{/* <View style={[styles().mb20]}>
                <Text
                  style={[
                    styles().fs18,
                    styles().mb5,
                    styles().fw700,
                    {color: currentTheme.lighterGold},
                  ]}>
                  About Fleet
                </Text>
                <Text
                  style={[
                    styles().fs14,
                    styles().lh24,
                    styles().fontRegular,
                    {color: currentTheme.E8E8C8},
                  ]}>
                  {data?.getCompanyDetail?.aboutFleet
                    ? data?.getCompanyDetail?.aboutFleet
                    : 'N/A'}
                </Text>
                {data?.getCompanyDetail?.fleetImage ? (
                  <View
                    style={[
                      styles().w100,
                      styles().h130px,
                      styles().br10,
                      styles().overflowH,
                      styles().mt10,
                    ]}>
                    <Image
                      source={{uri: data?.getCompanyDetail?.fleetImage}}
                      resizeMode="cover"
                      style={styles().wh100}
                    />
                  </View>
                ) : (
                  <Text
                    style={[
                      styles().fs14,
                      styles().lh24,
                      styles().fontRegular,
                      {color: currentTheme.E8E8C8},
                    ]}>
                    N/A
                  </Text>
                )}
              </View> */}

							<ScrollView horizontal>
								<TouchableOpacity
									onPress={() => setTab('Info')}
									style={[styles().mr25]}
								>
									<Text
										style={
											tab === 'Info'
												? [styles().fs16, styles().fontMedium, { color: currentTheme.themeBackground }]
												: [styles().fs18, styles().fontMedium, { color: currentTheme.C3C3C3 }]
										}
									>
										Company Info
									</Text>
									{tab === 'Info'
										? <View
												style={[
													styles().w60px,
													styles().mt5,
													styles().br100,
													{
														height: 3,
														backgroundColor: currentTheme.themeBackground,
													},
												]}
											/>
										: null}
								</TouchableOpacity>
								<TouchableOpacity
									onPress={() => setTab('NewsLetter')}
									style={[styles().mr25]}
								>
									<Text
										style={
											tab === 'NewsLetter'
												? [styles().fs16, styles().fontMedium, { color: currentTheme.themeBackground }]
												: [styles().fs18, styles().fontMedium, { color: currentTheme.C3C3C3 }]
										}
									>
										NewsLetter
									</Text>
									{tab === 'NewsLetter'
										? <View
												style={[
													styles().w60px,
													styles().mt5,
													styles().br100,
													{
														height: 3,
														backgroundColor: currentTheme.themeBackground,
													},
												]}
											/>
										: null}
								</TouchableOpacity>
								<TouchableOpacity
									onPress={() => setTab('Photos')}
									style={[styles().mr25]}
								>
									<Text
										style={
											tab === 'Photos'
												? [styles().fs16, styles().fontMedium, { color: currentTheme.themeBackground }]
												: [styles().fs18, styles().fontMedium, { color: currentTheme.C3C3C3 }]
										}
									>
										Photos
									</Text>
									{tab === 'Photos'
										? <View
												style={[
													styles().w60px,
													styles().mt5,
													styles().br100,
													{
														height: 3,
														backgroundColor: currentTheme.themeBackground,
													},
												]}
											/>
										: null}
								</TouchableOpacity>
							</ScrollView>

							{tab === 'Info' && (
								<View style={[styles().pv15, styles().ph20, styles().br10, styles().mt35, { backgroundColor: currentTheme.F3F0E4, marginBottom: 25 }]}>
									{data?.getCompanyDetail?.industry && (
										<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
											<Text style={[styles().fs16, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Industry:</Text>
											<Text
												numberOfLines={1}
												style={[
													styles().fs14,
													styles().fontRegular,
													styles().w60,
													{
														color: currentTheme.headingColor,
														textAlign: 'right',
													},
												]}
											>
												{data?.getCompanyDetail?.industry}
											</Text>
										</View>
									)}
									{data?.getCompanyDetail?.companySize && (
										<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
											<Text style={[styles().fs16, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Company size:</Text>
											<Text
												numberOfLines={1}
												style={[
													styles().fs14,
													styles().fontRegular,
													styles().w60,
													{
														color: currentTheme.headingColor,
														textAlign: 'right',
													},
												]}
											>
												{data?.getCompanyDetail?.companySize}
											</Text>
										</View>
									)}
									{data?.getCompanyDetail?.establishedIn && (
										<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
											<Text style={[styles().fs16, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Founded in:</Text>
											<Text
												numberOfLines={2}
												style={[
													styles().fs14,
													styles().fontRegular,
													styles().w60,
													{
														color: currentTheme.headingColor,
														textAlign: 'right',
													},
												]}
											>
												{data?.getCompanyDetail?.establishedIn}
											</Text>
										</View>
									)}
									<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
										<Text style={[styles().fs16, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Phone:</Text>
										<Text
											numberOfLines={2}
											style={[styles().fs14, styles().fontRegular, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
										>
											{!data?.getCompanyDetail?.hideContact
												? data?.getCompanyDetail?.phone
													? formatPhoneNumber(data?.getCompanyDetail?.phone)
													: 'N/A'
												: '***************'}
										</Text>
									</View>
									<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
										<Text style={[styles().fs16, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Email:</Text>
										<Text
											numberOfLines={2}
											style={[styles().fs14, styles().fontRegular, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
										>
											{!data?.getCompanyDetail?.hideEmail ? data?.getCompanyDetail?.email : '***************'}
										</Text>
									</View>

									<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
										<Text style={[styles().fs16, styles().fw700, { color: currentTheme.headingColor }]}>Social media:</Text>
										<View style={[styles().flexRow, styles().alignCenter]}>
											<TouchableOpacity
												onPress={() => handleSocial(data?.getCompanyDetail?.google)}
												style={[styles().ml15]}
											>
												<AntDesign
													name="googleplus"
													size={24}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
											<TouchableOpacity
												onPress={() => handleSocial(data?.getCompanyDetail?.facebook)}
												style={[styles().ml15]}
											>
												<FontAwesome
													name="facebook"
													size={20}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
											<TouchableOpacity
												onPress={() => handleSocial(data?.getCompanyDetail?.linkedIn)}
												style={[styles().ml15]}
											>
												<FontAwesome
													name="linkedin"
													size={20}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
											<TouchableOpacity
												onPress={() => handleSocial(data?.getCompanyDetail?.twitter)}
												style={[styles().ml15]}
											>
												<FontAwesome
													name="twitter"
													size={20}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
										</View>
									</View>
									{/* <View style={[styles().mb10, styles().mt10]}>
                    <ThemeButton
                      onPress={() => {
                        if (website) {
                          if (
                            schemes_regex.test(website?.toLowerCase()) == true
                          ) {
                            Linking.openURL(`${website}`);
                          } else {
                            Linking.openURL(`http://${website}`);
                          }
                        } else {
                          null;
                        }
                      }}
                      Title={website ? website : 'No Website'}
                      Style={[styles().br5, styles().h40px]}
                      StyleText={[styles().fs14, {color: currentTheme.black}]}
                    />
                  </View> */}
								</View>
							)}

							{tab === 'NewsLetter' && (
								<View style={[styles().pv15, styles().br10, styles().mt10, {}]}>
									{newsLetters?.NewsLetters?.results?.length !== 0
										? newsLetters?.NewsLetters?.results?.slice(0, 2).map((value, index) => {
												return (
													<NewsLetterCard
														item={value}
														index={index}
													/>
												)
											})
										: <Text style={([styles().fontMedium, styles().fs16], { color: currentTheme.E8E8C8 })}>N/A</Text>}
									{newsLetters?.NewsLetters?.results?.length > 2 && (
										<TouchableOpacity
											onPress={() =>
												props.navigation.navigate('NewsLetter', {
													companyId: companyId,
												})
											}
											activeOpacity={0.5}
											style={[styles().alignCenter, styles().justifyCenter, styles().mt10]}
										>
											<Text
												style={[
													styles().fs12,
													styles().fontRegular,
													{
														textDecorationLine: 'underline',
														color: currentTheme.black,
													},
												]}
											>
												View All
											</Text>
										</TouchableOpacity>
									)}
								</View>
							)}

							{tab === 'Photos' && (
								<View
									style={[
										styles().pv15,
										styles().ph20,
										styles().br10,
										styles().mt35,
										styles().flexRow,
										styles().alignCenter,
										// styles().justifyBetween,
										styles().flexWrap,
										{ backgroundColor: currentTheme.F3F0E4, marginBottom: 25 },
									]}
								>
									{data?.getCompanyDetail?.gallery?.length !== 0
										? data?.getCompanyDetail?.gallery?.map((a, index) => {
												return (
													<TouchableOpacity
														activeOpacity={0.8}
														onPress={() => {
															// setIsVisible(true);
														}}
														key={index}
														style={[styles().wh65px, styles().mr15, styles().mb15]}
													>
														<Image
															source={{ uri: a }}
															style={[styles().wh100, styles().br5]}
															resizeMode="cover"
														/>
													</TouchableOpacity>
												)
											})
										: <Text style={([styles().fw600, styles().fs16], { color: currentTheme.E8E8C8 })}>N/A</Text>}
								</View>
							)}

							{/* <View
                style={[
                  styles().br10,
                  styles().pall20,
                  {backgroundColor: currentTheme.F8F9FA},
                ]}>
                <Text
                  style={[
                    styles().fs24,
                    styles().fw600,
                    styles().mb20,
                    {color: currentTheme.headingColor},
                  ]}>
                  Contact Person
                </Text>
                <View
                  style={[
                    styles().flexRow,
                    styles().mb20,
                    styles().alignCenter,
                  ]}>
                  <View
                    style={[
                      styles().wh65px,
                      styles().mr15,
                      styles().overflowH,
                      styles().br30,
                    ]}>
                    <Image
                      source={{
                        uri: data?.getCompanyDetail?.emergencyContactInformation
                          ?.photo,
                      }}
                      style={styles().wh100}
                      resizeMode="cover"
                    />
                  </View>
                  <Text
                    style={[
                      styles().fs24,
                      styles().fw600,
                      styles().textCapitalize,
                      {color: currentTheme.lighterGold},
                    ]}>
                    {data?.getCompanyDetail?.emergencyContactInformation?.name}
                  </Text>
                </View>
                <View
                  style={[
                    styles().flexRow,
                    styles().mb10,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fw600,
                      {color: currentTheme.headingColor},
                    ]}>
                    Designation:
                  </Text>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fontRegular,
                      {color: currentTheme.headingColor},
                    ]}>
                    {
                      data?.getCompanyDetail?.emergencyContactInformation
                        ?.designation
                    }
                  </Text>
                </View>
                <View
                  style={[
                    styles().flexRow,
                    styles().mb10,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fw600,
                      {color: currentTheme.headingColor},
                    ]}>
                    Phone:
                  </Text>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fontRegular,
                      {color: currentTheme.headingColor},
                    ]}>
                    {formatPhoneNumber(
                      data?.getCompanyDetail?.emergencyContactInformation
                        ?.phone,
                    )}
                  </Text>
                </View>
                <View
                  style={[
                    styles().flexRow,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fw600,
                      {color: currentTheme.headingColor},
                    ]}>
                    Email:
                  </Text>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fontRegular,
                      {color: currentTheme.headingColor},
                    ]}>
                    {data?.getCompanyDetail?.emergencyContactInformation?.email}
                  </Text>
                </View>
              </View> */}
							<View style={[styles().mt30]}>
								<Text style={[styles().fs20, styles().fw700, { color: currentTheme.black }]}>Jobs Available</Text>
								<AllJobs
									company={companyId}
									{...props}
								/>
							</View>
						</View>
					</ScrollView>
					{alreadyAppliedBadge === false && alreadyHaveBadge === false
						? <View
								style={[
									{
										height: 55,
										borderTopLeftRadius: 20,
										borderTopRightRadius: 20,
										backgroundColor: currentTheme.lightGold,
									},
								]}
							>
								<TouchableOpacity
									activeOpacity={1}
									onPress={() => setBadgeRequest(true)}
									style={[styles().alignCenter, styles().flex, styles().justifyEnd]}
								>
									<View
										style={[
											styles().wh50px,
											styles().posAbs,
											styles().bw2,
											{ padding: 12 },
											{
												top: -30,
												backgroundColor: currentTheme.lightGold,
												borderColor: currentTheme.white,
											},
											styles().br50,
											styles().overflowH,
										]}
									>
										<Image
											source={require('../../assets/images/badge.png')}
											style={styles().wh100}
											resizeMode="contain"
										/>
									</View>
									<View style={[styles().mv10]}>
										<Text style={[styles().fs16, styles().textCapitalize, styles().fw600, { color: currentTheme.white }]}>Request for a badge</Text>
									</View>
								</TouchableOpacity>
							</View>
						: null}
				</KeyboardAvoidingView>
			</SafeAreaView>
			<CheckRide
				ModalIcon={true}
				companyId={data?.getCompanyDetail?._id}
				checkRide={data?.getCompanyDetail?.checkRideData}
				modalVisible={checkRide}
				onCancel={() => {
					setCheckRide(false)
				}}
			/>
			<BadgeRequestModal
				ModalIcon={true}
				modalVisible={badgeRequest}
				onCancel={() => setBadgeRequest(false)}
				navigation={props.navigation}
				jobs={completedJobs?.getCompletedJobs?.results}
				checkRides={checkRides?.getCheckRideOffers?.results}
				company_id={companyId}
				callback={async boolean => {
					if (boolean) {
						await getMyBadgeRequestsRefetch()
					}
				}}
			/>
		</View>
	)
}

export default React.memo(CompanyDetails)
