import { Dimensions, LayoutAnimation, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native'
import { useContext, useRef, useState } from 'react'
import styles from '../styles'
import Video from 'react-native-video'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import AntDesign from '@expo/vector-icons/AntDesign'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'

const VideoComponent = ({ video }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [mute, setMute] = useState(true)
	const [pause, setPause] = useState(true)
	const [isVideoWidgets, setIsVideoWidgets] = useState(true)
	const videoRef = useRef(null)
	const { height, width } = Dimensions.get('window')

	const MutedButton = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => setMute(!mute)}
				style={videoStyles.mute}
			>
				<FontAwesome5
					name={mute ? 'volume-mute' : 'volume-up'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const ReplayButton = ({ index }) => {
		const handleReplay = () => {
			if (videoRef.current) {
				videoRef.current.seek(0) // Seek to the beginning of the video
				setPause(false)
			}
		}
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={handleReplay}
				style={videoStyles.replay}
			>
				<MaterialCommunityIcons
					name={'reload'}
					color={currentTheme.themeBackground}
					size={20}
				/>
			</TouchableOpacity>
		)
	}

	const PausePlay = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => {
					setPause(!pause)
					setMute(false)
					setTimeout(() => {
						if (pause) {
							LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
							setIsVideoWidgets(false)
						}
					}, 2000)
				}}
				style={videoStyles.pausePlay}
			>
				<AntDesign
					name={pause ? 'play' : 'pausecircle'}
					color={currentTheme.themeBackground}
					size={18}
				/>
			</TouchableOpacity>
		)
	}

	return (
		<View style={[styles().flex, styles().pb20]}>
			<ScrollView
				contentContainerStyle={{ flexGrow: 1 }}
				showsVerticalScrollIndicator={false}
			>
				<TouchableOpacity
					activeOpacity={0.9}
					onPress={() => {
						LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
						setIsVideoWidgets(true)
					}}
					style={[styles().w100, styles().overflowH, styles().flex]}
				>
					<Video
						ref={videoRef}
						useNativeControls
						resizeMode="contain"
						autoplay={false}
						source={{ uri: video }}
						style={{
							width: '100%',
							aspectRatio: 1,
						}}
						paused={pause}
						muted={mute}
						onEnd={() => {
							videoRef.current.seek(0)
							setPause(true)
							setIsVideoWidgets(true)
						}}
					/>
					{isVideoWidgets
						? <>
								<ReplayButton />
								<MutedButton />
								<PausePlay />
							</>
						: null}
				</TouchableOpacity>
			</ScrollView>
		</View>
	)
}

export default VideoComponent

const videoStyles = StyleSheet.create({
	mute: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
	replay: {
		position: 'absolute',
		bottom: 0,
		right: 35,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: 100,
		height: 30,
		width: 30,
		backgroundColor: 'white',
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
	pausePlay: {
		position: 'absolute',
		bottom: 0,
		right: 70,
		margin: 10,
		zIndex: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
})
