import { useState, useRef, useEffect, useContext } from 'react'
import { View, TouchableOpacity, Animated, LayoutAnimation, Platform } from 'react-native'
import { Camera } from 'expo-camera'
import Layout from '../../component/Layout/Layout'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import UserContext from '../../context/User/User'
import * as FileSystem from 'expo-file-system'
import { uploadImageKitProgress } from '../../component/Cloudupload/CloudUpload'
import { useMutation } from '@apollo/client'
import { updateUser } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import VideoConfirmModal from '../../component/Modals/VideoConfirmModal'
import VideoComponent from './VideoComponent'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Loading from '../../context/Loading/Loading'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import Spinner from '../../component/Spinner/Spinner'
import { AnimatedCircularProgress } from 'react-native-circular-progress'
import { getFileExtension, getFileSize } from '../../utils/Constants'
import ProgressLoader from '../../ProgressLoader/ProgressLoader'

const ToeBoater = props => {
	const maxDuration = 60 //seconds
	const { isLoader } = useContext(Loading)
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const cameraRef = useRef(null)
	const [isRecording, setRecording] = useState(false)
	const [video, setVideo] = useState('')
	const [confirmModal, setConfirmModal] = useState(false)
	const [toggleVideo, setToggleVideo] = useState(false)
	const [cameraType, setCameraType] = useState('back')
	const [removeLoader, setRemoveLoader] = useState(false)
	const [countdownValue, setCountdownValue] = useState(0)
	const [uploadPercentage, setUploadPercentage] = useState(0)
	const intervalRef = useRef(null)
	const [isUploadButton, setIsUploadButton] = useState(false)
	const duration = (maxDuration * 1000) / 100
	const [showProgress, setShowProgress] = useState(false)

	const startCountdown = () => {
		// Reset countdown value
		setCountdownValue(0)
		// Start the countdown interval
		intervalRef.current = setInterval(() => {
			setCountdownValue(prevValue => {
				// Increment the countdown value
				const newValue = prevValue + 1

				// Check if the countdown is complete (reached 100)
				if (newValue === 100) {
					clearInterval(intervalRef.current)
				}

				return newValue
			})
		}, duration) // Interval duration (adjust as needed)
	}

	const stopCountdown = () => {
		// Stop the countdown interval
		clearInterval(intervalRef.current)
		setCountdownValue(0)
	}

	const startRecording = async () => {
		if (cameraRef.current) {
			try {
				setRecording(true)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				const { uri } = await cameraRef.current.recordAsync({
					maxDuration: maxDuration, //  in seconds
				})
				console.log('Video recorded at:', uri)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				setVideo(uri)
				setToggleVideo(true)
				clearInterval(intervalRef.current)
				setRecording(false)
				setConfirmModal(true)
				setIsUploadButton(true)
			} catch (error) {
				console.error('Error recording video:', error)
			}
		}
	}

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted: async ({ updateUser }) => {
			setRemoveLoader(false)
			// isLoader(false);
			setShowProgress(false)
			console.log('updateUser res :', updateUser)
			FlashMessage({
				msg: 'Profile Updated!',
				type: 'success',
			})
		},
		onError: error => {
			// isLoader(false);
			setShowProgress(false)
			setRemoveLoader(false)
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('updateUser error  :', error)
		},
	})

	const handleUpload = async () => {
		try {
			// isLoader(true);
			const fileExtension = getFileExtension(video)
			const { fileSizeInBytes } = await getFileSize(video)
			const file = await FileSystem.readAsStringAsync(video, {
				encoding: FileSystem.EncodingType.Base64,
			})
			const documentBase64 = video ? `data:video/mp4;base64,${file}` : null
			setShowProgress(true)
			const response = await uploadImageKitProgress(documentBase64, fileExtension, fileSizeInBytes, percentage => {
				console.log('Upload progress:', percentage)
				setUploadPercentage(percentage)
				// Update your UI with the upload progress if needed
			})
			if (response?.url) {
				setShowProgress(false)
				setVideo(response.url)
				await mutate({
					variables: {
						updateUserInput: {
							deckhand_video: response?.url,
						},
					},
				})
			}
		} catch (err) {
			setShowProgress(false)
			console.log('catch handelupload :', err)
			// isLoader(false);
			setCountdownValue(0)
		}
	}

	const handleRemove = async () => {
		try {
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setRemoveLoader(true)
			await mutate({
				variables: {
					updateUserInput: {
						deckhand_video: '',
					},
				},
			}).then(() => {
				setToggleVideo(false)
			})
		} catch (err) {
			console.log('catch handleRemove :', err)
		}
	}

	const stopRecording = () => {
		if (cameraRef.current) {
			cameraRef.current.stopRecording()
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setRecording(false)
		}
	}

	const handleLongPress = () => {
		setRecording(true)
		startRecording()
		// handlePressIn();
		startCountdown()
	}

	const handleRelease = () => {
		stopRecording()
		// handlePressOut();
		stopCountdown()
	}

	const [animatedValue] = useState(new Animated.Value(0))

	const _handlePressIn = () => {
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		setRecording(true)
		Animated.timing(animatedValue, {
			toValue: 80,
			duration: 200,
			useNativeDriver: false,
		}).start()
	}

	const _handlePressOut = () => {
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		setRecording(false)
		Animated.timing(animatedValue, {
			toValue: 0,
			duration: 200,
			useNativeDriver: false,
		}).start()
	}

	useEffect(() => {
		if (user?.deckhand_video) {
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setToggleVideo(true)
			setVideo(user?.deckhand_video)
		} else {
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setToggleVideo(false)
		}
	}, [])

	console.log('deckhand user video :', user?.deckhand_video)

	if (showProgress) {
		return (
			<View
				style={{
					position: 'absolute',
					height: '100%',
					width: '100%',
					zIndex: 1000,
					backgroundColor: 'rgba(0,0,0,0.6)',
					alignItems: 'center',
					justifyContent: 'center',
				}}
			>
				<ProgressLoader progress={uploadPercentage} />
			</View>
		)
	}
	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Upload Your Video'}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			{toggleVideo
				? <View style={[styles().flex]}>
						<VideoComponent video={video} />
						<View style={[styles().flex, styles().ph20, styles().justifyEnd, styles().mb30]}>
							{isUploadButton && !user?.deckhand_video
								? <ThemeButton
										Title={'Upload'}
										onPress={() => {
											LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
											handleUpload()
										}}
									/>
								: null}
							<ThemeButton
								Title={'Change Video'}
								onPress={() => {
									LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
									setToggleVideo(!toggleVideo)
									setVideo('')
									setIsUploadButton(false)
								}}
								Style={{
									backgroundColor: currentTheme.B7B7B7,
									borderColor: currentTheme.B7B7B7,
								}}
							/>

							{user?.deckhand_video
								? removeLoader
									? <View style={[styles().mt15]}>
											<Spinner />
										</View>
									: <ThemeButton
											Title={'Remove & Save'}
											Style={[
												{
													backgroundColor: currentTheme.red,
													borderColor: currentTheme.red,
												},
											]}
											onPress={() => handleRemove()}
										/>
								: null}
						</View>
					</View>
				: <View style={[styles().flex]}>
						<Camera
							defaultVideoQuality={Platform.OS === 'android' ? '288p' : undefined}
							type={cameraType}
							ref={cameraRef}
							style={{ flex: 1 }}
						/>
						<TouchableOpacity
							activeOpacity={0.7}
							onLongPress={handleLongPress}
							onPressOut={handleRelease}
							style={[
								styles().posAbs,
								styles().alignSelfCenter,
								styles().wh65px,
								styles().br100,
								styles().alignCenter,
								styles().justifyCenter,
								{
									bottom: 30,
									backgroundColor: isRecording ? currentTheme.countDownRed : currentTheme.lightGreen,
								},
							]}
						>
							{/* <Animated.View
              style={[
                styles().boxpeshadow,
                styles().wh65px,
                styles().br100,
                {
                  backgroundColor: isRecording
                    ? 'red'
                    : currentTheme.lightGreen,
                  borderWidth: 4,
                  borderColor: 'white',
                  transform: [
                    {
                      scale: animatedValue.interpolate({
                        inputRange: [0, 100],
                        outputRange: [1, 1.6],
                      }),
                    },
                  ],
                },
              ]}
            /> */}
							<AnimatedCircularProgress
								size={90}
								width={5}
								fill={countdownValue}
								tintColor={isRecording ? currentTheme.countDownRed : currentTheme.white}
								backgroundColor="white"
								duration={duration}
								lineCap="round"
								rotation={0}
							/>
						</TouchableOpacity>
						{/* <Text>{countdownValue}</Text> */}
						<TouchableOpacity
							activeOpacity={0.8}
							onPress={() => {
								setCameraType(prev => (prev === 'back' ? 'front' : 'front' ? 'back' : null))
							}}
							style={[
								styles().boxpeshadow,
								styles().wh45px,
								styles().alignCenter,
								styles().justifyCenter,
								styles().br100,
								styles().mall10,
								{
									backgroundColor: currentTheme.white,
									position: 'absolute',
									bottom: 30,
									right: 0,
									alignSelf: 'center',
								},
							]}
						>
							<MaterialIcons
								name={'flip-camera-ios'}
								color={currentTheme.c737373}
								size={23}
							/>
						</TouchableOpacity>
					</View>}
			<VideoConfirmModal
				onClose={() => setConfirmModal(false)}
				visible={confirmModal}
				callBack={boolean => {
					console.log('is upload :', boolean)
					if (boolean) {
						handleUpload()
					}
				}}
			/>
		</Layout>
	)
}

export default ToeBoater
