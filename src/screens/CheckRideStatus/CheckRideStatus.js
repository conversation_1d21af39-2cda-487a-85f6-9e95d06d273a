import { Text, View, SafeAreaView, Image, TouchableOpacity, ScrollView, StatusBar } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { acceptCheckRideOffer, rejectCheckRideOffer } from '../../apollo/server'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import moment from 'moment'
import Loading from '../../context/Loading/Loading'
import { removetags } from '../../utils/Constants'
import useCheckRideWebSocket from '../../hooks/useCheckRideWebSocket'

const CheckRideStatus = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { isLoader } = useContext(Loading)
	const { checkrideID, offer, company } = props?.route?.params
	const { checkRideData } = company
	const [offerStatus, setOfferStatus] = useState(offer?.status || 'pending')
	console.log('offer id:', offer?._id)

	// WebSocket for real-time updates (optional - gracefully degrades if server unavailable)
	useCheckRideWebSocket(null, checkrideID, (data) => {
		if (data && data.offerId === offer?._id) {
			setOfferStatus(data.status || data.newStatus)
		}
	})

	const [accept, {}] = useMutation(acceptCheckRideOffer, {
		onCompleted: ({ acceptCheckRideOffer }) => {
			console.log('acceptCheckRideOffer res :', acceptCheckRideOffer)
			setOfferStatus('accepted')
			isLoader(false)
			FlashMessage({ msg: 'Check Ride Accepted!', type: 'success' })
			// Navigate back to check rides list to see updated status
			props.navigation.navigate('Menu', { 
				screen: 'MyCheckRides',
				params: { activeTab: 1 } // Navigate to "In Progress" tab
			})
		},
		onError: err => {
			console.log('acceptCheckRideOffer err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	const [reject, {}] = useMutation(rejectCheckRideOffer, {
		onCompleted: ({ rejectCheckRideOffer }) => {
			console.log('rejectCheckRideOffer res :', rejectCheckRideOffer)
			setOfferStatus('rejected')
			isLoader(false)
			FlashMessage({ msg: 'Check Ride Rejected!', type: 'success' })
			// Navigate back to check rides list
			props.navigation.navigate('Menu', {
				screen: 'MyCheckRides',
				params: { activeTab: 3 } // Navigate to "Others" tab
			})
		},
		onError: err => {
			console.log('rejectCheckRideOffer res :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	async function AcceptCheckRide() {
		isLoader(true)
		const data = { checkRideId: checkrideID, offerId: offer?._id }
		console.log('data :', data)
		await accept({ variables: data })
	}

	async function RejectCheckRide() {
		isLoader(true)
		await reject({
			variables: {
				checkRideId: checkrideID,
				offerId: offer?._id,
			},
		})
	}

	useEffect(() => {
		isLoader(false)
	}, [])

	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			<SafeAreaView style={[styles().flex, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
				<View
					style={[
						// styles().pb30,
						styles().alignCenter,
						styles().ph20,
						// styles().mt10,
						styles().h170px,
						// styles().overflowH,
						{
							borderTopLeftRadius: 20,
							borderTopRightRadius: 20,
							backgroundColor: currentTheme.F3F0E4,
						},
					]}
					//   source={{uri: data?.getCompanyDetail?.coverImage}}
				>
					<TouchableOpacity
						onPress={() => props.navigation.goBack()}
						style={[
							styles().alignSelfStart,
							styles().justifyCenter,
							styles().h40px,
							styles().w25px,
							//   {borderWidth:2}
						]}
					>
						<FontAwesome
							name="angle-left"
							size={30}
							color={currentTheme.black}
						/>
					</TouchableOpacity>
					<View style={[styles().alignCenter]}>
						<View
							style={[
								styles().wh65px,
								styles().overflowH,
								styles().alignCenter,
								styles().justifyCenter,
								styles().br100,
								{ borderColor: currentTheme.themeBackground, borderWidth: 0.5 },
							]}
						>
							{company?.photo
								? <Image
										source={{ uri: company?.photo }}
										style={styles().wh100}
										resizeMode="cover"
									/>
								: <MaterialCommunityIcons
										name="city-variant"
										size={35}
										color={currentTheme.themeBackground}
									/>}
						</View>
						<Text style={[styles().fs16, styles().mt5, styles().textCapitalize, styles().fw600, { color: currentTheme.headingColor }]}>{company?.name}</Text>
					</View>
				</View>
				<ScrollView
					contentContainerStyle={[{ flexGrow: 1 }]}
					showsVerticalScrollIndicator={false}
					keyboardShouldPersistTaps={'handled'}
				>
					<View style={[styles().ph20, styles().mv25]}>
						{offerStatus === 'pending'
							? <Text style={[styles().fs14, styles().textCenter, styles().fw400, { color: currentTheme.B7B7B7 }]}>
									{`The offer expires ${moment(offer?.expireAt).endOf('day').fromNow()}`}
								</Text>
							: null}
						
						{offerStatus !== 'pending' && (
							<View style={[styles().alignCenter, styles().mb20]}>
								<Text style={[styles().fs16, styles().fw700, { 
									color: offerStatus === 'accepted' ? 'green' : 
									       offerStatus === 'rejected' ? 'red' : 
									       currentTheme.headingColor 
								}]}>
									Status: {offerStatus.toUpperCase()}
								</Text>
							</View>
						)}
						<View style={[styles().mt20]}>
							<Text style={[styles().fs16, styles().mb10, styles().mb10, styles().fw700, { color: currentTheme.headingColor }]}>
								{`Date: ${moment(offer?.startDate).format('LL')}`}
							</Text>
							<Text style={[styles().fs16, styles().mb10, styles().fw700, { color: currentTheme.headingColor }]}>{`Price: $${offer?.price}`}</Text>
							<Text style={[styles().fs16, styles().mb10, styles().fw700, { color: currentTheme.headingColor }]}>
								Hitch {`Days: ${checkRideData?.hitchMin}-${checkRideData?.hitchMax}`}
							</Text>
						</View>
						<View>
							<Text style={[styles().fs16, styles().mt15, styles().mb10, styles().fw700, { color: currentTheme.themeBackground }]}>Terms and Conditions</Text>
							<View>
								<Text style={[styles().fs14, styles().lh22, styles().mb10, styles().fw400, { color: currentTheme.E8E8C8 }]}>
									{removetags(checkRideData?.terms)}
								</Text>
							</View>
						</View>
					</View>
				</ScrollView>
				{offerStatus === 'pending'
					? <View style={[styles().flexRow, styles().mb20, styles().mt10, styles().ph20, styles().alignCenter, styles().justifyBetween]}>
							<ThemeButton
								onPress={() => AcceptCheckRide()}
								Title={'Accept'}
								Style={[styles().w45, styles().br5]}
								StyleText={[{ color: currentTheme.headingColor }]}
							/>
							<ThemeButton
								onPress={() => RejectCheckRide()}
								Title={'Reject'}
								Style={[
									styles().w45,
									styles().br5,
									{
										backgroundColor: currentTheme.C3C3C3,
										borderColor: currentTheme.C3C3C3,
									},
								]}
								StyleText={[{ color: currentTheme.headingColor }]}
							/>
						</View>
					: <View style={[styles().ph20, styles().mb20, styles().mt10]}>
							<ThemeButton
								onPress={() => props.navigation.goBack()}
								Title={'Back to Check Rides'}
								Style={[styles().br5]}
								StyleText={[{ color: currentTheme.headingColor }]}
							/>
						</View>}
			</SafeAreaView>
		</View>
	)
}

export default CheckRideStatus
