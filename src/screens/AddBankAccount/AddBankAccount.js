import { Text, View, TouchableOpacity, LayoutAnimation, Platform, UIManager } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation } from '@apollo/client'
import { updateUser } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import UserContext from '../../context/User/User'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const AddBankAccount = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()

	const [AccTitle, setAccTitle] = useState('')
	const [_AccTitleError, setAccTitleError] = useState(false)

	const [Iban, setIban] = useState('')
	const [IbanError, setIbanError] = useState(false)

	const [_Ifsc, setIfsc] = useState('')
	const [_IfscError, _setIfscError] = useState(false)

	const [bankName, setBankName] = useState('')
	const [bankNameError, setBankNameError] = useState(false)

	const [routingNo, setRoutingNo] = useState('')
	const [routingNoError, setRoutingNoError] = useState(false)

	const [terms, setTerms] = useState(false)

	const [Loading, setLoading] = useState(false)

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
			setLoading(false)
			FlashMessage({
				msg: 'Bank details have been updated successfully.',
				type: 'success',
			})
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			props.navigation.goBack()
		} catch (e) {
			setLoading(false)
			console.log('catch updateUser:', e)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('updateUser error  :', error)
	}

	const AddBank = async () => {
		try {
			let status = true
			if (AccTitle === '') {
				FlashMessage({ msg: 'Enter Your Account Title', type: 'warning' })
				setAccTitleError(true)
				status = false
				return
			}
			if (Iban === '') {
				FlashMessage({ msg: 'Enter Your Account Number', type: 'warning' })
				setIbanError(true)
				status = false
				return
			}
			// if (Iban?.length < 22) {
			//   FlashMessage({msg: 'Enter Valid IBAN Number', type: 'warning'});
			//   setIbanError(true);
			//   status = false;
			//   return;
			// }
			// if (Ifsc === '') {
			//   FlashMessage({msg: 'Enter Your IFSC Code', type: 'warning'});
			//   setIfscError(true);
			//   status = false;
			//   return;
			// }
			if (routingNo === '' || routingNo?.length < 9) {
				FlashMessage({ msg: 'Invalid Routing No. length', type: 'warning' })
				setRoutingNoError(true)
				status = false
				return
			}
			if (bankName === '') {
				FlashMessage({ msg: 'Enter Your Bank Name', type: 'warning' })
				setBankNameError(true)
				status = false
				return
			}
			if (!terms) {
				FlashMessage({
					msg: 'Accept Our Terms and Conditions!',
					type: 'warning',
				})
				status = false
				return
			}
			if (status) {
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				setLoading(true)
				const data = {
					bankName: bankName.trim(),
					bankAccountNo: Iban,
					bankAccountTitle: AccTitle.trim(),
					routingNo: routingNo,
				}
				console.log(data)
				await mutate({
					variables: {
						updateUserInput: data,
					},
				})
			}
		} catch (error) {
			console.log(error)
		}
	}

	useEffect(() => {
		setAccTitle(user?.bankAccountTitle)
		setBankName(user?.bankName)
		setIfsc(user?.ifscCode)
		setIban(user?.bankAccountNo)
		setRoutingNo(user?.routingNo)
	}, [isFocus])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			keyBoardArea={-60}
			pagetitle={'Bank Details'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().mb20]}>
					<Text style={[styles().fs16, styles().mb10, styles().fw400, { color: currentTheme.black }]}>Account Number</Text>
					<TextField
						keyboardType="numeric"
						value={Iban}
						errorText={IbanError}
						maxLength={22}
						autoCapitalize="none"
						placeholder={'Account Number'}
						onChangeText={text => {
							setIbanError(false)
							setIban(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>

				<View style={[styles().mb20]}>
					<Text style={[styles().fs16, styles().mb10, styles().fw400, { color: currentTheme.black }]}>Routing No</Text>
					<TextField
						keyboardType="numeric"
						value={routingNo}
						errorText={routingNoError}
						maxLength={9}
						autoCapitalize="none"
						placeholder={'Enter Routing No.'}
						onChangeText={text => {
							setRoutingNoError(false)
							setRoutingNo(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>

				<View style={[styles().mb20]}>
					<Text style={[styles().fs16, styles().mb10, styles().fw400, { color: currentTheme.black }]}>Bank Name</Text>
					<TextField
						keyboardType="default"
						value={bankName}
						errorText={bankNameError}
						autoCapitalize="none"
						placeholder={'Enter Bank Name'}
						onChangeText={text => {
							setBankNameError(false)
							setBankName(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>

				{/* <View style={[styles().mb20]}>
          <Text
            style={[
              styles().fs16,
              styles().mb10,
              styles().fw400,
              {color: currentTheme.black},
            ]}>
            Beneficiary Name
          </Text>
          <TextField
            keyboardType="default"
            value={AccTitle}
            errorText={AccTitleError}
            autoCapitalize="none"
            placeholder={'Enter beneficiary name'}
            onChangeText={text => {
              setAccTitleError(false);
              setAccTitle(text);
            }}
            style={[
              styles().bw1,
              styles().br10,
              styles().overflowH,
              {borderColor: currentTheme.B7B7B7},
            ]}
          />
        </View> */}
				<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>Your Designated Account for Funds Transfer</Text>
				<TouchableOpacity
					onPress={() => setTerms(!terms)}
					style={[styles().mt20, styles().flexRow, styles().alignCenter]}
				>
					<View
						style={[
							styles().mr10,
							styles().wh20px,
							styles().alignCenter,
							styles().justifyCenter,
							styles().br5,
							{ borderWidth: 1, borderColor: currentTheme.C3C3C3 },
						]}
					>
						{terms
							? <FontAwesome
									name="check"
									size={14}
									color={currentTheme.themeBackground}
								/>
							: null}
					</View>
					<Text style={[styles().fs14, styles().fw400, { color: currentTheme.black }]}>I agree with the terms & conditions</Text>
				</TouchableOpacity>
			</View>
			<View style={styles().mb20}>
				{Loading
					? <View style={styles().mb20}>
							<Spinner />
						</View>
					: <ThemeButton
							onPress={() => AddBank()}
							Title={'Save'}
						/>}
			</View>
		</Layout>
	)
}

export default AddBankAccount
