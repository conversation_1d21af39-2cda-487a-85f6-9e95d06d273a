import { Text, View, TouchableOpacity, LayoutAnimation, FlatList } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import { useQuery, useMutation } from '@apollo/client'
import Entypo from '@expo/vector-icons/Entypo'
import { updateUser, waterWays } from '../../apollo/server'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import { CommonActions, useIsFocused } from '@react-navigation/native'
import Spinner from '../../component/Spinner/Spinner'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { getDataAsync, removeItemAsync } from '../../utils/Constants'
import TextField from '../../component/FloatTextField/FloatTextField'
import CheckBox from '../../component/CheckBox/CheckBox'

const WizardStep5 = props => {
	const {
		role,
		companyWorkFor,
		yearsOfExperience,
		canYouHeadline,
		workedInFleetBefore,
		twicBack,
		twicFront,
		drugTest,
		driverLicense,
		captainLicense,
		medicalCertificate,
		fccLicenseFront,
		physical,
		radarCertificateFront,
		radioLicense,
	} = props?.route?.params
	const userinfo = props?.route?.params
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [selectedRoutes, setSelectedRoutes] = useState([])
	const [show, setShow] = useState(false)
	const [Index, setIndex] = useState('')
	const [buttonLoader, setButtonLoader] = useState(false)
	const [toggleOther, setToggleOther] = useState(false)
	const [others, setOthers] = useState('')
	const [othersErr, setOthersErr] = useState(false)

	const [mutate, { client }] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})
	console.log(userinfo)
	async function onCompleted(data) {
		try {
			setButtonLoader(false)
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: `Profile Completed, Welcome ${data?.updateUser?.name?.toUpperCase()}`,
				type: 'success',
			})
			// AsyncStorage.setItem('token', data?.mobileLogin?.token?.toString());
			await removeItemAsync('wizard1')
			await removeItemAsync('wizard2')
			await removeItemAsync('wizard3')
			await removeItemAsync('wizard4')
			await removeItemAsync('wizard5')
			await AsyncStorage.setItem('isProfileCompleted', data?.updateUser?.isProfileCompleted?.toString())
			await AsyncStorage.setItem('userId', data?.updateUser?._id?.toString())
			await AsyncStorage.setItem('user', JSON.stringify(data?.updateUser))
			props.navigation.dispatch(
				CommonActions.reset({
					index: 0,
					routes: [{ name: 'noDrawer' }],
				})
			)
		} catch (e) {
			console.log(e)
			setButtonLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
			setButtonLoader(false)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setButtonLoader(false)
		console.log('updateUser error  :', error)
	}

	const { data, loading, error, refetch } = useQuery(waterWays, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				limit: 1000,
			},
		},
		onCompleted: data => {
			console.log('waterways res :', data)
		},
		onError: err => {
			console.log('waterways Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function waterways() {
		const status = true
		// if (selectedRoutes?.length === 0) {
		//   FlashMessage({msg: 'Select Routes', type: 'danger'});
		//   status = false;
		//   return;
		// }

		if (status) {
			const data = {
				...userinfo,
				waterWays: selectedRoutes,
				otherWaterWays: others,
				about: '',
				postSettings: 'friends',
				skills: [],
				educations: [],
				experiances: [],
				isProfileCompleted: true,
			}
			console.log(data)
			await AsyncStorage.setItem('wizard5', JSON.stringify(data))
			setButtonLoader(true)
			await mutate({
				variables: { updateUserInput: data },
			})
			// props.navigation.navigate('WizardStep6', data);
		}
	}
	const multiSelect = item => {
		const x = selectedRoutes.indexOf(item._id)
		if (x === -1) {
			setSelectedRoutes(prev => [...prev, item._id])
		} else {
			selectedRoutes.splice(x, 1)
			setSelectedRoutes(prev => [...prev])
		}
	}

	const isFocus = useIsFocused()
	useEffect(() => {
		getDataAsync('wizard5').then(res => {
			setSelectedRoutes(res.waterWays)
		})
	}, [isFocus])

	useEffect(() => {
		setButtonLoader(false)
	}, [])

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={false}
			withBg={true}
			pagetitle={'Waterways'}
			headerShown={true}
			LeftIcon={true}
		>
			<View style={[styles().flex, styles().ph20]}>
				<ProgressBar
					active={4}
					done={[1, 2, 3]}
				/>
				<View style={styles().flex}>
					<Text style={[styles().fs20, styles().mt35, styles().fontMedium, { color: currentTheme.headingColor }]}>Routes on Waterways</Text>
					<FlatList
						data={data?.WaterWays?.results}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={{ flexGrow: 1 }}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontSize: 14,
										}}
									>
										{loading ? 'Loading...' : 'No Routes'}
									</Text>
								</View>
							)
						}}
						renderItem={({ item, index }) => {
							return (
								<>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => {
											setShow(!show)
											setIndex(index)
											LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
										}}
										style={[
											styles().flexRow,
											styles().mt25,
											styles().pb10,
											styles().mb10,
											styles().alignCenter,
											styles().justifyBetween,
											{
												borderBottomColor: currentTheme.C3C3C3,
												borderBottomWidth: 0.5,
											},
										]}
									>
										<Text
											numberOfLines={1}
											style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor }]}
										>
											{item.name}
										</Text>
										<Entypo
											name={show && Index === index ? 'chevron-with-circle-up' : 'chevron-with-circle-down'}
											size={20}
											color={currentTheme.black}
										/>
									</TouchableOpacity>
									{show && Index === index
										? <View style={[styles().ph20, styles().boxpeshadow, styles().br10, { marginHorizontal: 2 }]}>
												<FlatList
													data={item.subWaterWays}
													showsVerticalScrollIndicator={false}
													contentContainerStyle={{ flexGrow: 1 }}
													renderItem={({ item, index }) => {
														return (
															<TouchableOpacity
																activeOpacity={0.5}
																style={[
																	styles().flexRow,
																	styles().pb10,
																	styles().pt20,
																	styles().alignCenter,
																	styles().justifyBetween,
																	{
																		borderTopWidth: index === 0 ? 0 : 1,
																		borderTopColor: currentTheme.C3C3C3,
																		backgroundColor: currentTheme.white,
																	},
																]}
																onPress={() => multiSelect(item)}
															>
																<View style={[styles().flexRow, styles().alignCenter]}>
																	<View
																		style={[
																			styles().wh20px,
																			styles().alignCenter,
																			styles().justifyCenter,
																			styles().mr10,
																			styles().bw1,
																			styles().br5,
																			{
																				backgroundColor: selectedRoutes.includes(item._id) ? currentTheme.themeBackground : currentTheme.white,
																				borderColor: currentTheme.themeBackground,
																			},
																		]}
																	>
																		{selectedRoutes.includes(item._id) && (
																			<FontAwesome
																				name="check"
																				size={16}
																				color={currentTheme.white}
																			/>
																		)}
																	</View>
																	<Text
																		numberOfLines={2}
																		style={[styles().fs12, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
																	>
																		{item.name}
																	</Text>
																</View>
															</TouchableOpacity>
														)
													}}
													keyExtractor={(_item, index) => index.toString()}
													ListFooterComponent={<View style={styles().wh20px} />}
												/>
											</View>
										: null}
								</>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={
							<View style={[styles().mv20]}>
								<View style={[styles().flex, styles().flexRow, styles().alignCenter, styles().mb10]}>
									<CheckBox
										value={toggleOther}
										setValue={boolean => setToggleOther(boolean)}
									/>
									<Text style={[styles().fs14, styles().ml10, styles().fontMedium, { color: currentTheme.black }]}>Other?</Text>
								</View>
								{toggleOther
									? <TextField
											keyboardType="default"
											value={others}
											errorText={othersErr}
											autoCapitalize="none"
											placeholder={'Enter other...'}
											style
											onChangeText={text => {
												setOthersErr(false)
												setOthers(text)
											}}
										/>
									: null}
							</View>
						}
					/>
				</View>
				<View style={[styles().justifyEnd, styles().mb20]}>
					{buttonLoader
						? <Spinner />
						: <ThemeButton
								Title={`Let's Start`}
								onPress={() => waterways()}
							/>}
				</View>
			</View>
		</AuthLayout>
	)
}

export default WizardStep5
