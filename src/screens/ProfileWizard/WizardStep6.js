import { Text, View, TouchableOpacity, LayoutAnimation } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import { useMutation } from '@apollo/client'
import { updateUser } from '../../apollo/server'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import { CommonActions } from '@react-navigation/native'
import Spinner from '../../component/Spinner/Spinner'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { removeItemAsync } from '../../utils/Constants'
import TextField from '../../component/FloatTextField/FloatTextField'

const WizardStep6 = props => {
	const userinfo = props?.route?.params
	console.log(userinfo)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [buttonLoader, setButtonLoader] = useState(false)

	const [_AccTitle, _setAccTitle] = useState('')
	const [_AccTitleError, _setAccTitleError] = useState(false)

	const [Iban, setIban] = useState('')
	const [IbanError, setIbanError] = useState(false)

	const [_Ifsc, _setIfsc] = useState('')
	const [_IfscError, _setIfscError] = useState(false)

	const [bankName, setBankName] = useState('')
	const [bankNameError, setBankNameError] = useState(false)

	const [routingNo, setRoutingNo] = useState('')
	const [routingNoError, setRoutingNoError] = useState(false)

	const [terms, setTerms] = useState(false)

	const [mutate, { client }] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			setButtonLoader(false)
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: `Profile Completed, Welcome ${data?.updateUser?.name?.toUpperCase()}`,
				type: 'success',
			})
			// AsyncStorage.setItem('token', data?.mobileLogin?.token?.toString());
			await removeItemAsync('wizard1')
			await removeItemAsync('wizard2')
			await removeItemAsync('wizard3')
			await removeItemAsync('wizard4')
			await removeItemAsync('wizard5')
			await AsyncStorage.setItem('isProfileCompleted', data?.updateUser?.isProfileCompleted?.toString())
			await AsyncStorage.setItem('userId', data?.updateUser?._id?.toString())
			await AsyncStorage.setItem('user', JSON.stringify(data?.updateUser))
			props.navigation.dispatch(
				CommonActions.reset({
					index: 0,
					routes: [{ name: 'noDrawer' }],
				})
			)
		} catch (e) {
			console.log(e)
			setButtonLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
			setButtonLoader(false)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setButtonLoader(false)
		console.log('updateUser error  :', error)
	}

	async function ProfileComplete() {
		let status = true
		// if (AccTitle === '') {
		//   FlashMessage({msg: 'Enter Your Account Title', type: 'warning'});
		//   setAccTitleError(true);
		//   status = false;
		//   return;
		// }
		if (Iban === '') {
			FlashMessage({ msg: 'Enter Your Account Number', type: 'warning' })
			setIbanError(true)
			status = false
			return
		}
		if (Iban?.length < 8) {
			FlashMessage({
				msg: 'Account Number must contain 8-17 digits! ',
				type: 'warning',
			})
			setIbanError(true)
			status = false
			return
		}
		// if (Ifsc === '') {
		//   FlashMessage({msg: 'Enter Your IFSC Code', type: 'warning'});
		//   setIfscError(true);
		//   status = false;
		//   return;
		// }
		if (routingNo === '' || routingNo.length < 9) {
			FlashMessage({ msg: ' Invalid Routing No. length', type: 'warning' })
			setRoutingNoError(true)
			status = false
			return
		}
		if (bankName === '') {
			FlashMessage({ msg: 'Enter Your Bank Name', type: 'warning' })
			setBankNameError(true)
			status = false
			return
		}
		if (!terms) {
			FlashMessage({
				msg: 'Accept Our Terms and Conditions!',
				type: 'warning',
			})
			status = false
			return
		}
		if (status) {
			const data = {
				// companyWorkFor: companyWorkFor,
				// role: role,
				// yearsOfExperience: yearsOfExperience,
				// canYouHeadline: canYouHeadline,
				// workedInFleetBefore: workedInFleetBefore,
				// drugTest: drugTest,
				// driverLicense: driverLicense,
				// captainLicense: captainLicense,
				// twicBack: twicBack,
				// twicFront: twicFront,
				// medicalCertificate: medicalCertificate,
				// fccLicenseFront: fccLicenseFront,
				// physical: physical,
				// radarCertificateFront: radarCertificateFront,
				// radioLicense: radioLicense,
				...userinfo,
				bankName: bankName,
				bankAccountNo: Iban,
				// ifscCode: Ifsc,
				// bankAccountTitle: AccTitle,
				routingNo: routingNo,
				about: '',
				postSettings: 'friends',
				skills: [],
				educations: [],
				experiances: [],
				isProfileCompleted: true, // true for profile wizard only one time
			}
			console.log('data complete wizard :', data)
			setButtonLoader(true)
			await mutate({
				variables: { updateUserInput: data },
			})
		}
	}

	useEffect(() => {
		setButtonLoader(false)
	}, [])

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={false}
			withBg={true}
			pagetitle={'Complete Your Profile'}
			headerShown={true}
			LeftIcon={true}
		>
			<View style={[styles().flex, styles().ph20]}>
				<ProgressBar
					active={5}
					done={[1, 2, 3, 4]}
				/>
				<View style={styles().flex}>
					<Text style={[styles().fs20, styles().mt35, styles().fontMedium, { color: currentTheme.headingColor }]}>Bank Details</Text>
					<View style={[styles().flex, styles().mt20]}>
						{/* <View style={[styles().mb20]}>
              <Text
                style={[
                  styles().fs16,
                  styles().mb10,
                   styles().fontRegular,
                  {color: currentTheme.black},
                ]}>
                Account Title
              </Text>
              <TextField
                keyboardType="default"
                value={AccTitle}
                errorText={AccTitleError}
                autoCapitalize="none"
                placeholder={'Enter Name'}
                onChangeText={text => {
                  setAccTitleError(false);
                  setAccTitle(text);
                }}
                style={[
                  styles().bw1,
                  styles().br10,
                  styles().overflowH,
                  {borderColor: currentTheme.B7B7B7},
                ]}
              />
            </View> */}

						<View style={[styles().mb20]}>
							<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.black }]}>Account Number</Text>
							<TextField
								keyboardType="numeric"
								value={Iban}
								errorText={IbanError}
								maxLength={22}
								autoCapitalize="none"
								placeholder={'Account Number'}
								onChangeText={text => {
									setIbanError(false)
									setIban(text)
								}}
								style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							/>
						</View>

						{/* <View style={[styles().mb20]}>
              <Text
                style={[
                  styles().fs16,
                  styles().mb10,
                   styles().fontRegular,
                  {color: currentTheme.black},
                ]}>
                IFSC Code
              </Text>
              <TextField
                keyboardType="default"
                value={Ifsc}
                maxLength={11}
                errorText={IfscError}
                autoCapitalize="none"
                placeholder={'Enter Code'}
                onChangeText={text => {
                  setIfscError(false);
                  setIfsc(text);
                }}
                style={[
                  styles().bw1,
                  styles().br10,
                  styles().overflowH,
                  {borderColor: currentTheme.B7B7B7},
                ]}
              />
            </View> */}

						<View style={[styles().mb20]}>
							<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.black }]}>Routing No</Text>
							<TextField
								keyboardType="numeric"
								value={routingNo}
								errorText={routingNoError}
								maxLength={9}
								autoCapitalize="none"
								placeholder={'Enter Routing No.'}
								onChangeText={text => {
									setRoutingNoError(false)
									setRoutingNo(text)
								}}
								style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							/>
						</View>

						<View style={[styles().mb20]}>
							<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.black }]}>Bank Name</Text>
							<TextField
								keyboardType="default"
								value={bankName}
								errorText={bankNameError}
								autoCapitalize="none"
								placeholder={'Enter Bank Name'}
								onChangeText={text => {
									setBankNameError(false)
									setBankName(text)
								}}
								style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							/>
						</View>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>Your Designated Account for Funds Transfer</Text>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() => setTerms(!terms)}
							style={[styles().mv20, styles().flexRow, styles().alignCenter]}
						>
							<View
								style={[
									styles().mr10,
									styles().wh20px,
									styles().alignCenter,
									styles().justifyCenter,
									styles().br5,
									{ borderWidth: 1, borderColor: currentTheme.C3C3C3 },
								]}
							>
								{terms
									? <FontAwesome
											name="check"
											size={14}
											color={currentTheme.themeBackground}
										/>
									: null}
							</View>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black }]}>I agree with the terms & conditions</Text>
						</TouchableOpacity>
					</View>
				</View>
				<View style={[styles().justifyEnd, styles().mb20]}>
					{buttonLoader
						? <Spinner />
						: <ThemeButton
								Title={`Let's Start`}
								onPress={() => ProfileComplete()}
							/>}
				</View>
			</View>
		</AuthLayout>
	)
}

export default WizardStep6
