import { Text, View, TouchableOpacity, FlatList } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import { useIsFocused } from '@react-navigation/native'
import { getDataAsync } from '../../utils/Constants'
import AsyncStorage from '@react-native-async-storage/async-storage'

const WizardStep4 = props => {
	const {
		role,
		companyWorkFor,
		canYouHeadline,
		yearsOfExperience,
		workedInFleetBefore,
		twicBack,
		twicFront,
		numberOfBargesPushThroughCanals,
		numberOfBargesPushThroughRiver,
	} = props?.route?.params
	console.log(props?.route?.params)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [isImagePicker, setIsImagePicker] = useState(false)
	const [_Loader, setLoader] = useState(false)
	const [Index, setIndex] = useState('')
	const [drugtest, setDrugtest] = useState('')
	const [physicalTest, setPhysicalTest] = useState('')
	const [drivingLicence, setDrivingLicence] = useState('')
	const [captainLicence, setCaptainLicence] = useState('')
	const [FCClicenceFront, setFCClicenceFront] = useState('')
	const [radarCertificateFront, setRadarCertificateFront] = useState('')
	const [radioLicence, setRadioLicence] = useState('')
	const [medicalCertificate, setMedicalCertificate] = useState('')
	const [pictureOfInformationOnthereLicences, setPictureOfInformationOnthereLicences] = useState('')
	const [costguardPhysical, setCostguardPhysical] = useState('')

	const documentList = [
		{
			id: 0,
			value: drugtest,
			title: 'Upload Drug Test',
			setData: data => {
				setDrugtest(data)
			},
		},
		{
			id: 2,
			value: drivingLicence,
			title: 'Upload Driver License',
			setData: data => {
				setDrivingLicence(data)
			},
		},

		{
			id: 3,
			value: FCClicenceFront,
			title: 'Upload FCC Licence Front',
			setData: data => {
				setFCClicenceFront(data)
			},
		},
		{
			id: 4,
			value: radarCertificateFront,
			title: 'Upload Radar Certificate Front',
			setData: data => {
				setRadarCertificateFront(data)
			},
		},
		{
			id: 5,
			value: radioLicence,
			title: 'Upload Radio License',
			setData: data => {
				setRadioLicence(data)
			},
		},
		{
			id: 6,
			value: medicalCertificate,
			title: 'Upload Medical Certificate',
			setData: data => {
				setMedicalCertificate(data)
			},
		},
	]

	const onrole =
		role === 'captain'
			? [
					{
						id: 7,
						value: captainLicence,
						title: 'Upload Captain License',
						setData: data => {
							setCaptainLicence(data)
						},
					},
				]
			: role === 'deckhand'
				? []
				: [
						{
							id: 8,
							value: pictureOfInformationOnthereLicences,
							title: 'Upload Information on there License',
							setData: data => {
								setPictureOfInformationOnthereLicences(data)
							},
						},
					]

	const physical =
		role === 'deckhand' || role === 'tankerman'
			? [
					{
						id: 9,
						value: physicalTest,
						title: 'Upload Physical Test',
						setData: data => {
							setPhysicalTest(data)
						},
					},
				]
			: [
					{
						id: 10,
						value: costguardPhysical,
						title: 'Upload coast guard physical',
						setData: data => {
							setCostguardPhysical(data)
						},
					},
				]

	const onRoleList = [...documentList, ...onrole, ...physical]

	async function UpdateDocuments() {
		const status = true
		// if (drugtest === '') {
		//   FlashMessage({msg: 'Select Drug Test', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (physicalTest === '') {
		//   FlashMessage({msg: 'Select Physical Test', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (drivingLicence === '') {
		//   FlashMessage({msg: 'Select Driver Licence', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (captainLicence === '') {
		//   FlashMessage({msg: 'Select Captain Licence', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (FCClicenceFront === '') {
		//   FlashMessage({msg: 'Select FCC Licence Front', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (radarCertificateFront === '') {
		//   FlashMessage({msg: 'Select Radar Certificate Front', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (radioLicence === '') {
		//   FlashMessage({msg: 'Select Radio Licence', type: 'warning'});
		//   status = false;
		//   return;
		// }
		// if (medicalCertificate === '') {
		//   FlashMessage({msg: 'Select Medical Certificate', type: 'warning'});
		//   status = false;
		//   return;
		// }

		if (status) {
			const base = {
				drugTest: drugtest,
				driverLicense: drivingLicence,
				medicalCertificate: medicalCertificate,
				fccLicenseFront: FCClicenceFront,
				radarCertificateFront: radarCertificateFront,
				radioLicense: radioLicence,
				//
				// captainLicense: captainLicence,
			}
			const physical = role === 'deckhand' || role === 'tankerman' ? { physical: physicalTest } : { costGuardPhysical: costguardPhysical }

			const check =
				role === 'captain'
					? {
							captainLicense: captainLicence,
						}
					: role === 'deckhand'
						? {}
						: {
								pictureOfInformationOnthereLicences: pictureOfInformationOnthereLicences,
							}

			const data = { ...props.route.params, ...base, ...physical, ...check }
			// console.log(data);
			await AsyncStorage.setItem('wizard4', JSON.stringify(data))
			props.navigation.navigate('WizardStep5', data)

			// props.navigation.navigate('WizardStep5', {
			//   // companyWorkFor: companyWorkFor,
			//   // role: role,
			//   // yearsOfExperience: yearsOfExperience,
			//   // canYouHeadline: canYouHeadline,
			//   // workedInFleetBefore: workedInFleetBefore,
			//   // twicBack: twicBack,
			//   // twicFront: twicFront,
			//   drugTest: drugtest,
			//   driverLicense: drivingLicence,
			//   captainLicense: captainLicence,
			//   medicalCertificate: medicalCertificate,
			//   fccLicenseFront: FCClicenceFront,
			//   physical: physicalTest,
			//   radarCertificateFront: radarCertificateFront,
			//   radioLicense: radioLicence,
			// });
		}
	}

	const isFocus = useIsFocused()
	useEffect(() => {
		getDataAsync('wizard4').then(res => {
			setDrugtest(res.drugTest)
			setDrivingLicence(res.driverLicense)
			setFCClicenceFront(res.fccLicenseFront)
			setRadarCertificateFront(res.radarCertificateFront)
			setRadioLicence(res.radioLicense)
			setMedicalCertificate(res.medicalCertificate)
			setPictureOfInformationOnthereLicences(res.pictureOfInformationOnthereLicences)
			setCostguardPhysical(res.costGuardPhysical)
			setCaptainLicence(res.captainLicense)
		})
	}, [isFocus])

	return (
		<>
			<AuthLayout
				navigation={props.navigation}
				withoutScroll={false}
				withBg={true}
				pagetitle={'Documents'}
				headerShown={true}
				LeftIcon={true}
			>
				<View style={[styles().flex, styles().ph20]}>
					<ProgressBar
						active={4}
						done={[1, 2, 3]}
					/>
					<View>
						<Text style={[styles().fs16, styles().mt35, styles().fontBold, { color: currentTheme.headingColor }]}>Documents</Text>
					</View>

					<View
						style={[
							// styles().ph20,
							styles().flex,
							styles().mt20,
						]}
					>
						<FlatList
							data={onRoleList}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ flexGrow: 1 }}
							renderItem={({ item, index }) => {
								return (
									<TouchableOpacity
										onPress={() => {
											setIsImagePicker(true)
											setLoader(true)
											setIndex(index)
										}}
										style={[
											styles().br10,
											styles().mb20,
											styles().h50px,
											styles().alignCenter,
											styles().justifyCenter,
											styles().bw1,
											{
												borderColor: item.value ? currentTheme.green : currentTheme.headingColor,
											},
										]}
									>
										{/* {item.id === Index && Loader ? (
                      <ActivityIndicator
                        size={20}
                        color={currentTheme.B7B7B7}
                      />
                    ) : ( */}
										<Text
											style={[
												styles().fs16,
												styles().fontRegular,
												{
													color: item.value ? currentTheme.green : currentTheme.headingColor,
												},
											]}
										>
											{item.title}
										</Text>
										{/* )} */}
									</TouchableOpacity>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>

					<View style={[styles().justifyEnd, styles().mb20]}>
						<ThemeButton
							Title={'Next'}
							onPress={() => UpdateDocuments()}
						/>
					</View>
				</View>
			</AuthLayout>
			<ImageAndDocumentPicker
				isImagePicker={isImagePicker}
				setIsImagePicker={() => {
					setIsImagePicker(false)
					setLoader(false)
				}}
				setImage={data => {
					onRoleList[Index]?.setData(data)
					setLoader(false)
				}}
				isPdf={true}
			/>
		</>
	)
}
export default WizardStep4
