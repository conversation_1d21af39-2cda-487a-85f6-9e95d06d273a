import { Text, View, TouchableOpacity, Keyboard } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import { useIsFocused } from '@react-navigation/native'
import { getDataAsync } from '../../utils/Constants'
import AsyncStorage from '@react-native-async-storage/async-storage'

const WizardStep2 = props => {
	const { role, refrence_no } = props?.route?.params
	const check = role === 'deckhand' || role === 'tankerman'
	const check2 = role === 'captain' || role === 'master'
	// console.log(role, check, check2);

	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const [workFor, setWorkFor] = useState('')
	const [workForError, setWorkForError] = useState(false)

	const [experience, setExperience] = useState('')
	const [experienceError, setExperienceError] = useState(false)

	const [headLine, setHeadline] = useState(false)
	const [fleet, setFleet] = useState(false)
	const [haveDE, setHaveDE] = useState(false)

	const [numberOfBargesPushThroughRiver, setNumberOfBargesPushThroughRiver] = useState('')
	const [numberOfBargesPushThroughRiverErr, setNumberOfBargesPushThroughRiverErr] = useState(false)

	const [numberOfBargesPushThroughCanals, setNumberOfBargesPushThroughCanals] = useState('')
	const [numberOfBargesPushThroughCanalsErr, setNumberOfBargesPushThroughCanalsErr] = useState(false)
	const [tankedForCompanies, setTankedForCompanies] = useState('')
	const [tankedForCompaniesErr, setTankedForCompaniesErr] = useState(false)

	async function PersonalInfo() {
		Keyboard.dismiss()
		let status = true
		if (workFor === '') {
			FlashMessage({ msg: 'Enter the company you work for.', type: 'warning' })
			status = false
			return
		}
		if (experience === '') {
			FlashMessage({ msg: 'Enter your work experience', type: 'warning' })
			status = false
			return
		}

		if (!check) {
			if (numberOfBargesPushThroughRiver === '') {
				FlashMessage({
					msg: 'Enter the barges pushed through the river.',
					type: 'warning',
				})
				status = false
				return
			}
			if (numberOfBargesPushThroughCanals === '') {
				FlashMessage({
					msg: 'Enter the barges pushed through the canals',
					type: 'warning',
				})
				status = false
				return
			}
		}

		if (role === 'tankerman') {
			if (tankedForCompanies === '') {
				FlashMessage({ msg: 'Enter companies tanked for.', type: 'warning' })
				status = false
				return
			}
		}

		if (status) {
			const basic = {
				role: role,
				companyWorkFor: workFor,
				yearsOfExperience: experience,
				tankedForCompanies: '',
				workedInFleetBefore: fleet,
				refrence_no: refrence_no,
			}

			const onperroles = {
				numberOfBargesPushThroughCanals: numberOfBargesPushThroughCanals,
				numberOfBargesPushThroughRiver: numberOfBargesPushThroughRiver,
				canYouHeadline: headLine,
				tankedForCompanies: '',
			}

			const isTanker = {
				tankedForCompanies: tankedForCompanies,
			}

			const isDE = {
				haveDE: haveDE,
			}

			const all = check2 ? { ...basic, ...onperroles, ...isDE } : { ...basic, ...onperroles }

			const tanker = { ...basic, ...isTanker }
			const param = role === 'tankerman' ? tanker : check ? basic : all
			// console.log(param);
			await AsyncStorage.setItem('wizard2', JSON.stringify(param))
			props.navigation.navigate('WizardStep3', param)
		}
	}

	const isFocus = useIsFocused()
	useEffect(() => {
		getDataAsync('wizard2').then(res => {
			setWorkFor(res.companyWorkFor)
			setExperience(res.yearsOfExperience)
			setFleet(res.workedInFleetBefore)
			setNumberOfBargesPushThroughCanals(res.numberOfBargesPushThroughCanals)
			setNumberOfBargesPushThroughRiver(res.numberOfBargesPushThroughRiver)
			setHeadline(res.canYouHeadline)
			setHaveDE(res.haveDE ? res.haveDE : false)
			setTankedForCompanies(res.tankedForCompanies)
		})
	}, [isFocus])

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={false}
			withBg={true}
			pagetitle={'Complete Your Profile'}
			headerShown={true}
			LeftIcon={true}
		>
			<View style={[styles().flex, styles().ph20]}>
				<ProgressBar
					active={2}
					done={[1]}
				/>
				<View>
					<Text style={[styles().fs16, styles().mt35, styles().fontBold, { color: currentTheme.headingColor }]}>Professional information</Text>
					<View style={[styles().mt25]}>
						<TextField
							keyboardType="default"
							value={workFor}
							errorText={workForError}
							autoCapitalize="none"
							placeholder={'What company do you work for?'}
							style
							onChangeText={text => {
								setWorkForError(false)
								setWorkFor(text)
							}}
						/>
					</View>
					<View style={[styles().mt25]}>
						<TextField
							keyboardType="numeric"
							value={experience}
							errorText={experienceError}
							autoCapitalize="none"
							placeholder={'How many years of experience?'}
							style
							onChangeText={text => {
								setExperienceError(false)
								setExperience(text)
							}}
						/>
					</View>
					{check
						? null
						: <>
								<View style={[styles().mt25]}>
									<TextField
										keyboardType="numeric"
										value={numberOfBargesPushThroughRiver}
										errorText={numberOfBargesPushThroughRiverErr}
										autoCapitalize="none"
										placeholder={'Barges pushed through river?'}
										style
										onChangeText={text => {
											setNumberOfBargesPushThroughRiverErr(false)
											setNumberOfBargesPushThroughRiver(text)
										}}
									/>
								</View>
								<View style={[styles().mb20, styles().mt25]}>
									<TextField
										keyboardType="numeric"
										value={numberOfBargesPushThroughCanals}
										errorText={numberOfBargesPushThroughCanalsErr}
										autoCapitalize="none"
										placeholder={'Barges pushed through canals?'}
										style
										onChangeText={text => {
											setNumberOfBargesPushThroughCanalsErr(false)
											setNumberOfBargesPushThroughCanals(text)
										}}
									/>
								</View>
							</>}

					{role === 'tankerman' && (
						<View style={[styles().mb20, styles().mt25]}>
							<TextField
								keyboardType="default"
								value={tankedForCompanies}
								errorText={tankedForCompaniesErr}
								autoCapitalize="none"
								placeholder={'What Companies have you tanked for?'}
								style
								onChangeText={text => {
									setTankedForCompaniesErr(false)
									setTankedForCompanies(text)
								}}
							/>
						</View>
					)}

					{check
						? null
						: <View style={[styles().mb25, styles().mt15]}>
								<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Can you headline?</Text>
								<View style={[styles().flexRow, styles().alignCenter]}>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => setHeadline(true)}
										style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
									>
										<View
											style={[
												styles().wh25px,
												styles().mr5,
												styles().alignCenter,
												styles().justifyCenter,
												styles().br15,
												styles().bw1,
												{
													backgroundColor: headLine ? currentTheme.themeBackground : null,
													borderColor: headLine ? currentTheme.themeBackground : currentTheme.B7B7B7,
												},
											]}
										>
											{headLine
												? <FontAwesome
														name="check"
														size={16}
														color={currentTheme.white}
													/>
												: null}
										</View>
										<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
									</TouchableOpacity>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => setHeadline(false)}
										style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
									>
										<View
											style={[
												styles().wh25px,
												styles().mr5,
												styles().alignCenter,
												styles().justifyCenter,
												styles().br15,
												styles().bw1,
												{
													backgroundColor: headLine === false ? currentTheme.themeBackground : null,
													borderColor: headLine === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
												},
											]}
										>
											{headLine === false
												? <FontAwesome
														name="check"
														size={16}
														color={currentTheme.white}
													/>
												: null}
										</View>
										<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
									</TouchableOpacity>
								</View>
							</View>}

					<View style={[styles().mb25, styles().mt15]}>
						<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Have you worked in a fleet before?</Text>
						<View style={[styles().flexRow, styles().alignCenter]}>
							<TouchableOpacity
								activeOpacity={0.5}
								onPress={() => setFleet(true)}
								style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
							>
								<View
									style={[
										styles().wh25px,
										styles().mr5,
										styles().alignCenter,
										styles().justifyCenter,
										styles().br15,
										styles().bw1,
										{
											backgroundColor: fleet ? currentTheme.themeBackground : null,
											borderColor: fleet ? currentTheme.themeBackground : currentTheme.B7B7B7,
										},
									]}
								>
									{fleet
										? <FontAwesome
												name="check"
												size={16}
												color={currentTheme.white}
											/>
										: null}
								</View>
								<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
							</TouchableOpacity>
							<TouchableOpacity
								activeOpacity={0.5}
								onPress={() => setFleet(false)}
								style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
							>
								<View
									style={[
										styles().wh25px,
										styles().mr5,
										styles().alignCenter,
										styles().justifyCenter,
										styles().br15,
										styles().bw1,
										{
											backgroundColor: fleet === false ? currentTheme.themeBackground : null,
											borderColor: fleet === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
										},
									]}
								>
									{fleet === false
										? <FontAwesome
												name="check"
												size={16}
												color={currentTheme.white}
											/>
										: null}
								</View>
								<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
							</TouchableOpacity>
						</View>
					</View>
					{check2
						? <View style={[styles().mb25, styles().mt15]}>
								<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Do You Have a D.E?</Text>
								<View style={[styles().flexRow, styles().alignCenter]}>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => setHaveDE(true)}
										style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
									>
										<View
											style={[
												styles().wh25px,
												styles().mr5,
												styles().alignCenter,
												styles().justifyCenter,
												styles().br15,
												styles().bw1,
												{
													backgroundColor: haveDE ? currentTheme.themeBackground : null,
													borderColor: haveDE ? currentTheme.themeBackground : currentTheme.B7B7B7,
												},
											]}
										>
											{haveDE
												? <FontAwesome
														name="check"
														size={16}
														color={currentTheme.white}
													/>
												: null}
										</View>
										<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
									</TouchableOpacity>
									<TouchableOpacity
										activeOpacity={0.5}
										onPress={() => setHaveDE(false)}
										style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
									>
										<View
											style={[
												styles().wh25px,
												styles().mr5,
												styles().alignCenter,
												styles().justifyCenter,
												styles().br15,
												styles().bw1,
												{
													backgroundColor: haveDE === false ? currentTheme.themeBackground : null,
													borderColor: haveDE === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
												},
											]}
										>
											{haveDE === false
												? <FontAwesome
														name="check"
														size={16}
														color={currentTheme.white}
													/>
												: null}
										</View>
										<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
									</TouchableOpacity>
								</View>
							</View>
						: null}
				</View>

				<View style={[styles().justifyEnd, styles().mb20, styles().flex]}>
					<ThemeButton
						Title={'Next'}
						onPress={() => PersonalInfo()}
					/>
				</View>
			</View>
		</AuthLayout>
	)
}

export default WizardStep2
