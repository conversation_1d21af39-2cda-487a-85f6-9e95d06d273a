import { Text, View, TouchableOpacity, ActivityIndicator } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'

import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import { getDataAsync } from '../../utils/Constants'
import { useIsFocused } from '@react-navigation/native'
import AsyncStorage from '@react-native-async-storage/async-storage'

const WizardStep3 = props => {
	const { role, companyWorkFor, canYouHeadline, yearsOfExperience, workedInFleetBefore, numberOfBargesPushThroughCanals, numberOfBargesPushThroughRiver } =
		props?.route?.params
	console.log(props?.route?.params)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [isImagePicker, setIsImagePicker] = useState(false)
	const [loader1, setLoader1] = useState(false)
	const [loader2, setLoader2] = useState(false)
	const [isImagePicker2, setIsImagePicker2] = useState(false)
	const [twicFront, settwicFront] = useState('')
	const [twicBack, settwicBack] = useState('')

	async function Validate() {
		let status = true
		if (twicFront === '') {
			FlashMessage({ msg: 'Select T.W.I.C front', type: 'warning' })
			status = false
			return
		}
		if (twicFront === '') {
			FlashMessage({ msg: 'Select T.W.I.C back', type: 'warning' })
			status = false
			return
		}
		if (status) {
			const twic = { twicBack: twicBack, twicFront: twicFront }
			const param = { ...props.route.params, ...twic }
			await AsyncStorage.setItem('wizard3', JSON.stringify(twic))
			props.navigation.navigate('WizardStep5', param)
		}
	}

	const isFocus = useIsFocused()
	useEffect(() => {
		getDataAsync('wizard3').then(res => {
			settwicFront(res.twicFront)
			settwicBack(res.twicBack)
		})
	}, [isFocus])

	return (
		<>
			<AuthLayout
				navigation={props.navigation}
				withoutScroll={false}
				withBg={true}
				pagetitle={'TWIC'}
				headerShown={true}
				LeftIcon={true}
			>
				<View style={[styles().flex, styles().ph20]}>
					<ProgressBar
						active={3}
						done={[1, 2]}
					/>
					<View>
						<Text style={[styles().fs16, styles().mt35, styles().fontBold, { color: currentTheme.headingColor }]}>VALID T.W.I.C</Text>
					</View>

					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => {
							setIsImagePicker(true)
							setLoader1(true)
						}}
						style={[
							styles().br10,
							styles().mt20,
							styles().mb20,
							styles().h50px,
							styles().alignCenter,
							styles().justifyCenter,
							styles().bw1,
							{
								borderColor: twicFront ? currentTheme.green : currentTheme.headingColor,
							},
						]}
					>
						{loader1
							? <ActivityIndicator
									size={20}
									color={currentTheme.B7B7B7}
								/>
							: <Text
									style={[
										styles().fs16,
										styles().fontRegular,
										{
											color: twicFront ? currentTheme.green : currentTheme.headingColor,
										},
									]}
								>
									Upload T.W.I.C Front
								</Text>}
					</TouchableOpacity>

					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => {
							setIsImagePicker2(true)
							setLoader2(true)
						}}
						style={[
							styles().br10,
							styles().mb30,
							styles().h50px,
							styles().alignCenter,
							styles().justifyCenter,
							styles().bw1,
							{
								borderColor: twicBack ? currentTheme.green : currentTheme.headingColor,
							},
						]}
					>
						{loader2
							? <ActivityIndicator
									size={20}
									color={currentTheme.B7B7B7}
								/>
							: <Text
									style={[
										styles().fs16,
										styles().fontRegular,
										{
											color: twicBack ? currentTheme.green : currentTheme.headingColor,
										},
									]}
								>
									Upload T.W.I.C Back
								</Text>}
					</TouchableOpacity>

					<View style={[styles().justifyEnd, styles().mb20, styles().flex]}>
						<ThemeButton
							Title={'Next'}
							onPress={() => Validate()}
						/>
					</View>
				</View>
			</AuthLayout>
			<ImageAndDocumentPicker
				isImagePicker={isImagePicker}
				setIsImagePicker={() => {
					setIsImagePicker(false)
					setLoader1(false)
				}}
				setImage={data => {
					settwicFront(data)
					setLoader1(false)
				}}
				isPdf={true}
			/>
			<ImageAndDocumentPicker
				isImagePicker={isImagePicker2}
				setIsImagePicker={() => {
					setIsImagePicker2(false)
					setLoader2(false)
				}}
				setImage={data => {
					settwicBack(data)
					setLoader2(false)
				}}
				isPdf={true}
			/>
		</>
	)
}

export default WizardStep3
