import { Text, View, TouchableOpacity, LayoutAnimation, FlatList, Keyboard } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Entypo from '@expo/vector-icons/Entypo'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ProgressBar from '../../component/ProgressBar/ProgressBar'
import { roles } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { CommonActions, useIsFocused } from '@react-navigation/native'
import UserContext from '../../context/User/User'
import { getDataAsync, removeRoles } from '../../utils/Constants'

const WizardStep1 = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [designationSelect, setdesignationSelect] = useState(false)
	const [refNumber, setRefNumber] = useState('')
	const [refNumberError, setRefNUmberError] = useState(false)
	const [role, setRole] = useState([])
	const [selectedRoles, setSelectedRoles] = useState('')
	const _user = useContext(UserContext)
	const { data, loading, error, refetch } = useQuery(roles, {
		fetchPolicy: 'cache-and-network',
		onCompleted: data => {
			// console.log('roles res :', data.roles);
			const filterRoles = data?.roles?.filter(item => !removeRoles?.includes(item))
			setRole(filterRoles)
		},
		onError: err => {
			console.log('roles err :', err)
		},
	})

	console.log(role)

	async function SelectRole() {
		let status = true
		if (selectedRoles === '') {
			FlashMessage({ msg: 'Select your role.', type: 'warning' })
			status = false
			return
		}

		if (selectedRoles !== 'deckhand') {
			if (refNumber === '') {
				FlashMessage({ msg: 'Enter Reference no.', type: 'warning' })
				setRefNUmberError(true)
				status = false
				return
			}
			if (refNumber.length < 7) {
				FlashMessage({
					msg: 'Reference no. must be of 7 digits',
					type: 'warning',
				})
				setRefNUmberError(true)
				status = false
				return
			}
		}

		if (status) {
			Keyboard.dismiss()
			await AsyncStorage.setItem('wizard1', JSON.stringify({ role: selectedRoles, refrence_no: refNumber }))
			props.navigation.navigate('WizardStep2', {
				role: selectedRoles,
				refrence_no: refNumber,
			})
		}
	}

	async function Logout() {
		try {
			await AsyncStorage.removeItem('token')
			await AsyncStorage.removeItem('isProfileCompleted')
			await AsyncStorage.removeItem('wizard1')
			await AsyncStorage.removeItem('wizard2')
			await AsyncStorage.removeItem('wizard3')
			await AsyncStorage.removeItem('wizard4')
			props.navigation.dispatch(
				CommonActions.reset({
					index: 0,
					routes: [{ name: 'Auth' }],
				})
			)
		} catch (error) {
			console.log('logout Err :', error)
		}
	}

	function _capitalizeFirstLetter(string) {
		return string?.charAt(0)?.toUpperCase() + string?.slice(1)
	}

	const isFocus = useIsFocused()

	useEffect(() => {
		getDataAsync('wizard1').then(res => {
			setSelectedRoles(res.role)
			setRefNumber(res.refrence_no)
		})
	}, [isFocus])

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={false}
			withBg={true}
			pagetitle={'Complete Your Profile'}
			headerShown={true}
			LeftIcon={false}
		>
			<View style={[styles().flex, styles().ph20]}>
				<ProgressBar
					active={1}
					done={[]}
				/>
				<View style={styles().flex}>
					<Text style={[styles().fs20, styles().mt35, styles().fontBold, { color: currentTheme.headingColor }]}>I'm A...</Text>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => {
							setdesignationSelect(!designationSelect)
							LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
						}}
						style={[
							styles().flexRow,
							styles().mt25,
							styles().pb10,
							styles().bbw005,
							styles().mb10,
							styles().alignCenter,
							styles().justifyBetween,
							{ borderBottomColor: currentTheme.C3C3C3 },
						]}
					>
						<Text style={[styles().fs16, styles().fontRegular, styles().textCapitalize, { color: currentTheme.headingColor }]}>
							{selectedRoles ? (selectedRoles === 'matePilot' ? 'Mate Pilot' : selectedRoles) : 'Select your role...'}
						</Text>
						<Entypo
							name={designationSelect ? 'chevron-with-circle-up' : 'chevron-with-circle-down'}
							size={20}
							color={currentTheme.black}
						/>
					</TouchableOpacity>
					<View style={[styles().flex, styles().mb20]}>
						{designationSelect
							? <View style={[styles().ph20, styles().boxpeshadow, styles().br10]}>
									<FlatList
										data={role}
										showsVerticalScrollIndicator={false}
										contentContainerStyle={{ flexGrow: 1 }}
										renderItem={({ item, index }) => {
											return (
												<TouchableOpacity
													activeOpacity={0.5}
													onPress={() => {
														setSelectedRoles(item)
														setdesignationSelect(false)
														LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
													}}
													key={index}
													style={[
														styles().flexRow,
														styles().pb10,
														styles().pt20,
														styles().alignCenter,
														styles().justifyBetween,
														{
															borderTopWidth: index === 0 ? 0 : 0.5,
															borderTopColor: currentTheme.C3C3C3,
															backgroundColor: currentTheme.white,
														},
													]}
												>
													<View style={[styles().flex]}>
														<Text style={[styles().fs14, styles().fontRegular, styles().textCapitalize, { color: currentTheme.headingColor }]}>
															{item === 'matePilot' ? 'Mate Pilot' : item}
															{}
														</Text>
													</View>
												</TouchableOpacity>
											)
										}}
										keyExtractor={(_item, index) => index.toString()}
										ListFooterComponent={<View style={styles().wh20px} />}
									/>
								</View>
							: null}
						{selectedRoles === ''
							? null
							: selectedRoles !== 'deckhand'
								? <View style={[styles().mt20]}>
										<TextField
											maxLength={7}
											keyboardType="numeric"
											value={refNumber}
											errorText={refNumberError}
											autoCapitalize="none"
											placeholder={'Reference Number'}
											style={{ borderBottomWidth: 0.7 }}
											onChangeText={text => {
												setRefNUmberError(false)
												setRefNumber(text)
											}}
											FieldIcon={
												<FontAwesome5
													name="hashtag"
													size={18}
													color={currentTheme.black}
												/>
											}
										/>
									</View>
								: null}
					</View>
				</View>
				<View style={[styles().justifyEnd, styles().mb20]}>
					<ThemeButton
						Title={'Next'}
						onPress={() => SelectRole()}
					/>
					<ThemeButton
						Title={'Exit'}
						onPress={() => Logout()}
						Style={{
							backgroundColor: currentTheme.headingColor,
							borderColor: currentTheme.headingColor,
						}}
					/>
				</View>
			</View>
		</AuthLayout>
	)
}

export default WizardStep1
