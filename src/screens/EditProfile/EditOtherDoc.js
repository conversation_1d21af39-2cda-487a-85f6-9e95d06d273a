import { Text, View, TouchableOpacity, Image, FlatList, LayoutAnimation } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import UserContext from '../../context/User/User'
import { updateUser } from '../../apollo/server'
import { get_url_extension } from '../../utils/Constants'

const EditOtherDoc = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const user = useContext(UserContext)
	const [Loader, setLoader] = useState(false)
	const [isImagePicker, setIsImagePicker] = useState(false)
	const [Index, setIndex] = useState('')
	const [_docLoader, setDocLoader] = useState(false)
	const [drugtest, setDrugtest] = useState('')
	const [physicalTest, setPhysicalTest] = useState('')
	const [drivingLicence, setDrivingLicence] = useState('')
	const [captainLicence, setCaptainLicence] = useState('')
	const [fccLicenseFront, setFccLicenseFront] = useState('')
	const [radarCertificateFront, setRadarCertificateFront] = useState('')
	const [radioLicence, setRadioLicence] = useState('')
	const [medicalCertificate, setMedicalCertificate] = useState('')
	const [pictureOfInformationOnthereLicences, setPictureOfInformationOnthereLicences] = useState('')
	const [costguardPhysical, setCostguardPhysical] = useState('')

	const documentList = [
		{
			id: 0,
			value: drugtest,
			title: 'Upload Drug Test',
			setData: data => {
				setDrugtest(data)
			},
		},
		{
			id: 1,
			value: physicalTest,
			title: 'Upload Physical Test',
			setData: data => {
				setPhysicalTest(data)
			},
		},
		{
			id: 2,
			value: drivingLicence,
			title: 'Upload Driving License',
			setData: data => {
				setDrivingLicence(data)
			},
		},
		{
			id: 4,
			value: fccLicenseFront,
			title: 'Upload FCC License Front',
			setData: data => {
				setFccLicenseFront(data)
			},
		},
		{
			id: 5,
			value: radarCertificateFront,
			title: 'Upload Radar Certificate Front',
			setData: data => {
				setRadarCertificateFront(data)
			},
		},
		{
			id: 6,
			value: radioLicence,
			title: 'Upload Radio License',
			setData: data => {
				setRadioLicence(data)
			},
		},
		{
			id: 7,
			value: medicalCertificate,
			title: 'Upload Medical Certificate',
			setData: data => {
				setMedicalCertificate(data)
			},
		},
	]

	const onrole =
		user?.role === 'captain'
			? [
					{
						value: captainLicence,
						title: 'Upload Captain License',
						setData: data => {
							setCaptainLicence(data)
						},
					},
				]
			: user?.role === 'deckhand'
				? []
				: [
						{
							value: pictureOfInformationOnthereLicences,
							title: 'Upload Information on there License',
							setData: data => {
								setPictureOfInformationOnthereLicences(data)
							},
						},
					]

	const physical =
		user?.role === 'deckhand' || user?.role === 'tankerman'
			? [
					{
						value: physicalTest,
						title: 'Upload Physical Test',
						setData: data => {
							setPhysicalTest(data)
						},
					},
				]
			: [
					{
						id: 10,
						value: costguardPhysical,
						title: 'Upload coast guard physical',
						setData: data => {
							setCostguardPhysical(data)
						},
					},
				]

	const onRoleList = [...documentList, ...onrole, ...physical]

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			setLoader(false)
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Profile Updated.',
				type: 'success',
			})
			props.navigation.goBack()
		} catch (e) {
			console.log(e)
			setLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoader(false)
		console.log('updateUser error  :', error)
	}

	async function UpdateDOCS() {
		try {
			setLoader(true)
			await mutate({
				variables: {
					updateUserInput: {
						captainLicense: captainLicence,
						driverLicense: drivingLicence,
						drugTest: drugtest,
						fccLicenseFront: fccLicenseFront,
						medicalCertificate: medicalCertificate,
						radarCertificateFront: radarCertificateFront,
						radioLicense: radioLicence,
						physical: physicalTest,
						costGuardPhysical: costguardPhysical,
						pictureOfInformationOnthereLicences: pictureOfInformationOnthereLicences,
					},
				},
			})
		} catch (e) {
			console.log('catch editotherdocs :', e)
		}
	}

	useEffect(() => {
		setDrugtest(user?.drugTest)
		setPhysicalTest(user?.physical)
		setDrivingLicence(user?.driverLicense)
		setCaptainLicence(user?.captainLicense)
		setFccLicenseFront(user?.fccLicenseFront)
		setRadarCertificateFront(user?.radarCertificateFront)
		setRadioLicence(user?.radioLicense)
		setMedicalCertificate(user?.medicalCertificate)
		setCostguardPhysical(user?.costGuardPhysical)
		setPictureOfInformationOnthereLicences(user?.pictureOfInformationOnthereLicences)
	}, [isFocus])

	return (
		<>
			<Layout
				LeftIcon={true}
				headerShown={true}
				pagetitle={'Edit Other Documents'}
				withoutScroll={true}
				navigation={props.navigation}
				keyBoardArea={55}
			>
				<View style={[styles().flex]}>
					<View style={[styles().ph20, styles().flex]}>
						<View style={[styles().sectionHead, styles().mb20]}>
							<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Other Documents</Text>
						</View>
						<FlatList
							data={onRoleList}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ flexGrow: 1 }}
							renderItem={({ item, index }) => {
								const docType = get_url_extension(item.value)
								const icon =
									docType === 'pdf'
										? <FontAwesome5
												name="file-pdf"
												size={45}
												color={currentTheme.themeBackground}
											/>
										: docType === 'docx' || docType === 'doc'
											? <FontAwesome
													name="file-word-o"
													size={45}
													color={currentTheme.themeBackground}
												/>
											: docType === 'png' || docType === 'jpeg' || docType === 'jpg'
												? <Image
														source={{ uri: item.value }}
														style={[styles().wh100, styles().br8]}
														resizeMode="cover"
													/>
												: <Entypo
														name="plus"
														size={45}
														color={currentTheme.themeBackground}
													/>
								return (
									<View style={[]}>
										<Text style={[styles().mb10, styles().fs12, { color: currentTheme.themeBackground }]}>{item.title}</Text>
										<TouchableOpacity
											activeOpacity={0.5}
											key={index}
											onPress={() => {
												console.log(item.value)
												setIsImagePicker(true)
												setDocLoader(true)
												setIndex(index)
											}}
											style={[
												styles().br10,
												styles().mb20,
												styles().h100px,
												styles().alignCenter,
												styles().justifyCenter,
												styles().bw1,
												{
													borderStyle: 'dashed',
													borderColor: currentTheme.headingColor,
													// borderColor: item.value
													//   ? currentTheme.green
													//   : currentTheme.headingColor,
												},
											]}
										>
											{/* {index === Index && docLoader ? (
                        <ActivityIndicator
                          size={30}
                          color={currentTheme.B7B7B7}
                        />
                      ) : ( */}
											<View
												style={[
													styles().wh80px,
													styles().br8,
													styles().alignCenter,
													styles().justifyCenter,
													{
														borderColor: currentTheme.themeBackground,
														borderWidth: 0.5,
													},
												]}
											>
												{icon}
											</View>
											{/* )} */}
										</TouchableOpacity>
									</View>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>

					<View
						style={[
							styles().justifyEnd,
							// styles().flex,
							styles().mb20,
							styles().ph20,
						]}
					>
						{Loader
							? <Spinner />
							: <ThemeButton
									onPress={() => UpdateDOCS()}
									Title={'Save'}
									Style={styles().br10}
								/>}
					</View>
				</View>
			</Layout>
			<ImageAndDocumentPicker
				isImagePicker={isImagePicker}
				setIsImagePicker={() => {
					setIsImagePicker(false)
					setDocLoader(false)
				}}
				setImage={data => {
					onRoleList[Index]?.setData(data)
					setDocLoader(false)
				}}
				isPdf={true}
			/>
		</>
	)
}

export default EditOtherDoc
