import { Text, View, TouchableOpacity, FlatList, Platform, LayoutAnimation, UIManager } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AntDesign from '@expo/vector-icons/AntDesign'
import Feather from '@expo/vector-icons/Feather'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import moment from 'moment'
import DateTimePicker from '@react-native-community/datetimepicker'
import { updateUser } from '../../apollo/server'
import UserContext from '../../context/User/User'
import { validateArray } from '../../utils/Constants'
import FloatButton from '../../component/FloatButton/FloatButton'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const EditWork = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const user = useContext(UserContext)
	const [_educationValues, _setEducationValues] = useState({})
	const [_index, setIndex] = useState('')
	const [workForm, setWorknForm] = useState([])
	const [Loader, setLoader] = useState(false)
	const [datePicker, setDatePicker] = useState(false)
	const [dateValue, _setDateValue] = useState(new Date())
	function showDatePicker() {
		setDatePicker(!datePicker)
	}
	const [dateToPicker, setDateToPicker] = useState(false)
	const [dateToValue, _setDateToValue] = useState(new Date())

	function showDateToPicker() {
		setDateToPicker(!dateToPicker)
	}

	function AddNewWork() {
		const form = {
			title: '',
			company: '',
			startYear: '',
			endYear: '',
			detail: '',
		}
		setWorknForm(prev => [...prev, form])
		LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
	}

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			setLoader(false)
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Profile Updated.',
				type: 'success',
			})
			props.navigation.goBack()
		} catch (e) {
			console.log(e)
			setLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoader(false)
		console.log('updateUser error  :', error)
	}

	async function UpdateWork() {
		const newArr = workForm?.map(({ __typename, ...rest }) => {
			return rest
		})
		try {
			if (validateArray(newArr)) {
				setLoader(true)
				await mutate({
					variables: {
						updateUserInput: {
							experiances: newArr,
						},
					},
				})
			} else {
				FlashMessage({
					msg: 'Kindly Add All Work Experience Fields.',
					type: 'warning',
				})
			}
		} catch (e) {
			console.log('catch editeducation :', e)
		}
	}

	const removeWork = index => {
		const newArr = [...workForm]
		newArr?.splice(index, 1)
		setWorknForm(_prev => [...newArr])
		LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
	}

	useEffect(() => {
		if (user?.experiances?.length === 0) {
			AddNewWork()
		} else {
			setWorknForm(user?.experiances)
		}
	}, [isFocus])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Edit Work'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<FlatList
				data={workForm}
				onEndReachedThreshold={0.75}
				showsVerticalScrollIndicator={true}
				contentContainerStyle={{ flexGrow: 1 }}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
								}}
							>
								{'No Work'}
							</Text>
						</View>
					)
				}}
				renderItem={({ item, index }) => {
					return (
						<View
							style={[
								styles().flex,
								styles().mb30,
								styles().pt15,
								{
									borderColor: currentTheme.C3C3C3,
									borderTopWidth: index === 0 ? 0 : 0.5,
								},
							]}
						>
							<TouchableOpacity
								onPress={() => removeWork(index)}
								activeOpacity={0.5}
								style={[styles().mb10, styles().alignSelfEnd]}
							>
								<AntDesign
									name="minuscircle"
									color={currentTheme.red}
									size={20}
								/>
							</TouchableOpacity>
							<View style={[styles().mb20]}>
								<TextField
									keyboardType="default"
									// value={educationValues[`educationTitle${index}`]}
									value={workForm[index]?.title}
									autoCapitalize="none"
									placeholder={'Designation'}
									onChangeText={text => {
										const value = (item.title = text)
										const newData = [...workForm]
										newData[index].title = value
										setWorknForm(newData)
										// setEducationValues({
										//   ...educationValues,
										//   [`educationTitle${index}`]: text,
										// });
									}}
									style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
								/>
							</View>

							<View style={[styles().mb20]}>
								<TextField
									keyboardType="default"
									// value={educationValues[`school${index}`]}
									value={workForm[index]?.company}
									autoCapitalize="none"
									placeholder={'Organization'}
									onChangeText={text => {
										const value = (item.company = text)
										const newData = [...workForm]
										newData[index].company = value
										setWorknForm(newData)
										// setEducationValues({
										//   ...educationValues,
										//   [`school${index}`]: text,
										// });
									}}
									style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
								/>
							</View>

							<View style={[styles().mb20]}>
								<Text style={[styles().fs14, styles().fw400, styles().mb5, { color: currentTheme.headingColor }]}>{'From'}</Text>
								<TouchableOpacity
									onPress={() => {
										showDatePicker()
										setIndex(index)
									}}
									style={[
										styles().w100,
										styles().h50px,
										styles().bw1,
										styles().br10,
										styles().justifyBetween,
										styles().ph20,
										styles().alignCenter,
										styles().flexRow,
										{
											borderColor: currentTheme.B7B7B7,
										},
									]}
								>
									<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>
										{workForm[index]?.startYear ? moment(workForm[index]?.startYear)?.format('LL') : 'Select Date'}
										{/* {educationValues[`from${index}`]
                      ? moment(educationValues[`from${index}`]).format('LL')
                      : 'Select Date'} */}
									</Text>
									<Feather
										name="calendar"
										color={currentTheme.C3C3C3}
										size={20}
									/>
								</TouchableOpacity>
							</View>
							{datePicker && (
								<>
									<DateTimePicker
										testID="dateTimePicker"
										minimumDate={new Date('1947-08-14')}
										value={dateValue}
										mode={'date'}
										themeVariant="light"
										textColor={currentTheme.themeBackground}
										accentColor="blue"
										positiveButton={{
											label: 'OK',
											textColor: currentTheme.themeBackground,
										}}
										negativeButton={{
											label: 'Cancel',
											textColor: currentTheme.themeBackground,
										}}
										display={Platform.OS === 'ios' ? 'spinner' : 'default'}
										is24Hour={true}
										onChange={(event, value) => {
											if (event.type === 'dismissed') {
												// Handle cancel button press
												setDatePicker(false)
												return
											}
											if (event.type === 'set' && Platform.OS !== 'ios') {
												// Handle cancel button press
												setDatePicker(false)
											}
											const currentDate = value || dateValue
											// setEducationValues({
											//   ...educationValues,
											//   [`from${index}`]: currentDate,
											// });
											const date = (workForm[index].startYear = currentDate)
											const newData = [...workForm]
											newData[index].startYear = date?.toString()
											setWorknForm(newData)
										}}
										style={{
											alignSelf: 'center',
											justifyContent: 'center',
											alignItems: 'flex-start',
											// width: "90%",
											// height: "70%",
										}}
									/>
									{Platform.OS === 'ios' && (
										<View
											style={[
												styles().alignSelfCenter,
												styles().justifyCenter,
												styles().alignCenter,
												styles().mb30,
												styles().bw1,
												styles().br5,
												{ borderColor: currentTheme.themeBackground },
											]}
										>
											<TouchableOpacity
												style={[styles().ph30, styles().pv5]}
												onPress={() => {
													setDatePicker(false)
												}}
											>
												<Text style={[styles().fontMedium, styles().fs14, { color: currentTheme.themeBackground }]}>Close</Text>
											</TouchableOpacity>
										</View>
									)}
								</>
							)}

							<View style={[styles().mb20]}>
								<Text style={[styles().fs14, styles().fw400, styles().mb5, { color: currentTheme.headingColor }]}>{'To'}</Text>
								<TouchableOpacity
									onPress={() => {
										showDateToPicker()
										setIndex(index)
									}}
									style={[
										styles().w100,
										styles().h50px,
										styles().bw1,
										styles().br10,
										styles().justifyBetween,
										styles().ph20,
										styles().alignCenter,
										styles().flexRow,
										{
											borderColor: currentTheme.B7B7B7,
										},
									]}
								>
									<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>
										{workForm[index]?.endYear ? moment(workForm[index]?.endYear)?.format('LL') : 'Select Date'}
										{/* {educationValues[`to${index}`]
                      ? moment(educationValues[`to${index}`]).format('LL')
                      : 'Select Date'} */}
									</Text>
									<Feather
										name="calendar"
										color={currentTheme.C3C3C3}
										size={20}
									/>
								</TouchableOpacity>
							</View>
							{dateToPicker && (
								<>
									<DateTimePicker
										testID="dateTimePicker"
										minimumDate={new Date('1947-08-14')}
										value={dateToValue}
										mode={'date'}
										themeVariant="light"
										textColor={currentTheme.themeBackground}
										accentColor="blue"
										positiveButton={{
											label: 'OK',
											textColor: currentTheme.themeBackground,
										}}
										negativeButton={{
											label: 'Cancel',
											textColor: currentTheme.themeBackground,
										}}
										display={Platform.OS === 'ios' ? 'spinner' : 'default'}
										is24Hour={true}
										onChange={(event, value) => {
											if (event.type === 'dismissed') {
												// Handle cancel button press
												setDateToPicker(false)
												return
											}
											if (event.type === 'set' && Platform.OS !== 'ios') {
												setDateToPicker(false)
											}
											const currentDate = value || dateValue
											// setEducationValues({
											//   ...educationValues,
											//   [`to${index}`]: currentDate,
											// });
											const date = (workForm[index].endYear = currentDate)
											const newData = [...workForm]
											newData[index].endYear = date?.toString()
											setWorknForm(newData)
										}}
										style={{
											alignSelf: 'center',
											justifyContent: 'center',
											alignItems: 'flex-start',
											// width: "90%",
											// height: "70%",
										}}
									/>
									{Platform.OS === 'ios' && (
										<View
											style={[
												styles().alignSelfCenter,
												styles().justifyCenter,
												styles().alignCenter,
												styles().mb30,
												styles().bw1,
												styles().br5,
												{ borderColor: currentTheme.themeBackground },
											]}
										>
											<TouchableOpacity
												style={[styles().ph30, styles().pv5]}
												onPress={() => {
													setDateToPicker(false)
												}}
											>
												<Text style={[styles().fontMedium, styles().fs14, { color: currentTheme.themeBackground }]}>Close</Text>
											</TouchableOpacity>
										</View>
									)}
								</>
							)}

							<View style={[styles().mb20]}>
								<TextField
									keyboardType="default"
									// value={educationValues[`educationTitle${index}`]}
									value={workForm[index]?.detail}
									autoCapitalize="none"
									multiline={true}
									placeholder={'Description'}
									onChangeText={text => {
										const value = (item.detail = text)
										const newData = [...workForm]
										newData[index].detail = value
										setWorknForm(newData)
										// setEducationValues({
										//   ...educationValues,
										//   [`educationTitle${index}`]: text,
										// });
									}}
									stylesInput={[styles().h100px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
									style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
								/>
							</View>
						</View>
					)
				}}
			/>

			<View style={[styles().justifyEnd, styles().flex, styles().mb20]}>
				<FloatButton onPress={() => AddNewWork()} />
				{Loader
					? <View style={[styles().mt20]}>
							<Spinner />
						</View>
					: <ThemeButton
							onPress={() => UpdateWork()}
							Title={'Save'}
							Style={[styles().br10]}
						/>}
			</View>
		</Layout>
	)
}

export default EditWork
