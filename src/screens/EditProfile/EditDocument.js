import { Text, View, TouchableOpacity, Image, FlatList, Dimensions } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import { useIsFocused } from '@react-navigation/native'
import { get_url_extension } from '../../utils/Constants'

const { width, height } = Dimensions.get('window')

const EditDocuments = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const _isFocus = useIsFocused()
	const user = useContext(UserContext)

	const docList = [
		{
			image: user?.drugTest,
			name: 'Drug test',
		},

		{
			image: user?.twicFront,
			name: 'TWIC front',
		},
		{
			image: user?.twicBack,
			name: 'TWIC back',
		},
		{
			image: user?.driverLicense,
			name: 'Driving license',
		},
		{
			image: user?.captainLicense,
			name: 'Captain license',
		},
		{
			image: user?.fccLicenseFront,
			name: 'FCC license front',
		},
		{
			image: user?.radarCertificateFront,
			name: 'Radar certificate front',
		},
		{
			image: user?.radioLicense,
			name: 'Radio license',
		},
		{
			image: user?.medicalCertificate,
			name: 'Medical certificate',
		},
	]

	const onRoleList = [
		user?.role === 'deckhand'
			? {}
			: {
					image: user?.pictureOfInformationOnthereLicences,
					name: 'Picture of information on Licences',
				},
	]

	const onRolePhycical = [
		user?.role === 'deckhand' || user?.role === 'tankerman'
			? {
					image: user?.physical,
					name: 'Physical test',
				}
			: {
					image: user?.costGuardPhysical,
					name: 'Coast guard physical',
				},
	]
	const _docs = [...docList, ...onRoleList, ...onRolePhycical]

	const twic = [
		{
			image: user?.twicFront,
			name: 'TWIC front',
		},
		{
			image: user?.twicBack,
			name: 'TWIC back',
		},
	]

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Edit Document'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<View style={[styles().flex]}>
				<View style={[styles().bioSection, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb30, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>VALID T.W.I.C </Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditTWIC')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fw400, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					<View style={styles().sectionContent}>
						<FlatList
							data={twic}
							showsVerticalScrollIndicator={false}
							horizontal
							onEndReachedThreshold={0.75}
							contentContainerStyle={{ flexGrow: 1 }}
							ListEmptyComponent={() => {
								return (
									<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
										<Text
											style={{
												color: currentTheme.E8E8C8,
												fontSize: 14,
											}}
										>
											{'No twic'}
										</Text>
									</View>
								)
							}}
							renderItem={({ item, index }) => {
								const docType = get_url_extension(item.image)
								const icon =
									docType === 'pdf'
										? <FontAwesome5
												name="file-pdf"
												size={45}
												color={currentTheme.themeBackground}
											/>
										: docType === 'docx' || docType === 'doc'
											? <FontAwesome
													name="file-word-o"
													size={45}
													color={currentTheme.themeBackground}
												/>
											: docType === 'png' || docType === 'jpeg' || docType === 'jpg'
												? <Image
														source={{ uri: item.image }}
														style={styles().wh100}
														resizeMode="cover"
													/>
												: <Entypo
														name="plus"
														size={45}
														color={currentTheme.themeBackground}
													/>
								return (
									<View style={[styles().alignCenter, styles().mr10]}>
										<View
											style={[
												// styles().wh100px,
												styles().overflowH,
												styles().br10,
												styles().mb5,
												styles().alignCenter,
												styles().justifyCenter,
												{
													borderWidth: 0.5,
													borderColor: currentTheme.themeBackground,
													height: height * 0.13,
													width: width * 0.27,
												},
											]}
										>
											{icon}
										</View>
										<Text
											numberOfLines={2}
											style={{
												fontSize: 10,
												color: currentTheme.themeBackground,
											}}
										>
											{item.name}
										</Text>
									</View>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>
				</View>

				{/* <View style={[styles().bioSection, styles().mt20, styles().pb10]}>
          <View
            style={[
              styles().sectionHead,
              styles().mb30,
              styles().flexRow,
              styles().alignCenter,
              styles().justifyBetween,
            ]}>
            <Text
              style={[
                styles().fs20,
                styles().fw700,
                {color: currentTheme.headingColor},
              ]}>
              DOCUMENTS
            </Text>
            <TouchableOpacity
              onPress={() => props.navigation.navigate('EditOtherDoc')}
              activeOpacity={0.7}>
              <Text
                style={[
                  styles().fs16,
                  styles().fw400,
                  {color: currentTheme.B7B7B7},
                ]}>
                Edit
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles().sectionContent}>
            <FlatList
              data={docs}
              showsVerticalScrollIndicator={false}
              //   horizontal
              numColumns={3}
              onEndReachedThreshold={0.75}
              contentContainerStyle={{flexGrow: 1}}
              ListEmptyComponent={() => {
                return (
                  <View
                    style={[
                      styles().alignCenter,
                      styles().justifyCenter,

                      styles().flex,
                    ]}>
                    <Text
                      style={{
                        color: currentTheme.E8E8C8,
                        fontSize: 14,
                      }}>
                      {'No Documents'}
                    </Text>
                  </View>
                );
              }}
              renderItem={({item, index}) => {
                let docType = get_url_extension(item.image);
                let icon =
                  docType === 'pdf' ? (
                    <FontAwesome5
                      name="file-pdf"
                      size={45}
                      color={currentTheme.themeBackground}
                    />
                  ) : docType === 'docx' || docType === 'doc' ? (
                    <FontAwesome
                      name="file-word-o"
                      size={45}
                      color={currentTheme.themeBackground}
                    />
                  ) : docType === 'png' ||
                    docType === 'jpeg' ||
                    docType === 'jpg' ? (
                    <Image
                      source={{uri: item.image}}
                      style={styles().wh100}
                      resizeMode="cover"
                    />
                  ) : (
                    <Entypo
                      name="plus"
                      size={45}
                      color={currentTheme.themeBackground}
                    />
                  );
                return (
                  <View
                    style={[
                      styles().alignCenter,
                      styles().mr10,
                      styles().mb15,
                    ]}>
                    <View
                      style={[
                        // styles().wh100px,
                        styles().overflowH,
                        styles().br10,
                        styles().mb5,
                        styles().alignCenter,
                        styles().justifyCenter,
                        {
                          borderWidth: 0.5,
                          borderColor: currentTheme.themeBackground,
                          height: height * 0.13,
                          width: width * 0.27,
                        },
                      ]}>
                      {icon}
                    </View>
                    <Text
                      numberOfLines={2}
                      style={{
                        fontSize: 10,
                        color: currentTheme.themeBackground,
                      }}>
                      {item.name}
                    </Text>
                  </View>
                );
              }}
              keyExtractor={(item, index) => index.toString()}
              ListFooterComponent={<View style={styles().wh20px} />}
            />
          </View>
        </View> */}
			</View>
		</Layout>
	)
}

export default EditDocuments
