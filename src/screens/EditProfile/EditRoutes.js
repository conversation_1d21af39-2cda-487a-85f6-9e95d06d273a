import { Text, View, TouchableOpacity, LayoutAnimation, FlatList } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery, useMutation } from '@apollo/client'
import { updateUser, waterWays } from '../../apollo/server'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import Spinner from '../../component/Spinner/Spinner'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import TextField from '../../component/FloatTextField/FloatTextField'
import CheckBox from '../../component/CheckBox/CheckBox'

const WizardStep5 = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const { refetch } = useContext(UserContext)
	const [selectedRoutes, setSelectedRoutes] = useState([])
	const [show, setShow] = useState(false)
	const [Index, setIndex] = useState('')
	const [buttonLoader, setButtonLoader] = useState(false)
	const [toggleOther, setToggleOther] = useState(false)
	const [others, setOthers] = useState('')
	const [othersErr, setOthersErr] = useState(false)

	const { data, loading, error } = useQuery(waterWays, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				limit: 1000,
			},
		},
		onCompleted: _data => {
			//   console.log('waterways res :', data.WaterWays);
		},
		onError: err => {
			console.log('waterways Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			await refetch()
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Routes Updated.',
				type: 'success',
			})
			props.navigation.goBack()
			setButtonLoader(false)
		} catch (e) {
			console.log('edit route on catch :', e)
			setButtonLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setButtonLoader(false)
		console.log('updateUser error  :', error)
	}

	async function UpdateRoutes() {
		let status = true
		if (selectedRoutes?.length === 0) {
			FlashMessage({ msg: 'Select Routes', type: 'danger' })
			status = false
			return
		}
		if (status) {
			const data = {
				otherWaterWays: others,
				waterWays: selectedRoutes,
			}
			console.log('data :', data)
			setButtonLoader(true)
			mutate({
				variables: { updateUserInput: data },
			})
		}
	}

	const multiSelect = item => {
		const x = selectedRoutes.indexOf(item._id)
		if (x === -1) {
			setSelectedRoutes(prev => [...prev, item?._id])
		} else {
			selectedRoutes.splice(x, 1)
			setSelectedRoutes(prev => [...prev])
		}
	}

	const routes = arr => {
		const prevRoutes = []
		arr?.forEach(item => {
			prevRoutes.push(item._id)
		})
		setSelectedRoutes(prev => [...prev, ...prevRoutes])
	}

	useEffect(() => {
		routes(user?.waterWays)
		setOthers(user?.otherWaterWays)
	}, [user?.waterWays])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Edit Waterways'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<View style={styles().flex}>
				<FlatList
					data={data?.WaterWays?.results}
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ flexGrow: 1 }}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={[
										styles().fontRegular,
										{
											color: currentTheme.E8E8C8,
											fontSize: 16,
										},
									]}
								>
									{loading ? 'Loading...' : 'No Routes'}
								</Text>
							</View>
						)
					}}
					renderItem={({ item, index }) => {
						return (
							<>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => {
										setShow(!show)
										setIndex(index)
										LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
									}}
									style={[
										styles().flexRow,
										styles().mt25,
										// styles().pb10,
										styles().alignCenter,
										styles().justifyBetween,
										styles().br10,
										styles().zIndex1,
										styles().pall15,
										{
											borderColor: currentTheme.B7B7B7,
											borderWidth: 1,
										},
									]}
								>
									<Text
										numberOfLines={1}
										style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor }]}
									>
										{item.name}
									</Text>
									<MaterialIcons
										name={show && Index === index ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
										size={20}
										color={currentTheme.blackish}
									/>
								</TouchableOpacity>
								{show && Index === index
									? <View
											style={[
												styles().ph20,
												{
													marginHorizontal: 1,
													borderBottomWidth: 1,
													borderLeftWidth: 1,
													borderRightWidth: 1,
													borderColor: currentTheme.B7B7B7,
													borderBottomLeftRadius: 10,
													borderBottomRightRadius: 10,
													bottom: 5,
												},
											]}
										>
											<FlatList
												data={item.subWaterWays}
												showsVerticalScrollIndicator={false}
												contentContainerStyle={{ flexGrow: 1 }}
												renderItem={({ item, index }) => {
													return (
														<TouchableOpacity
															activeOpacity={0.5}
															style={[
																styles().flexRow,
																styles().pb10,
																styles().pt15,
																styles().alignCenter,
																styles().justifyBetween,
																{
																	borderTopWidth: index === 0 ? 0 : 1,
																	borderTopColor: currentTheme.B7B7B7,
																	backgroundColor: currentTheme.white,
																},
															]}
															onPress={() => multiSelect(item)}
														>
															<View style={[styles().flexRow, styles().alignCenter]}>
																<View
																	style={[
																		styles().wh20px,
																		styles().alignCenter,
																		styles().justifyCenter,
																		styles().mr10,
																		styles().bw1,
																		styles().br5,
																		{
																			backgroundColor: selectedRoutes.includes(item._id) ? currentTheme.themeBackground : currentTheme.white,
																			borderColor: currentTheme.themeBackground,
																		},
																	]}
																>
																	{selectedRoutes.includes(item._id) && (
																		<FontAwesome
																			name="check"
																			size={16}
																			color={currentTheme.white}
																		/>
																	)}
																</View>
																<Text
																	numberOfLines={2}
																	style={[styles().fs12, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
																>
																	{item.name}
																</Text>
															</View>
														</TouchableOpacity>
													)
												}}
												keyExtractor={(_item, index) => index.toString()}
												ListFooterComponent={<View style={styles().wh20px} />}
											/>
										</View>
									: null}
							</>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={
						<View style={[styles().mv20]}>
							<View style={[styles().flex, styles().flexRow, styles().alignCenter, styles().mb10]}>
								<CheckBox
									value={toggleOther}
									setValue={boolean => setToggleOther(boolean)}
								/>
								<Text style={[styles().fs14, styles().ml10, styles().fontMedium, { color: currentTheme.black }]}>Other?</Text>
							</View>
							{toggleOther
								? <TextField
										keyboardType="default"
										value={others}
										errorText={othersErr}
										autoCapitalize="none"
										placeholder={'Enter other...'}
										style
										onChangeText={text => {
											setOthersErr(false)
											setOthers(text)
										}}
									/>
								: null}
						</View>
					}
				/>
			</View>
			<View style={[styles().justifyEnd, styles().mb20]}>
				{buttonLoader
					? <Spinner />
					: <ThemeButton
							Title={'Save'}
							onPress={() => UpdateRoutes()}
						/>}
			</View>
		</Layout>
	)
}

export default WizardStep5
