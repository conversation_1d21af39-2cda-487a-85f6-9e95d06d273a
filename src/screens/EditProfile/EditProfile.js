import { Text, View, TouchableOpacity, Image, FlatList, LayoutAnimation, Dimensions, Platform, UIManager } from 'react-native'
import { useContext, useEffect, useState, useRef, useCallback } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Ionicons from '@expo/vector-icons/Ionicons'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import { formatPhoneNumber } from '../../utils/Constants'
import moment from 'moment'
import { useIsFocused } from '@react-navigation/native'
import StarRating from 'react-native-star-rating-widget'
import ProgressLine from '../../component/ProgressLine/ProgressLine'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { Ratings, getMyRecommendations, getMyWaterWayBadges } from '../../apollo/server'
import Loading from '../../context/Loading/Loading'
import { useQuery } from '@apollo/client'
import Badges from '../../component/Badges/Badges'
import AsyncStorage from '@react-native-async-storage/async-storage'
import VideoThumbnail from '../../component/VideoThumbnail/VideoTHumbnail'
import BadgeModal from '../../component/Modals/BadgeModal'

const { width } = Dimensions.get('window')

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const EditProfile = props => {
	const scrollViewRef = useRef(null)
	const isFocus = useIsFocused()
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [asyncUser, setAsyncUser] = useState('')
	const recommendation = props?.route?.params?.recommendation
	const rating = props?.route?.params?.rating
	const [waterways, setWaterways] = useState(null)
	const [badge, setBadge] = useState('')
	const [showBadgeModal, setShowBadgeModal] = useState(false)
	const { isLoader } = useContext(Loading)
	const [readmore, setReadmore] = useState({})
	const [ratings, setRatings] = useState([
		{ percentage: 0, stars: 5 },
		{ percentage: 0, stars: 4 },
		{ percentage: 0, stars: 3 },
		{ percentage: 0, stars: 2 },
		{ percentage: 0, stars: 1 },
	])

	const { data, loading, refetch } = useQuery(getMyRecommendations, {
		fetchPolicy: 'network-only',
		variables: {
			options: {
				limit: 2,
				page: 1,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: _data => {
			// console.log(
			//   'getMyRecommendations res :',
			//   JSON.stringify(data?.getMyRecommendations),
			// );
			isLoader(false)
		},
		onError: err => {
			console.log('getMyRecommendations err :', err)
			// ErrorHandler(err.message);
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	const {
		data: ratingData,
		loading: ratingLoading,
		refetch: refetchRatings,
	} = useQuery(Ratings, {
		errorPolicy: 'all',
		fetchPolicy: 'cache-and-network',
		variables: {
			filters: {
				user: user?._id,
			},
			options: {
				page: 1,
				limit: 100000,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			try {
				// console.log('Ratings res :', JSON.stringify(data?.Ratings));
				const updatedRatings = [...ratings] // Create a copy of the original ratings array
				data.Ratings?.results?.forEach(result => {
					const avgRating = result.avgRating

					// Find the index of the rating in the ratings array that corresponds to the average rating
					const index = updatedRatings.findIndex(rating => rating.stars === Math.round(avgRating))
					if (index !== -1) {
						// Update the percentage based on the calculation
						updatedRatings[index].percentage += 100 / data.Ratings?.results?.length
					}
				})

				// Set the updated ratings state
				setRatings(updatedRatings)
			} catch (error) {
				console.log(error)
			}
		},
		onError: err => {
			console.log('Ratings err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: mybadgesData,
		loading: mybadgesLoader,
		refetch: mybadgesRefetch,
	} = useQuery(getMyWaterWayBadges, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		variables: {
			userId: user?._id,
		},
		onCompleted: _data => {
			// console.log(
			//   'getMyWaterWayBadges res :',
			//   JSON.stringify(data?.getMyWaterWayBadges),
			// );
		},
		onError: err => {
			console.log('getMyWaterWayBadges err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const createParentObjects = async arr => {
		try {
			const otherArr = {
				parentName: 'Other',
				children: [{ name: user?.otherWaterWays }],
			}
			const result = []
			const parentMap = {}
			await arr?.forEach(item => {
				const parentId = item?.parentWaterWay?._id
				if (!parentMap[parentId]) {
					parentMap[parentId] = {
						parentName: item?.parentWaterWay?.name,
						children: [],
					}
				}
				parentMap[parentId].children.push({
					name: item?.name,
					_id: item?._id,
				})
			})

			for (const parentId in parentMap) {
				result?.push(parentMap[parentId])
			}
			if (user?.otherWaterWays) {
				result?.push(otherArr)
			}
			const waterwaysArray = mybadgesData?.getMyWaterWayBadges?.flatMap(item => item?.waterwaysId)

			const updatedParentArray = result?.map(parent => {
				return {
					...parent,
					children: parent?.children?.map(child => {
						const count = waterwaysArray?.filter(waterway => waterway?._id === child?._id).length
						return {
							...child,
							count: count,
						}
					}),
				}
			})
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setWaterways(updatedParentArray)
		} catch (e) {
			console.log('catch waterways filter :', e)
		}
	}

	const scrollToBottom = () => {
		// Use a reference to the ScrollView component to scroll
		if (scrollViewRef.current) {
			scrollViewRef.current.scrollToEnd({ animated: true })
		}
	}

	const handleBadge = useCallback(badge => {
		setBadge(badge)
		setShowBadgeModal(true)
	})

	const _getuser = async () => {
		const userAsync = await AsyncStorage.getItem('user')
		const user = JSON.parse(userAsync)
		setAsyncUser(user)
	}

	// useEffect(() => {}, [isFocus]);

	useEffect(() => {
		createParentObjects(user?.waterWays)
		// getuser();
		refetchRatings()
		if (rating) {
			scrollToBottom()
		}
		if (recommendation) {
			scrollToBottom()
		}
	}, [isFocus, data?.getMyRecommendations, mybadgesData])

	// useEffect(() => {
	//   isLoader(loading);
	// }, [loading]);

	return (
		<Layout
			scrollRef={scrollViewRef}
			LeftIcon={true}
			navigation={props.navigation}
			headerShown={true}
			pagetitle={'Edit Profile'}
			ContentArea={styles().ph20}
		>
			{showBadgeModal && (
				<BadgeModal
					currentUser={'you'}
					badge={badge}
					visible={showBadgeModal}
					onClose={() => setShowBadgeModal(false)}
				/>
			)}
			<View style={[styles().flex]}>
				<View style={[styles().bioSection, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Bio</Text>

						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditBio')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					<View style={styles().sectionContent}>
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<FontAwesome5
								name="user-alt"
								size={18}
								color={currentTheme.themeBackground}
							/>
							<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
								{user?.name ? user?.name : asyncUser?.name ? asyncUser?.name : 'N/A'}
							</Text>
						</View>
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<Ionicons
								name="md-briefcase"
								size={20}
								color={currentTheme.themeBackground}
							/>
							<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
								{user?.companyWorkFor ? `Work at ${user?.companyWorkFor}` : 'Add work'}
							</Text>
						</View>
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<FontAwesome
								name="envelope"
								size={18}
								color={currentTheme.themeBackground}
							/>
							<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>{user?.email}</Text>
						</View>
						{user?.role !== 'deckhand'
							? <View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
									<FontAwesome5
										name="hashtag"
										size={18}
										color={currentTheme.themeBackground}
									/>
									<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
										{user?.refrence_no ? user?.refrence_no : 'N/A'}
									</Text>
								</View>
							: null}
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<MaterialIcons
								name="location-on"
								size={22}
								color={currentTheme.themeBackground}
							/>
							<Text
								numberOfLines={2}
								style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor, marginLeft: 8 }]}
							>
								{user?.address ? user?.address : 'Add Address'}
							</Text>
						</View>
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<FontAwesome
								name="phone"
								size={20}
								color={currentTheme.themeBackground}
							/>
							<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
								{user?.phone ? formatPhoneNumber(user?.phone) : 'Add phone'}
							</Text>
						</View>
					</View>
				</View>
				{user?.role === 'deckhand'
					? <View style={[styles().eduSection, styles().mt15, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
							<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
								<View
									onPress={() => props.navigation.navigate('ToeBoater')}
									activeOpacity={0.7}
								>
									<Text style={[styles().fs16, styles().fw700, { color: currentTheme.headingColor }]}>I Wanna Be A Toe Boater</Text>
								</View>
								<TouchableOpacity
									onPress={() => props.navigation.navigate('ToeBoater')}
									activeOpacity={0.7}
								>
									<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
								</TouchableOpacity>
							</View>
							{user?.deckhand_video
								? <VideoThumbnail
										video={user?.deckhand_video}
										isFocus={isFocus}
									/>
								: <TouchableOpacity
										onPress={() => props.navigation.navigate('ToeBoater')}
										activeOpacity={0.9}
										style={[
											styles().w90,
											styles().bw1,
											styles().alignSelfCenter,
											styles().h80px,
											styles().br10,
											styles().alignCenter,
											styles().justifyCenter,
											{ borderColor: currentTheme.line, borderStyle: 'dashed' },
										]}
									>
										<Text style={[styles().fontRegular, styles().fs14, { color: currentTheme.black }]}>Upload a Video</Text>
									</TouchableOpacity>}
						</View>
					: null}
				<View style={[styles().eduSection, styles().mt15, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Skills</Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditSkills')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					<View style={[styles().flexRow, styles().alignStart]}>
						<MaterialCommunityIcons
							name="lightbulb-on-outline"
							size={25}
							color={currentTheme.themeBackground}
						/>
						<View style={[styles().flex, styles().flexRow, styles().flexWrap, styles().ml10]}>
							{user?.skills?.length !== 0
								? user?.skills?.map((skills, i) => {
										return (
											<View
												key={i}
												style={[
													styles().ph10,
													styles().pv10,
													styles().mr5,
													styles().mb5,
													styles().alignCenter,
													styles().justifyCenter,
													styles().br20,
													{ backgroundColor: currentTheme.ebebeb },
												]}
											>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>{skills}</Text>
											</View>
										)
									})
								: <Text style={[styles().fs14, styles().fw600, { color: currentTheme.themeBackground, marginTop: 3 }]}>Add skills</Text>}
						</View>
					</View>
				</View>
				<View style={[styles().eduSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Education</Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditEducation')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					{user?.educations?.length !== 0
						? user?.educations?.map((education, i) => {
								const { title, startYear, endYear, institute } = education

								return (
									<View
										key={i}
										style={[styles().sectionContent]}
									>
										<View style={[styles().flexRow, styles().mb15, styles().alignStart]}>
											<FontAwesome5
												name="graduation-cap"
												size={20}
												color={currentTheme.themeBackground}
											/>
											<View style={[styles().ml10, styles().flex]}>
												<View style={[styles().flexRow, styles().alignStart, styles().flex]}>
													<Text
														// numberOfLines={1}
														style={[
															styles().fs14,
															styles().mr10,
															styles().fontRegular,
															// styles().flex,
															styles().flexShrink,
															styles().textCapitalize,
															{ color: currentTheme.headingColor },
														]}
													>
														{title}
													</Text>
													<View style={[styles().br20, styles().pv5, styles().ph15, { backgroundColor: currentTheme.EEE8D5 }]}>
														<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.themeBackground }]}>
															{`${moment(startYear).year()} - ${moment(endYear).year()}`}
														</Text>
													</View>
												</View>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.lightGold }]}>{institute}</Text>
											</View>
										</View>
									</View>
								)
							})
						: <View style={[styles().mb5]}>
								<Text style={[styles().fs12, styles().fw600, { color: currentTheme.themeBackground }]}>No Education</Text>
							</View>}
				</View>

				<View style={[styles().workSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Work & Experience</Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditWork')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					{user?.experiances?.length !== 0
						? user?.experiances?.map((experience, i) => {
								const { title, startYear, endYear, company } = experience
								return (
									<View
										key={i}
										style={styles().sectionContent}
									>
										<View style={[styles().flexRow, styles().mb15, styles().alignStart]}>
											<Ionicons
												name="md-briefcase"
												size={20}
												color={currentTheme.themeBackground}
											/>
											<View style={[styles().ml10, styles().flex]}>
												<View style={[styles().flexRow, styles().alignStart, styles().flex]}>
													<Text
														// numberOfLines={1}
														style={[
															styles().fs14,
															styles().mr10,
															styles().fontRegular,
															styles().flexShrink,
															styles().textCapitalize,
															{ color: currentTheme.headingColor },
														]}
													>
														{title}
													</Text>

													<View style={[styles().br20, styles().pv5, styles().ph15, { backgroundColor: currentTheme.EEE8D5 }]}>
														<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.themeBackground }]}>
															{`${moment(startYear).year()} - ${moment(endYear).year()}`}
														</Text>
													</View>
												</View>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.lightGold }]}>{company}</Text>
											</View>
										</View>
									</View>
								)
							})
						: <View style={[styles().mb5]}>
								<Text style={[styles().fs12, styles().fw600, { color: currentTheme.themeBackground }]}>No Work & Experience</Text>
							</View>}
				</View>

				<View style={[styles().waterWaysSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb10, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Routes on Waterways</Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('EditRoutes')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Edit</Text>
						</TouchableOpacity>
					</View>
					{waterways?.map((ways, i) => {
						return (
							<View
								key={i}
								style={[styles().sectionContent]}
							>
								<Text style={[styles().fs14, styles().fontMedium, styles().mb5, { color: currentTheme.themeBackground }]}>
									{/* {`${ways?.children?.length}) ${ways.parentName}`} */}
									{`${i + 1}) ${ways?.parentName}`}
								</Text>
								<View style={[styles().pl15]}>
									{ways?.children?.map((child, j) => {
										return (
											<View
												key={j}
												style={[styles().flexRow, styles().alignCenter, styles().mb10, styles().flex, styles().flexWrap]}
											>
												<View
													style={[
														styles().br5,
														styles().mr5,
														styles().wh5px,
														{
															backgroundColor: currentTheme.themeBackground,
														},
													]}
												/>
												<Text
													style={[
														styles().fs12,
														styles().fontRegular,
														{
															color: currentTheme.headingColor,
														},
													]}
												>
													{child?.name ? child?.name : 'N/A'}
												</Text>
												{child?.count > 0 && (
													<View
														style={[
															styles().pv5,
															styles().ph10,
															styles().ml5,
															styles().br100,
															styles().flexRow,
															styles().alignCenter,
															{ backgroundColor: currentTheme.black },
														]}
													>
														<Text style={[styles().fs12, styles().mr5, styles().fontRegular, { color: currentTheme.white }]}>{child?.count}</Text>
														<Image
															source={require('../../assets/images/badge_success.png')}
															style={[styles().wh15px, { tintColor: currentTheme.white }]}
														/>
													</View>
												)}
											</View>
										)
									})}
								</View>
							</View>
						)
					})}
				</View>

				<View style={[styles().waterWaysSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb10, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Company Badges</Text>
						<TouchableOpacity
							onPress={() => props.navigation.navigate('AllBadges')}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>View All</Text>
						</TouchableOpacity>
					</View>
					<FlatList
						// data={badgesData?.getMyBadges}
						data={mybadgesData?.getMyWaterWayBadges?.slice(0, 2)}
						showsVerticalScrollIndicator={false}
						renderItem={({ index, item }) => (
							<Badges
								index={index}
								badge={item}
								callback={badge => handleBadge(badge)}
							/>
						)}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().mv10, styles().flex, { width: width * 0.9 }]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontSize: 14,
										}}
									>
										{mybadgesLoader ? 'Loading...' : ' No Bagdes'}
									</Text>
								</View>
							)
						}}
					/>
				</View>
				{/* <View
          style={[
            styles().docSection,
            styles().mt20,
            styles().pb10,
            styles().bbw1,
            {borderBottomColor: currentTheme.c707070},
          ]}>
          <View
            style={[
              styles().sectionHead,
              styles().mb20,
              styles().flexRow,
              styles().alignCenter,
              styles().justifyBetween,
            ]}>
            <Text
              style={[
                styles().fs20,
                styles().fw700,
                {color: currentTheme.headingColor},
              ]}>
              Documents
            </Text>
            {!edit ? null : (
              <TouchableOpacity
                // onPress={() => props.navigation.navigate('EditDocument')}
                onPress={() => props.navigation.navigate('EditTWIC')}
                activeOpacity={0.7}>
                <Text
                  style={[
                    styles().fs16,
                    styles().fontRegular,
                    {color: currentTheme.B7B7B7},
                  ]}>
                  Edit
                </Text>
              </TouchableOpacity>
            )}
          </View>
          <View style={styles().sectionContent}>
            <View style={[styles().mb25]}>
              <FlatList
                data={docs}
                showsVerticalScrollIndicator={false}
                horizontal
                onEndReachedThreshold={0.75}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{flexGrow: 1}}
                ListEmptyComponent={() => {
                  return (
                    <View
                      style={[
                        styles().alignCenter,
                        styles().justifyCenter,

                        styles().flex,
                      ]}>
                      <Text
                        style={{
                          color: currentTheme.E8E8C8,
                          fontSize: 14,
                        }}>
                        {'No Documents'}
                      </Text>
                    </View>
                  );
                }}
                renderItem={({item, index}) => {
                  let docType = get_url_extension(item?.image);
                  let icon =
                    docType === 'pdf' ? (
                      <FontAwesome5
                        name="file-pdf"
                        size={45}
                        color={currentTheme.themeBackground}
                      />
                    ) : docType === 'docx' || docType === 'doc' ? (
                      <FontAwesome
                        name="file-word-o"
                        size={45}
                        color={currentTheme.themeBackground}
                      />
                    ) : docType === 'png' ||
                      docType === 'jpeg' ||
                      docType === 'jpg' ? (
                      <Image
                        source={{uri: item?.image}}
                        style={styles().wh100}
                        resizeMode="cover"
                      />
                    ) : (
                      <Entypo
                        name="plus"
                        size={45}
                        color={currentTheme.themeBackground}
                      />
                    );
                  return (
                    <View
                      key={index}
                      style={[styles().alignCenter, styles().mr10]}>
                      <View
                        style={[
                          styles().wh100px,
                          styles().overflowH,
                          styles().br10,
                          styles().mb5,
                          styles().alignCenter,
                          styles().justifyCenter,
                          {
                            borderWidth: 0.5,
                            borderColor: currentTheme.themeBackground,
                          },
                        ]}>
                        {icon}
                      </View>
                      <Text
                        numberOfLines={2}
                        style={{
                          fontSize: 10,
                          color: currentTheme.themeBackground,
                        }}>
                        {item?.name}
                      </Text>
                    </View>
                  );
                }}
                keyExtractor={(item, index) => index.toString()}
                ListFooterComponent={<View style={styles().wh20px} />}
              />
            </View>
          </View>
        </View> */}

				{/* <View
          style={[
            styles().docSection,
            styles().mt20,
            styles().pb10,
            styles().bbw1,
            {borderBottomColor: currentTheme.c707070},
          ]}>
          <View
            style={[
              styles().sectionHead,
              styles().mb20,
              styles().flexRow,
              styles().alignCenter,
              styles().justifyBetween,
            ]}>
            <Text
              style={[
                styles().fs20,
                styles().fw700,
                {color: currentTheme.headingColor},
              ]}>
              Bank Details
            </Text>
            {!edit ? null : (
              <TouchableOpacity
                onPress={() => props.navigation.navigate('AddBankAccount')}
                activeOpacity={0.7}>
                <Text
                  style={[
                    styles().fs16,
                    styles().fontRegular,
                    {color: currentTheme.B7B7B7},
                  ]}>
                  Edit
                </Text>
              </TouchableOpacity>
            )}
          </View>
          {user?.routingNo ? (
            <ImageBackground
              source={require('../../assets/images/bankcard.png')}
              resizeMode="contain"
              style={{width: '100%', height: 200, marginBottom: 20}}>
              <View
                style={[
                  styles().flex,
                  {
                    justifyContent: 'space-between',
                    paddingTop: 20,
                    paddingBottom: 20,
                    paddingHorizontal: 15,
                  },
                ]}>
                <View style={[styles().ph20]}>
                  <Text
                    numberOfLines={2}
                    style={[
                      styles().fontBold,
                      styles().fs16,
                      {color: currentTheme.white},
                    ]}>{`Account Number:`}</Text>
                  <Text
                    style={[
                      styles().fontMedium,
                      styles().fs16,
                      styles().mb5,
                      {color: currentTheme.white, letterSpacing: 1},
                    ]}>
                    {user?.bankAccountNo}
                  </Text>
                  <Text
                    style={[
                      styles().fontBold,
                      styles().fs16,
                      {color: currentTheme.white, letterSpacing: 1},
                    ]}>{`Routing No:`}</Text>
                  <Text
                    style={[
                      styles().fontMedium,
                      styles().fs16,
                      {color: currentTheme.white, letterSpacing: 1},
                    ]}>{`${user?.routingNo}`}</Text>
                </View>
                <View
                  style={[
                    styles().flexRow,
                    styles().alignCenter,
                    styles().justifyBetween,
                    styles().ph20,
                  ]}>
                  <Text
                    numberOfLines={2}
                    style={[
                      styles().fontMedium,
                      styles().flex,
                      styles().fs14,
                      styles().textCapitalize,
                      {color: currentTheme.white},
                    ]}>
                    {user?.bankName}
                  </Text>
                </View>
              </View>
            </ImageBackground>
          ) : (
            <View style={[styles().mb5]}>
              <Text
                style={[
                  styles().fs12,
                  styles().fw600,
                  {color: currentTheme.themeBackground},
                ]}>
                No Bank Account
              </Text>
            </View>
          )}
        </View> */}

				<View style={[styles().docSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Reviews & Ratings</Text>
						<TouchableOpacity
							onPress={() =>
								props.navigation.navigate('Reviews', {
									user: user,
									overallRatings: ratings,
								})
							}
							activeOpacity={0.7}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>View Reviews</Text>
						</TouchableOpacity>
					</View>
					<View style={styles().sectionContent}>
						<View
							style={[
								styles().mt10,
								styles().br10,
								styles().flexRow,
								styles().alignCenter,
								styles().justifyBetween,
								styles().pall20,
								{ backgroundColor: currentTheme.F8F9FA },
							]}
						>
							<View style={[styles().mr15, styles().mt10]}>
								<Text style={[styles().fs32, styles().mb10, styles().fw600, { color: currentTheme.headingColor }]}>
									{user?.ratings ? user?.ratings?.toFixed(1) : 0}
									<Text style={[styles().fs20, styles().ml10, styles().fw600, { color: currentTheme.headingColor }]}>/5</Text>
								</Text>
								<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>
									{`Based on ${user?.ratingsCount ? user?.ratingsCount : 0} Review`}
								</Text>
								<View style={[styles().flexRow, styles().mt15]}>
									<StarRating
										onChange={() => {}}
										enableHalfStar={true}
										starSize={20}
										starStyle={styles().ml5}
										maxStars={5}
										rating={user?.ratings}
										color={currentTheme.starColor}
										emptyColor={currentTheme.e6e6e6}
									/>
								</View>
							</View>
							<View style={[styles().flex]}>
								{ratings.map((rating, i) => {
									return (
										<View
											key={i}
											style={[styles().flexRow, styles().flex, styles().alignCenter, styles().justifyBetween]}
										>
											<Text style={[styles().fs12, styles().mr10, { color: currentTheme.textColor }]}>{`${rating?.stars} Star`}</Text>
											<View style={[styles().flex, styles().overflowH, styles().br10, { height: 8, backgroundColor: currentTheme.e6e6e6 }]}>
												<ProgressLine progress={rating?.percentage} />
											</View>
										</View>
									)
								})}
							</View>
						</View>
					</View>
				</View>

				<View style={[styles().docSection, styles().mt10, styles().pb10]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Recommendation</Text>
					</View>
					<View style={styles().sectionContent}>
						{data?.getMyRecommendations?.results?.length === 0
							? <View style={{ alignItems: 'center', marginVertical: 15 }}>
									<Text
										style={[
											styles().fontRegular,
											styles().fs12,
											{
												color: currentTheme.c737373,
											},
										]}
									>
										{'No Reccomendations'}
									</Text>
								</View>
							: data?.getMyRecommendations?.results?.map((item, index) => {
									return (
										<View
											key={index}
											style={[
												styles().flexRow,
												styles().pb15,
												styles().flex,
												index !== 0 && {
													paddingTop: 15,
													borderTopWidth: 1,
													borderTopColor: currentTheme.c707070,
												},
											]}
										>
											{item?.user?.photo
												? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
														<Image
															source={{ uri: item?.user?.photo }}
															style={styles().wh100}
															resizeMode="cover"
														/>
													</View>
												: <View
														style={[
															styles().overflowH,
															styles().justifyCenter,
															styles().alignCenter,
															styles().br50,
															styles().wh40px,
															{
																borderWidth: 1,
																borderColor: currentTheme.themeBackground,
															},
														]}
													>
														<FontAwesome5
															name="user-alt"
															size={16}
															color={currentTheme.themeBackground}
														/>
													</View>}
											<View style={[styles().ml10, styles().flex]}>
												<Text style={[styles().fs14, styles().fw700, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
													{item?.user?.name}
												</Text>
												<View style={[styles().flexRow, styles().alignCenter]}>
													<Text style={[styles().fs12, styles().mr5, styles().fw600, styles().textCapitalize, { color: currentTheme.headingColor }]}>
														{item?.user?.role === 'matePilot' ? 'Mate Pilot' : item?.user?.role}
													</Text>
													<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).format('LL')}</Text>
												</View>
												<View style={[styles().mt5, styles().flex]}>
													<Text
														numberOfLines={readmore[`readmore${index}`] === undefined ? 3 : undefined}
														style={[styles().fs12, styles().lh18, styles().fontRegular, { color: currentTheme.headingColor }]}
													>
														{item.text}
													</Text>
													{!readmore[`readmore${index}`] && item?.text?.length > 150
														? <TouchableOpacity
																onPress={() => {
																	LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
																	setReadmore({
																		...readmore,
																		[`readmore${index}`]: true,
																	})
																}}
															>
																<Text style={[styles().fs12, styles().fw600, { color: currentTheme.c737373 }]}>read more...</Text>
															</TouchableOpacity>
														: null}
												</View>
											</View>
										</View>
									)
								})}
						<View style={[styles().mb20]}>
							<ThemeButton
								Title={'Show All'}
								onPress={() =>
									props.navigation.navigate('Recommendation', {
										userId: user?._id,
									})
								}
								StyleText={{ color: currentTheme.headingColor }}
								Style={[
									styles().br10,
									{
										backgroundColor: currentTheme.C3C3C3,
										borderColor: currentTheme.C3C3C3,
									},
								]}
							/>
						</View>
					</View>
				</View>
			</View>
		</Layout>
	)
}

export default EditProfile
