import { Text, View, Keyboard, TouchableOpacity, LayoutAnimation, Platform, StyleSheet } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Feather from '@expo/vector-icons/Feather'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import { updateUser, languages } from '../../apollo/server'
import UserContext from '../../context/User/User'
import Dropdown from '../../component/DropDown/Dropdopwn'
import DateTimePicker from '@react-native-community/datetimepicker'
import moment from 'moment'
import AsyncStorage from '@react-native-async-storage/async-storage'
import fontStyles from '../../utils/fonts/fontStyles'

const EditBio = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const user = useContext(UserContext)
	const check = user?.role === 'deckhand' || user?.role === 'tankerman'
	const check2 = user?.role === 'captain' || user?.role === 'master'
	const [fullName, setFullName] = useState('')
	const [fullNameError, setFullNameError] = useState(false)
	const [middleName, setMiddleName] = useState('')
	const [middleNameErr, setMiddleNameErr] = useState(false)
	const [lastName, setLastName] = useState('')
	const [lastNameErr, setLastNameErr] = useState(false)
	const [suffix, setSuffix] = useState('')
	const [suffixErr, setSuffixErr] = useState(false)
	const [email, setEmail] = useState('')
	const [emailError, setEmailError] = useState(false)
	const [phone, setPhone] = useState('')
	const [about, setAbout] = useState('')
	const [aboutErr, setAboutErr] = useState(false)
	const [aboutLength, _setAboutLength] = useState(500)
	const [address, setAddress] = useState('')
	const [city, setcity] = useState('')
	const [cityError, setcityError] = useState(false)
	const [state, setState] = useState('')
	const [stateError, setStateError] = useState(false)
	const [country, setCountry] = useState('')
	const [countryError, setCountryError] = useState(false)
	const [addressError, setAddressError] = useState(false)
	const [phoneError, setPhoneError] = useState(false)
	const [headLine, setHeadline] = useState(false)
	const [fleet, setFleet] = useState(false)
	const [Loading, setLoading] = useState(false)
	const [companyWorkFor, setCompanyWorkFor] = useState('')
	const [refrence_no, setRefrence_no] = useState('')
	const [companyWorkForError, setCompanyWorkForError] = useState(false)
	const [haveDE, setHaveDE] = useState(false)
	const [gender, setGender] = useState('')
	const genderList = ['Male', 'Female', 'Other']
	const [Languages, setLanguages] = useState('')
	const [languageList, setlanguageList] = useState([])
	const [datePicker, setDatePicker] = useState(false)
	const [dob, setDOB] = useState(new Date())
	const [hourlyRate, setHourlyRate] = useState('')
	const [hourlyRateErr, setHourlyRateErr] = useState(false)
	const [experience, setExperience] = useState('')
	const [experienceError, setExperienceError] = useState(false)
	const [tankedForCompanies, setTankedForCompanies] = useState('')
	const [tankedForCompaniesErr, setTankedForCompaniesErr] = useState(false)
	const [numberOfBargesPushThroughRiver, setNumberOfBargesPushThroughRiver] = useState('')
	const [numberOfBargesPushThroughRiverErr, setNumberOfBargesPushThroughRiverErr] = useState(false)
	const [numberOfBargesPushThroughCanals, setNumberOfBargesPushThroughCanals] = useState('')
	const [numberOfBargesPushThroughCanalsErr, setNumberOfBargesPushThroughCanalsErr] = useState(false)

	function showDatePicker() {
		setDatePicker(!datePicker)
	}

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
			await AsyncStorage.setItem('user', JSON.stringify(data?.updateUser))
			FlashMessage({
				msg: 'Profile Updated!',
				type: 'success',
			})
			setLoading(false)
			props.navigation.goBack()
		} catch (e) {
			console.log('catch updateUser:', e)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setLoading(false)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('updateUser error  :', error)
	}

	const { data, loading, error, refetch } = useQuery(languages, {
		fetchPolicy: 'no-cache',
		variables: {
			options: {
				limit: 1000,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log('Languages res :', JSON.stringify(data?.Languages));
			setlanguageList(data?.Languages?.results)
		},
		onError: err => {
			console.log('Languages err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function UpdateBio() {
		let status = true
		if (fullName === '') {
			setFullNameError(true)
			FlashMessage({ msg: 'Enter First Name', type: 'warning' })
			status = false
			return
		}
		if (fullName?.length < 3) {
			setFullNameError(true)
			FlashMessage({
				msg: 'First name length must be 3 characters.',
				type: 'warning',
			})
			status = false
			return
		}
		// if (phone === '') {
		//   setPhoneError(true);
		//   status = false;
		//   return;
		// }
		if (status) {
			Keyboard.dismiss()
			setLoading(true)
			const data = {
				// name: fullName?.trim(),
				first_name: fullName,
				middle_name: middleName,
				last_name: lastName,
				name_initial: suffix,
				phone: phone?.trim(),
				canYouHeadline: headLine,
				workedInFleetBefore: fleet,
				address: address,
				companyWorkFor: companyWorkFor,
				gender: gender,
				dob: dob,
				state: state,
				country: country,
				city: city,
				languages: [Languages],
				haveDE: haveDE,
				about: about?.trim(),
				hourlyRate: hourlyRate,
				yearsOfExperience: experience,
				tankedForCompanies: tankedForCompanies,
				numberOfBargesPushThroughRiver: numberOfBargesPushThroughRiver,
				numberOfBargesPushThroughCanals: numberOfBargesPushThroughCanals,
			}

			const _checkRole = user?.role

			await mutate({
				variables: {
					updateUserInput: data,
				},
			})
		}
	}

	useEffect(() => {
		setEmail(user?.email)
		setFullName(user?.first_name)
		setMiddleName(user?.middle_name)
		setLastName(user?.last_name)
		setSuffix(user?.name_initial)
		setPhone(user?.phone)
		setHeadline(user?.canYouHeadline)
		setFleet(user?.workedInFleetBefore)
		setAddress(user?.address)
		setCompanyWorkFor(user?.companyWorkFor)
		setDOB(new Date(user?.dob))
		setGender(user?.gender)
		setLanguages(user?.languages[0])
		setHaveDE(user?.haveDE)
		setAbout(user?.about)
		setRefrence_no(user?.refrence_no)
		setHourlyRate(user?.hourlyRate)
		setExperience(user?.yearsOfExperience)
		setTankedForCompanies(user?.tankedForCompanies[0])
		setNumberOfBargesPushThroughRiver(user?.numberOfBargesPushThroughRiver)
		setNumberOfBargesPushThroughCanals(user?.numberOfBargesPushThroughCanals)
		setcity(user?.city)
		setState(user?.state)
		setCountry(user?.country)
	}, [isFocus])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Edit Bio'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={65}
		>
			<View style={[styles().flex]}>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>First Name</Text>
					<TextField
						keyboardType="default"
						value={fullName}
						errorText={fullNameError}
						autoCapitalize="none"
						placeholder={'First Name'}
						label={'First Name'}
						onChangeText={text => {
							setFullNameError(false)
							setFullName(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Middle Name</Text>
					<TextField
						keyboardType="default"
						value={middleName}
						label={'Middle Name'}
						errorText={middleNameErr}
						autoCapitalize="none"
						placeholder={'Middle Name'}
						onChangeText={text => {
							setMiddleNameErr(false)
							setMiddleName(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Last Name</Text>
					<TextField
						keyboardType="default"
						value={lastName}
						errorText={lastNameErr}
						autoCapitalize="none"
						placeholder={'Last Name'}
						label={'Last Name'}
						onChangeText={text => {
							setLastNameErr(false)
							setLastName(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Suffix</Text>
					<TextField
						keyboardType="default"
						value={suffix}
						errorText={suffixErr}
						autoCapitalize="none"
						placeholder={'Suffix'}
						label={'Suffix'}
						onChangeText={text => {
							setSuffixErr(false)
							setSuffix(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Email</Text>
					<TextField
						keyboardType="default"
						editable={false}
						value={email}
						errorText={emailError}
						autoCapitalize="none"
						placeholder={'Email'}
						label={'Email'}
						onChangeText={text => {
							setEmailError(false)
							setEmail(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				{user?.role !== 'deckhand'
					? <View style={[styles().mb20]}>
							<Text style={customStyles.text}>Reference number</Text>
							<TextField
								keyboardType="default"
								editable={false}
								value={refrence_no}
								autoCapitalize="none"
								placeholder={'Reference number'}
								label={'Reference number'}
								onChangeText={_text => {
									setRefrence_no(false)
								}}
								style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							/>
						</View>
					: null}
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Phone</Text>
					<TextField
						keyboardType="default"
						value={phone}
						maxLength={11}
						errorText={phoneError}
						autoCapitalize="none"
						placeholder={'Phone'}
						label={'Phone'}
						onChangeText={text => {
							setPhoneError(false)
							setPhone(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Per Day Rate</Text>
					<TextField
						keyboardType="numeric"
						value={hourlyRate}
						errorText={hourlyRateErr}
						autoCapitalize="none"
						placeholder={'Per Day Rate'}
						label={'Per Day Rate'}
						onChangeText={text => {
							setHourlyRateErr(false)
							setHourlyRate(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().w100, styles().mb20]}>
					<Dropdown
						placeholder={gender ? gender : 'Gender'}
						data={genderList}
						selectedValue={value => setGender(value)}
					/>
				</View>

				<View style={[styles().mb20]}>
					<TouchableOpacity
						onPress={() => {
							showDatePicker()
						}}
						style={[
							styles().w100,
							styles().h50px,
							styles().bw1,
							styles().br10,
							styles().justifyBetween,
							styles().ph20,
							styles().alignCenter,
							styles().flexRow,
							{
								borderColor: currentTheme.B7B7B7,
							},
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>{dob ? moment(dob).format('LL') : 'Date of Birth'}</Text>
						<Feather
							name="calendar"
							color={currentTheme.C3C3C3}
							size={20}
						/>
					</TouchableOpacity>
				</View>
				{datePicker && (
					<>
						<DateTimePicker
							testID="dateTimePicker"
							minimumDate={new Date('1947-08-14')}
							value={dob ? dob : new Date()}
							mode={'date'}
							themeVariant="light"
							textColor={currentTheme.themeBackground}
							accentColor="blue"
							positiveButton={{
								label: 'OK',
								textColor: currentTheme.themeBackground,
							}}
							negativeButton={{
								label: 'Cancel',
								textColor: currentTheme.themeBackground,
							}}
							display={Platform.OS === 'ios' ? 'spinner' : 'default'}
							is24Hour={true}
							onChange={(event, value) => {
								if (event.type === 'dismissed') {
									// Handle cancel button press
									setDatePicker(false)
									return
								}
								if (event.type === 'set' && Platform.OS !== 'ios') {
									setDatePicker(false)
								}

								const currentDate = value || dob
								setDOB(currentDate)
								// console.log(currentDate)
								// setEducationValues({
								//   ...educationValues,
								//   [`from${index}`]: currentDate,
								// });
								// let date = (educationForm[index].startYear = currentDate);
								// let newData = [...educationForm];
								// newData[index].startYear = date?.toString();
								// setEducationForm(newData);
							}}
							style={{
								alignSelf: 'center',
								justifyContent: 'center',
								alignItems: 'flex-start',
								// width: "90%",
								// height: "70%",
							}}
						/>
						{Platform.OS === 'ios' && (
							<View
								style={[
									styles().alignSelfCenter,
									styles().justifyCenter,
									styles().alignCenter,
									styles().mb30,
									styles().bw1,
									styles().br5,
									{ borderColor: currentTheme.themeBackground },
								]}
							>
								<TouchableOpacity
									style={[styles().ph30, styles().pv5]}
									onPress={() => {
										setDatePicker(false)
									}}
								>
									<Text style={[styles().fontMedium, styles().fs14, { color: currentTheme.themeBackground }]}>Close</Text>
								</TouchableOpacity>
							</View>
						)}
					</>
				)}
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Currently working</Text>
					<TextField
						keyboardType="default"
						value={companyWorkFor}
						errorText={companyWorkForError}
						autoCapitalize="none"
						placeholder={'Currently working'}
						label={'Currently working'}
						onChangeText={text => {
							setCompanyWorkForError(false)
							setCompanyWorkFor(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>City</Text>
					<TextField
						keyboardType="default"
						value={city}
						errorText={cityError}
						autoCapitalize="none"
						placeholder={'City'}
						label={'City'}
						onChangeText={text => {
							setcityError(false)
							setcity(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Address</Text>
					<TextField
						keyboardType="default"
						value={address}
						errorText={addressError}
						autoCapitalize="none"
						placeholder={'Address'}
						label={'Address'}
						onChangeText={text => {
							setAddressError(false)
							setAddress(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>State</Text>
					<TextField
						keyboardType="default"
						value={state}
						errorText={stateError}
						autoCapitalize="none"
						placeholder={'State'}
						label={'State'}
						onChangeText={text => {
							setStateError(false)
							setState(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Country</Text>
					<TextField
						keyboardType="default"
						value={country}
						errorText={countryError}
						autoCapitalize="none"
						placeholder={'Country'}
						label={'Country'}
						onChangeText={text => {
							setCountryError(false)
							setCountry(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>About</Text>
					<TextField
						keyboardType="default"
						value={about}
						maxLength={aboutLength}
						errorText={aboutErr}
						autoCapitalize="none"
						placeholder={'about...'}
						label={'About'}
						multiline={true}
						style={[
							styles().bw1,
							styles().br10,
							styles().overflowH,
							{
								borderColor: currentTheme.B7B7B7,
							},
						]}
						stylesInput={[styles().h150px, { textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top' }]}
						onChangeText={text => {
							setAboutErr(false)
							setAbout(text)
						}}
					/>
					<Text
						style={{
							marginTop: 5,
							fontSize: 10,
							alignSelf: 'flex-end',
							color: currentTheme.c737373,
						}}
					>{`${aboutLength - about?.length}/${aboutLength}`}</Text>
				</View>
				<View style={[styles().w100, styles().mb20]}>
					<Dropdown
						placeholder={Languages ? Languages : 'Language'}
						data={languageList}
						selectedValue={value => setLanguages(value)}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Text style={customStyles.text}>Years of experience</Text>
					<TextField
						keyboardType="numeric"
						value={experience}
						errorText={experienceError}
						autoCapitalize="none"
						placeholder={'How many years of experience?'}
						label={'Years of experience?'}
						onChangeText={text => {
							setExperienceError(false)
							setExperience(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
					/>
				</View>
				{user?.role === 'tankerman'
					? <View style={[styles().mb20]}>
							<Text style={customStyles.text}>Companies have you tanked for</Text>
							<TextField
								keyboardType="default"
								value={tankedForCompanies}
								errorText={tankedForCompaniesErr}
								autoCapitalize="none"
								label={'Companies have you tanked for'}
								placeholder={'What Companies have you tanked for?'}
								onChangeText={text => {
									setTankedForCompaniesErr(false)
									setTankedForCompanies(text)
								}}
								style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							/>
						</View>
					: null}
				{check
					? null
					: <>
							<View style={[styles().mb20]}>
								<Text style={customStyles.text}>Barges have you pushed through river</Text>
								<TextField
									keyboardType="numeric"
									value={numberOfBargesPushThroughRiver}
									errorText={numberOfBargesPushThroughRiverErr}
									autoCapitalize="none"
									placeholder={'Barges have you pushed through river?'}
									label={'Barges have you pushed through river?'}
									onChangeText={text => {
										setNumberOfBargesPushThroughRiverErr(false)
										setNumberOfBargesPushThroughRiver(text)
									}}
									style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
								/>
							</View>
							<View style={[styles().mb20]}>
								<Text style={customStyles.text}>Barges have you pushed through canal</Text>
								<TextField
									keyboardType="numeric"
									value={numberOfBargesPushThroughCanals}
									errorText={numberOfBargesPushThroughCanalsErr}
									autoCapitalize="none"
									placeholder={'Barges have you pushed through canal?'}
									label={'Barges have you pushed through canal?'}
									onChangeText={text => {
										setNumberOfBargesPushThroughCanalsErr(false)
										setNumberOfBargesPushThroughCanals(text)
									}}
									style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
								/>
							</View>
						</>}
				<View style={[styles().mb25]}>
					<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Can you headline?</Text>
					<View style={[styles().flexRow, styles().alignCenter]}>
						<TouchableOpacity
							onPress={() => setHeadline(true)}
							style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
						>
							<View
								style={[
									styles().wh25px,
									styles().mr5,
									styles().alignCenter,
									styles().justifyCenter,
									styles().br15,
									styles().bw1,
									{
										backgroundColor: headLine ? currentTheme.themeBackground : null,
										borderColor: headLine ? currentTheme.themeBackground : currentTheme.B7B7B7,
									},
								]}
							>
								{headLine
									? <FontAwesome
											name="check"
											size={16}
											color={currentTheme.white}
										/>
									: null}
							</View>
							<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={() => setHeadline(false)}
							style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
						>
							<View
								style={[
									styles().wh25px,
									styles().mr5,
									styles().alignCenter,
									styles().justifyCenter,
									styles().br15,
									styles().bw1,
									{
										backgroundColor: headLine === false ? currentTheme.themeBackground : null,
										borderColor: headLine === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
									},
								]}
							>
								{headLine === false
									? <FontAwesome
											name="check"
											size={16}
											color={currentTheme.white}
										/>
									: null}
							</View>
							<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
						</TouchableOpacity>
					</View>
				</View>
				<View style={[styles().mb25]}>
					<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Have you worked in a fleet before ?</Text>
					<View style={[styles().flexRow, styles().alignCenter]}>
						<TouchableOpacity
							onPress={() => setFleet(true)}
							style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
						>
							<View
								style={[
									styles().wh25px,
									styles().mr5,
									styles().alignCenter,
									styles().justifyCenter,
									styles().br15,
									styles().bw1,
									{
										backgroundColor: fleet ? currentTheme.themeBackground : null,
										borderColor: fleet ? currentTheme.themeBackground : currentTheme.B7B7B7,
									},
								]}
							>
								{fleet
									? <FontAwesome
											name="check"
											size={16}
											color={currentTheme.white}
										/>
									: null}
							</View>
							<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={() => setFleet(false)}
							style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
						>
							<View
								style={[
									styles().wh25px,
									styles().mr5,
									styles().alignCenter,
									styles().justifyCenter,
									styles().br15,
									styles().bw1,
									{
										backgroundColor: fleet === false ? currentTheme.themeBackground : null,
										borderColor: fleet === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
									},
								]}
							>
								{fleet === false
									? <FontAwesome
											name="check"
											size={16}
											color={currentTheme.white}
										/>
									: null}
							</View>
							<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
						</TouchableOpacity>
					</View>
				</View>
				{check2
					? <View style={[styles().mb25]}>
							<Text style={[styles().fs16, styles().mb10, styles().fontRegular, { color: currentTheme.headingColor }]}>Do You Have a D.E?</Text>
							<View style={[styles().flexRow, styles().alignCenter]}>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => setHaveDE(true)}
									style={[styles().flexRow, styles().mr35, styles().alignCenter, styles().justifyCenter]}
								>
									<View
										style={[
											styles().wh25px,
											styles().mr5,
											styles().alignCenter,
											styles().justifyCenter,
											styles().br15,
											styles().bw1,
											{
												backgroundColor: haveDE ? currentTheme.themeBackground : null,
												borderColor: haveDE ? currentTheme.themeBackground : currentTheme.B7B7B7,
											},
										]}
									>
										{haveDE
											? <FontAwesome
													name="check"
													size={16}
													color={currentTheme.white}
												/>
											: null}
									</View>
									<Text style={[styles().fs14, styles().fontRegular]}>Yes</Text>
								</TouchableOpacity>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => setHaveDE(false)}
									style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
								>
									<View
										style={[
											styles().wh25px,
											styles().mr5,
											styles().alignCenter,
											styles().justifyCenter,
											styles().br15,
											styles().bw1,
											{
												backgroundColor: haveDE === false ? currentTheme.themeBackground : null,
												borderColor: haveDE === false ? currentTheme.themeBackground : currentTheme.B7B7B7,
											},
										]}
									>
										{haveDE === false
											? <FontAwesome
													name="check"
													size={16}
													color={currentTheme.white}
												/>
											: null}
									</View>
									<Text style={[styles().fs14, styles().fontRegular]}>No</Text>
								</TouchableOpacity>
							</View>
						</View>
					: null}
				<View style={[styles().justifyEnd, styles().flex, styles().pb20]}>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => UpdateBio()}
								Title={'Save'}
								Style={styles().br10}
							/>}
				</View>
			</View>
		</Layout>
	)
}

export default EditBio

const customStyles = StyleSheet.create({
	text: {
		fontSize: 14,
		fontFamily: fontStyles.PoppinsRegular,
		color: '#2A241A',
		marginBottom: 5,
	},
})
