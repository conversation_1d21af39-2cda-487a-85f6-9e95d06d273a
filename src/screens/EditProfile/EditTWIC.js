import { Text, View, TouchableOpacity, Image, ActivityIndicator, FlatList, LayoutAnimation } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import { updateUser } from '../../apollo/server'
import UserContext from '../../context/User/User'
import { get_url_extension } from '../../utils/Constants'

const EditTWIC = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const user = useContext(UserContext)
	const [twicFront, setTwicFront] = useState('')
	const [twicBack, setTwicBack] = useState('')
	const [Loader, setLoader] = useState(false)
	const [isImagePicker, setIsImagePicker] = useState(false)
	const [Index, setIndex] = useState('')
	const [docLoader, setDocLoader] = useState(false)

	const documentList = [
		{
			id: 0,
			value: twicFront,
			title: 'Upload TWIC Front',
			setData: data => {
				setTwicFront(data)
			},
		},
		{
			id: 1,
			value: twicBack,
			title: 'Upload TWIC Back',
			setData: data => {
				setTwicBack(data)
			},
		},
	]

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			setLoader(false)
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Profile Updated.',
				type: 'success',
			})
			props.navigation.goBack()
		} catch (e) {
			console.log(e)
			setLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoader(false)
		console.log('updateUser error  :', error)
	}

	async function UpdateTWIC() {
		try {
			setLoader(true)
			await mutate({
				variables: {
					updateUserInput: {
						twicFront: twicFront,
						twicBack: twicBack,
					},
				},
			})
		} catch (e) {
			console.log('catch edittwic :', e)
		}
	}

	useEffect(() => {
		setTwicBack(user?.twicBack)
		setTwicFront(user?.twicFront)
	}, [isFocus])

	return (
		<>
			<Layout
				LeftIcon={true}
				headerShown={true}
				pagetitle={'Edit T.W.I.C'}
				// ContentArea={styles().ph20}
				navigation={props.navigation}
				keyBoardArea={55}
			>
				<View style={[styles().flex]}>
					<View style={[styles().ph20, styles().flex]}>
						<View style={[styles().sectionHead, styles().mb20]}>
							<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>VALID T.W.I.C</Text>
						</View>
						<FlatList
							data={documentList}
							showsVerticalScrollIndicator={false}
							contentContainerStyle={{ flexGrow: 1 }}
							renderItem={({ item, index }) => {
								const docType = get_url_extension(item.value)
								const icon =
									docType === 'pdf'
										? <FontAwesome5
												name="file-pdf"
												size={45}
												color={currentTheme.themeBackground}
											/>
										: docType === 'docx' || docType === 'doc'
											? <FontAwesome
													name="file-word-o"
													size={45}
													color={currentTheme.themeBackground}
												/>
											: docType === 'png' || docType === 'jpeg' || docType === 'jpg'
												? <Image
														source={{ uri: item.value }}
														style={[styles().wh100, styles().br8]}
														resizeMode="cover"
													/>
												: <Entypo
														name="plus"
														size={45}
														color={currentTheme.themeBackground}
													/>
								return (
									<View style={[]}>
										<Text style={[styles().mb10, styles().fs12, { color: currentTheme.themeBackground }]}>{item.title}</Text>
										<TouchableOpacity
											key={index}
											activeOpacity={0.5}
											onPress={() => {
												setIsImagePicker(true)
												setDocLoader(true)
												setIndex(index)
											}}
											style={[
												styles().br10,
												styles().mb20,
												styles().h100px,
												styles().alignCenter,
												styles().justifyCenter,
												styles().bw1,
												{
													borderStyle: 'dashed',
													borderColor: currentTheme.headingColor,
													// borderColor: item.value
													//   ? currentTheme.green
													//   : currentTheme.headingColor,
												},
											]}
										>
											{index === Index && docLoader
												? <ActivityIndicator
														size={30}
														color={currentTheme.B7B7B7}
													/>
												: <View
														style={[
															styles().wh80px,
															styles().br8,
															styles().alignCenter,
															styles().justifyCenter,
															{
																borderColor: currentTheme.themeBackground,
																borderWidth: 0.5,
															},
														]}
													>
														{icon}
													</View>}
										</TouchableOpacity>
									</View>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>

					<View style={[styles().justifyEnd, styles().flex, styles().mb20, styles().ph20]}>
						{Loader
							? <Spinner />
							: <ThemeButton
									onPress={() => UpdateTWIC()}
									Title={'Save'}
									Style={styles().br10}
								/>}
					</View>
				</View>
			</Layout>
			<ImageAndDocumentPicker
				isImagePicker={isImagePicker}
				setIsImagePicker={() => {
					setIsImagePicker(false)
					setDocLoader(false)
				}}
				setImage={data => {
					documentList[Index]?.setData(data)
					setDocLoader(false)
				}}
				isPdf={true}
			/>
		</>
	)
}

export default EditTWIC
