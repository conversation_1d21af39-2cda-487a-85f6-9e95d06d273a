import {
	Text,
	View,
	TouchableOpacity,
	Image,
	SafeAreaView,
	ScrollView,
	KeyboardAvoidingView,
	StatusBar,
	RefreshControl,
	Platform,
	LayoutAnimation,
	UIManager,
	Animated,
	Easing,
} from 'react-native'
import { useContext, useEffect, useRef, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Ionicons from '@expo/vector-icons/Ionicons'
import Feather from '@expo/vector-icons/Feather'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import ApplyJobPopup from '../../component/Modals/ApplyJobPopup'
import { acceptOffer, applicationListener, applications, createChatRoom, getJob, rejectOffer, saveJob, removeSavedJob } from '../../apollo/server'
import { useMutation, useQuery, useSubscription } from '@apollo/client'
import moment from 'moment'
import AllJobs from '../MyJobs/AllJobs'
import CounterPopup from '../../component/Modals/CounterPopup'
import UserContext from '../../context/User/User'
import RenderHtml from 'react-native-render-html'
import Loading from '../../context/Loading/Loading'
import MapView, { PROVIDER_GOOGLE, PROVIDER_DEFAULT, Marker } from 'react-native-maps'
import { delta, user_eligible } from '../../utils/Constants'
import Lottie from 'lottie-react-native'
import Eligible from '../../context/EligibleContext/EligibleContext'
import NotFound from '../../component/NotFound'
import Share from '../../context/Share/Share'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const JobDetails = props => {
	const scrollViewRef = useRef(null)
	const themeContext = useContext(ThemeContext)
	const { isShare, shareNow } = useContext(Share)
	const currentTheme = theme[themeContext.ThemeValue]
	const androidSafeTop = useAndroidSafeTop()
	const { isLoader } = useContext(Loading)
	const { setStripeEligible, setCheckrEligible } = useContext(Eligible)
	const user = useContext(UserContext)
	const { item, jobId } = props?.route?.params
	const alreadyApplied = user?.appliedJobs?.includes(jobId)
	const [applyJobModal, setApplyJobModal] = useState(false)
	const [isApplied, SetisApplied] = useState(false)
	const [applicationID, setApplicationID] = useState('')
	const [companyNegotiation, setCompanyNegotiation] = useState('')
	const [applicantNegotiation, setApplicantNegotiation] = useState('')
	const [counterModal, setCounterModal] = useState(false)
	const [_isSpin, setIsSpin] = useState(false)
	const [chatLoader, setChatLoader] = useState(false)
	const [applicantOffers, setApplicantOffers] = useState('')
	const [checksavedJob, setChecksavedJob] = useState(undefined)
	const [isHired, setIsHired] = useState('')
	const [loaderRefesh, setLoaderRefesh] = useState(false)
	// console.log('Job id :', jobId);

	function share(item) {
		isShare(true)
		shareNow({
			title: item?.title,
			_id: item?._id,
			photo: item?.company.photo,
			description: item?.description,
			shareType: 'job',
		})
	}

	const spinValue = new Animated.Value(0)
	Animated.loop(
		Animated.timing(spinValue, {
			toValue: 1,
			duration: 1000,
			easing: Easing.linear, // Easing is an additional import from react-native
			useNativeDriver: true, // To make use of native driver for performance
		})
	).start()
	const _spin = spinValue.interpolate({
		inputRange: [0, 1],
		outputRange: ['0deg', '360deg'],
	})

	const { data, loading, error, refetch } = useQuery(getJob, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		variables: {
			jobId: jobId,
		},
		onCompleted: res => {
			console.log('getjob res :', res?.getJob)
			const alreadyApplied = user?.appliedJobs?.includes(jobId)
			SetisApplied(alreadyApplied)
			scrollToTop()
			const isSave = user?.savedJobs?.some(_id => _id === res?.getJob?._id)
			setChecksavedJob(isSave)
		},
		onError: err => {
			console.log('getjob err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})
	// console.log('application id :', applicationID);

	const {
		data: applicationSub,
		error: applicationErrorSub,
		loading: applicationLloadingSub,
	} = useSubscription(applicationListener, {
		variables: { applicationId: applicationID },
		onSubscriptionData: async ({ subscriptionData }) => {
			const Listener = subscriptionData?.data?.applicationListener?.offers
			const applicant_offers = Listener?.filter(offers => {
				if (offers?.by?._id === user?._id) {
					return offers
				}
			})
			setApplicantOffers(applicant_offers)
			// console.log('applicant_offers :', applicant_offers);
			const newValue = await Listener[Listener?.length - 1]
			if (data?.getJob?.company?._id === newValue.by?._id) {
				setCompanyNegotiation(newValue)
				console.log('Listener new company :', newValue)
			}
			if (user?._id === newValue.by?._id) {
				setApplicantNegotiation(newValue)
				console.log('Listener new applicant :', newValue)
			}
		},
		onError: err => {
			console.log('applicationListener Err :', err)
			FlashMessage({ msg: err?.message?.toString(), type: 'warning' })
		},
	})

	const {
		data: applictionData,
		loading: applicationLoading,
		refetch: applicationRefetch,
	} = useQuery(applications, {
		fetchPolicy: 'cache-and-network',
		errorPolicy: 'all',
		variables: {
			filters: {
				job: jobId,
				applicant: user?._id,
			},
		},
		onCompleted: async res => {
			// console.log('application :', res.applications);
			const status = res?.applications?.results[0]?.contract
			setIsHired(status)
			// console.log('isHired :', res?.applications?.results[0]?.isHired);
			// console.log('contract :', res?.applications?.results[0]?.contract);
			setApplicationID(res.applications.results[0]?._id)
			const applicant_offers = await res.applications.results[0]?.offers?.filter(offers => {
				if (offers?.by?._id === user?._id) {
					return offers
				}
			})
			setApplicantOffers(applicant_offers)
			// console.log('applicant_offers :', applicant_offers);
			// setIsSpin(false);
			if (res.applications.totalResults > 0) {
				const companyfilter = await res?.applications?.results[0]?.offers?.filter(item => {
					if (data?.getJob?.company?._id === item.by?._id) {
						return item
					}
				})
				const lastCompanyOffer = companyfilter?.length !== 0 ? companyfilter[companyfilter?.length - 1] : []
				setCompanyNegotiation(lastCompanyOffer)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				const applicantfilter = res?.applications?.results[0]?.offers?.filter(item => {
					if (user?._id === item.by?._id) {
						return item
					}
				})
				const lastApplicantOffer = applicantfilter?.length !== 0 ? applicantfilter[applicantfilter?.length - 1] : []
				// console.log('========>', lastApplicantOffer);
				setApplicantNegotiation(lastApplicantOffer)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			}
		},
		onError: err => {
			console.log('applications err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			props.navigation.goBack()
		},
	})

	const [mutate, { client }] = useMutation(checksavedJob ? removeSavedJob : saveJob, {
		onCompleted,
		onError,
	})

	async function onCompleted(res) {
		try {
			await user?.refetch()
			setChecksavedJob(!checksavedJob)
			console.log(`${!checksavedJob ? 'saveJob' : 'removeSavedJob'} res`, res)
			FlashMessage({
				msg: !checksavedJob ? res.saveJob.message : res.removeSavedJob.message,
				type: 'success',
			})
		} catch (e) {
			console.log(e)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		console.log(`${!checksavedJob ? 'saveJob' : 'removeSavedJob'} error :`, error)
	}

	const [acceptmutate, {}] = useMutation(acceptOffer, {
		errorPolicy: 'all',
		onCompleted: async _res => {
			isLoader(false)
			await applicationRefetch()
		},
		onError: err => {
			console.log('acceptOffer Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [rejecttmutate, {}] = useMutation(rejectOffer, {
		errorPolicy: 'all',
		onCompleted: async _res => {
			isLoader(false)
			await applicationRefetch()
		},
		onError: err => {
			console.log('rejectOffer Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [createChatroomMutate, {}] = useMutation(createChatRoom, {
		errorPolicy: 'all',
		onCompleted: async ({ createChatRoom }) => {
			console.log('createChatRoom res :', createChatRoom)
			const filterUser = await createChatRoom?.users?.find(users => {
				return users?._id !== user?._id
			})
			props.navigation.navigate('Chat', {
				item: createChatRoom,
				chatUser: filterUser,
				roomId: createChatRoom?._id,
				isAnonymousChat: createChatRoom?.isAnonymousChat,
			})
			setChatLoader(false)
		},
		onError: err => {
			console.log('createChatRoom err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setChatLoader(false)
		},
	})

	async function Chat() {
		setChatLoader(true)
		await createChatroomMutate({
			variables: {
				inputRoom: {
					users: [user._id, data?.getJob?.company?._id],
					// users: ['6435410f3c432bc84c9fdab6', '646206ce3b28d492b58bf7cd'],
				},
			},
		})
	}

	async function reject() {
		isLoader(true)
		await rejecttmutate({
			variables: {
				applicationId: applictionData?.applications?.results[0]?._id,
				offerId: companyNegotiation?._id,
			},
		})
	}

	async function accept() {
		isLoader(true)
		await acceptmutate({
			variables: {
				applicationId: applictionData?.applications?.results[0]?._id,
				offerId: companyNegotiation?._id,
			},
		})
	}

	const handleApply = () => {
		console.log('eligible :', user_eligible())
		if (user_eligible(user).status) {
			setApplyJobModal(!applyJobModal)
		} else {
			// if (user_eligible(user).type === 'stripe') {
			//   setStripeEligible(true);
			// }
			if (user_eligible(user).type === 'checkr') {
				setCheckrEligible(true)
			}
		}
	}

	const refresh = async () => {
		setLoaderRefesh(true)
		await refetch({
			jobId: jobId,
		}).then(_res => {
			// console.log(res, 'hello here');
			setLoaderRefesh(false)
		})
		await applicationRefetch({
			filters: {
				job: jobId,
				applicant: user?._id,
			},
		}).then(res => {
			console.log(res, 'hello 2')
		})
	}

	async function isSaveJob() {
		const save = {
			saveJobId: data?.getJob?._id,
		}

		const unsave = {
			removeSavedJobId: data?.getJob?._id,
		}
		await mutate({
			variables: checksavedJob ? unsave : save,
		})
	}
	const scrollToTop = () => {
		// Use a reference to the ScrollView component to scroll to the top
		if (scrollViewRef.current) {
			scrollViewRef.current.scrollTo({ y: 0, animated: true })
			// scrollViewRef.current.scrollToIndex({index: 0, animated: true});
		}
	}
	const _website = data?.getJob?.company?.website
	var _schemes_regex = /^(http|https)/

	useEffect(() => {
		applicationRefetch()
	}, [counterModal])

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	const _reload = async () => {
		setIsSpin(true)
		await applicationRefetch()
	}

	// console.log('companyNegotiation :', companyNegotiation);
	// console.log('ApplicantNegotiation :', applicantNegotiation);
	// console.log('applicantOffers :', applicantOffers);

	const locationOfVessel = {
		latitude:
			//  data?.getJob?.loc?.coordinates[1]  ?
			Number(data?.getJob?.loc?.coordinates[1]),
		// : 37.78825,
		longitude:
			//  data?.getJob?.loc?.coordinates[0] ?
			Number(data?.getJob?.loc?.coordinates[0]),
		// : -122.4324,
		latitudeDelta: delta.latitudeDelta,
		longitudeDelta: delta.longitudeDelta,
	}
	// console.log(locationOfVessel);
	const timer = async () => {
		const timer = setInterval(async () => {
			await applicationRefetch()
		}, 1500)
		return () => clearInterval(timer)
	}

	useEffect(() => {
		applicationRefetch()
		refetch()
		timer()

		return () => {
			timer()
		}
	}, [jobId])

	if (loading || !data?.getJob || data?.getJob?.deleted) {
		return (
			<NotFound
				navigation={() => props.navigation.goBack()}
				loading={loading}
				message={'This job is no longer available or may have expired. Please explore other opportunities or try again later.'}
			/>
		)
	}
	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			<SafeAreaView style={[styles().flex, { paddingTop: androidSafeTop }]}>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
					style={[styles().flex]}
				>
					<ScrollView
						ref={scrollViewRef}
						contentContainerStyle={[{ flexGrow: 1 }]}
						showsVerticalScrollIndicator={false}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={loaderRefesh}
							/>
						}
						keyboardShouldPersistTaps={'handled'}
					>
						<View
							style={[
								styles().pb30,
								// styles().alignCenter,
								styles().ph20,
								styles().mt10,
								styles().overflowH,
								{
									borderTopLeftRadius: 30,
									borderTopRightRadius: 30,
									backgroundColor: currentTheme.F3F0E4,
								},
							]}
						>
							<View style={[styles().flexRow, styles().flex, styles().alignCenter, styles().justifyBetween]}>
								<TouchableOpacity
									onPress={() => props.navigation.goBack()}
									style={[styles().alignSelfStart, styles().justifyCenter, styles().h50px, styles().w25px]}
								>
									<FontAwesome
										name="angle-left"
										size={30}
										color={currentTheme.blackish}
									/>
								</TouchableOpacity>
								<View style={[styles().mt10, styles().flexRow, styles().alignCenter]}>
									<View
										style={[
											styles().ph15,
											styles().pv5,
											styles().br5,
											styles().alignCenter,

											styles().justifyCenter,
											{ backgroundColor: currentTheme.themeBackground },
										]}
									>
										<Text style={[styles().fs12, styles().fontMedium, styles().textCapitalize, { color: currentTheme.white }]}>
											{data?.getJob?.jobType === 'hireOn' ? 'Hire On' : data?.getJob?.jobType ? data?.getJob?.jobType : 'N/A'}
										</Text>
									</View>
									<TouchableOpacity
										onPress={() => share(item)}
										activeOpacity={0.5}
										style={[styles().pall8, styles().pall8, styles().br5, styles().ml10, { backgroundColor: currentTheme.white }]}
									>
										<FontAwesome
											name="share"
											size={16}
											color={currentTheme.lighterGold}
										/>
									</TouchableOpacity>
								</View>
							</View>
							<View style={[styles().flex, styles().alignCenter]}>
								<TouchableOpacity
									activeOpacity={0.7}
									onPress={() =>
										props.navigation.navigate('CompanyDetails', {
											item: data?.getJob?.company,
										})
									}
									style={[
										styles().wh65px,
										styles().br50,
										styles().overflowH,
										styles().justifyCenter,
										styles().alignCenter,
										styles().mr10,
										{
											borderWidth: data?.getJob?.company?.photo ? 0 : 1,
											borderColor: currentTheme.themeBackground,
										},
									]}
								>
									{data?.getJob?.company?.photo
										? <Image
												source={{ uri: data?.getJob?.company?.photo }}
												resizeMode="cover"
												style={styles().wh100}
											/>
										: <Ionicons
												name="md-briefcase"
												size={30}
												color={currentTheme.themeBackground}
											/>}
								</TouchableOpacity>

								<View style={[styles().mt10, styles().mb10]}>
									<Text style={[styles().fs12, styles().fontBold, styles().lh20, styles().textCenter, { color: currentTheme.headingColor }]}>
										{data?.getJob?.title}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().alignCenter, styles().flexWrap, styles().justifyCenter]}>
									<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
										<FontAwesome
											name="briefcase"
											size={12}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
											{data?.getJob?.type === 'matePilot' ? 'Mate Pilot' : data?.getJob?.type}
										</Text>
									</View>
									<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
										<FontAwesome
											name="map-pin"
											size={12}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{data?.getJob?.city ? data?.getJob?.city : 'N/A'}
										</Text>
									</View>

									<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
										<Feather
											name="clock"
											size={12}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{moment(data?.getJob?.createdAt).fromNow()}
										</Text>
									</View>

									<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
										<Ionicons
											name="ios-cash-outline"
											size={12}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{/* {`$${data?.getJob?.rates}/${data?.getJob?.ratesType}`} */}
											{data?.getJob?.rates ? `$${data?.getJob?.rates}/Day` : 'N/A'}
										</Text>
									</View>
									<View style={[styles().flexRow, styles().alignCenter, styles().mr10]}>
										<FontAwesome5
											name="ship"
											size={12}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs10, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{/* {`$${data?.getJob?.rates}/${data?.getJob?.ratesType}`} */}
											{data?.getJob?.name_of_vessel ? data?.getJob?.name_of_vessel : 'N/A'}
										</Text>
									</View>
								</View>
							</View>
							{data?.getJob?.status === 'active' && (
								<View style={[styles().flexRow, styles().mt20, styles().alignCenter, styles().justifyCenter]}>
									{isHired?.status
										? <View
												style={[
													styles().h40px,
													styles().br100,
													styles().alignCenter,
													styles().ph20,
													styles().justifyCenter,
													styles().mr10,
													{
														backgroundColor:
															isHired?.status === 'active'
																? currentTheme.starColorBG
																: isHired?.status === 'cancelled'
																	? currentTheme.FFE5E5
																	: currentTheme.E6FFEA,
													},
												]}
											>
												<Text
													style={[
														styles().fs12,
														styles().textCapitalize,
														styles().fw500,

														{
															letterSpacing: 0.5,
															color:
																isHired?.status === 'active' ? currentTheme.starColor : isHired?.status === 'cancelled' ? currentTheme.red : currentTheme.green,
														},
													]}
												>
													{isHired?.status === 'completed'
														? 'Job Completed'
														: isHired?.status === 'active'
															? 'Hired'
															: isHired?.status === 'cancelled'
																? 'Terminated'
																: isHired?.status}
												</Text>
											</View>
										: user?.disposable === 'false'
											? <ThemeButton
													Title={alreadyApplied || isApplied ? 'Already Applied' : 'Apply for Job'}
													onPress={() => handleApply()}
													disabled={!!(alreadyApplied || isApplied)}
													Style={[
														styles().br5,
														styles().mt0,
														styles().mr10,
														styles().h40px,
														{
															backgroundColor: alreadyApplied || isApplied ? currentTheme.C3C3C3 : currentTheme.themeBackground,
															borderColor: alreadyApplied || isApplied ? currentTheme.C3C3C3 : currentTheme.themeBackground,
														},
													]}
													StyleText={[styles().fs12, { color: currentTheme.black }]}
												/>
											: null}
									{user?.disposable === 'false'
										? <TouchableOpacity onPress={() => isSaveJob()}>
												<FontAwesome
													name={checksavedJob ? 'bookmark' : 'bookmark-o'}
													size={24}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
										: null}
									{applicantNegotiation?.status === 'accepted'
										? <View>
												{chatLoader
													? <Lottie
															source={require('./../../assets/other/chat_loader.json')}
															autoPlay={true}
															loop={true}
															style={{ height: 25, width: 25, marginLeft: 5 }}
														/>
													: <TouchableOpacity
															style={[styles().ml5]}
															activeOpacity={0.5}
															onPress={() => Chat()}
														>
															<Ionicons
																name={'ios-chatbox-ellipses-outline'}
																size={25}
																color={currentTheme.themeBackground}
															/>
														</TouchableOpacity>}
											</View>
										: null}
								</View>
							)}
						</View>
						{/* {isHired?.status === 'active' ? (
              <> */}
						<View style={[styles().ph20, styles().mt20]}>
							{applicantNegotiation?.length !== 0
								? <Text style={[styles().fs16, styles().alignSelfCenter, styles().fw400, { color: currentTheme.themeBackground }]}>
										{applicantOffers ? `Your proposal was $${applicantOffers[0]?.rates}/Day` : 'N/A'}
									</Text>
								: null}
							{companyNegotiation?.length !== 0 && applicantNegotiation?.status === 'pending'
								? <View style={[styles().pall15, styles().mt15, styles().br10, { backgroundColor: currentTheme.headingColor }]}>
										<Text style={[styles().fs16, styles().mb10, styles().fw700, styles().textUpper, { color: currentTheme.lightGold }]}>New Offer</Text>
										<Text style={[styles().fs16, styles().mb5, styles().fw400, { color: currentTheme.B7B7B7 }]}>You have a new offer</Text>
										<Text
											style={[
												styles().fs16,
												styles().fw700,
												{
													color: currentTheme.white,
													textDecorationLine: applicantOffers[1]?.rates ? 'line-through' : 'none',
												},
											]}
										>
											{`$${companyNegotiation?.rates}/Day`}
										</Text>
										{applicantOffers[1]?.rates
											? <Text style={[styles().fs14, styles().fw600, styles().mt5, { color: currentTheme.themeBackground }]}>
													{`You send counter offer of $${applicantOffers[1]?.rates} `}
												</Text>
											: null}
										{companyNegotiation?.status !== 'pending'
											? <View style={[styles().mt15]}>
													<Text style={[styles().fs14, { color: currentTheme.B7B7B7 }]}>
														**You
														<Text style={[styles().textCapitalize, { fontStyle: 'italic', fontWeight: '600' }]}>{` ${companyNegotiation?.status} `}</Text>
														the counter offer
													</Text>
												</View>
											: !applicantOffers[1]?.rates
												? <View style={[styles().flexRow, styles().flex, styles().alignCenter]}>
														<ThemeButton
															onPress={() => accept()}
															Title={'Accept'}
															Style={[styles().ph15, styles().mr10, styles().h35px]}
															StyleText={styles().fs12}
														/>
														<ThemeButton
															onPress={() => reject()}
															Title={'Reject'}
															Style={[styles().ph15, styles().mr10, styles().h35px]}
															StyleText={styles().fs12}
														/>
														<ThemeButton
															onPress={() => setCounterModal(true)}
															Title={'Counter Offer'}
															Style={[styles().ph15, styles().h35px]}
															StyleText={styles().fs12}
														/>
													</View>
												: null}
										{/* <Animated.View
                    style={[
                      styles().posAbs,
                      styles().right10,
                      styles().top10,
                      {transform: [{rotate: isSpin ? spin : '0deg'}]},
                    ]}>
                    <TouchableOpacity
                      activeOpacity={0.5}
                      onPress={() => reload()}>
                      <Ionicons
                        name="reload-circle"
                        size={24}
                        color={currentTheme.white}
                      />
                    </TouchableOpacity>
                  </Animated.View> */}
									</View>
								: null}
						</View>
						{applicantNegotiation?.status === 'accepted' || applicantNegotiation?.status === 'rejected'
							? <View style={[styles().pall15, styles().mt15, styles().br10, styles().mh20, { backgroundColor: currentTheme.headingColor }]}>
									<Text style={[styles().fs16, styles().mb10, styles().fw700, styles().textUpper, { color: currentTheme.lightGold }]}>
										{applicantNegotiation?.status === 'accepted' ? 'Congratulations!' : 'Sorry!'}
									</Text>
									<Text style={[styles().fs16, styles().mb5, styles().fw400, { color: currentTheme.B7B7B7 }]}>
										{`Your offer has been ${applicantNegotiation?.status}.`}
									</Text>
									<Text style={[styles().fs16, styles().fw700, { color: currentTheme.white }]}>{`$${applicantNegotiation?.rates}/Day`}</Text>
								</View>
							: null}
						{/* </>
            ) : null} */}
						<View style={[styles().ph20, styles().mt20]}>
							{data?.getJob?.description
								? <>
										<Text style={[styles().fs20, styles().mb5, styles().fw700, { color: currentTheme.themeBackground }]}>Job Description</Text>
										<View style={[styles().mb20]}>
											<RenderHtml
												contentWidth={300}
												source={{ html: data?.getJob?.description }}
												baseStyle={{
													paddingHorizontal: 0,
													paddingVertical: 0,
												}}
												tagsStyles={{
													p: {
														marginVertical: 0,
													},
												}}
											/>
										</View>
									</>
								: null}
							{data?.getJob?.skills
								? <>
										<Text style={[styles().fs20, styles().mb5, styles().fw700, { color: currentTheme.themeBackground }]}>Skills & Experience</Text>
										<View style={[styles().mb20]}>
											<RenderHtml
												contentWidth={300}
												source={{ html: data?.getJob?.skills }}
												baseStyle={{
													paddingHorizontal: 0,
													paddingVertical: 0,
												}}
												tagsStyles={{
													p: {
														marginVertical: 0,
													},
												}}
											/>
										</View>
									</>
								: null}
							{data?.getJob?.loc?.coordinates[0] === -88.715237
								? null
								: data?.getJob?.loc?.coordinates[0]
									? <>
											<Text style={[styles().fs20, styles().mb10, styles().fw700, { color: currentTheme.themeBackground }]}>Location Of Vessel</Text>
											<View style={[styles().br10, styles().overflowH, styles().mb20]}>
												<MapView
													toolbarEnabled={false}
													provider={Platform.OS === 'ios' ? PROVIDER_DEFAULT : PROVIDER_GOOGLE}
													style={[styles().h200px, styles().w100]}
													region={locationOfVessel}
												>
													<Marker
														coordinate={locationOfVessel}
														title={'Location of Vessel'}
														// description={'description'}
													/>
												</MapView>
											</View>
										</>
									: null}
							<View style={[styles().pv15, styles().ph20, styles().br10, { backgroundColor: currentTheme.F3F0E4, marginBottom: 25 }]}>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Hitch Date:</Text>
									<Text
										numberOfLines={1}
										style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
									>
										{`${moment(data?.getJob?.startDate).format('l')} - ${moment(data?.getJob?.expireAt).format('l')}`}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Date Posted:</Text>
									<Text
										numberOfLines={2}
										style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
									>
										{moment(data?.getJob?.createdAt).format('LL')}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Vessel Location:</Text>
									<Text
										numberOfLines={2}
										style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
									>
										{data?.getJob?.city ? data?.getJob?.city : 'N/A'}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Experience Required:</Text>
									<Text
										numberOfLines={2}
										style={[styles().fs14, styles().fw400, styles().w60, { color: currentTheme.headingColor, textAlign: 'right' }]}
									>
										{data?.getJob?.experience}
									</Text>
								</View>
								{/* <View
                  style={[
                    styles().flexRow,
                    styles().mb10,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fw700,
                      styles().w40,
                      {color: currentTheme.headingColor},
                    ]}>
                    Qualification:
                  </Text>
                  <Text
                    numberOfLines={2}
                    style={[
                      styles().fs16,
                      styles().fw400,
                      styles().w60,
                      {color: currentTheme.headingColor, textAlign: 'right'},
                    ]}>
                    {data?.getJob?.qualification}
                  </Text>
                </View> */}
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, styles().textCapitalize, { color: currentTheme.headingColor }]}>
										{/* {`Starting ${data?.getJob?.ratesType} Price:`} */}
										{'Starting Daily Price:'}
									</Text>
									<Text
										style={[
											styles().fs14,
											styles().fw400,
											styles().w60,

											{ color: currentTheme.headingColor, textAlign: 'right' },
										]}
									>
										{/* {`$${data?.getJob?.offeredSalary}`} */}
										{data?.getJob?.rates ? `$${data?.getJob?.rates}` : 'N/A'}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, { color: currentTheme.headingColor }]}>Barges Handled:</Text>
									<Text
										style={[
											styles().fs14,
											styles().fw400,
											styles().w60,

											{ color: currentTheme.headingColor, textAlign: 'right' },
										]}
									>
										{data?.getJob?.horsePower ? `${data?.getJob?.horsePower}` : 'N/A'}
									</Text>
								</View>
								<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
									<Text style={[styles().fs14, styles().fw700, styles().w40, styles().textCapitalize, { color: currentTheme.headingColor }]}>Job Shift:</Text>
									<Text
										style={[styles().fs14, styles().fw400, styles().w60, styles().textCapitalize, { color: currentTheme.headingColor, textAlign: 'right' }]}
									>
										{data?.getJob?.jobShift === 'frontwatch'
											? 'Front Watch'
											: data?.getJob?.jobShift === 'backwatch'
												? 'Back Watch'
												: `${data?.getJob?.jobShift ? data?.getJob?.jobShift : 'N/A'}`}
									</Text>
								</View>
								{/* <View
                  style={[
                    styles().flexRow,
                    styles().mb10,
                    styles().alignCenter,
                    styles().justifyBetween,
                  ]}>
                  <Text
                    style={[
                      styles().fs16,
                      styles().fw700,
                      {color: currentTheme.headingColor},
                    ]}>
                    Social media:
                  </Text>
                  <View style={[styles().flexRow, styles().alignCenter]}>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL(data?.getJob?.company?.facebook)
                      }
                      style={[styles().ml15]}>
                      <FontAwesome
                        name="facebook"
                        size={20}
                        color={currentTheme.themeBackground}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL(data?.getJob?.company?.linkedIn)
                      }
                      style={[styles().ml15]}>
                      <FontAwesome
                        name="linkedin"
                        size={20}
                        color={currentTheme.themeBackground}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL(data?.getJob?.company?.twitter)
                      }
                      style={[styles().ml15]}>
                      <FontAwesome
                        name="twitter"
                        size={20}
                        color={currentTheme.themeBackground}
                      />
                    </TouchableOpacity>
                  </View>
                </View> */}
								{/* <View style={[styles().mb10, styles().mt10]}>
                  <ThemeButton
                    onPress={() => {
                      if (website) {
                        if (
                          schemes_regex.test(website?.toLowerCase()) == true
                        ) {
                          Linking.openURL(`${website}`);
                        } else {
                          Linking.openURL(`http://${website}`);
                        }
                      } else {
                        null;
                      }
                    }}
                    Title={website ? website : 'No Website'}
                    Style={[styles().br5, styles().h40px]}
                    StyleText={[styles().fs14, {color: currentTheme.black}]}
                  />
                </View> */}
							</View>
							{!isHired && user?.disposable === 'false' && data?.getJob?.status === 'active'
								? <ThemeButton
										onPress={() => handleApply()}
										Title={alreadyApplied || isApplied ? 'Already Applied' : 'Apply for Job'}
										disabled={!!(alreadyApplied || isApplied)}
										Style={[
											styles().br5,
											styles().mt0,
											{
												backgroundColor: alreadyApplied || isApplied ? currentTheme.C3C3C3 : currentTheme.themeBackground,
												borderColor: alreadyApplied ? currentTheme.C3C3C3 : currentTheme.themeBackground,
												borderWidth: alreadyApplied || isApplied ? 0 : 1,
											},
										]}
										StyleText={[{ color: currentTheme.black }]}
									/>
								: null}
							{data?.getJob?.status !== 'active' && (
								<View
									style={[
										styles().alignCenter,
										styles().justifyCenter,
										styles().pall10,
										styles().bw1,
										styles().br5,
										styles().mt10,
										{ borderColor: currentTheme.redLight },
									]}
								>
									<Text
										style={[styles().textCapitalize, styles().fs16, styles().fontMedium, { color: currentTheme.redLight }]}
									>{`The job is ${data?.getJob?.status}`}</Text>
								</View>
							)}
							<View style={[styles().mt30]}>
								<Text style={[styles().fs20, styles().fw700, { color: currentTheme.black }]}>Related Jobs</Text>
								<AllJobs
									type={item?.type ? item?.type : user?.role}
									{...props}
									currentJob={data?.getJob?._id}
								/>
							</View>
						</View>

						<ApplyJobPopup
							visible={applyJobModal}
							onClose={() => setApplyJobModal(false)}
							job={data?.getJob}
							isApplied={async isapplied => {
								SetisApplied(isapplied)
								await applicationRefetch({
									filters: {
										job: item?._id,
										applicant: user?._id,
									},
								})
							}}
						/>
						<CounterPopup
							modalCounterVisible={counterModal}
							onClose={() => setCounterModal(false)}
							applicationID={applictionData?.applications?.results[0]?._id}
						/>
					</ScrollView>
				</KeyboardAvoidingView>
			</SafeAreaView>
		</View>
	)
}

export default JobDetails
