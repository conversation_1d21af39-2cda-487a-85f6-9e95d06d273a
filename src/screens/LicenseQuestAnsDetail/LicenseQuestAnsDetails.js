import { Text, View, Platform, UIManager } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { removetags } from '../../utils/Constants'
import ThemeButton from '../../component/ThemeButton/ThemeButton'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const LicenseQuestAnsDetail = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, parentItem } = props?.route?.params
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Licensing Guidelines'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<Text
					style={[
						styles().fs18,
						styles().lh24,
						styles().alignSelfEnd,
						styles().mv10,
						styles().fontMedium,
						{
							color: currentTheme.themeBackground,
							textDecorationLine: 'underline',
						},
					]}
				>
					{item?.question ? `Fee $${Number.parseInt(item?.appointmentPrice)?.toFixed(2)}` : 'N/A'}
				</Text>
				<View style={[styles().mb20, styles().flexRow, styles().justifyBetween]}>
					<Text style={[styles().fs16, styles().lh24, styles().fontMedium, { color: currentTheme.themeBackground }]}>
						{item?.question ? item?.question : 'N/A'}
					</Text>
				</View>
				<View style={[styles().mb20]}>
					<Text style={[styles().fs14, styles().lh24, styles().fontRegular, { color: currentTheme.headingColor }]}>
						{item?.answer ? removetags(item?.answer) : 'N/A'}
					</Text>
				</View>
				<View style={[styles().flex, styles().justifyEnd, styles().mb20]}>
					<ThemeButton
						Title={'Book an Appointment'}
						onPress={() =>
							props.navigation.navigate('AppointmentBooking', {
								item: item,
								parentItem,
							})
						}
					/>
				</View>
			</View>
		</Layout>
	)
}

export default LicenseQuestAnsDetail
