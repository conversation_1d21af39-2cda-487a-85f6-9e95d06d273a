import { Text, View, Platform, Image, UIManager, FlatList, RefreshControl, TouchableOpacity, LayoutAnimation } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Layout from '../../component/Layout/Layout'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'
import { getRecommendationsById } from '../../apollo/server'
import moment from 'moment'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Recommendation = props => {
	const userId = props?.route?.params?.userId
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [reccomendations, setReccomendations] = useState([])
	const [readmore, setReadmore] = useState({})
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 30

	const { data, loading, refetch } = useQuery(getRecommendationsById, {
		fetchPolicy: 'no-cache',
		variables: {
			userId: userId,
			options: {
				limit: pageSize,
				page: page,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('getRecommendationsById res :', JSON.stringify(data?.getRecommendationsById))
			setReccomendations(prev => [...prev, ...data?.getRecommendationsById?.results])
		},
		onError: err => {
			console.log('getRecommendationsById err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	const nextPage = async () => {
		if (page < data?.getRecommendationsById?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			}).then(_res => {})
		}
	}

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setReccomendations([])
		setReadmore({})
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setReccomendations(_prev => res?.data?.getRecommendationsById?.results)
			setLoading(false)
			console.log('refreshed')
		})
	}

	useEffect(() => {
		setLoading(loading)
	}, [loading])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Recommendations'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<FlatList
					data={reccomendations}
					showsVerticalScrollIndicator={false}
					onEndReachedThreshold={0.75}
					onEndReached={() => nextPage()}
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No Recomendations'}
								</Text>
							</View>
						)
					}}
					contentContainerStyle={{ padding: 3, flexGrow: 1 }}
					renderItem={({ item, index }) => {
						return (
							<View
								key={index}
								style={[
									styles().flexRow,
									styles().flex,
									styles().mb10,
									{
										padding: 15,
										borderWidth: 1,
										borderColor: currentTheme.CFCFCF,
										borderRadius: 10,
									},
								]}
							>
								{item?.user?.photo
									? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
											<Image
												source={{ uri: item?.user?.photo }}
												style={styles().wh100}
												resizeMode="cover"
											/>
										</View>
									: <View
											style={[
												styles().overflowH,
												styles().justifyCenter,
												styles().alignCenter,
												styles().br50,
												styles().wh40px,
												{
													borderWidth: 1,
													borderColor: currentTheme.themeBackground,
												},
											]}
										>
											<FontAwesome5
												name="user-alt"
												size={16}
												color={currentTheme.themeBackground}
											/>
										</View>}
								<View style={[styles().ml10, styles().flex]}>
									<Text style={[styles().fs14, styles().textCapitalize, styles().fontMedium, { color: currentTheme.themeBackground }]}>{item?.user?.name}</Text>
									<View style={[styles().flexRow, styles().alignCenter]}>
										<Text style={[styles().fs12, styles().mr5, styles().fontMedium, styles().textCapitalize, { color: currentTheme.headingColor }]}>
											{item?.user?.role === 'matePilot' ? 'Mate Pilot' : item?.user?.role}
										</Text>
										<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).format('LL')}</Text>
									</View>
									<View style={[styles().mt5, styles().flex]}>
										<Text
											numberOfLines={readmore[`readmore${index}`] === undefined ? 3 : undefined}
											style={[styles().fs12, styles().lh18, styles().fontRegular, { color: currentTheme.headingColor }]}
										>
											{item.text}
										</Text>
										{!readmore[`readmore${index}`] && item?.text?.length > 100
											? <TouchableOpacity
													onPress={() => {
														LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
														setReadmore({
															...readmore,
															[`readmore${index}`]: true,
														})
													}}
												>
													<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.c737373 }]}>read more...</Text>
												</TouchableOpacity>
											: null}
									</View>
								</View>
							</View>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={<View style={styles().wh30px} />}
				/>
			</View>
		</Layout>
	)
}

export default Recommendation
