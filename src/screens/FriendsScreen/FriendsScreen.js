import { View, useWindowDimensions, Text, Dimensions } from 'react-native'
import React, { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { TabView, SceneMap, TabBar } from 'react-native-tab-view'
import MyFriends from './MyFriends'
import FriendRequests from './FriendRequests'
import Suggestions from './Suggestions'

const FriendsScreen = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const activeTab = props?.route?.params?.active
	const { width } = Dimensions.get('window')

	const layout = useWindowDimensions()

	const [index, setIndex] = React.useState(activeTab ? activeTab : 0)
	const [routes] = React.useState([
		{ key: 'first', title: 'My Friends' },
		{ key: 'second', title: 'Friend Requests' },
		{ key: 'third', title: 'Suggestions' },
	])

	const renderScene = SceneMap({
		first: () => <MyFriends {...props} />,
		second: () => <FriendRequests {...props} />,
		third: () => <Suggestions {...props} />,
	})

	const renderTabBar = props => (
		<TabBar
			{...props}
			indicatorStyle={[{ marginBottom: 10, width: '100%', height: 2 }]}
			indicatorContainerStyle={{ width: 0 }}
			//   indicatorContainerStyle={{marginRight:10}}
			style={[{ backgroundColor: currentTheme.white, elevation: 0 }]}
			activeColor={currentTheme.black}
			inactiveColor={currentTheme.blackish}
			scrollEnabled={true}
			pressOpacity={1}
			pressColor={currentTheme.white}
			contentContainerStyle={{ width: 'auto' }}
			tabStyle={[styles().alignStart, styles().mr10, { elevation: 0, width: 'auto', overflow: 'hidden' }]}
			renderLabel={({ route, focused, color }) => (
				<>
					<Text
						style={[
							styles().fs18,
							styles().fontMedium,
							{ textTransform: 'capitalize' },
							{
								color: focused ? currentTheme.themeBackground : currentTheme.C3C3C3,
							},
						]}
					>
						{route.title}
					</Text>
					{focused
						? <View
								style={[
									styles().w70px,
									styles().mt5,
									{
										borderBottomWidth: 2,
										borderBottomColor: currentTheme.themeBackground,
									},
								]}
							/>
						: null}
				</>
			)}
		/>
	)

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Friends'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				<TabView
					navigationState={{ index, routes }}
					renderScene={renderScene}
					onIndexChange={setIndex}
					renderTabBar={renderTabBar}
					initialLayout={{ width: layout.width }}
				/>
			</View>
		</Layout>
	)
}

export default React.memo(FriendsScreen)
