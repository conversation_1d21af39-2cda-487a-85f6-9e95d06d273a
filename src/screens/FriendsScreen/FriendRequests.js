import { Text, View, TouchableOpacity, FlatList, Image, RefreshControl } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import UserContext from '../../context/User/User'
import Ionicons from '@expo/vector-icons/Ionicons'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import { acceptFriendship, myFriendshipRequests, removeFriendship } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery, useMutation } from '@apollo/client'
import fontStyles from '../../utils/fonts/fontStyles'

const FriendRequests = ({ item, navigation }) => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_friend, _setFriend] = useState(false)
	const [requests, setRequests] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 25

	const { data, loading, error, refetch } = useQuery(myFriendshipRequests, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			userId: user?._id,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('myFriendshipRequests res :', JSON.stringify(data?.myFriendshipRequests))
			setRequests(prev => [...prev, ...data?.myFriendshipRequests?.results])
		},
		onError: err => {
			console.log('myFriendshipRequests err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [acceptFriendRequest, {}] = useMutation(acceptFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('acceptFriendship res :', res.acceptFriendship)
			FlashMessage({ msg: 'Your are friends now!', type: 'success' })
			const myArray = requests?.filter(obj => obj?._id !== res.acceptFriendship?._id)
			setRequests(myArray)
		},
		onError: err => {
			console.log('acceptFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [rejectFriendRequest, {}] = useMutation(removeFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('removeFriendship res :', res.removeFriendship)
			const myArray = requests?.filter(obj => obj?._id !== res.removeFriendship?._id)
			setRequests(myArray)
		},
		onError: err => {
			console.log('removeFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function acceptRequest(_id) {
		await acceptFriendRequest({
			variables: {
				friendId: _id,
			},
		})
	}

	async function rejectRequest(_id) {
		await rejectFriendRequest({
			variables: {
				friendId: _id,
			},
		})
	}

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		console.log('refresh')
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setRequests(_prev => res?.data?.myFriendshipRequests?.results)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.myFriendshipRequests?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			// await refetch();
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
		}
	}

	return (
		<FlatList
			data={requests}
			showsVerticalScrollIndicator={false}
			contentContainerStyle={{ flexGrow: 1 }}
			onEndReachedThreshold={0.5}
			onEndReached={() => nextPage()}
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => refresh()}
					refreshing={Loading}
				/>
			}
			renderItem={({ item, index }) => {
				return (
					<TouchableOpacity
						onPress={() =>
							navigation.navigate('ViewFriendProfile', {
								userId: item?._id,
								isFriend: false,
								isRequested: true,
							})
						}
						activeOpacity={0.7}
						style={[
							styles().flexRow,
							styles().mb10,
							index === 0 && styles().mt10,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb10,
							{ borderBottomColor: currentTheme.c707070 },
						]}
					>
						{item?.photo
							? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
									<Image
										source={{ uri: item?.photo }}
										style={styles().wh100}
										resizeMode="cover"
									/>
								</View>
							: <View
									style={[
										styles().overflowH,
										styles().justifyCenter,
										styles().alignCenter,
										styles().br50,
										styles().wh40px,
										{ borderWidth: 1, borderColor: currentTheme.themeBackground },
									]}
								>
									<FontAwesome5
										name="user-alt"
										size={16}
										color={currentTheme.themeBackground}
									/>
								</View>}
						<View style={[styles().flex, styles().ml10]}>
							<View style={[styles().flexRow, styles().alignCenter]}>
								<Text style={[styles().fs12, styles().fontMedium, styles().textCapitalize, { color: currentTheme.black }]}>{item?.name}</Text>
								{item?.profileVerified && (
									<View style={[styles().wh10px, styles().ml5, styles().overflowH]}>
										<Image
											source={require('../../assets/images/verified-icon.png')}
											style={styles().wh100}
											resizeMode="cover"
										/>
									</View>
								)}
							</View>
							<View>
								<Text style={[styles().fs10, styles().fontRegular, styles().textCapitalize, { color: currentTheme.c9E9E9E, letterSpacing: 0.5 }]}>
									{item.role === 'matePilot' ? 'Mate Pilot' : item.role}
								</Text>
							</View>
						</View>
						{/* <ThemeButton
              onPress={() => acceptRequest(item?._id)}
              Title={'Add Friend'}
              Style={[styles().h35px, styles().ph15, {borderRadius: 7}]}
              StyleText={[styles().fs12, {marginTop: 3}, styles().fw400]}
            /> */}
						<TouchableOpacity
							onPress={() => rejectRequest(item?._id)}
							style={[
								styles().mt10,
								styles().ml5,
								styles().alignCenter,
								styles().justifyCenter,
								styles().bw1,
								styles().br100,
								styles().wh30px,
								{ borderColor: currentTheme.B7B7B7 },
							]}
						>
							<Ionicons
								name="close"
								size={20}
								color={currentTheme.B7B7B7}
							/>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={() => acceptRequest(item?._id)}
							style={[
								styles().mt10,
								styles().ml5,
								styles().alignCenter,
								styles().justifyCenter,
								styles().bw1,
								styles().br100,
								styles().wh30px,
								{
									borderColor: currentTheme.themeBackground,
								},
							]}
						>
							<Ionicons
								name="checkmark"
								size={20}
								color={currentTheme.themeBackground}
							/>
						</TouchableOpacity>
					</TouchableOpacity>
				)
			}}
			ListEmptyComponent={() => {
				return (
					<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
						<Text
							style={{
								color: currentTheme.E8E8C8,
								fontSize: 14,
								fontFamily: fontStyles.PoppinsRegular,
							}}
						>
							{Loading || loading ? 'Loading...' : 'No Friend Requests'}
						</Text>
					</View>
				)
			}}
			keyExtractor={(_item, index) => index.toString()}
			ListFooterComponent={<View style={styles().wh20px} />}
		/>
	)
}

export default FriendRequests
