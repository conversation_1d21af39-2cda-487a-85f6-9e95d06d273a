import { Text, View, TouchableOpacity, FlatList, Image, RefreshControl } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import UserContext from '../../context/User/User'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { myFriendships, removeFriendship } from '../../apollo/server'
import { useQuery, useMutation } from '@apollo/client'
import fontStyles from '../../utils/fonts/fontStyles'

const MyFriends = ({ item, navigation }) => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [friends, setFriends] = useState([])
	const [searchData, setSearchData] = useState([])
	const [search, setSearch] = useState('')
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 25

	const { data, loading, error, refetch } = useQuery(myFriendships, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			userId: user?._id,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('myFriendships res :', JSON.stringify(data?.myFriendships))
			setFriends(prev => [...prev, ...data?.myFriendships?.results])
			setSearchData(prev => [...prev, ...data?.myFriendships?.results])
		},
		onError: err => {
			console.log('myFriendships err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			// ErrorHandler(err.message);
		},
	})

	const [removeFriend, {}] = useMutation(removeFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('removeFriendship res :', res.removeFriendship)
			const myArray = searchData?.filter(obj => obj?._id !== res.removeFriendship?._id)
			setFriends(myArray)
			setSearchData(myArray)
		},
		onError: err => {
			console.log('removeFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function RemoveFriend(_id) {
		await removeFriend({
			variables: {
				friendId: _id,
			},
		})
	}

	const searchFilterFunction = text => {
		if (text) {
			const newData = friends.filter(item => {
				const itemData = item.name ? item.name.toUpperCase() : ''.toUpperCase()
				const textData = text.toUpperCase()
				return itemData.indexOf(textData) > -1
			})
			console.log('search result :', newData)
			setSearchData(newData)
			setSearch(text)
		} else {
			setSearchData(friends)
			setSearch(text)
		}
	}

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setSearchData([])
		setFriends([])
		setSearch('')
		console.log('refresh')
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setFriends(_prev => res?.data?.myFriendships?.results)
			setSearchData(_prev => res?.data?.myFriendships?.results)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.myFriendships?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			// await refetch();
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
		}
	}

	return (
		<>
			<View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
				<View style={[styles().flex, styles().mt20]}>
					<TextField
						keyboardType="default"
						value={search}
						// errorText={searchError}
						autoCapitalize="none"
						placeholder={'Search'}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
						onChangeText={text => searchFilterFunction(text)}
					/>
				</View>
			</View>
			<FlatList
				data={searchData}
				showsVerticalScrollIndicator={false}
				onEndReachedThreshold={0.5}
				onEndReached={() => nextPage()}
				contentContainerStyle={{ flexGrow: 1 }}
				refreshControl={
					<RefreshControl
						colors={[currentTheme.themeBackground, currentTheme.black]}
						onRefresh={() => refresh()}
						refreshing={Loading}
					/>
				}
				renderItem={({ item, index }) => {
					return (
						<TouchableOpacity
							onPress={() =>
								navigation.navigate('ViewFriendProfile', {
									userId: item?._id,
									isFriend: true,
								})
							}
							activeOpacity={0.7}
							style={[
								styles().flexRow,
								styles().mb10,
								index === 0 && styles().mt10,
								styles().alignCenter,
								styles().justifyBetween,
								styles().bbw1,
								styles().pb10,
								{ borderBottomColor: currentTheme.c707070 },
							]}
						>
							{item?.photo
								? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
										<Image
											source={{ uri: item?.photo }}
											style={styles().wh100}
											resizeMode="cover"
										/>
									</View>
								: <View
										style={[
											styles().overflowH,
											styles().justifyCenter,
											styles().alignCenter,
											styles().br50,
											styles().wh40px,
											{ borderWidth: 1, borderColor: currentTheme.themeBackground },
										]}
									>
										<FontAwesome5
											name="user-alt"
											size={16}
											color={currentTheme.themeBackground}
										/>
									</View>}
							<View style={[styles().flex, styles().ml10]}>
								<View style={[styles().flexRow, styles().alignCenter]}>
									<Text style={[styles().fs12, styles().fontMedium, styles().textCapitalize, { color: currentTheme.black }]}>{item?.name}</Text>
									{item?.profileVerified && (
										<View style={[styles().wh10px, styles().ml5, styles().overflowH]}>
											<Image
												source={require('../../assets/images/verified-icon.png')}
												style={styles().wh100}
												resizeMode="cover"
											/>
										</View>
									)}
								</View>
								<View>
									<Text style={[styles().fs10, styles().fontRegular, styles().textCapitalize, { color: currentTheme.c9E9E9E, letterSpacing: 0.5 }]}>
										{item.role === 'matePilot' ? 'Mate Pilot' : item.role}
									</Text>
								</View>
							</View>
							<ThemeButton
								onPress={() => RemoveFriend(item?._id)}
								Title={' Unfriend'}
								Style={[
									styles().h30px,
									styles().ph15,
									friends === index && {
										backgroundColor: currentTheme.B7B7B7,
										borderColor: currentTheme.B7B7B7,
									},
									{ borderRadius: 7 },
								]}
								StyleText={[
									styles().fs12,
									// friends === index && {color: currentTheme.black},
									{ marginTop: 3 },
									styles().fontMedium,
								]}
								// onPress={() => setFriend(index)}
							/>
						</TouchableOpacity>
					)
				}}
				ListEmptyComponent={() => {
					return (
						<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
							<Text
								style={{
									color: currentTheme.E8E8C8,
									fontSize: 14,
									fontFamily: fontStyles.PoppinsRegular,
								}}
							>
								{Loading || loading ? 'Loading...' : 'No Friends'}
							</Text>
						</View>
					)
				}}
				keyExtractor={(_item, index) => index.toString()}
				ListFooterComponent={<View style={styles().wh20px} />}
			/>
		</>
	)
}

export default MyFriends
