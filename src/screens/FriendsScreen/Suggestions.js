import { Text, View, TouchableOpacity, FlatList, Image, RefreshControl } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../../screens/styles'
import UserContext from '../../context/User/User'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import { useQuery } from '@apollo/client'
import { getSuggestionList } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import fontStyles from '../../utils/fonts/fontStyles'

const Suggestions = ({ item, navigation }) => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_friend, _setFriend] = useState(false)
	const [suggestions, setSuggestions] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 25
	const { data, loading, error, refetch } = useQuery(getSuggestionList, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			userId: user?._id,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log(
			//   'getSuggestionList res :',
			//   JSON.stringify(data?.getSuggestionList),
			// );
			const myArray = data?.getSuggestionList?.results?.filter(obj => obj?._id !== user?._id)
			setSuggestions(prev => [...prev, ...myArray])
		},
		onError: err => {
			console.log('getSuggestionList err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setSuggestions([])
		console.log('refresh')
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			const myArray = res?.data?.getSuggestionList?.results?.filter(obj => obj?._id !== user?._id)
			setSuggestions(_prev => myArray)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.getSuggestionList?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			// await refetch();
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
		}
	}

	return (
		<FlatList
			data={suggestions}
			showsVerticalScrollIndicator={false}
			contentContainerStyle={{ flexGrow: 1 }}
			onEndReachedThreshold={0.5}
			onEndReached={() => nextPage()}
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => refresh()}
					refreshing={Loading}
				/>
			}
			renderItem={({ item, index }) => {
				return (
					<TouchableOpacity
						onPress={() =>
							navigation.navigate('ViewFriendProfile', {
								userId: item?._id,
								isFriend: false,
							})
						}
						activeOpacity={0.7}
						style={[
							styles().flexRow,
							styles().mb20,
							index === 0 && styles().mt20,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb15,
							{ borderBottomColor: currentTheme.c707070 },
						]}
					>
						{item?.photo
							? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
									<Image
										source={{ uri: item?.photo }}
										style={styles().wh100}
										resizeMode="cover"
									/>
								</View>
							: <View
									style={[
										styles().overflowH,
										styles().justifyCenter,
										styles().alignCenter,
										styles().br50,
										styles().wh40px,
										{ borderWidth: 1, borderColor: currentTheme.themeBackground },
									]}
								>
									<FontAwesome5
										name="user-alt"
										size={16}
										color={currentTheme.themeBackground}
									/>
								</View>}
						<View style={[styles().flex, styles().ml10]}>
							<View style={[styles().flexRow, styles().alignCenter]}>
								<Text style={[styles().fs12, styles().fontMedium, styles().textCapitalize, { color: currentTheme.black }]}>{item?.name}</Text>
								{item?.profileVerified && (
									<View style={[styles().wh10px, styles().ml5, styles().overflowH]}>
										<Image
											source={require('../../assets/images/verified-icon.png')}
											style={styles().wh100}
											resizeMode="cover"
										/>
									</View>
								)}
							</View>
							<View>
								<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.c9E9E9E, letterSpacing: 0.5 }]}>
									{item?.role === 'matePilot' ? 'Mate Pilot' : item?.role}
								</Text>
							</View>
						</View>
						<FontAwesome5
							name="arrow-right"
							color={currentTheme.D5D4D4}
							size={16}
						/>
						{/* <ThemeButton
              Title={friend === index ? 'Unfriend' : 'Add Friend'}
              Style={[
                styles().h35px,
                friend === index && {
                  backgroundColor: currentTheme.B7B7B7,
                  borderColor: currentTheme.B7B7B7,
                },
              ]}
              StyleText={[
                styles().fs12,
                friend === index && {color: currentTheme.black},
                {marginTop: 3},
                styles().fw400,
              ]}
              onPress={() => setFriend(index)}
            />
            <TouchableOpacity style={[styles().mt10, styles().ml5]}>
              <Ionicons name="close" size={24} color={currentTheme.C3C3C3} />
            </TouchableOpacity> */}
					</TouchableOpacity>
				)
			}}
			ListEmptyComponent={() => {
				return (
					<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
						<Text
							style={{
								color: currentTheme.E8E8C8,
								fontSize: 14,
								fontFamily: fontStyles.PoppinsRegular,
							}}
						>
							{Loading || loading ? 'Loading...' : 'No Suggestions'}
						</Text>
					</View>
				)
			}}
			keyExtractor={(_item, index) => index.toString()}
			ListFooterComponent={<View style={styles().wh20px} />}
		/>
	)
}

export default Suggestions
