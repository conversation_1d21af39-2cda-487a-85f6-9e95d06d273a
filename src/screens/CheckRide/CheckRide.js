import { Text, View, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import styles from '../styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getAppliedCheckRides } from '../../apollo/server'
import CheckRideComponent from '../../component/CheckRideComponent/CheckRideComponent'
import Layout from '../../component/Layout/Layout'

const CheckRide = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [checkRides, setCheckRides] = useState([])
	const [page, setPage] = useState(1)
	const pageSize = 30

	const [Loading, setLoading] = useState(false)
	const { data, loading, error, refetch } = useQuery(getAppliedCheckRides, {
		fetchPolicy: 'no-cache',
		variables: {
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			console.log('getAppliedCheckRides res :', JSON.stringify(data?.getAppliedCheckRides))
			setCheckRides(prev => [...prev, ...res?.getAppliedCheckRides?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('getAppliedCheckRides err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setCheckRides([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setCheckRides(res?.data?.getAppliedCheckRides?.results)
			setLoading(false)
			console.log('refresh')
		})
	}

	const nextPage = async () => {
		if (page < data?.getAppliedCheckRides?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(true)
	}, [])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			ContentArea={styles().ph20}
			pagetitle={'Check Rides'}
		>
			<View style={styles().flex}>
				<FlatList
					data={checkRides}
					showsVerticalScrollIndicator={false}
					onEndReachedThreshold={0.75}
					contentContainerStyle={{ flexGrow: 1 }}
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					onEndReached={() => nextPage()}
					ListEmptyComponent={() => {
						return (
							<View
								style={[
									styles().alignCenter,
									styles().justifyCenter,

									styles().flex,
								]}
							>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No Check Rides'}
								</Text>
							</View>
						)
					}}
					renderItem={({ item, index }) => {
						return (
							<CheckRideComponent
								item={item}
								index={index}
								navigation={props.navigation}
								checkride={true}
							/>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={<View style={styles().wh20px} />}
				/>
			</View>
		</Layout>
	)
}

export default CheckRide
