import {
	Text,
	View,
	TouchableOpacity,
	Image,
	SafeAreaView,
	ScrollView,
	KeyboardAvoidingView,
	StatusBar,
	Dimensions,
	Platform,
	ImageBackground,
	UIManager,
	RefreshControl,
	FlatList,
} from 'react-native'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import AntDesign from '@expo/vector-icons/AntDesign'
import Ionicons from '@expo/vector-icons/Ionicons'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import {
	Ratings,
	acceptFriendship,
	addFriendship,
	checkFriendship,
	createChatRoom,
	getBadgesByUserId,
	getUserDetail,
	posts,
	removeFriendship,
} from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import ImageView from 'react-native-image-viewing'
import { useIsFocused } from '@react-navigation/native'
import { formatPhoneNumber } from '../../utils/Constants'
import StarRating from 'react-native-star-rating-widget'
import ProgressLine from '../../component/ProgressLine/ProgressLine'
import Loading from '../../context/Loading/Loading'
import UserContext from '../../context/User/User'
import Lottie from 'lottie-react-native'
import RecommendationModal from '../../component/Modals/RecommendationModal'
import PostSkeleton from '../../component/Skeleton/PostSkeleton'
import PostComponent from '../../component/PostComponent/PostComponent'
import VideoThumbnail from '../../component/VideoThumbnail/VideoTHumbnail'
import fontStyles from '../../utils/fonts/fontStyles'
import Badges from '../../component/Badges/Badges'
import BadgeModal from '../../component/Modals/BadgeModal'

const { width } = Dimensions.get('window')

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const ViewFriendProfile = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { userId, isRequested, isFriend } = props?.route?.params
	const isFocus = useIsFocused()
	const { isLoader } = useContext(Loading)
	const [refreshLoading, setRefreshLoading] = useState(false)
	const [photo, setPhoto] = useState('')
	const [coverPhoto, setCoverPhoto] = useState('')
	const [friendshipStatus, setFriendshipStatus] = useState('')
	const [requestStatus, setRequestStatus] = useState('')
	const [sentRequest, setSentRequest] = useState(false)
	const [chatLoader, setChatLoader] = useState(false)
	const [getPosts, setGetPosts] = useState([])
	const [badge, setBadge] = useState('')
	const [showBadgeModal, setShowBadgeModal] = useState(false)
	// const [Loading, setLoading] = useState(false);
	const user = useContext(UserContext)
	const [postPage, _setPostPage] = useState(1)
	const postPageSize = 50
	const [visible, setIsVisible] = useState({
		dp: false,
		cp: false,
	})
	const [ratings, setRatings] = useState([
		{ percentage: 0, stars: 5 },
		{ percentage: 0, stars: 4 },
		{ percentage: 0, stars: 3 },
		{ percentage: 0, stars: 2 },
		{ percentage: 0, stars: 1 },
	])

	const { data, loading, refetch } = useQuery(getUserDetail, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			userId: userId,
		},
		onCompleted: data => {
			console.log('getUserDetail res :', JSON.stringify(data?.getUserDetail))
			setPhoto(data?.getUserDetail?.photo)
			setCoverPhoto(data?.getUserDetail?.coverImage)
			isLoader(false)
			setRefreshLoading(false)
		},
		onError: err => {
			console.log('getUserDetail err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
			setRefreshLoading(false)
		},
	})

	const {
		data: mybadgesData,
		loading: mybadgesLoader,
		refetch: mybadgesRefetch,
	} = useQuery(getBadgesByUserId, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			userId: userId,
		},
		onCompleted: data => {
			console.log('getBadgesByUserId res :', JSON.stringify(data?.getBadgesByUserId))
		},
		onError: err => {
			console.log('getBadgesByUserId err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: postData,
		loading: postLoading,
		refetch: postRefetch,
	} = useQuery(posts, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: {
				author: userId,
			},
			options: {
				page: postPage,
				limit: postPageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			console.log('posts res :', res)
			setGetPosts(prev => [...prev, ...res?.Posts?.results])
		},
		onError: err => {
			console.log('posts err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})
	// const { data: hasRequestData, loading: hasRequestLoading, refetch: hasRequestRefetch } = useQuery(hasFriendRequest, {
	//   fetchPolicy: 'no-cache',
	//   variables: {
	//     friendId: userId,
	//   },
	//   onCompleted: data => {
	//     console.log('hasFriendRequest res :', JSON.stringify(data));
	//   },
	//   onError: err => {
	//     console.log('hasFriendRequest err :', err);
	//     FlashMessage({ msg: err.message, type: 'danger' });
	//   },
	// });

	const {
		data: ratingData,
		loading: ratingLoading,
		refetch: refetchRatings,
	} = useQuery(Ratings, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filters: {
				user: userId,
			},
			options: {
				page: 1,
				limit: 100000,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			try {
				// console.log('Ratings res :', JSON.stringify(data?.Ratings));
				const updatedRatings = [...ratings] // Create a copy of the original ratings array
				data.Ratings?.results?.forEach(result => {
					const avgRating = result.avgRating

					// Find the index of the rating in the ratings array that corresponds to the average rating
					const index = updatedRatings.findIndex(rating => rating.stars === Math.round(avgRating))
					if (index !== -1) {
						// Update the percentage based on the calculation
						updatedRatings[index].percentage += 100 / data.Ratings?.results?.length
					}
				})

				// Set the updated ratings state
				setRatings(updatedRatings)
			} catch (error) {
				console.log(error)
			}
		},
		onError: err => {
			console.log('Ratings err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: checkFriendshipData,
		loading: checkFriendshipLoading,
		refetch: checkFriendshipRefetch,
	} = useQuery(checkFriendship, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			friendId: userId,
		},
		onCompleted: data => {
			console.log('checkFriendship res :', JSON.stringify(data.checkFriendship))
			if (data.checkFriendship?.status === 'pending' && data.checkFriendship?.user?._id !== user?._id) {
				// setSentRequest(true)
				setRequestStatus(true)
			}
			if (data.checkFriendship?.status === 'pending' && data.checkFriendship?.user?._id === user?._id) {
				setSentRequest(true)
			}
			if (data.checkFriendship?.status === 'accepted') {
				setFriendshipStatus(true)
			}
		},
		onError: err => {
			console.log('checkFriendship err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [acceptFriendRequest, {}] = useMutation(acceptFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('acceptFriendship res :', res.acceptFriendship)
			setFriendshipStatus(true)
			setRequestStatus(false)
			FlashMessage({ msg: 'Your are friends now!', type: 'info' })
		},
		onError: err => {
			console.log('acceptFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [sendFriendRequest, {}] = useMutation(addFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('addFriendship res :', res.addFriendship)
			setFriendshipStatus(false)
			setRequestStatus(false)
			setSentRequest(true)
		},
		onError: err => {
			console.log('addFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [removeFriend, {}] = useMutation(removeFriendship, {
		errorPolicy: 'all',
		onCompleted: async res => {
			console.log('removeFriendship res :', res.removeFriendship)
			setFriendshipStatus(false)
			setRequestStatus(false)
			setSentRequest(false)
		},
		onError: err => {
			console.log('removeFriendship Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [createChatroomMutate, {}] = useMutation(createChatRoom, {
		errorPolicy: 'all',
		onCompleted: async ({ createChatRoom }) => {
			console.log('createChatRoom res :', createChatRoom)
			const filterUser = await createChatRoom?.users?.find(users => {
				return users?._id !== user?._id
			})
			props.navigation.navigate('Chat', {
				item: createChatRoom,
				chatUser: filterUser,
				roomId: createChatRoom?._id,
				isAnonymousChat: createChatRoom?.isAnonymousChat,
			})
			setChatLoader(false)
		},
		onError: err => {
			console.log('createChatRoom err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setChatLoader(false)
		},
	})

	async function Chat() {
		setChatLoader(true)
		const data = {
			users: [user._id, userId],
		}
		console.log(data)
		await createChatroomMutate({
			variables: {
				inputRoom: data,
			},
		})
	}

	async function RemoveFriend(_id) {
		await removeFriend({
			variables: {
				friendId: _id,
			},
		})
	}

	async function sendRequest(_id) {
		await sendFriendRequest({
			variables: {
				friendId: _id,
			},
		})
	}

	async function acceptRequest(_id) {
		await acceptFriendRequest({
			variables: {
				friendId: _id,
			},
		})
	}

	const refresh = async () => {
		await refetchRatings()
		setRefreshLoading(true)
		await refetch({
			userId: userId,
		}).then(res => {
			setPhoto(res?.data?.getUserDetail?.photo)
			setCoverPhoto(res?.data?.getUserDetail?.coverImage)
			isLoader(false)
			setRefreshLoading(false)
		})
		await checkFriendshipRefetch({
			friendId: userId,
		}).then(res => {
			if (res?.data?.checkFriendship === null) {
				setRequestStatus(false)
				setSentRequest(false)
				setFriendshipStatus(false)
			}
			if (res?.data.checkFriendship?.status === 'pending' && res?.data.checkFriendship?.user?._id !== user?._id) {
				setRequestStatus(true)
			}
			if (res?.data.checkFriendship?.status === 'pending' && res?.data.checkFriendship?.user?._id === user?._id) {
				setSentRequest(true)
			}
			if (res?.data.checkFriendship?.status === 'accepted') {
				setFriendshipStatus(true)
			}
		})
	}

	const likePost = async _id => {
		const updateLikes = getPosts?.map(post => {
			if (post._id === _id) {
				const isLiked = post?.likes?.includes(user?._id)
				return {
					...post,
					likes: isLiked ? post?.likes?.filter(id => id !== user?._id) : [...post?.likes, user?._id],
				}
			}
			return post
		})

		if (updateLikes) {
			setGetPosts(updateLikes)
		}
	}

	const handleBadge = useCallback(badge => {
		setBadge(badge)
		setShowBadgeModal(true)
	})

	useEffect(() => {
		isLoader(loading)
		setFriendshipStatus(isFriend)
		setRequestStatus(isRequested)
	}, [])

	const DP = [{ uri: photo }]
	const CP = [{ uri: coverPhoto }]

	// console.log(friendshipStatus, requestStatus, sentRequest);
	// console.log(
	//   'userId :',
	//   userId,
	//   'isRequested :',
	//   isRequested,
	//   'isFriend :',
	//   isFriend,
	// );

	const [recommandationModal, setRecommendationModal] = useState(false)
	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			{showBadgeModal && (
				<BadgeModal
					currentUser={data?.getUserDetail?.name}
					badge={badge}
					visible={showBadgeModal}
					onClose={() => setShowBadgeModal(false)}
				/>
			)}
			{photo
				? <ImageView
						images={DP}
						imageIndex={0}
						presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
						visible={visible.dp}
						onRequestClose={() => setIsVisible({ ...visible, dp: false })}
					/>
				: null}
			{coverPhoto
				? <ImageView
						images={CP}
						imageIndex={0}
						presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
						visible={visible.cp}
						onRequestClose={() => setIsVisible({ ...visible, cp: false })}
					/>
				: null}
			<SafeAreaView style={[styles().flex, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 35}
					style={[styles().flex]}
				>
					<ScrollView
						contentContainerStyle={[{ flexGrow: 1 }]}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps={'handled'}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={refreshLoading}
							/>
						}
					>
						<TouchableOpacity
							activeOpacity={0.9}
							onPress={() => setIsVisible({ ...visible, cp: true })}
						>
							<ImageBackground
								style={[
									styles().pb30,
									styles().alignCenter,
									styles().ph20,
									styles().mt10,
									styles().h220px,
									styles().flexRow,
									styles().alignCenter,
									styles().justifyBetween,
									styles().overflowH,
									{
										borderTopLeftRadius: 30,
										borderTopRightRadius: 30,
										backgroundColor: currentTheme.F3F0E4,
									},
								]}
								source={{ uri: coverPhoto }}
							>
								<TouchableOpacity
									onPress={() => props.navigation.goBack()}
									style={[
										styles().alignSelfStart,
										styles().justifyCenter,
										styles().alignCenter,
										styles().h50px,
										styles().w50px,
										{ zIndex: 300, right: 20, top: 10 },
									]}
								>
									<FontAwesome
										name="angle-left"
										size={30}
										color={currentTheme.white}
									/>
								</TouchableOpacity>
							</ImageBackground>
						</TouchableOpacity>

						<View style={[styles().mtminus75, styles().alignCenter]}>
							<View>
								<TouchableOpacity
									activeOpacity={0.9}
									onPress={() => setIsVisible({ ...visible, dp: true })}
									style={[
										styles().wh140px,
										styles().br100,
										styles().overflowH,
										styles().mr10,
										styles().alignCenter,
										styles().justifyCenter,
										{
											borderWidth: 8,
											borderColor: currentTheme.white,
											backgroundColor: currentTheme.F3F0E4,
										},
									]}
								>
									{photo
										? <Image
												source={{ uri: photo }}
												resizeMode="cover"
												style={[styles().wh100, styles().br100]}
											/>
										: <FontAwesome5
												name="user-alt"
												size={50}
												color={currentTheme.themeBackground}
											/>}
								</TouchableOpacity>
							</View>

							<View style={[styles().mt5, styles().alignCenter, styles().mb15]}>
								<View style={[styles().flexRow, styles().alignStart, styles().w80, styles().mb5]}>
									<Text
										// numberOfLines={}
										style={[styles().fs22, styles().fontBold, styles().textCenter, { color: currentTheme.lightGold }]}
									>
										{data?.getUserDetail?.name}
									</Text>
									{data?.getUserDetail?.profileVerified
										? <View style={[styles().wh20px, styles().ml10, styles().overflowH, { marginTop: 5 }]}>
												<Image
													source={require('../../assets/images/verified-icon.png')}
													style={styles().wh100}
													resizeMode="cover"
												/>
											</View>
										: null}
								</View>
								<View style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}>
									<Text style={[styles().fs16, styles().mr5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.headingColor }]}>
										{data?.getUserDetail?.role === 'matePilot' ? 'Mate Pilot' : data?.getUserDetail?.role}
									</Text>
									<TouchableOpacity
										activeOpacity={0.9}
										style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
									>
										<View style={[styles().mb5]}>
											<FontAwesome
												name="star"
												color={currentTheme.starColor}
												size={16}
											/>
										</View>
										<Text style={[styles().fs12, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.headingColor }]}>
											{`${data?.getUserDetail?.ratings ? data?.getUserDetail?.ratings?.toFixed(1) : 0} (${
												data?.getUserDetail?.ratingsCount ? data?.getUserDetail?.ratingsCount : 0
											} reviews)`}
										</Text>
									</TouchableOpacity>
								</View>
							</View>
							{data?.getUserDetail
								? <View style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}>
										<TouchableOpacity
											onPress={() => {
												if (friendshipStatus) {
													//remove friend
													RemoveFriend(userId)
												}
												if (requestStatus) {
													//accept request
													acceptRequest(userId)
												}
												if (sentRequest) {
													RemoveFriend(userId)
													//cancel sent request
												}
												if (!friendshipStatus && !requestStatus && !sentRequest) {
													//add friend
													sendRequest(userId)
												}
											}}
											activeOpacity={0.7}
											style={[
												styles().br20,
												// styles().w130px,
												styles().h40px,
												styles().ph20,
												styles().alignCenter,
												styles().flexRow,
												styles().justifyCenter,
												{
													backgroundColor: friendshipStatus
														? currentTheme.lightGreen
														: !friendshipStatus
															? currentTheme.darkBlue
															: currentTheme.themeBackground,
												},
											]}
										>
											{friendshipStatus
												? <FontAwesome5
														name="user-check"
														size={14}
														color={currentTheme.white}
													/>
												: <FontAwesome
														name="user-plus"
														size={14}
														color={currentTheme.white}
													/>}
											<Text style={[styles().fs12, styles().ml5, styles().fontRegular, { color: currentTheme.white }]}>
												{friendshipStatus ? 'Unfriend' : requestStatus ? 'Accept Friend Request' : sentRequest ? 'Cancel Request' : 'Add Friend'}
											</Text>
										</TouchableOpacity>
										{
											// friendshipStatus ? (
											chatLoader
												? <Lottie
														source={require('./../../assets/other/chat_loader.json')}
														autoPlay={true}
														loop={true}
														style={{ height: 25, width: 25, marginLeft: 5 }}
													/>
												: <TouchableOpacity
														activeOpacity={0.7}
														onPress={() => Chat()}
														style={[
															styles().wh35px,
															styles().ml5,
															styles().alignCenter,
															styles().justifyCenter,
															styles().br50,
															styles().pall10,
															styles().overflowH,
															{ backgroundColor: currentTheme.EEE8D5 },
														]}
													>
														<Image
															source={require('../../assets/images/messages.png')}
															style={styles().wh100}
															resizeMode="contain"
														/>
													</TouchableOpacity>
											// )
											//  :
											//  null
										}
										{/* {friendshipStatus ? ( */}
										<TouchableOpacity
											activeOpacity={0.7}
											onPress={() => setRecommendationModal(true)}
											style={[
												styles().wh35px,
												styles().ml5,
												styles().alignCenter,
												styles().justifyCenter,
												styles().br50,
												{ backgroundColor: currentTheme.recommendBlue },
											]}
										>
											<AntDesign
												name="like1"
												size={16}
												color={currentTheme.white}
											/>
										</TouchableOpacity>
										{/* ) : null} */}
									</View>
								: null}
						</View>
						{data?.getUserDetail
							? <View
									style={[
										styles().alignCenter,
										styles().mt20,
										styles().mb25,
										styles().ml20,
										{
											borderBottomWidth: 1,
											width: width * 1 - 40,
											borderBottomColor: currentTheme.c707070,
										},
									]}
								/>
							: null}
						{data?.getUserDetail
							? <View style={[styles().ph20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
									{data?.getUserDetail?.companyWorkFor
										? <View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
												<Ionicons
													name="md-briefcase"
													size={20}
													color={currentTheme.themeBackground}
												/>
												<Text
													numberOfLines={2}
													style={[styles().fs14, styles().fontRegular, styles().ml10, styles().flex, { color: currentTheme.headingColor }]}
												>
													{`Work at ${data?.getUserDetail?.companyWorkFor}`}
												</Text>
											</View>
										: null}

									{!data?.getUserDetail?.hideEmail && friendshipStatus
										? <View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
												<FontAwesome
													name="envelope"
													size={18}
													color={currentTheme.themeBackground}
												/>
												<Text
													numberOfLines={1}
													style={[styles().fs14, styles().fontRegular, styles().ml10, styles().flex, { color: currentTheme.headingColor }]}
												>
													{data?.getUserDetail?.email ? data?.getUserDetail?.email : 'N/A'}
												</Text>
											</View>
										: null}
									{!data?.getUserDetail?.hideContact && friendshipStatus
										? <View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
												<FontAwesome
													name="phone"
													size={20}
													color={currentTheme.themeBackground}
												/>
												<Text
													numberOfLines={1}
													style={[styles().fs14, styles().fontRegular, styles().ml10, styles().flex, { color: currentTheme.headingColor }]}
												>
													{data?.getUserDetail?.phone ? formatPhoneNumber(data?.getUserDetail?.phone) : 'N/A'}
												</Text>
											</View>
										: null}
									{data?.getUserDetail?.name && friendshipStatus
										? <View style={[styles().flexRow, styles().mb10, styles().alignCenter]}>
												<AntDesign
													name="exclamationcircle"
													size={18}
													color={currentTheme.themeBackground}
												/>
												<TouchableOpacity
													onPress={() =>
														props.navigation.navigate('ViewFriendAbout', {
															user: data?.getUserDetail,
															isFriend: friendshipStatus,
														})
													}
													activeOpacity={0.8}
													style={[styles().flexRow, styles().alignCenter, styles().justifyCenter]}
												>
													<Text style={[styles().fs14, styles().fontRegular, styles().ml10, { color: currentTheme.headingColor }]}>See more About</Text>
													<Text
														numberOfLines={1}
														style={[styles().fs14, styles().fontMedium, styles().ml5, styles().flex, { color: currentTheme.themeBackground }]}
													>
														{`${data?.getUserDetail?.name}`}
													</Text>
												</TouchableOpacity>
											</View>
										: null}
								</View>
							: null}
						{/* {!friendshipStatus ? ( */}
						{data?.getUserDetail?.role === 'deckhand'
							? <>
									<View style={[styles().ph15, styles().flex, styles().pt15, styles().pb15]}>
										<Text style={[styles().fs18, styles().fontMedium, { color: currentTheme.black }]}>I Wanna Be A Toe Boater</Text>

										{data?.getUserDetail?.deckhand_video
											? <VideoThumbnail
													video={data?.getUserDetail?.deckhand_video}
													isFocus={isFocus}
												/>
											: <Text style={[styles().fontRegular, styles().mt25, styles().alignSelfCenter, styles().fs16, { color: currentTheme.black }]}>
													No Video
												</Text>}
									</View>
									<View
										style={[
											styles().alignCenter,
											styles().mv10,
											styles().ml20,
											{
												borderBottomWidth: 1,
												width: width * 1 - 40,
												borderBottomColor: currentTheme.c707070,
											},
										]}
									/>
								</>
							: null}
						<View style={[styles().waterWaysSection, styles().mt20, styles().pb10, styles().ph20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
							<View style={[styles().sectionHead, styles().mb10, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
								<Text style={[styles().fs20, styles().fontMedium, { color: currentTheme.headingColor }]}>Company Badges</Text>
							</View>
							<FlatList
								// data={userBadges?.getBadgesByUserId}
								data={mybadgesData?.getBadgesByUserId}
								showsHorizontalScrollIndicator={false}
								horizontal
								renderItem={({ index, item }) => (
									<Badges
										index={index}
										badge={item}
										callback={badge => handleBadge(badge)}
									/>
								)}
								ListEmptyComponent={() => {
									return (
										<View style={[styles().alignCenter, styles().justifyCenter, styles().mv10, styles().flex, { width: width * 0.9 }]}>
											<Text
												style={{
													color: currentTheme.E8E8C8,
													fontSize: 14,
												}}
											>
												{mybadgesLoader ? 'Loading...' : ' No Bagdes'}
											</Text>
										</View>
									)
								}}
							/>
						</View>
						<View style={[styles().docSection, styles().mt20, styles().pb10, styles().ph20, styles().flex]}>
							<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
								<Text style={[styles().fs20, styles().fontMedium, { color: currentTheme.headingColor }]}>Reviews & Ratings</Text>
								{!data?.getUserDetail?.hideReviews && friendshipStatus
									? <TouchableOpacity
											onPress={() =>
												props.navigation.navigate('Reviews', {
													user: data?.getUserDetail,
													overallRatings: ratings,
												})
											}
											activeOpacity={0.7}
										>
											<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.B7B7B7 }]}>View Reviews</Text>
										</TouchableOpacity>
									: null}
							</View>
							<View style={styles().sectionContent}>
								<View
									style={[
										styles().br10,
										styles().flexRow,
										styles().alignCenter,
										styles().justifyBetween,
										styles().pall20,
										{ backgroundColor: currentTheme.F8F9FA },
									]}
								>
									<View style={[styles().mr15, styles().mt10]}>
										<Text style={[styles().fs32, styles().mb10, styles().fontMedium, { color: currentTheme.headingColor }]}>
											{data?.getUserDetail?.ratings ? data?.getUserDetail?.ratings?.toFixed(1) : 0}
											<Text style={[styles().fs20, styles().ml10, styles().fw600, { color: currentTheme.headingColor }]}>/5</Text>
										</Text>
										<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>
											{`Based on ${data?.getUserDetail?.ratingsCount ? data?.getUserDetail?.ratingsCount : 0} Review`}
										</Text>
										<View style={[styles().flexRow, styles().mt15]}>
											<StarRating
												rating={data?.getUserDetail?.ratings || 0}
												onChange={() => {}}
												starSize={20}
												starStyle={styles().ml5}
												maxStars={5}
												color={currentTheme.starColor}
												emptyColor={currentTheme.e6e6e6}
												enableHalfStar={true}
											/>
										</View>
									</View>
									<View style={[styles().flex]}>
										{ratings.map((rating, i) => {
											return (
												<View
													key={i}
													style={[styles().flexRow, styles().flex, styles().alignCenter, styles().justifyBetween]}
												>
													<Text style={[styles().fs12, styles().mr10, styles().fontRegular, { color: currentTheme.textColor }]}>{`${rating?.stars} Star`}</Text>
													<View
														style={[
															styles().flex,
															styles().overflowH,
															styles().br10,
															{
																height: 8,
																backgroundColor: currentTheme.e6e6e6,
															},
														]}
													>
														<ProgressLine progress={rating?.percentage} />
													</View>
												</View>
											)
										})}
									</View>
								</View>
							</View>
						</View>
						{/* ) : null} */}
						{
							// friendshipStatus &&
							data?.getUserDetail?.postSettings === 'friends'
								? <View style={[styles().ph15, styles().mt20]}>
										{getPosts?.length === 0
											? <View style={[styles().flex]}>
													{postLoading
														? Array.from({ length: 2 }).map(item => {
																return <PostSkeleton data={item} />
															})
														: <Text
																style={{
																	marginTop: 100,
																	marginBottom: 50,
																	alignSelf: 'center',
																	color: currentTheme.E8E8C8,
																	fontFamily: fontStyles.PoppinsRegular,
																	fontSize: 14,
																}}
															>
																{'No Posts'}
															</Text>}
												</View>
											: null}
										{getPosts?.map((item, index) => {
											return (
												<PostComponent
													navigation={props.navigation}
													item={item}
													index={index}
													update={(updateType, post_id) => {
														if (updateType === 'deletePost') {
															removePost(post_id)
														}
														if (updateType === 'postLike') {
															likePost(post_id)
														}
													}}
												/>
											)
										})}
									</View>
								: null
						}
						<View style={styles().wh30px} />
					</ScrollView>
				</KeyboardAvoidingView>
			</SafeAreaView>
			<RecommendationModal
				modalVisible={recommandationModal}
				RecommendUser={data?.getUserDetail}
				onCancel={() => setRecommendationModal(false)}
			/>
		</View>
	)
}

export default React.memo(ViewFriendProfile)
