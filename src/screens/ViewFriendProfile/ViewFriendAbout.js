import { Text, View, Image, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Ionicons from '@expo/vector-icons/Ionicons'
import Layout from '../../component/Layout/Layout'
import { formatPhoneNumber } from '../../utils/Constants'
import moment from 'moment'
import { getRecommendationsById } from '../../apollo/server'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Loading from '../../context/Loading/Loading'
import { useQuery } from '@apollo/client'

const ViewFriendAbout = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { user } = props?.route?.params
	const [waterways, setWaterways] = useState(null)
	const { isLoader } = useContext(Loading)
	const [readmore, setReadmore] = useState({})

	const _ratings = [
		{ ratingCount: 100, stars: 5 },
		{ ratingCount: 80, stars: 4 },
		{ ratingCount: 60, stars: 3 },
		{ ratingCount: 40, stars: 2 },
		{ ratingCount: 20, stars: 1 },
	]

	const createParentObjects = async arr => {
		try {
			const result = []
			const parentMap = {}
			await arr?.forEach(item => {
				const parentId = item?.parentWaterWay?._id
				if (!parentMap[parentId]) {
					parentMap[parentId] = {
						parentName: item?.parentWaterWay?.name,
						children: [],
					}
				}
				parentMap[parentId].children.push({
					name: item?.name,
					_id: item?._id,
				})
			})

			for (const parentId in parentMap) {
				result?.push(parentMap[parentId])
			}
			// console.log('=========>', JSON.stringify(result));
			setWaterways(result)
		} catch (e) {
			console.log('catch waterways filter :', e)
		}
	}

	const { data, loading, refetch } = useQuery(getRecommendationsById, {
		fetchPolicy: 'no-cache',
		variables: {
			userId: user?._id,
			options: {
				limit: 2,
				page: 1,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('getRecommendationsById res :', JSON.stringify(data?.getRecommendationsById))
			isLoader(false)
		},
		onError: err => {
			console.log('getRecommendationsById err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	useEffect(() => {
		createParentObjects(user?.waterWays)
	}, [user?.waterWays])

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	return (
		<Layout
			LeftIcon={true}
			navigation={props.navigation}
			headerShown={true}
			pagetitle={'Profile'}
			ContentArea={styles().ph20}
			containerStyle={{ backgroundColor: currentTheme.postBackground }}
		>
			<View style={[styles().flex]}>
				<View style={[styles().bioSection, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Bio</Text>
					</View>
					<View style={styles().sectionContent}>
						<View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
							<FontAwesome5
								name="user-alt"
								size={18}
								color={currentTheme.themeBackground}
							/>
							<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>{user?.name}</Text>
						</View>
						{user?.companyWorkFor
							? <View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
									<Ionicons
										name="md-briefcase"
										size={20}
										color={currentTheme.themeBackground}
									/>
									<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
										{`Work at ${user?.companyWorkFor}`}
									</Text>
								</View>
							: null}
						{!user?.hideEmail
							? <View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
									<FontAwesome
										name="envelope"
										size={18}
										color={currentTheme.themeBackground}
									/>
									<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>{user?.email}</Text>
								</View>
							: null}
						{user?.address
							? <View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
									<MaterialIcons
										name="location-on"
										size={22}
										color={currentTheme.themeBackground}
									/>
									<Text
										numberOfLines={2}
										style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor, marginLeft: 8 }]}
									>
										{user?.address}
									</Text>
								</View>
							: null}
						{!user?.hideContact && user?.phone
							? <View style={[styles().flexRow, styles().mb15, styles().alignCenter]}>
									<FontAwesome
										name="phone"
										size={20}
										color={currentTheme.themeBackground}
									/>
									<Text style={[styles().fs14, styles().ml10, styles().fontRegular, { color: currentTheme.headingColor }]}>
										{formatPhoneNumber(user?.phone)}
									</Text>
								</View>
							: null}
					</View>
				</View>

				<View style={[styles().eduSection, styles().mt15, styles().pb20, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Skills</Text>
					</View>
					<View style={[styles().flexRow, styles().alignStart]}>
						<MaterialCommunityIcons
							name="lightbulb-on-outline"
							size={25}
							color={currentTheme.themeBackground}
						/>
						<View style={[styles().flex, styles().flexRow, styles().flexWrap, styles().ml10]}>
							{user?.skills?.length !== 0
								? user?.skills?.map(skills => {
										return (
											<View
												style={[
													styles().ph10,
													styles().pv5,
													styles().mr5,
													styles().mb5,
													styles().alignCenter,
													styles().justifyCenter,
													styles().br20,
													{ backgroundColor: currentTheme.ebebeb },
												]}
											>
												<Text style={[styles().fs12, { color: currentTheme.headingColor }]}>{skills}</Text>
											</View>
										)
									})
								: <Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.themeBackground, marginTop: 3 }]}>No skills</Text>}
						</View>
					</View>
				</View>
				<View style={[styles().eduSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Education</Text>
					</View>
					{user?.educations?.length !== 0
						? user?.educations?.map(education => {
								const { title, startYear, endYear, institute } = education

								return (
									<View style={[styles().sectionContent]}>
										<View style={[styles().flexRow, styles().mb15, styles().alignStart]}>
											<FontAwesome5
												name="graduation-cap"
												size={20}
												color={currentTheme.themeBackground}
											/>
											<View style={[styles().ml10, styles().flex]}>
												<View style={[styles().flexRow, styles().alignStart, styles().flex]}>
													<Text
														// numberOfLines={1}
														style={[
															styles().fs14,
															styles().mr10,
															styles().fontRegular,
															// styles().flex,
															styles().flexShrink,
															styles().textCapitalize,
															{ color: currentTheme.headingColor },
														]}
													>
														{title}
													</Text>
													<View style={[styles().br20, styles().pv5, styles().ph15, { backgroundColor: currentTheme.EEE8D5 }]}>
														<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.themeBackground }]}>
															{`${moment(startYear).year()} - ${moment(endYear).year()}`}
														</Text>
													</View>
												</View>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.lightGold }]}>{institute}</Text>
											</View>
										</View>
									</View>
								)
							})
						: <View style={[styles().mb5]}>
								<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.themeBackground }]}>No Education</Text>
							</View>}
				</View>

				<View style={[styles().workSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Work & Experience</Text>
					</View>
					{user?.experiances?.length !== 0
						? user?.experiances?.map(experience => {
								const { title, startYear, endYear, company } = experience

								return (
									<View style={styles().sectionContent}>
										<View style={[styles().flexRow, styles().mb15, styles().alignStart]}>
											<Ionicons
												name="md-briefcase"
												size={20}
												color={currentTheme.themeBackground}
											/>
											<View style={[styles().ml10, styles().flex]}>
												<View style={[styles().flexRow, styles().alignStart, styles().flex]}>
													<Text
														// numberOfLines={1}
														style={[
															styles().fs14,
															styles().mr10,
															styles().fontRegular,
															styles().flexShrink,
															styles().textCapitalize,
															{ color: currentTheme.headingColor },
														]}
													>
														{title}
													</Text>

													<View style={[styles().br20, styles().pv5, styles().ph15, { backgroundColor: currentTheme.EEE8D5 }]}>
														<Text style={[styles().fs9, styles().fontRegular, { color: currentTheme.themeBackground }]}>
															{`${moment(startYear).year()} - ${moment(endYear).year()}`}
														</Text>
													</View>
												</View>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.lightGold }]}>{company}</Text>
											</View>
										</View>
									</View>
								)
							})
						: <View style={[styles().mb5]}>
								<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.themeBackground }]}>No Work & Experience</Text>
							</View>}
				</View>

				<View style={[styles().waterWaysSection, styles().mt20, styles().pb10, styles().bbw1, { borderBottomColor: currentTheme.c707070 }]}>
					<View style={[styles().sectionHead, styles().mb10, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Routes on Waterways</Text>
					</View>
					{waterways?.map((ways, i) => {
						return (
							<View
								key={i}
								style={[styles().sectionContent, styles().mb15]}
							>
								<Text style={[styles().fs14, styles().fontMedium, styles().mb5, { color: currentTheme.themeBackground }]}>
									{/* {`${ways?.children?.length}) ${ways.parentName}`} */}
									{`${i + 1}) ${ways.parentName}`}
								</Text>
								<View style={[styles().pl15]}>
									{ways?.children?.map(child => {
										return (
											<View style={[styles().flexRow, styles().alignCenter]}>
												<View
													style={[
														styles().br5,
														styles().mr5,
														styles().wh5px,
														{
															backgroundColor: currentTheme.themeBackground,
														},
													]}
												/>
												<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>{child.name}</Text>
											</View>
										)
									})}
								</View>
							</View>
						)
					})}
				</View>
				{!user?.hideRecommendations
					? <View style={[styles().docSection, styles().mt10, styles().pb10]}>
							<View style={[styles().sectionHead, styles().mb20, styles().flexRow, styles().alignCenter, styles().justifyBetween]}>
								<Text style={[styles().fs20, styles().fw700, { color: currentTheme.headingColor }]}>Recommendation</Text>
							</View>
							<View style={styles().sectionContent}>
								{data?.getRecommendationsById?.results?.length === 0
									? <View style={{ alignItems: 'center', marginVertical: 15 }}>
											<Text>{loading ? 'Loading...' : 'No Reccomendations'}</Text>
										</View>
									: data?.getRecommendationsById?.results?.map((item, index) => {
											return (
												<View
													key={index}
													style={[
														styles().flexRow,
														styles().pb15,
														styles().flex,
														index !== 0 && {
															paddingTop: 15,
															borderTopWidth: 1,
															borderTopColor: currentTheme.c707070,
														},
													]}
												>
													{item?.user?.photo
														? <View style={[styles().wh40px, styles().overflowH, styles().br50]}>
																<Image
																	source={{ uri: item?.user?.photo }}
																	style={styles().wh100}
																	resizeMode="cover"
																/>
															</View>
														: <View
																style={[
																	styles().overflowH,
																	styles().justifyCenter,
																	styles().alignCenter,
																	styles().br50,
																	styles().wh40px,
																	{
																		borderWidth: 1,
																		borderColor: currentTheme.themeBackground,
																	},
																]}
															>
																<FontAwesome5
																	name="user-alt"
																	size={16}
																	color={currentTheme.themeBackground}
																/>
															</View>}
													<View style={[styles().ml10, styles().flex]}>
														<Text style={[styles().fs14, styles().fw700, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
															{item?.user?.name}
														</Text>
														<View style={[styles().flexRow, styles().alignCenter]}>
															<Text style={[styles().fs12, styles().mr5, styles().fontMedium, styles().textCapitalize, { color: currentTheme.headingColor }]}>
																{item?.user?.role === 'matePilot' ? 'Mate Pilot' : item?.user?.role}
															</Text>
															<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>{moment(item?.createdAt).format('LL')}</Text>
														</View>
														<View style={[styles().mt5, styles().flex]}>
															<Text
																numberOfLines={readmore[`readmore${index}`] === undefined ? 3 : undefined}
																style={[styles().fs12, styles().lh18, styles().fontRegular, { color: currentTheme.headingColor }]}
															>
																{item.text}
															</Text>
															{!readmore[`readmore${index}`] && item?.text?.length > 150
																? <TouchableOpacity
																		onPress={() =>
																			setReadmore({
																				...readmore,
																				[`readmore${index}`]: true,
																			})
																		}
																	>
																		<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.c737373 }]}>read more...</Text>
																	</TouchableOpacity>
																: null}
														</View>
													</View>
												</View>
											)
										})}
								<View style={[styles().mb20]}>
									<ThemeButton
										Title={'Show All'}
										onPress={() =>
											props.navigation.navigate('Recommendation', {
												userId: user?._id,
											})
										}
										StyleText={{ color: currentTheme.headingColor }}
										Style={[
											styles().br10,
											{
												backgroundColor: currentTheme.C3C3C3,
												borderColor: currentTheme.C3C3C3,
											},
										]}
									/>
								</View>
							</View>
						</View>
					: null}
			</View>
		</Layout>
	)
}

export default React.memo(ViewFriendAbout)
