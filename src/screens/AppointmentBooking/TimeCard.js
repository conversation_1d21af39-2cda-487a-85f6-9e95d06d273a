import { Text, TouchableOpacity } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import moment from 'moment'

const TimeCard = ({ item, index, selected, callBack, available_slot }) => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const endTime = moment(item, 'hh:mmA').add(1, 'hour').format('hh:mmA')
	const _time = `${item} - ${endTime}`

	return (
		<TouchableOpacity
			disabled={!available_slot}
			onPress={() => {
				callBack(item === selected ? '' : item)
			}}
			activeOpacity={0.5}
			key={index}
			style={[
				styles().alignCenter,
				styles().justifyCenter,
				styles().bw1,
				styles().mall5,
				styles().pv10,
				// styles().ph20,
				styles().w45,
				styles().br10,
				{
					borderColor: !available_slot ? currentTheme.FFE5E5 : selected === item ? currentTheme.themeBackground : currentTheme.blackish,
					backgroundColor: !available_slot ? currentTheme.FFE5E5 : selected === item ? currentTheme.themeBackground : null,
				},
			]}
		>
			<Text
				style={[
					styles().fs14,
					styles().fontMedium,
					{
						color: !available_slot ? currentTheme.red : selected === item ? currentTheme.white : currentTheme.blackish,
					},
				]}
			>
				{item}
			</Text>
		</TouchableOpacity>
	)
}

export default TimeCard
