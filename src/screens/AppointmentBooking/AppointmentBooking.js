import { Text, View, Platform, UIManager, TouchableOpacity, FlatList, LayoutAnimation } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import Octicons from '@expo/vector-icons/Octicons'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import DateTimePicker from '@react-native-community/datetimepicker'
import moment from 'moment'
import TimeCard from './TimeCard'
import fontStyles from '../../utils/fonts/fontStyles'
import AppointmentSuccessModal from '../../component/Modals/AppointmentSuccessModal'
import { createLicenseBooking, getAvaBookingSlots, getBookingSchedule } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation, useQuery } from '@apollo/client'
import UserContext from '../../context/User/User'
import Spinner from '../../component/Spinner/Spinner'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const AppointmentBooking = props => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { item, parentItem } = props?.route?.params
	const [isCalender, setIsCalender] = useState(false)
	const [isVisible, setIsVisible] = useState(false)
	const [initDate, setInitDate] = useState(new Date())
	const today = new Date()
	const tomorrow = new Date(today)
	tomorrow.setDate(today.getDate() + 1)
	const [selected, setSelected] = useState({
		date: moment(tomorrow).format('MM-DD-YYYY'),
		day: moment(tomorrow).format('dddd'),
		slot: '',
	})

	const { data, loading, error, refetch } = useQuery(getBookingSchedule, {
		errorPolicy: 'all',
		onCompleted: _data => {
			// console.log(
			//   'getBookingSchedule res :',
			//   JSON.stringify(data?.getBookingSchedule),
			// );
		},
		onError: err => {
			console.log('getBookingSchedule err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const {
		data: availableSlotData,
		refetch: availableSlotRefetch,
		loading: availableSlotLoading,
	} = useQuery(getAvaBookingSlots, {
		variables: {
			date: selected?.date,
		},
		errorPolicy: 'all',
		onCompleted: _data => {
			// console.log(
			//   'getAvaBookingSlots res :',
			//   JSON.stringify(data?.getAvaBookingSlots),
			// );
		},
		onError: err => {
			console.log('getAvaBookingSlots err :', err)
			FlashMessage({ msg: err.message, type: 'warning' })
		},
	})

	const [mutate, { loading: mutateLoader, data: createLicenseBookingData }] = useMutation(createLicenseBooking, {
		errorPolicy: 'all',
		onCompleted: res => {
			console.log('createLicenseBooking res:', res?.createLicenseBooking)
			setIsVisible(true)
		},
		onError: err => {
			console.log('createLicenseBooking err:', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	function getTimeDifferenceInMinutes(timeRange) {
		const [startTime, endTime] = timeRange.split(' - ')
		const startMoment = moment(startTime, 'h:mm A')
		const endMoment = moment(endTime, 'h:mm A')
		const differenceInMinutes = endMoment.diff(startMoment, 'minutes')
		// return `${differenceInMinutes} minutes`;
		return differenceInMinutes
	}

	const handleBooking = async () => {
		const meeting_secs = getTimeDifferenceInMinutes(selected?.slot) * 60
		let status = true
		if (selected.slot === '') {
			FlashMessage({ msg: 'Select Booking Slot', type: 'warning' })
			status = false
			return
		}
		if (status) {
			const data = {
				inputLicenseBooking: {
					meeting_date: selected?.date,
					meeting_secs: meeting_secs.toString(),
					meeting_time_slot: selected?.slot,
					user: user?._id,
					booking: {
						appointmentPrice: item?.appointmentPrice,
						title: parentItem?.title,
						question: item?.question,
						category: parentItem?.category,
						answer: item?.answer,
					},
				},
			}
			console.log(data)
			await mutate({
				variables: data,
			})
		}
	}

	// const INTERVAL = 60; // 60 minutes interval, you can change this to 30 for half-hour or 120 for two hours
	// const schedule = [
	//   {
	//     day: 'Monday',
	//     time_slots: [
	//       {start: '9:00AM', end: '1:00PM'},
	//       {start: '2:00PM', end: '8:00PM'},
	//     ],
	//   },
	//   {
	//     day: 'Tuesday',
	//     time_slots: [
	//       {start: '9:00AM', end: '1:00PM'},
	//       {start: '2:00PM', end: '8:00PM'},
	//     ],
	//   },
	// ];

	// function convertTo24Hour(time) {
	//   const [timePart, period] = time.split(/(AM|PM)/);
	//   let [hours, minutes] = timePart.split(':');
	//   hours = parseInt(hours);
	//   minutes = parseInt(minutes) || 0;
	//   if (period === 'PM' && hours !== 12) {
	//     hours += 12;
	//   } else if (period === 'AM' && hours === 12) {
	//     hours = 0;
	//   }
	//   return `${hours.toString().padStart(2, '0')}:${minutes
	//     .toString()
	//     .padStart(2, '0')}`;
	// }

	// function convertTo12Hour(time) {
	//   let [hours, minutes] = time.split(':');
	//   hours = parseInt(hours);
	//   minutes = parseInt(minutes);
	//   // Determine AM/PM period
	//   const period = hours >= 12 ? 'PM' : 'AM';
	//   // Convert hours to 12-hour format and format with leading zero if necessary
	//   hours = (hours % 12 || 12).toString().padStart(2, '0');
	//   // Format minutes with leading zero if necessary
	//   minutes = minutes.toString().padStart(2, '0');
	//   // Return formatted time in 12-hour format
	//   return `${hours}:${minutes}${period}`;
	// }

	// function getTimeSlotsInInterval(start, end, interval) {
	//   const startTime = new Date(`1970-01-01T${start}:00`);
	//   const endTime = new Date(`1970-01-01T${end}:00`);
	//   const timeSlots = [];
	//   while (startTime < endTime) {
	//     timeSlots.push(convertTo12Hour(startTime.toTimeString().slice(0, 5)));
	//     startTime.setMinutes(startTime.getMinutes() + interval);
	//   }
	//   return timeSlots;
	// }

	// const updatedSchedule = data?.getBookingSchedule?.map(daySchedule => {
	//   const updatedTimeSlots = [];
	//   daySchedule?.time_slots?.forEach(slot => {
	//     const start = convertTo24Hour(slot?.start);
	//     const end = convertTo24Hour(slot?.end);
	//     const slotsInInterval = getTimeSlotsInInterval(start, end, INTERVAL);
	//     updatedTimeSlots.push(...slotsInInterval);
	//   });
	//   return {
	//     day: daySchedule.day,
	//     time_slots: updatedTimeSlots,
	//   };
	// });

	const getScheduleByDay = dayName => {
		if (dayName) {
			const day = dayName?.toLowerCase()
			return data?.getBookingSchedule?.find(schedule => schedule?.day?.toLowerCase() === day)
		}
	}

	const slots = availableSlotLoading ? [] : getScheduleByDay(selected?.day)

	// useEffect(() => {
	//   const day = moment(new Date()).format('dddd');
	//   const date = moment(new Date()).format('MM-DD-YYYY');
	//   setSelected({...selected, date: date, day: day});
	// }, []);
	// console.log(availableSlotData.getAvaBookingSlots);
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Book An Appointment'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<Text style={[styles().fs14, styles().fontMedium, styles().mv5, { color: currentTheme.blackish }]}>Date</Text>
				<TouchableOpacity
					activeOpacity={0.5}
					onPress={() => setIsCalender(!isCalender)}
					style={[
						styles().br10,
						styles().h55px,
						styles().w100,
						styles().mb20,
						styles().alignCenter,
						styles().flexRow,
						styles().ph15,
						styles().justifyBetween,
						styles().bw1,
						{
							borderColor: currentTheme.B7B7B7,
						},
					]}
				>
					<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>{selected?.date ? selected?.date : 'Select Date'}</Text>
					<Octicons
						name={'calendar'}
						size={18}
						color={currentTheme.B7B7B7}
					/>
				</TouchableOpacity>
				{isCalender && (
					<>
						<DateTimePicker
							testID="dateTimePicker"
							minimumDate={tomorrow}
							value={!initDate ? new Date(selected.date) : initDate}
							mode={'date'}
							themeVariant="light"
							textColor={currentTheme.themeBackground}
							accentColor="blue"
							positiveButton={{
								label: 'OK',
								textColor: currentTheme.themeBackground,
							}}
							negativeButton={{
								label: 'Cancel',
								textColor: currentTheme.themeBackground,
							}}
							display={Platform.OS === 'ios' ? 'spinner' : 'default'}
							is24Hour={true}
							onChange={(event, value) => {
								if (event.type === 'dismissed') {
									// Handle cancel button press
									setIsCalender(false)
									return
								}
								if (Platform.OS !== 'ios' && event.type === 'set') {
									setIsCalender(false)
									setInitDate(null)
								}
								const currentDate = value
								const day = moment(currentDate).format('dddd')
								const date = moment(currentDate).format('MM-DD-YYYY')
								console.log(currentDate)
								console.log(day)
								setSelected({
									...selected,
									date: date,
									day: day,
									slot: '',
								})
							}}
							style={{
								alignSelf: 'center',
								justifyContent: 'center',
								alignItems: 'flex-start',
								// width: "90%",
								// height: "70%",
							}}
						/>
						<View
							style={[
								styles().alignSelfCenter,
								styles().justifyCenter,
								styles().alignCenter,
								styles().mb30,
								styles().bw1,
								styles().br5,
								{ borderColor: currentTheme.themeBackground },
							]}
						>
							<TouchableOpacity
								style={[styles().ph30, styles().pv5]}
								onPress={() => {
									setIsCalender(false)
								}}
							>
								<Text style={[styles().fontMedium, styles().fs14, { color: currentTheme.themeBackground }]}>Close</Text>
							</TouchableOpacity>
						</View>
					</>
				)}
				<Text style={[styles().fs14, styles().fontMedium, styles().mv5, { color: currentTheme.blackish }]}>Select Time</Text>
				<FlatList
					numColumns={2}
					contentContainerStyle={{
						justifyContent: 'center',
						flexDirection: 'column',
						// flex: 1,
						// alignItems: 'center',
					}}
					data={slots?.time_slot}
					keyExtractor={(_item, index) => index.toString()}
					renderItem={({ index, item }) => {
						const available_slot = availableSlotData?.getAvaBookingSlots?.length > 0 && availableSlotData?.getAvaBookingSlots?.includes(item)
						return (
							<TimeCard
								available_slot={available_slot}
								selected={selected?.slot}
								item={item}
								index={index}
								callBack={time => {
									LayoutAnimation.configureNext(LayoutAnimation.Presets.linear)
									setSelected({ ...selected, slot: time })
								}}
							/>
						)
					}}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().mt100, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontFamily: fontStyles.PoppinsRegular,
										fontSize: 14,
									}}
								>
									{availableSlotLoading ? 'Loading...' : 'No Slots Available'}
								</Text>
							</View>
						)
					}}
				/>
				<View style={([styles().flex, styles().justifyEnd], styles().mb20)}>
					{mutateLoader
						? <Spinner />
						: <ThemeButton
								Title={`Book Now $${Number.parseFloat(item?.appointmentPrice)?.toFixed(2)}`}
								onPress={() => handleBooking()}
							/>}
				</View>
			</View>
			<AppointmentSuccessModal
				onClose={() => setIsVisible(false)}
				visible={isVisible}
				booking_data={createLicenseBookingData?.createLicenseBooking}
			/>
		</Layout>
	)
}

export default AppointmentBooking
