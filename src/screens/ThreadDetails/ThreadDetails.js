import { Text, View, TouchableOpacity, LayoutAnimation, Platform, Image, RefreshControl, UIManager, FlatList } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Feather from '@expo/vector-icons/Feather'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import { getThreadById, threadComment } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import moment from 'moment'
import Loading from '../../context/Loading/Loading'
import UserContext from '../../context/User/User'
import Spinner from '../../component/Spinner/Spinner'
import { capitalizeFirstLetter } from '../../utils/Constants'
import TextField from '../../component/FloatTextField/FloatTextField'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const ThreadDetails = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [thread, setThread] = useState([])
	const { ticketId } = props.route.params
	const { isLoader } = useContext(Loading)
	const [isSend, setIsSend] = useState(false)
	const [newComment, setNewComment] = useState('')
	const [newCommentError, setNewCommentError] = useState(false)
	const [refreshLoader, setRefreshLoader] = useState(false)
	const [readmore, setReadmore] = useState(4)
	const characterLength = 500

	const { data, loading, error, refetch } = useQuery(getThreadById, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			threadId: ticketId,
		},
		onCompleted: data => {
			console.log('getThreadById res :', JSON.stringify(data))
			isLoader(false)
			setThread(() => data?.getThreadById)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		},
		onError: err => {
			console.log('getThreadById err :', err)
			isLoader(false)
			setRefreshLoader(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [mutate, { loading: commentLoading }] = useMutation(threadComment, {
		errorPolicy: 'all',
		onCompleted: async ({ threadComment }) => {
			console.log('threadComment res :', threadComment)
			setIsSend(false)
			await UpdateComment()
		},
		onError: err => {
			console.log('threadComment err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setIsSend(false)
		},
	})

	async function Comment() {
		if (newComment === '') {
			setNewCommentError(true)
			return
		}
		setIsSend(true)
		await mutate({
			variables: {
				threadCommentId: ticketId,
				inputComment: {
					text: newComment,
					user: user?._id,
				},
			},
		})
	}

	const refresh = async () => {
		setRefreshLoader(true)
		console.log('refresh')
		await refetch({
			threadId: ticketId,
		}).then(res => {
			setThread(() => res?.data?.getThreadById)
			setRefreshLoader(false)
		})
	}

	async function UpdateComment() {
		const myComment = {
			text: newComment,
			user: {
				_id: user?._id,
				name: user?.name,
				photo: user?.photo,
			},
		}
		console.log('myComment :', JSON.stringify(myComment))
		setThread(prev => ({ ...prev, comments: [...prev.comments, myComment] }))
		setNewComment('')
	}

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	return (
		<Layout
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => {
						refresh()
					}}
					refreshing={refreshLoader}
				/>
			}
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={thread?.thread_ref ? thread?.thread_ref : 'Issue Detail'}
			ContentArea={[styles().ph20]}
		>
			{loading
				? null
				: <View style={[styles().flex]}>
						<View style={[styles().mb15, styles().flexRow, styles().br10]}>
							<MaterialCommunityIcons
								name="flag"
								size={30}
								color={currentTheme.E8E8C8}
							/>
							<View style={[styles().ml10]}>
								<Text style={[styles().fs16, styles().fontBold, styles().textCapitalize, { color: currentTheme.headingColor }]}>
									{thread?.title ? thread?.title : 'N/A'}
								</Text>
								<View style={[styles().flexRow, styles().mt5, styles().alignCenter, styles().flexWrap]}>
									<View style={[styles().flexRow, styles().mr20, styles().alignCenter, { marginBottom: 3 }]}>
										<FontAwesome5
											name="hashtag"
											size={14}
											color={currentTheme.themeBackground}
										/>
										<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.themeBackground }]}>{thread?.thread_ref}</Text>
									</View>
									<View style={[styles().flexRow, styles().mr20, styles().alignCenter, { marginBottom: 3 }]}>
										<FontAwesome5
											name="user-alt"
											size={14}
											color={currentTheme.themeBackground}
										/>
										<Text style={[styles().fs11, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
											{thread?.author?.name ? thread?.author?.name : 'N/A'}
										</Text>
									</View>
									<View style={[styles().flexRow, styles().alignCenter, { marginBottom: 3 }]}>
										<View style={[styles().wh15px, styles().overflowH]}>
											<Image
												source={require('../../assets/images/issue-icon.png')}
												style={styles().wh100}
												resizeMode="contain"
											/>
										</View>
										<Text style={[styles().fs11, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.themeBackground }]}>
											{thread?.type ? thread?.type : 'N/A'}
										</Text>
									</View>
								</View>
								<View style={[styles().flexRow, styles().mt5, styles().alignCenter]}>
									<View style={[styles().flexRow, styles().alignCenter]}>
										<Feather
											name="clock"
											size={14}
											color={currentTheme.E8E8C8}
										/>
										<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{thread?.createdAt ? moment(thread?.createdAt).fromNow() : 'N/A'}
										</Text>
									</View>
									<View style={[styles().wh5px, styles().ml15, styles().br50, { backgroundColor: currentTheme.E8E8C8 }]} />
									<Text style={[styles().fs11, styles().ml5, styles().fontRegular, styles().textCapitalize, { color: currentTheme.E8E8C8 }]}>
										{thread?.status ? thread?.status : 'N/A'}
									</Text>
									<View style={[styles().wh5px, styles().ml15, styles().br50, { backgroundColor: currentTheme.E8E8C8 }]} />
									<View style={[styles().flexRow, styles().alignCenter]}>
										<FontAwesome
											name="comment-o"
											size={12}
											color={currentTheme.E8E8C8}
											style={[styles().ml5]}
										/>
										<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.E8E8C8 }]}>
											{thread?.comments?.length ? thread?.comments?.length : 0}
										</Text>
									</View>
								</View>
							</View>
						</View>
						<Text style={[styles().fs14, styles().fontBold, { color: currentTheme.blackish }]}>Description</Text>
						<Text
							numberOfLines={readmore}
							style={[
								styles().fs12,
								styles().lh20,
								styles().fw400,
								// styles().textCapitalize,
								{ color: currentTheme.c9E9E9E },
							]}
						>
							{thread?.content ? capitalizeFirstLetter(thread?.content) : 'N/A'}
						</Text>
						{readmore && thread?.content?.length > characterLength
							? <TouchableOpacity onPress={() => setReadmore(undefined)}>
									<Text style={[styles().fs12, { color: currentTheme.c737373 }]}>Read more...</Text>
								</TouchableOpacity>
							: null}
						<View style={[styles().btw1, styles().pt15, styles().mt15, { borderTopColor: currentTheme.c707070 }]}>
							<FlatList
								data={thread?.comments}
								showsVerticalScrollIndicator={false}
								onEndReachedThreshold={0.75}
								contentContainerStyle={{ flexGrow: 1, padding: 3 }}
								renderItem={({ item, index }) => {
									return (
										<>
											<View
												key={index}
												style={[styles().flexRow, styles().mb15]}
											>
												<View style={[styles().wh30px, styles().mr10, styles().overflowH, styles().br50]}>
													{item?.user?.photo
														? <Image
																source={{ uri: item?.user?.photo }}
																style={styles().wh100}
																resizeMode="cover"
															/>
														: <View
																style={[
																	styles().overflowH,
																	styles().justifyCenter,
																	styles().alignCenter,
																	styles().br50,
																	styles().wh30px,
																	{
																		borderWidth: 1,
																		borderColor: currentTheme.themeBackground,
																	},
																]}
															>
																<FontAwesome5
																	name="user-alt"
																	size={12}
																	color={currentTheme.themeBackground}
																/>
															</View>}
												</View>
												<View style={{ flex: 1 }}>
													<Text style={[styles().fs13, styles().fontMedium, styles().textCapitalize, { color: currentTheme.c1B1B1B }]}>{item?.user?.name}</Text>
													<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c9E9E9E, marginTop: 3 }]}>{item.text}</Text>
													<Text
														style={[
															styles().fs10,
															styles().fontRegular,
															{
																color: currentTheme.themeBackground,
																fontStyle: 'italic',
															},
														]}
													>
														{moment(item.createdAt).fromNow()}
													</Text>
												</View>
											</View>
										</>
									)
								}}
								ListEmptyComponent={() => {
									return (
										<View style={[styles().alignCenter, styles().justifyCenter, styles().mb25, styles().mt20]}>
											<Text
												style={{
													color: currentTheme.E8E8C8,
													fontSize: 14,
												}}
											>
												{'No Comments'}
											</Text>
										</View>
									)
								}}
								keyExtractor={(_item, index) => index.toString()}
							/>
							{thread?.status !== 'closed'
								? <View style={[styles().flexRow, styles().mt10, styles().alignStart, styles().justifyBetween, styles().mb20]}>
										<View style={[styles().flex]}>
											<TextField
												keyboardType="default"
												multiline={true}
												value={newComment}
												errorText={newCommentError}
												autoCapitalize="none"
												placeholder={'Write your comment'}
												style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
												stylesInput={[
													styles().fs12,
													styles().h80px,
													{
														textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
													},
												]}
												onChangeText={text => {
													setNewCommentError(false)
													setNewComment(text)
												}}
											/>
										</View>
										{!isSend
											? <TouchableOpacity
													onPress={() => Comment()}
													activeOpacity={0.5}
													style={[
														styles().wh30px,
														styles().ml5,
														styles().mt5,
														styles().alignCenter,
														styles().justifyCenter,
														styles().br20,
														{ backgroundColor: currentTheme.EEE8D5 },
													]}
												>
													<Entypo
														name="paper-plane"
														size={16}
														color={currentTheme.themeBackground}
													/>
												</TouchableOpacity>
											: <View style={[styles().ml5, styles().mt5]}>
													<Spinner size={18} />
												</View>}
									</View>
								: null}
						</View>
					</View>}
		</Layout>
	)
}

export default ThreadDetails
