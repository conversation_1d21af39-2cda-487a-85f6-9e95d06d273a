import { Text, View, Keyboard, TouchableOpacity, LayoutAnimation, TextInput, Platform, UIManager } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AntDesign from '@expo/vector-icons/AntDesign'
import Ionicons from '@expo/vector-icons/Ionicons'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import { updateUser } from '../../apollo/server'
import UserContext from '../../context/User/User'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const EditSkills = props => {
	const user = useContext(UserContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const [Loading, setLoading] = useState(false)
	const [skill, setSkill] = useState('')
	const [skillsListed, setSkillsListed] = useState([])
	const [newSkills, setNewSkills] = useState([])

	const removeSkills = index => {
		const newArr = [...skillsListed]
		newArr.splice(index, 1)
		setSkillsListed(newArr)
	}

	const addSkills = skill => {
		setNewSkills(prev => [...prev, skill])
		setSkill('')
	}

	const removeNewSkills = index => {
		const newArr = [...newSkills]
		newArr.splice(index, 1)
		setNewSkills(newArr)
	}

	const [mutate, { client }] = useMutation(updateUser, {
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
			FlashMessage({
				msg: 'Profile Updated!',
				type: 'success',
			})
			setLoading(false)
			props.navigation.goBack()
		} catch (e) {
			console.log('catch updateUser:', e)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setLoading(false)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('updateUser error  :', error)
	}

	async function UpdateSkills() {
		Keyboard.dismiss()
		setLoading(true)
		await mutate({
			variables: {
				updateUserInput: {
					skills: [...skillsListed, ...newSkills],
				},
			},
		})
	}

	useEffect(() => {
		setSkillsListed(user?.skills !== null ? user?.skills : [])
	}, [isFocus])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Edit Skills'}
			ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<View style={[styles().flex, styles().mb20]}>
				<View style={[styles().mb20, styles().flexRow, styles().alignCenter, styles().flexWrap]}>
					<View
						style={[
							styles().flexRow,
							styles().pall10,
							styles().flexWrap,
							styles().bw1,
							styles().overflowH,
							styles().br10,
							styles().alignCenter,
							{
								flexShrink: 1,
								borderColor: currentTheme.B7B7B7,
							},
						]}
					>
						{newSkills?.map((skills, index) => {
							return (
								<View
									key={index}
									style={[
										styles().flexRow,
										styles().justifyBetween,
										styles().alignCenter,
										styles().ph10,
										styles().pv10,
										styles().br50,
										styles().mr5,
										styles().mb10,
										// index === 0 ? styles().ml0 : styles().ml5,
										styles().justifyBetween,
										{ backgroundColor: currentTheme.e6e6e6 },
									]}
								>
									<Text style={[styles().fs12, styles().mr10, styles().fontRegular, { color: currentTheme.headingColor, marginBottom: 2 }]}>{skills}</Text>
									<TouchableOpacity onPress={() => removeNewSkills(index)}>
										<Ionicons
											name="close"
											size={16}
											color={currentTheme.black}
										/>
									</TouchableOpacity>
								</View>
							)
						})}
						<View style={[styles().flex, styles().flexRow, styles().alignCenter, { minWidth: 40 }]}>
							<TextInput
								value={skill}
								placeholder={'Add skills'}
								placeholderTextColor={currentTheme.CFCFCF}
								onChangeText={text => setSkill(text)}
								style={[
									styles().overflowH,
									styles().ml5,
									{
										borderColor: 'transparent',
										borderBottomColor: 'transparent',
										padding: 0,
										height: 30,
										color: currentTheme.black,
									},
								]}
							/>
							{skill.length > 2
								? <TouchableOpacity
										activeOpacity={0.5}
										onPress={() => addSkills(skill)}
									>
										<AntDesign
											name="plus"
											size={18}
											color={currentTheme.themeBackground}
										/>
									</TouchableOpacity>
								: null}
						</View>
					</View>
				</View>
				{skillsListed?.length > 0
					? <View style={[styles().flexRow, styles().flexWrap, styles().overflowH, styles().br10, styles().alignCenter]}>
							{skillsListed?.map((skills, index) => {
								return (
									<View
										key={index}
										style={[
											styles().flexRow,
											styles().justifyBetween,
											styles().alignCenter,
											styles().ph10,
											styles().pv10,
											styles().br50,
											styles().mr5,
											styles().mb10,
											// index === 0 ? styles().ml0 : styles().ml5,
											styles().justifyBetween,
											{ backgroundColor: currentTheme.e6e6e6 },
										]}
									>
										<Text style={[styles().fs12, styles().mr10, styles().fontRegular, { color: currentTheme.headingColor, marginBottom: 2 }]}>{skills}</Text>
										<TouchableOpacity onPress={() => removeSkills(index)}>
											<Ionicons
												name="close"
												size={16}
												color={currentTheme.black}
											/>
										</TouchableOpacity>
									</View>
								)
							})}
						</View>
					: <View style={[styles().alignSelfCenter, styles().mt30]}>
							<Text style={[styles().fs14, { color: currentTheme.CFCFCF }]}>No skills</Text>
						</View>}
				<View style={[styles().justifyEnd, styles().flex, styles().mb20]}>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => UpdateSkills()}
								Title={'Save'}
								Style={styles().br10}
							/>}
				</View>
			</View>
		</Layout>
	)
}

export default EditSkills
