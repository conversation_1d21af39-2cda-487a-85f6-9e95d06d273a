import { Text, View, LayoutAnimation, Platform, UIManager, FlatList, RefreshControl } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'
import { Ratings } from '../../apollo/server'
import ReviewsComponent from '../../component/Reviews/Reviews'
import StarRating from 'react-native-star-rating-widget'
import ProgressLine from '../../component/ProgressLine/ProgressLine'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Reviews = props => {
	const { user, overallRatings } = props?.route?.params
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	// let user = useContext(UserContext);
	const pageSize = 20
	const [ratings, setRatings] = useState([])
	const [Loading, setLoading] = useState(true)
	const [page, setPage] = useState(1)

	const { data, loading, error, refetch } = useQuery(Ratings, {
		errorPolicy: 'all',
		fetchPolicy: 'cache-and-network',
		variables: {
			filters: {
				user: user?._id,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log('Ratings res :', JSON.stringify(data?.Ratings));
			setLoading(false)
			setRatings(prev => [...prev, ...data?.Ratings?.results])
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		},
		onError: err => {
			console.log('Ratings err :', err)
			setLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const nextPage = async () => {
		if (page < data?.Ratings?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
				filters: null,
			}).then(_res => {})
			console.log('rating page :', page)
		}
	}

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setRatings([])
		// await user?.refetch()
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setRatings(_prev => res?.data?.Ratings?.results)
			setLoading(false)
			console.log('ratings refreshed')
		})
	}
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			keyBoardArea={50}
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => refresh()}
					refreshing={Loading}
				/>
			}
			pagetitle={'Reviews & Ratings'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View
					style={[
						styles().mt10,
						styles().br10,
						styles().flexRow,
						styles().alignCenter,
						styles().justifyBetween,
						styles().pall20,
						{ backgroundColor: currentTheme.F8F9FA },
					]}
				>
					<View style={[styles().mr15, styles().mt10]}>
						<Text style={[styles().fs32, styles().mb10, styles().fontMedium, { color: currentTheme.headingColor }]}>
							{user?.ratings ? user?.ratings?.toFixed(1) : 0}
							<Text style={[styles().fs20, styles().ml10, styles().fontMedium, { color: currentTheme.headingColor }]}>/5</Text>
						</Text>
						<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>
							{`Based on ${user?.ratingsCount ? user?.ratingsCount : 0} Review`}
						</Text>
						<View style={[styles().flexRow, styles().mt15]}>
							<StarRating
								onChange={() => {}}
								enableHalfStar={true}
								starSize={20}
								starStyle={styles().ml5}
								maxStars={5}
								rating={user?.ratings}
								color={currentTheme.starColor}
								emptyColor={currentTheme.e6e6e6}
							/>
						</View>
					</View>
					<View style={[styles().flex]}>
						{overallRatings?.map((rating, i) => {
							return (
								<View
									key={i}
									style={[styles().flexRow, styles().flex, styles().alignCenter, styles().justifyBetween]}
								>
									<Text style={[styles().fs12, styles().mr10, { color: currentTheme.textColor }]}>{`${rating?.stars} Star`}</Text>
									<View style={[styles().flex, styles().overflowH, styles().br10, { height: 8, backgroundColor: currentTheme.e6e6e6 }]}>
										<ProgressLine progress={rating?.percentage} />
									</View>
								</View>
							)
						})}
					</View>
				</View>
				<View style={[styles().mt20, styles().pt20, styles().flex, styles().btw1, { borderTopColor: currentTheme.e6e6e6 }]}>
					<FlatList
						data={ratings}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={{ flexGrow: 1 }}
						onEndReachedThreshold={0.5}
						onEndReached={() => nextPage()}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontSize: 14,
										}}
									>
										{Loading ? 'Loading...' : 'No reviews & ratings'}
									</Text>
								</View>
							)
						}}
						renderItem={({ item, index }) => {
							return (
								<View style={{ paddingHorizontal: 3 }}>
									<ReviewsComponent
										item={item}
										index={index}
									/>
								</View>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={<View style={[styles().wh30px]} />}
					/>
				</View>
			</View>
		</Layout>
	)
}

export default Reviews
