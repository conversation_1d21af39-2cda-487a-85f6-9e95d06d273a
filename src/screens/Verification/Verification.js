import { Text, View, TouchableOpacity } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { CodeField, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field'
import { ForgotPasswordVerification, forgotPassword, sendVerificationEmail, verifyEmail } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import { useMutation } from '@apollo/client'
import InfoPopup from '../../component/Modals/infoPopup'
import FlashMessage from '../../component/FlashMessage/FlashMessage'

const CELL_COUNT = 6

const Verification = props => {
	const { email, verification } = props?.route?.params
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [value, setValue] = useState('')
	const [Loading, setLoading] = useState(false)
	const [emailVerified, setEmailVerified] = useState(false)
	const [countdownTimer, setCountdownTimer] = useState()
	const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT })
	const [_propss, getCellOnLayoutHandler] = useClearByFocusCell({
		value,
		setValue,
	})

	useEffect(() => {
		const unsubscribe = props.navigation.addListener('focus', async () => {
			setCountdownTimer(1 * 180)
		})
		return unsubscribe
	}, [props.navigation])

	useEffect(() => {
		const interval = setInterval(() => {
			if (countdownTimer > 0) {
				setCountdownTimer(prev => --prev)
			}
		}, 1000)

		return () => clearInterval(interval)
	}, [countdownTimer])

	const [mutate, { client }] = useMutation(verification ? sendVerificationEmail : forgotPassword, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('verify res :', data)
			FlashMessage({
				msg: 'Verification code sent to your email.',
				type: 'success',
			})
			setCountdownTimer(1 * 180)
		} catch (e) {
			console.log(e)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('verify error  :', error)
	}

	const [verify_mutate, { verify_client }] = useMutation(verification ? verifyEmail : ForgotPasswordVerification, {
		errorPolicy: 'all',
		onCompleted: onCompleted_verify,
		onError: onError_verify,
	})

	async function onCompleted_verify(data) {
		try {
			if (verification) {
				console.log('verifyEmail res :', data)
				setEmailVerified(true)
			} else {
				console.log('ForgotPasswordVerification res :', data)
				FlashMessage({
					msg: 'Account Verified!',
					type: 'success',
				})
				setLoading(false)
				props.navigation.navigate('ResetPassword', {
					email: email,
					token: data?.forgotPasswordVerification?.token,
				})
			}
		} catch (e) {
			console.log(e)
			setLoading(false)
		} finally {
			setLoading(false)
		}
	}

	function onError_verify(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('verify error  :', error)
	}

	async function Verify() {
		let status = true
		if (value.length !== 6) {
			FlashMessage({ msg: 'Enter 6-Digits OTP!', type: 'warning' })
			status = false
			return
		}
		if (status) {
			setLoading(true)
			await verify_mutate({
				variables: {
					email: email,
					code: value,
				},
			})
		}
	}

	return (
		<AuthLayout
			navigation={props.navigation}
			withoutScroll={true}
			withBg={true}
			pagetitle={'Verify Your Account'}
			headerShown={true}
			LeftIcon={true}
		>
			<View style={[styles().mt50, styles().flex, styles().ph20]}>
				<View style={[styles().flex]}>
					<View style={[styles().mb20]}>
						<Text style={[styles().fs16, styles().mb15, styles().fontBold, { color: currentTheme.headingColor }]}>Verify your Email</Text>
						<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.headingColor }]}>
							Please enter the 6 digits code sent to {'\n'} your email.
						</Text>
					</View>
					<View style={[styles().w100, styles().alignSelfCenter, styles().mb25]}>
						<CodeField
							ref={ref}
							{...props}
							// Use `caretHidden={false}` when users can't paste a text value, because context menu doesn't appear
							value={value}
							onChangeText={setValue}
							cellCount={CELL_COUNT}
							rootStyle={[]}
							keyboardType="number-pad"
							textContentType="oneTimeCode"
							renderCell={({ index, symbol, isFocused }) => {
								return (
									<View
										key={index}
										style={[
											styles(currentTheme).verifyContainer,
											isFocused && {
												borderColor: currentTheme.themeBackground,
												borderWidth: 1,
											},
										]}
									>
										<Text
											style={[
												styles().fs24,
												styles().fontRegular,
												{
													color: currentTheme.themeBackground,
													backgroundColor: currentTheme.white,
												},
											]}
											onLayout={getCellOnLayoutHandler(index)}
										>
											{symbol || (isFocused ? <Cursor /> : null)}
										</Text>
									</View>
								)
							}}
						/>

						<View style={[styles().mt35, styles().mb15, styles().flexRow, styles().alignCenter, styles().justifyCenter, styles().alignCenter]}>
							{countdownTimer > 0 && (
								<Text style={[styles().fontRegular, { color: currentTheme.headingColor }, styles().fs16]}>
									Resend code after{' '}
									<Text style={[styles().fontBold, { color: currentTheme.themeBackground }]}>
										{`${Math.floor(countdownTimer / 60)
											.toString()
											.padStart(2, '0')}:${(countdownTimer % 60).toString().padStart(2, '0')} `}
									</Text>
									Min.
								</Text>
							)}
							{countdownTimer === 0 && (
								<TouchableOpacity
									onPress={() => {
										mutate({
											variables: {
												email: email?.toLowerCase()?.trim(),
											},
										})
									}}
								>
									<Text
										style={[
											styles().textDecorationUnderline,
											styles().fs16,

											styles().fw600,
											{ color: currentTheme.headingColor },
										]}
									>
										Resend Code
									</Text>
								</TouchableOpacity>
							)}
						</View>
					</View>
					<View style={[styles().mb20]}>
						{Loading
							? <Spinner />
							: <ThemeButton
									onPress={() => Verify()}
									Title={'Confirm'}
									Style={{
										backgroundColor: currentTheme.headingColor,
										borderColor: currentTheme.headingColor,
									}}
								/>}
					</View>
				</View>
			</View>
			<InfoPopup
				ModalIcon={true}
				ModalHeading={'Verified!'}
				ModalText={'You have successfully verified the account.'}
				modalVisible={emailVerified}
				ActionBtn1={() => {
					setEmailVerified(false)
					props.navigation.navigate('Login')
				}}
				ActionBtn1Text={'Start'}
			/>
		</AuthLayout>
	)
}

export default Verification
