import { View, FlatList, Text, Image, TouchableOpacity, RefreshControl } from 'react-native'
import React, { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Entypo from '@expo/vector-icons/Entypo'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { users } from '../../apollo/server'
import UserContext from '../../context/User/User'
import SearchUserFilter from '../../component/Modals/SearchUserFilter'

const HomeSearch = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [search, setSearch] = useState('')
	const [searchResults, setSearchResults] = useState([])
	const [addfriendList, _setAddfriendList] = useState([])
	const [Loading, setLoading] = useState(false)
	const [showclearfilter, setShowclearfilter] = useState(false)
	const [isFilter, setIsFilter] = useState(false)
	const [filter, setFilter] = useState(null)
	const [page, setPage] = useState(1)
	const pageSize = 30

	const { data, loading, error, refetch } = useQuery(users, {
		fetchPolicy: 'no-cache',
		variables: {
			filters: { name: search }, //empty to search nothing else this will give response with only {}
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			console.log('users res :', JSON.stringify(data?.users))
			const myArray = data?.users?.results?.filter(obj => obj?._id !== user?._id)
			setSearchResults(prev => [...prev, ...myArray])
			setLoading(false)
		},
		onError: err => {
			console.log('users err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	// const [sendFriendRequest, {}] = useMutation(addFriendship, {
	//   onCompleted: async res => {
	//     console.log('addFriendship res :', res.addFriendship);
	//     let arr = [];
	//     let friend = res?.addFriendship;
	//     arr.push(friend);
	//     setAddfriendList(prev => [...prev, ...arr]);
	//   },
	//   onError: err => {
	//     console.log('addFriendship Err :', err);
	//     FlashMessage({msg: err.message, type: 'danger'});
	//   },
	// });

	// async function sendRequest(_id) {
	//   await sendFriendRequest({
	//     variables: {
	//       friendId: _id,
	//     },
	//   });
	// }

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		console.log('refresh')
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setSearchResults(_prev => res?.data?.users?.results)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.users?.totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
				filters: search ? { name: search } : {},
			})
		}
	}

	const onFilter = async filter => {
		setFilter(filter)
		setShowclearfilter(true)
		console.log('filter values :', filter)
		setLoading(true)
		setSearch('')
		setPage(1)
		setSearchResults([])
		console.log('filter page :', page)
		await refetch({
			filters: { ...filter },
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			console.log('filter data =======>', JSON.stringify(res?.data?.users))
			const myArray = res?.data?.users?.results?.filter(obj => obj?._id !== user?._id)
			setSearchResults(myArray)
			setLoading(false)
		})
	}

	const clearFiter = async () => {
		setPage(1)
		setSearch('')
		setShowclearfilter(false)
		setFilter(null)
		setSearchResults([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
			filters: filter,
		})
	}

	React.useEffect(() => {
		if (search.length > 2) {
			setPage(1)
			const searchData = setTimeout(async () => {
				setLoading(true)
				setShowclearfilter(false)
				setFilter(null)
				await refetch({
					filters: search ? { name: search.trim() } : {},
					options: { page: page, limit: pageSize, sortBy: 'createdAt:desc' },
				}).then(res => {
					const myArray = res?.data?.users?.results?.filter(obj => obj?._id !== user?._id)
					setSearchResults(myArray)
					// setSearchResults(prev => res?.data?.users?.results);
					setLoading(false)
				})
			}, 1500)
			return () => clearTimeout(searchData)
		}
	}, [search])

	console.log(page, searchResults.length)
	console.log('search :', search)
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Search'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
					<View style={[styles().flex]}>
						<TextField
							keyboardType="default"
							value={search}
							// errorText={searchError}
							autoCapitalize="none"
							placeholder={'Search'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => setSearch(text)}
						/>
					</View>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => setIsFilter(true)}
						style={[styles().wh35px, styles().ml15, styles().alignCenter, styles().justifyCenter, styles().br20, { backgroundColor: currentTheme.EEE8D5 }]}
					>
						<FontAwesome
							name="filter"
							size={20}
							color={currentTheme.themeBackground}
						/>
					</TouchableOpacity>
				</View>
				{showclearfilter
					? <TouchableOpacity
							onPress={() => clearFiter()}
							style={[styles().flexRow, styles().alignCenter, styles().mb15, { width: 130 }]}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black, marginTop: 1 }]}>Clear Filters</Text>
							<Entypo
								name={'cross'}
								size={20}
							/>
						</TouchableOpacity>
					: null}
				<FlatList
					data={searchResults}
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ flexGrow: 1 }}
					onEndReachedThreshold={0.5}
					onEndReached={() => nextPage()}
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					renderItem={({ item, index }) => {
						const _isRequestSent = addfriendList?.some(some => some?._id === item?._id)
						return (
							<TouchableOpacity
								onPress={() =>
									props.navigation.navigate('ViewFriendProfile', {
										userId: item?._id,
										isFriend: undefined,
										isRequested: undefined,
									})
								}
								activeOpacity={0.7}
								style={[
									styles().flexRow,
									styles().mb20,
									index === 0 && styles().mt10,
									styles().alignCenter,
									styles().justifyBetween,
									styles().bbw1,
									styles().pb10,
									{ borderBottomColor: currentTheme.c707070 },
								]}
							>
								{item?.photo
									? <View style={[styles().wh35px, styles().overflowH, styles().br50]}>
											<Image
												source={{ uri: item?.photo }}
												style={styles().wh100}
												resizeMode="cover"
											/>
										</View>
									: <View
											style={[
												styles().overflowH,
												styles().justifyCenter,
												styles().alignCenter,
												styles().br50,
												styles().wh35px,
												{
													borderWidth: 1,
													borderColor: currentTheme.themeBackground,
												},
											]}
										>
											<FontAwesome5
												name="user-alt"
												size={16}
												color={currentTheme.themeBackground}
											/>
										</View>}
								<View style={[styles().flex, styles().ml10]}>
									<View style={[styles().flexRow, styles().alignCenter]}>
										<Text
											numberOfLines={2}
											style={[styles().fs12, styles().fontMedium, { color: currentTheme.black, letterSpacing: 0.3 }]}
										>
											{item?.name}
										</Text>
										{item?.profileVerified && (
											<View style={[styles().wh10px, styles().ml5, styles().overflowH]}>
												<Image
													source={require('../../assets/images/verified-icon.png')}
													style={styles().wh100}
													resizeMode="cover"
												/>
											</View>
										)}
									</View>
									<View>
										<Text style={[styles().fs10, styles().fontRegular, styles().textCapitalize, { color: currentTheme.c9E9E9E, letterSpacing: 0.5 }]}>
											{item?.role === 'matePilot' ? 'Mate Pilot' : item?.role}
										</Text>
									</View>
								</View>
								<FontAwesome5
									name="arrow-right"
									color={currentTheme.D5D4D4}
									size={16}
								/>
								{/* <ThemeButton
                disabled={isRequestSent ? true : false}
                onPress={() => sendRequest(item?._id)}
                Title={isRequestSent ? 'Request Sent' : 'Add Friend'}
                Style={[
                  styles().h35px,
                  isRequestSent && {
                    backgroundColor: currentTheme.B7B7B7,
                    borderColor: currentTheme.B7B7B7,
                  },
                ]}
                StyleText={[
                  styles().fs12,
                  {marginTop: 3},
                  isRequestSent && {color: currentTheme.black},
                  styles().fw400,
                ]}
              /> */}
							</TouchableOpacity>
						)
					}}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={[
										styles().fontRegular,
										{
											color: currentTheme.E8E8C8,
											fontSize: 14,
										},
									]}
								>
									{Loading ? 'Loading...' : `${search ? `${search} not found...` : 'Search for people...'}`}
								</Text>
							</View>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={<View style={[styles().wh50px]} />}
				/>
				<SearchUserFilter
					ModalHeading={'Filters'}
					modalVisible={isFilter}
					onClose={() => setIsFilter(false)}
					filters={filters => onFilter(filters)}
					clear={() => {
						if (showclearfilter === false) {
							return true
						}
					}}
				/>
			</View>
		</Layout>
	)
}

export default React.memo(HomeSearch)
