import { Text, View, FlatList } from 'react-native'
import { useCallback, useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { getMyWaterWayBadges } from '../../apollo/server'
import { useQuery } from '@apollo/client'
import Badges from '../../component/Badges/Badges'
import BadgeModal from '../../component/Modals/BadgeModal'

const AllBadges = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [badge, setBadge] = useState('')
	const [showBadgeModal, setShowBadgeModal] = useState(false)

	const {
		data: mybadgesData,
		loading: mybadgesLoader,
		refetch: mybadgesRefetch,
	} = useQuery(getMyWaterWayBadges, {
		errorPolicy: 'all',
		fetchPolicy: 'network-only',
		variables: {
			userId: user?._id,
		},
		onCompleted: _data => {
			// console.log(
			//   'getMyWaterWayBadges res :',
			//   JSON.stringify(data?.getMyWaterWayBadges),
			// );
		},
		onError: err => {
			console.log('getMyWaterWayBadges err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const handleBadge = useCallback(badge => {
		setBadge(badge)
		setShowBadgeModal(true)
	})

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Badges'}
			navigation={props.navigation}
		>
			<View style={[styles().ph20, styles().flex]}>
				{showBadgeModal && (
					<BadgeModal
						currentUser={'you'}
						badge={badge}
						visible={showBadgeModal}
						onClose={() => setShowBadgeModal(false)}
					/>
				)}
				<FlatList
					data={mybadgesData?.getMyWaterWayBadges}
					showsVerticalScrollIndicator={false}
					renderItem={({ index, item }) => (
						<Badges
							index={index}
							badge={item}
							callback={badge => handleBadge(badge)}
						/>
					)}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().mt100]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{mybadgesLoader ? 'Loading...' : ' No Bagdes'}
								</Text>
							</View>
						)
					}}
				/>
			</View>
		</Layout>
	)
}

export default AllBadges
