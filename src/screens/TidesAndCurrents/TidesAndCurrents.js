import { View } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { WebView } from 'react-native-webview'
import Loading from '../../context/Loading/Loading'

const TidesAndCurrents = props => {
	const [loading, setLoading] = useState(true)
	const { isLoader } = useContext(Loading)
	// let end_date = moment(new Date('2023-06-25')).format('YYYY/MM/DD');
	// let start_date = moment(new Date('2023-06-21')).format('YYYY/MM/DD');
	const start_date = '2022-01-01'
	const end_date = '2022-01-07'
	const station_id = '8454000'
	const url = 'https://tidesandcurrents.noaa.gov/map/index.html?region=Texas'
	const _clientURL = `https://tidesandcurrents.noaa.gov/api/datagetter?product=predictions&application=NOS.COOPS.TAC.WL&begin_date=${start_date}&end_date=${end_date}&datum=MLLW&station=${station_id}&time_zone=lst_ldt&units=english&interval=hilo&format=json`
	useEffect(() => {
		isLoader(loading)
	}, [loading])
	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			keyBoardArea={50}
			pagetitle={'Tides & Currents'}
		>
			<View style={[styles().flex]}>
				<WebView
					source={{
						uri: url,
					}}
					style={{ flex: 1 }}
					onLoad={() => setLoading(true)}
					onLoadEnd={() => setLoading(false)}
				/>
			</View>
		</Layout>
	)
}

export default TidesAndCurrents
