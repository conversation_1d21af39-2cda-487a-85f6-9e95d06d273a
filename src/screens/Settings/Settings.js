import { Text, View, TouchableOpacity, Image, Switch } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Layout from '../../component/Layout/Layout'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation } from '@apollo/client'
import { updateUser } from '../../apollo/server'
import UserContext from '../../context/User/User'
import AccountDeactivate from '../../component/Modals/AccountDeactivate'
import { useIsFocused } from '@react-navigation/native'
import Dropdown from '../../component/DropDown/Dropdopwn'
import AccountDelete from '../../component/Modals/AccountDelete'
import { switchSize, user_eligible } from '../../utils/Constants'
import Eligible from '../../context/EligibleContext/EligibleContext'

const Settings = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { setStripeEligible, setCheckrEligible } = useContext(Eligible)
	const [accountDeactivate, setAccountDeactivate] = useState(false)
	const [accountDelete, setAccountDelete] = useState(false)
	const user = useContext(UserContext)
	const isFocus = useIsFocused()
	const postPrivacy = [
		'onlyMe',
		'friends',
		//  'public'
	]

	function SectionHeading(props) {
		return (
			<View style={[styles().mb10]}>
				<Text style={[styles().fs14, styles().fw700, { color: currentTheme.themeBackground }]}>{props.title}</Text>
			</View>
		)
	}

	const [jobOffer, setJobOffer] = useState(false)
	const [friendRequest, setFriendRequest] = useState(false)
	const [postNoti, setPostNoti] = useState(false)
	const [generalNoti, setGeneralNoti] = useState(false)
	const [hideEmail, setHideEmail] = useState(false)
	const [hideNumber, setHideNumber] = useState(false)
	const [hideRecommendations, setHideRecommendations] = useState(false)
	const [chatNotifications, setChatNotifications] = useState(false)
	const [myPostPrivacy, setMypostPrivacy] = useState('')
	const [reviews, setReviews] = useState(false)
	// const switchSize =
	//   Platform.OS === 'ios'
	//     ? [{scaleX: 0.8}, {scaleY: 0.8}]
	//     : [{scaleX: 1}, {scaleY: 1}];

	const [mutate, { client }] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('updateUser res :', data?.updateUser)
		} catch (e) {
			console.log('catch updateUser:', e)
		} finally {
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		console.log('updateUser error  :', error)
	}

	const isPostNotifiactions = async boolean => {
		setPostNoti(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					postNotification: boolean,
				},
			},
		})
	}

	const isJobNotifiactions = async boolean => {
		setJobOffer(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					jobOfferNotification: boolean,
				},
			},
		})
	}

	const isFriendNotifiactions = async boolean => {
		setFriendRequest(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					friendRequestNotification: boolean,
				},
			},
		})
	}

	const isGeneraltNotifiactions = async boolean => {
		setGeneralNoti(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					generalNotification: boolean,
				},
			},
		})
	}

	const isEmailVisible = async boolean => {
		setHideEmail(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					hideEmail: boolean,
				},
			},
		})
	}

	const isRecommendationVisible = async boolean => {
		setHideRecommendations(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					hideRecommendations: boolean,
				},
			},
		})
	}

	const isContactVisible = async boolean => {
		setHideNumber(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					hideContact: boolean,
				},
			},
		})
	}

	const isChatNotifications = async boolean => {
		setChatNotifications(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					chatNotifications: boolean,
				},
			},
		})
	}

	const isPostVisibleToFriends = async value => {
		setMypostPrivacy(value)
		await mutate({
			variables: {
				updateUserInput: {
					postSettings: value,
				},
			},
		})
	}

	const isReviews = async boolean => {
		setReviews(boolean)
		await mutate({
			variables: {
				updateUserInput: {
					hideReviews: boolean,
				},
			},
		})
	}

	const handleVerify = () => {
		if (user_eligible(user).status) {
			props.navigation.navigate('ApplyForVerify')
		} else {
			// if (user_eligible(user).type === 'stripe') {
			//   setStripeEligible(true);
			// }
			if (user_eligible(user).type === 'checkr') {
				setCheckrEligible(true)
			}
		}
	}

	useEffect(() => {
		setJobOffer(user?.jobOfferNotification)
		setFriendRequest(user?.friendRequestNotification)
		setPostNoti(user?.postNotification)
		setGeneralNoti(user?.generalNotification)
		setHideEmail(user?.hideEmail)
		setHideNumber(user?.hideContact)
		setChatNotifications(user?.chatNotifications)
		setHideRecommendations(user?.hideRecommendations)
		setMypostPrivacy(user?.postSettings)
		setReviews(user?.hideReviews)
	}, [isFocus])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Settings and Privacy'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex, styles().mt10]}>
				<View style={[styles().mb20]}>
					<SectionHeading title={'Account Setting'} />
					<TouchableOpacity
						onPress={() => props.navigation.navigate('ChangePassword')}
						style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().bbw1, styles().pb5, { borderColor: currentTheme.E8E8E8 }]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Change Password</Text>
						<FontAwesome
							name="angle-right"
							size={20}
							color={currentTheme.blackish}
						/>
					</TouchableOpacity>

					<TouchableOpacity
						// onPress={() => props.navigation.navigate('ChangePassword')}
						onPress={() => props.navigation.navigate('Recovery')}
						style={[
							styles().flexRow,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb10,
							styles().pt10,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Recovery Information</Text>
						<FontAwesome
							name="angle-right"
							size={20}
							color={currentTheme.blackish}
						/>
					</TouchableOpacity>
				</View>

				<View style={styles().mb20}>
					<SectionHeading title={'Notifications'} />
					<View
						style={[
							styles().flexRow,
							styles().mb5,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb5,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Job Offer</Text>
						<Switch
							style={{ transform: switchSize }}
							trackColor={{
								false: currentTheme.E2E2E2,
								true: currentTheme.E2E2E2,
							}}
							thumbColor={jobOffer ? currentTheme.lightGreen : currentTheme.themeBackground}
							ios_backgroundColor={currentTheme.F4F5F6}
							onValueChange={boolean => isJobNotifiactions(boolean)}
							value={jobOffer}
						/>
					</View>
					<View
						style={[
							styles().flexRow,
							styles().mb5,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb5,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Friend Request</Text>
						<Switch
							style={{ transform: switchSize }}
							trackColor={{
								false: currentTheme.E2E2E2,
								true: currentTheme.E2E2E2,
							}}
							thumbColor={friendRequest ? currentTheme.lightGreen : currentTheme.themeBackground}
							ios_backgroundColor={currentTheme.F4F5F6}
							onValueChange={boolean => isFriendNotifiactions(boolean)}
							value={friendRequest}
						/>
					</View>
					<View
						style={[
							styles().flexRow,
							styles().mb5,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb5,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Post Notification</Text>
						<Switch
							style={{ transform: switchSize }}
							trackColor={{
								false: currentTheme.E2E2E2,
								true: currentTheme.E2E2E2,
							}}
							thumbColor={postNoti ? currentTheme.lightGreen : currentTheme.themeBackground}
							ios_backgroundColor={currentTheme.F4F5F6}
							onValueChange={boolean => isPostNotifiactions(boolean)}
							value={postNoti}
						/>
					</View>
					<View
						style={[
							styles().flexRow,
							styles().mb5,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb5,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>General Notification</Text>
						<Switch
							style={{ transform: switchSize }}
							trackColor={{
								false: currentTheme.E2E2E2,
								true: currentTheme.E2E2E2,
							}}
							thumbColor={generalNoti ? currentTheme.lightGreen : currentTheme.themeBackground}
							ios_backgroundColor={currentTheme.F4F5F6}
							onValueChange={boolean => isGeneraltNotifiactions(boolean)}
							value={generalNoti}
						/>
					</View>
					<View
						style={[
							styles().flexRow,
							styles().mb5,
							styles().alignCenter,
							styles().justifyBetween,
							styles().bbw1,
							styles().pb5,
							{ borderColor: currentTheme.E8E8E8 },
						]}
					>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Chat Notification</Text>
						<Switch
							style={{ transform: switchSize }}
							trackColor={{
								false: currentTheme.E2E2E2,
								true: currentTheme.E2E2E2,
							}}
							thumbColor={chatNotifications ? currentTheme.lightGreen : currentTheme.themeBackground}
							ios_backgroundColor={currentTheme.F4F5F6}
							onValueChange={boolean => isChatNotifications(boolean)}
							value={chatNotifications}
						/>
					</View>
				</View>

				<SectionHeading title={'Privacy'} />
				<View
					style={[
						styles().flexRow,
						styles().mb5,
						styles().alignCenter,
						styles().justifyBetween,
						styles().bbw1,
						styles().pb5,
						{ borderColor: currentTheme.E8E8E8 },
					]}
				>
					<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Hide Email</Text>
					<Switch
						style={{ transform: switchSize }}
						trackColor={{
							false: currentTheme.E2E2E2,
							true: currentTheme.E2E2E2,
						}}
						thumbColor={hideEmail ? currentTheme.lightGreen : currentTheme.themeBackground}
						ios_backgroundColor={currentTheme.F4F5F6}
						onValueChange={boolean => isEmailVisible(boolean)}
						value={hideEmail}
					/>
				</View>
				<View
					style={[
						styles().flexRow,
						styles().mb5,
						styles().alignCenter,
						styles().justifyBetween,
						styles().bbw1,
						styles().pb5,
						{ borderColor: currentTheme.E8E8E8 },
					]}
				>
					<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Hide Number</Text>
					<Switch
						style={{ transform: switchSize }}
						trackColor={{
							false: currentTheme.E2E2E2,
							true: currentTheme.E2E2E2,
						}}
						thumbColor={hideNumber ? currentTheme.lightGreen : currentTheme.themeBackground}
						ios_backgroundColor={currentTheme.F4F5F6}
						onValueChange={boolean => isContactVisible(boolean)}
						value={hideNumber}
					/>
				</View>
				<View
					style={[
						styles().flexRow,
						styles().mb5,
						styles().alignCenter,
						styles().justifyBetween,
						styles().bbw1,
						styles().pb5,
						{ borderColor: currentTheme.E8E8E8 },
					]}
				>
					<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Hide Recommendations</Text>
					<Switch
						style={{ transform: switchSize }}
						trackColor={{
							false: currentTheme.E2E2E2,
							true: currentTheme.E2E2E2,
						}}
						thumbColor={hideRecommendations ? currentTheme.lightGreen : currentTheme.themeBackground}
						ios_backgroundColor={currentTheme.F4F5F6}
						onValueChange={boolean => isRecommendationVisible(boolean)}
						value={hideRecommendations}
					/>
				</View>
				<View
					style={[
						styles().flexRow,
						styles().mb5,
						styles().alignCenter,
						styles().justifyBetween,
						styles().bbw1,
						styles().pb5,
						{ borderColor: currentTheme.E8E8E8 },
					]}
				>
					<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.blackish }]}>Hide Reviews</Text>
					<Switch
						style={{ transform: switchSize }}
						trackColor={{
							false: currentTheme.E2E2E2,
							true: currentTheme.E2E2E2,
						}}
						thumbColor={reviews ? currentTheme.lightGreen : currentTheme.themeBackground}
						ios_backgroundColor={currentTheme.F4F5F6}
						onValueChange={boolean => isReviews(boolean)}
						value={reviews}
					/>
				</View>
				<View style={[styles().mt20]}>
					<SectionHeading title={'Posts'} />
					<View style={[styles().w100, styles().mb15]}>
						<Dropdown
							placeholder={myPostPrivacy ? (myPostPrivacy === 'onlyMe' ? 'Only Me' : myPostPrivacy) : 'Set Posts Privacy...'}
							data={postPrivacy}
							selectedValue={value => isPostVisibleToFriends(value)}
						/>
					</View>
				</View>
				{user?.disposable === 'false'
					? <TouchableOpacity
							activeOpacity={0.7}
							onPress={() => handleVerify()}
							style={[styles().flexRow, styles().mb5, styles().alignCenter, styles().justifyBetween, styles().pb5, styles().mt20]}
						>
							<View style={[styles().flexRow, styles().alignCenter]}>
								<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.darkBlue }]}>Apply for verified account</Text>
								<View style={[styles().wh15px, styles().ml5, styles().overflowH]}>
									<Image
										source={require('../../assets/images/verified-icon.png')}
										style={styles().wh100}
										resizeMode="cover"
									/>
								</View>
							</View>
							<FontAwesome
								name="angle-right"
								size={20}
								color={currentTheme.blackish}
							/>
						</TouchableOpacity>
					: null}
				<View style={[styles().justifyEnd, styles().mt20, styles().mb30]}>
					<ThemeButton
						Title={'Deactivate Account'}
						onPress={() => setAccountDeactivate(true)}
						Style={[
							{
								backgroundColor: currentTheme.red,
								borderColor: currentTheme.red,
							},
						]}
					/>
					<ThemeButton
						Title={'Delete Account'}
						onPress={() => setAccountDelete(true)}
						Style={[
							{
								backgroundColor: currentTheme.red,
								borderColor: currentTheme.red,
							},
						]}
					/>
				</View>
			</View>
			<AccountDeactivate
				onClose={() => setAccountDeactivate(false)}
				visible={accountDeactivate}
				props={props}
			/>
			<AccountDelete
				onClose={() => setAccountDelete(false)}
				visible={accountDelete}
				props={props}
			/>
		</Layout>
	)
}

export default Settings
