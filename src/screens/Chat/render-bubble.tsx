export const RenderBubble = props => {
	const { currentMessage, position, nextMessage } = props

	if (currentMessage?.type === 'card') {
		return <CustomCard {...props} />
	}
	if (currentMessage?.type === 'badge') {
		return <BadgeCard {...props} />
	}
	if (currentMessage?.type === 'application') {
		return <JobApplicationCard {...props} />
	}

	const isMyMessage = currentMessage?.user?._id === user?._id

	const shouldShowAvatar = !isMyMessage && (!nextMessage || !nextMessage.user || nextMessage.user._id !== currentMessage.user._id)

	const getUserInitials = user => {
		if (!user?.name) return 'U'
		const names = user.name.split(' ')
		if (names.length >= 2) {
			return `${names[0][0]}${names[1][0]}`.toUpperCase()
		}
		return user.name[0].toUpperCase()
	}

	console.log('RenderBubble', { currentMessage })

	return (
		<View style={[styles().flexRow, styles().alignItems, { justifyContent: isMyMessage ? 'flex-end' : 'flex-start' }]}>
			{!isMyMessage && (
				<View
					style={{
						width: 30,
						height: 30,
						marginRight: 8,
						marginBottom: 2,
					}}
				>
					{shouldShowAvatar &&
						(currentMessage?.user?.avatar ? (
							<Image
								source={{ uri: currentMessage.user.avatar }}
								style={{
									width: 30,
									height: 30,
									borderRadius: 15,
								}}
								resizeMode="cover"
							/>
						) : (
							<View
								style={{
									width: 30,
									height: 30,
									borderRadius: 15,
									backgroundColor: currentTheme.themeBackground,
									justifyContent: 'center',
									alignItems: 'center',
								}}
							>
								<Text
									style={{
										color: currentTheme.white,
										fontSize: 12,
										fontWeight: 'bold',
									}}
								>
									{getUserInitials(currentMessage?.user)}
								</Text>
							</View>
						))}
				</View>
			)}

			<Bubble
				{...props}
				renderCustomView={props => {
					if (props.currentMessage.file) {
						return <RenderMessageFile {...props} />
					}
				}}
				renderMessageImage={RenderMessageImage}
				containerStyle={{
					right: {},
					left: {},
				}}
				wrapperStyle={{
					right: {
						backgroundColor: currentTheme.lightGold,
						borderTopRightRadius: 0,
						marginBottom: 3,
						marginRight: 0,
					},
					left: {
						marginBottom: 3,
						backgroundColor: currentTheme.F4F5F6,
						borderTopLeftRadius: 0,
						maxWidth: '90%',
						marginLeft: 0,
					},
				}}
				textStyle={{
					right: {
						color: currentTheme.white,
						fontSize: 12,
						fontWeight: '400',
					},
					left: {
						color: currentTheme.black,
						fontSize: 12,
						fontWeight: '400',
					},
				}}
			/>
		</View>
	)
}
