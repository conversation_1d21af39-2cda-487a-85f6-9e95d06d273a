import { Text, View, Linking, ActivityIndicator, Image, SafeAreaView, TouchableOpacity, Platform, KeyboardAvoidingView, Animated } from 'react-native'
import React, { useContext, useState, useRef, useEffect, useCallback } from 'react'
import { useAndroidSafeTop } from '../../utils/SafeAreaUtils'
import { message_listener, roomJoin, roomLeave, sendMessage, startTyping, stopTyping, appJoin, socket } from '../../socket/Socket'
import getEnvVars from '../../../environment'
const { WS_GRAPHQL_URL } = getEnvVars()
import CustomChat from '../../components/CustomChat/CustomChat'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Ionicons from '@expo/vector-icons/Ionicons'
import AntDesign from '@expo/vector-icons/AntDesign'
import Entypo from '@expo/vector-icons/Entypo'
import UserContext from '../../context/User/User'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { JoinRoom, LeaveRoom, getMessages, getRoomById, postMessage } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import styles from '../styles'
import * as FileSystem from 'expo-file-system'
import * as DocumentPicker from 'expo-document-picker'
import { uploadImageToImageKit } from '../../component/Cloudupload/CloudUpload'
import { launchImageLibrary } from 'react-native-image-picker'
import { get_url_extension } from '../../utils/Constants'
import fontStyles from '../../utils/fonts/fontStyles'
import ChatSkeleton from '../../component/Skeleton/ChatSkeleton'
import ChatHeaderSkeleton from '../../component/Skeleton/ChatHeaderSkeleton'

// Custom chat implementation is now in CustomChat component

const Chat = props => {
	const { item, roomId } = props?.route?.params
	const { navigation } = props
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [Loading, setLoading] = useState(false)
	const [isActionTypes, setActionTypes] = useState(false)
	const [myfile, setFile] = useState('')
	const [myimage, setImage] = useState('')
	const [page, setPage] = useState(1)
	const [messages, setMessages] = useState([])
	const [totalPages, setTotalPages] = useState('')
	const [text, setText] = useState('')
	const [uploadLoader, setUploadLoader] = useState(false)
	const [initialLoad, setInitialLoad] = useState(true)
	const [isTyping, setIsTyping] = useState(false)
	const [typingUsers, setTypingUsers] = useState([])
	const [typingTimeout, setTypingTimeout] = useState(null)
	const isMountedRef = useRef(true)
	const androidSafeTop = useAndroidSafeTop()

	// Animated values for typing indicator using React Native Animated
	const typingIndicatorScale = useRef(new Animated.Value(0)).current
	const typingIndicatorOpacity = useRef(new Animated.Value(0)).current
	const messagesTranslateY = useRef(new Animated.Value(0)).current

	const pageSize = 80

	// console.log('page :', page);
	// console.log('chatUser :', chatUser);
	// console.log('total messages :', messages?.length);
	console.log('roomID :', roomId)

	const [mutate, { client }] = useMutation(postMessage, {
		onCompleted: response => {
			console.log('postMessage response :', response)
		},
		onError: error => {
			console.log('postMessage Error :', error)
		},
	})

	const [mutateJoinRoom, {}] = useMutation(JoinRoom, {
		errorPolicy: 'all',
		onCompleted: response => {
			console.log('JoinRoom response :', response)
		},
		onError: error => {
			console.log('JoinRoom Error :', error)
		},
	})

	const [mutateLeaveRoom, {}] = useMutation(LeaveRoom, {
		errorPolicy: 'all',
		onCompleted: response => {
			console.log('LeaveRoom response :', response)
		},
		onError: error => {
			console.log('LeaveRoom Error :', error)
		},
	})

	const { data, loading, error, refetch } = useQuery(getMessages, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: {
				room: roomId,
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: async res => {
			try {
				setTotalPages(res?.getMessages?.totalPages)
				const messagesArr = []
				// console.log('getMessages res :', res?.getMessages.results);
				await res?.getMessages.results?.map(async msgs => {
					const docType = get_url_extension(msgs?.files[0])
					const formateMessage = {
						// _id: Math.floor(Math.random() * 101), //msg id
						_id: msgs?._id, //msg id
						text: msgs?.message, //msg
						createdAt: msgs?.at, //time
						type: msgs.type,
						user: {
							_id: msgs?.from?._id, //user id
							name: msgs?.from?.name, //user msgs.from
							avatar: msgs?.from?.photo, //user image
						},
						cardData: {
							title: msgs?.cardData?.title,
							description: msgs?.cardData?.description,
							photo: msgs?.cardData?.photo,
							cardID: msgs?.cardData?.cardID,
							shareType: msgs.cardData?.shareType,
							cardType: msgs.cardData?.cardType,
						},
						// image: msgs?.files[0],
						image: docType !== 'pdf' && msgs?.files[0],
						file: docType === 'pdf' && msgs?.files[0],
					}
					messagesArr.push(formateMessage)
				})
				setMessages(prevMessages => [...prevMessages, ...messagesArr])
				// setMessages(messagesArr);
				// setMessages(prevState =>
				//   GiftedChat.append(...prevState, ...messagesArr),
				// );
				// console.log('getMessages res :', messagesArr);
				setLoading(false)
			} catch (e) {
				console.log('catch e :', e)
			}
		},
		onError: err => {
			setLoading(false)

			console.log('getMessages err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	// Temporarily disabled - getRoomById doesn't exist in schema
	const getRoomByIdData = null
	const getRoomLoader = false
	React.useEffect(() => {
		setInitialLoad(false)
	}, [])

	const roomInfo = getRoomByIdData?.getRoomById || item
	const isAnonymousChat = roomInfo?.isAnonymousChat || item?.isAnonymousChat
	const chatUser =
		roomInfo?.users?.find(users => {
			return users?._id !== user?._id
		}) ||
		item?.users?.find(users => {
			return users?._id !== user?._id
		}) ||
		item

	function clear() {
		setFile('')
		setImage('')
		setText('')
	}

	async function LibraryPick() {
		await launchImageLibrary(
			{
				mediaType: 'photo',
				quality: 0.5,
				// includeBase64: true,
			},
			async res => {
				console.log('camera res :', res)

				const type = res.assets[0]?.type?.split('/')
				const mimeType = type[1]
				if (!res.didCancel) {
					const file = await FileSystem.readAsStringAsync(res.assets[0].uri, {
						encoding: FileSystem.EncodingType.Base64,
					})
					setUploadLoader(true)
					setActionTypes(false)
					const documentBase64 = res.assets[0].uri ? `data:image/png;base64,${file}` : null
					await uploadImageToImageKit(documentBase64, mimeType).then(async res => {
						setUploadLoader(false)
						setText(res.name)
						setImage(res.url)
						setFile('')
						setActionTypes(false)
						// await roomJoin(user?._id, roomId,);
					})
					// setFile(res?.assets[0]);
				}
			}
		)
	}

	async function DocumentPick() {
		try {
			// const results = await DocumentPicker.pick({
			//   type: [DocumentPicker.types.pdf],
			// });
			const document = await DocumentPicker.getDocumentAsync({
				type: 'application/pdf',
			})
			console.log('DocumnetPicker :', document)
			if (!document.canceled) {
				setUploadLoader(true)
				setActionTypes(false)
			}
			if (document.canceled) {
				setUploadLoader(false)
				setActionTypes(true)
			}

			const file = await FileSystem.readAsStringAsync(document?.assets[0]?.uri, {
				encoding: FileSystem.EncodingType.Base64,
			})
			const documentBase64 = document ? `data:application/pdf;base64,${file}` : null
			await uploadImageToImageKit(documentBase64, 'pdf').then(async res => {
				setUploadLoader(false)
				setText(res.name)
				setFile(res.url)
				setImage('')
				// await roomJoin(user?._id, roomId,);
				setActionTypes(false)
			})
		} catch (err) {
			setUploadLoader(false)
			if (DocumentPicker.isCancel(err)) {
				// User cancelled the picker, exit any dialogs or menus and move on
			} else {
				throw err
			}
		}
	}

	const nextPage = async () => {
		console.log('total page ', totalPages)
		if (page < totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			await refetch({
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
		}
	}

	const onSend = async (newmessages = []) => {
		const image = myimage
		const file = myfile
		const myMsg = newmessages[0]
		let message = null
		let data = null

		if (file !== '') {
			message = {
				...myMsg,
				file: file,
			}
			data = {
				authId: user?._id,
				roomId: roomId,
				message: newmessages[0].text,
				messageType: 'file',
				files: [file],
				createdAt: new Date(),
			}
		} else if (image !== '') {
			message = {
				...myMsg,
				image: image,
			}
			data = {
				authId: user?._id,
				roomId: roomId,
				message: newmessages[0].text,
				messageType: 'file',
				files: [image],
				createdAt: new Date(),
			}
		} else {
			message = {
				...myMsg,
			}
			data = {
				authId: user?._id,
				roomId: roomId,
				message: newmessages[0].text,
				messageType: 'text',
				files: [],
				createdAt: new Date(),
			}
		}

		// Stop typing when sending message
		if (isTyping) {
			stopTyping(roomId)
			setIsTyping(false)
		}

		// Optimistic update - add message to UI immediately
		setMessages(previousMessages => [message, ...previousMessages])

		// Send ONLY via WebSocket (backend handles database save)
		sendMessage(data.authId, data.roomId, data.message, data.messageType, data.files)

		// Clear input fields
		setFile('')
		setImage('')
		setText('')
	}

	const renderActions = _props => {
		// let docType = get_url_extension(myfile);
		const icon = myfile
			? <FontAwesome5
					name="file-pdf"
					size={22}
					color={currentTheme.themeBackground}
				/>
			: myimage
				? <Image
						source={{ uri: myimage }}
						style={{ height: '100%', width: '100%' }}
						resizeMode="cover"
					/>
				: null

		return (
			// <Actions {...props}>
			<View style={[styles().flexRow, styles().alignCenter]}>
				<TouchableOpacity
					onPress={() => {
						if (!uploadLoader) {
							setActionTypes(!isActionTypes)
						}
					}}
					activeOpacity={0.7}
					style={{
						height: 35,
						width: 35,
						alignItems: 'center',
						justifyContent: 'center',
						// backgroundColor: 'teal',
						marginBottom: 5,
					}}
				>
					{uploadLoader
						? <ActivityIndicator
								size={16}
								color={currentTheme.themeBackground}
							/>
						: <Entypo
								name="plus"
								color={currentTheme.themeBackground}
								size={25}
							/>}
				</TouchableOpacity>
				{myfile || myimage
					? <>
							<TouchableOpacity
								onPress={() => clear()}
								activeOpacity={0.5}
								style={{
									position: 'absolute',
									// backgroundColor: 'teal',
									zIndex: 10,
									padding: 5,
									bottom: 22,
									right: -12,
								}}
							>
								<AntDesign
									name={'closecircle'}
									color={currentTheme.themeBackground}
									size={15}
								/>
							</TouchableOpacity>
							<View
								style={[
									styles().wh35px,
									styles().alignCenter,
									styles().justifyCenter,
									styles().overflowH,

									{
										borderWidth: 0.5,
										borderColor: currentTheme.lightGold,
										borderRadius: 3,
									},
									styles().mb5,
									styles().ml5,
								]}
							>
								{icon}
							</View>
						</>
					: null}
			</View>
			// </Actions>
		)
	}

	const renderChatEmpty = () => {
		if (messages?.length === 0 && loading === false) {
			return null
		}
		if (messages?.length === 0 && loading === true) {
			return (
				<View style={[styles().flex]}>
					{Array.from({ length: 3 }).map(() => {
						return <ChatSkeleton />
					})}
				</View>
			)
		}
	}

	const renderActionView = () => {
		if (isActionTypes) {
			return (
				<View
					style={[
						styles().zIndex100,
						styles().posAbs,
						styles().mh10,
						{
							bottom: 60,
							left: -40,
						},
					]}
				>
					<TouchableOpacity
						activeOpacity={0.7}
						onPress={() => LibraryPick()}
						style={[
							styles().br100,
							styles().alignCenter,
							styles().boxpeshadow,
							styles().justifyCenter,
							styles().mb10,
							styles().wh35px,
							{ backgroundColor: currentTheme.themeBackground },
						]}
					>
						<FontAwesome
							name={'image'}
							size={16}
							color={currentTheme.white}
						/>
					</TouchableOpacity>
					<TouchableOpacity
						activeOpacity={0.7}
						onPress={() => DocumentPick()}
						style={[
							styles().br100,
							styles().alignCenter,
							styles().boxpeshadow,
							styles().justifyCenter,
							styles().wh35px,
							{ backgroundColor: currentTheme.themeBackground },
						]}
					>
						<FontAwesome5
							name={'file-pdf'}
							size={16}
							color={currentTheme.white}
							solid
						/>
					</TouchableOpacity>
				</View>
			)
		}
		return null
	}

	const RenderSend = props => {
		return (
			<Send {...props}>
				<View
					style={{
						height: 35,
						width: 35,
						alignItems: 'center',
						justifyContent: 'center',
						marginBottom: 5,
					}}
				>
					<Ionicons
						name="send"
						color={currentTheme.themeBackground}
						size={18}
					/>
				</View>
			</Send>
		)
	}

	const renderDay = props => {
		return (
			<View
				style={{
					alignItems: 'center',
					justifyContent: 'center',
				}}
			>
				<Day
					{...props}
					textStyle={{
						color: currentTheme.themeBackground,
						fontSize: 10,
						fontFamily: fontStyles.PoppinsMedium,
					}}
					dateFormat="LL"
					containerStyle={{
						paddingHorizontal: 10,
						borderRadius: 5,
						paddingVertical: 7,
						backgroundColor: currentTheme.timeContainerColor,
						width: '50%',
						alignItems: 'center',
						justifyContent: 'center',
					}}
				/>
			</View>
		)
	}

	const RenderMessageFile = props => {
		const { currentMessage } = props
		const handleFilePress = () => {
			// Open the file URL
			console.log(currentMessage)
			if (currentMessage?.file) {
				Linking.openURL(currentMessage?.file)
			}
		}

		return (
			<TouchableOpacity
				activeOpacity={0.7}
				style={{
					padding: 10,
					alignItems: 'center',
					justifyContent: 'center',
					backgroundColor: currentTheme.lightGold,
					// borderRadius: 10,
					// borderRadius: 10,
					borderTopLeftRadius: 10,
					borderBottomRightRadius: 10,
					width: 100,
				}}
				onPress={handleFilePress}
			>
				<FontAwesome5
					name={'file-pdf'}
					size={45}
					color={currentTheme.white}
				/>
			</TouchableOpacity>
		)
	}

	const RenderMessageImage = props => {
		const { currentMessage } = props
		const handleFilePress = () => {
			// Open the file URL
			if (currentMessage?.image) {
				Linking.openURL(currentMessage?.image)
			}
		}
		return (
			<View
				style={{
					height: 150,
					width: '100%',
					minWidth: 170,
					// maxWidth:250,
					overflow: 'hidden',
					borderTopLeftRadius: 10,
				}}
			>
				<TouchableOpacity
					onPress={handleFilePress}
					activeOpacity={0.7}
				>
					<Image
						source={{ uri: currentMessage?.image }}
						style={{ height: '100%', width: '100%' }}
						resizeMode="cover"
					/>
				</TouchableOpacity>
			</View>
		)
	}

	const CustomCard = props => {
		const { currentMessage } = props
		const check = user?.appliedJobs?.find(applied => currentMessage?.cardData?.cardID === applied)
		// console.log('card currentMessage ;', currentMessage);
		return (
			<TouchableOpacity
				activeOpacity={0.4}
				onPress={() => {
					console.log('current card data =>', currentMessage?.cardData)
					if (currentMessage.cardData.shareType === 'job') {
						navigation.navigate('JobDetails', {
							item: { _id: currentMessage?.cardData?.cardID },
							jobId: currentMessage?.cardData?.cardID,
							alreadyApplied: !!check,
						})
					}
					if (currentMessage.cardData.shareType === 'company') {
						navigation.navigate('CompanyDetails', {
							item: { _id: currentMessage?.cardData?.cardID },
						})
					}
				}}
				style={{
					backgroundColor: currentTheme.F8F9FA,
					padding: 10,
					borderRadius: 8,
					flexDirection: 'row',
					alignItems: 'flex-start',
					borderWidth: 1,
					borderColor: currentTheme.F8F9FA,
					paddingVertical: 15,
					paddingRight: 20,
					paddingLeft: 10,
					marginTop: 10,
					maxWidth: '80%',
				}}
			>
				<View style={{ marginRight: 10 }}>
					<View
						style={{
							height: 40,
							width: 40,
							borderRadius: 100,
							alignItems: 'center',
							justifyContent: 'center',
							overflow: 'hidden',
							borderWidth: currentMessage?.cardData?.photo ? 0 : 1,
							borderColor: currentTheme.themeBackground,
						}}
					>
						{currentMessage?.cardData?.photo
							? <Image
									source={{ uri: currentMessage?.cardData?.photo }}
									style={{ height: '100%', width: '100%' }}
									resizeMode="cover"
								/>
							: <View>
									{currentMessage?.cardData?.shareType === 'company'
										? <MaterialCommunityIcons
												name="city-variant"
												size={25}
												color={currentTheme.themeBackground}
											/>
										: <Ionicons
												name="md-briefcase"
												size={20}
												color={currentTheme.themeBackground}
											/>}
								</View>}
					</View>
				</View>
				<View style={{ flex: 1 }}>
					<Text
						numberOfLines={2}
						style={{
							fontSize: 14,
							fontFamily: fontStyles.PoppinsMedium,
							color: currentTheme.black,
							textDecorationLine: 'underline',
						}}
					>
						{currentMessage?.cardData?.title}
					</Text>
					<Text
						numberOfLines={3}
						style={{
							fontSize: 12,
							marginTop: 5,
							color: currentTheme.blackish,
							fontFamily: fontStyles.PoppinsRegular,
							// flex: 1,
						}}
					>
						{currentMessage?.cardData?.description ? currentMessage?.cardData?.description : 'N/A'}
					</Text>
				</View>
			</TouchableOpacity>
		)
	}

	const BadgeCard = props => {
		const { currentMessage } = props
		if (currentMessage?.cardData?.cardType === 'assign' || !currentMessage?.cardData?.cardType) {
			return (
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						console.log(currentMessage)
					}}
					style={{
						alignItems: 'center',
						justifyContent: 'center',
						width: '80%',
						marginVertical: 5,
					}}
				>
					<View
						style={{
							backgroundColor: currentTheme.badgeCardBG,
							paddingVertical: 8,
							paddingHorizontal: 10,
							alignItems: 'center',
							justifyContent: 'center',
							borderRadius: 5,
							marginBottom: 10,
							width: '100%',
						}}
					>
						<Text
							style={{
								color: currentTheme.themeBackground,
								textAlign: 'center',
								fontSize: 12,
								fontFamily: fontStyles.PoppinsMedium,
							}}
						>
							Badge Has Been Assigned
						</Text>
					</View>
					<View style={{ aspectRatio: 1, width: 50 }}>
						<Image
							source={require('../../assets/images/badge_success.png')}
							style={{ height: '100%', width: '100%' }}
							resizeMode="contain"
						/>
					</View>
				</TouchableOpacity>
			)
		}
		if (currentMessage?.cardData?.cardType === 'take') {
			return (
				<TouchableOpacity
					activeOpacity={1}
					onPress={() => {
						console.log(currentMessage)
					}}
					style={{
						alignItems: 'center',
						justifyContent: 'center',
						width: '80%',
						marginVertical: 5,
					}}
				>
					<View
						style={{
							backgroundColor: currentTheme.white,
							paddingVertical: 8,
							paddingHorizontal: 10,
							alignItems: 'center',
							justifyContent: 'center',
							borderRadius: 5,
							marginBottom: 10,
							width: '100%',
							borderWidth: 1,
							borderColor: currentTheme.themeBackground,
						}}
					>
						<Text
							style={{
								color: currentTheme.themeBackground,
								fontSize: 12,
								textAlign: 'center',
								fontFamily: fontStyles.PoppinsMedium,
							}}
						>
							Badge Has Been Removed
						</Text>
					</View>
					<View style={{ aspectRatio: 1, width: 50 }}>
						<Image
							source={require('../../assets/images/cardtake.png')}
							style={{ height: '100%', width: '100%' }}
							resizeMode="contain"
						/>
					</View>
				</TouchableOpacity>
			)
		}
	}

	const JobApplicationCard = props => {
		const { currentMessage } = props
		return (
			<TouchableOpacity
				activeOpacity={1}
				onPress={() => {
					console.log(currentMessage)
				}}
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					width: '80%',
					marginVertical: 5,
				}}
			>
				<View
					style={{
						backgroundColor:
							currentMessage?.cardData?.cardType === 'jobComplete'
								? currentTheme.backgroundGreen
								: currentMessage?.cardData?.cardType === 'terminate'
									? currentTheme.FFE5E5
									: currentTheme.badgeCardBG,
						paddingVertical: 8,
						paddingHorizontal: 10,
						alignItems: 'center',
						justifyContent: 'center',
						borderRadius: 5,
						marginBottom: 10,
						width: '100%',
					}}
				>
					<Text
						style={{
							color:
								currentMessage?.cardData?.cardType === 'jobComplete'
									? currentTheme.lightGreen
									: currentMessage?.cardData?.cardType === 'terminate'
										? currentTheme.red
										: currentTheme.themeBackground,
							fontSize: 12,
							textAlign: 'center',
							fontFamily: fontStyles.PoppinsMedium,
						}}
					>
						{currentMessage?.text}
					</Text>
				</View>
				<View style={{ height: 60, width: 60 }}>
					<Image
						source={
							currentMessage?.cardData?.cardType === 'jobComplete'
								? require('../../assets/images/job_complete.png')
								: currentMessage?.cardData?.cardType === 'terminate'
									? require('../../assets/images/job_terminate.png')
									: require('../../assets/images/application_chat.png')
						}
						style={{
							height: '100%',
							width: '100%',
							tintColor:
								currentMessage?.cardData?.cardType === 'jobComplete'
									? currentTheme.lightGreen
									: currentMessage?.cardData?.cardType === 'terminate'
										? currentTheme.red
										: null,
						}}
						resizeMode="contain"
					/>
				</View>
			</TouchableOpacity>
		)
	}

	const RenderBubble = props => {
		const { currentMessage, position, nextMessage } = props

		if (currentMessage?.type === 'card') {
			return <CustomCard {...props} />
		}
		if (currentMessage?.type === 'badge') {
			return <BadgeCard {...props} />
		}
		if (currentMessage?.type === 'application') {
			return <JobApplicationCard {...props} />
		}

		const isMyMessage = currentMessage?.user?._id === user?._id

		// Show avatar only for latest message from other users
		// Check if next message is from different user or doesn't exist (meaning this is the latest)
		const shouldShowAvatar = !isMyMessage && (!nextMessage || !nextMessage.user || nextMessage.user._id !== currentMessage.user._id)

		// Get user initials for placeholder
		const getUserInitials = user => {
			if (!user?.name) return 'U'
			const names = user.name.split(' ')
			if (names.length >= 2) {
				return `${names[0][0]}${names[1][0]}`.toUpperCase()
			}
			return user.name[0].toUpperCase()
		}

		console.log('RenderBubble', { currentMessage })

		return (
			<View style={[styles().flexRow, styles().alignItems, { justifyContent: isMyMessage ? 'flex-end' : 'flex-start' }]}>
				{/* Avatar space for ALL other user messages */}
				{!isMyMessage && (
					<View
						style={{
							width: 30,
							height: 30,
							marginRight: 8,
							marginBottom: 2,
						}}
					>
						{shouldShowAvatar &&
							(currentMessage?.user?.avatar
								? <Image
										source={{ uri: currentMessage.user.avatar }}
										style={{
											width: 30,
											height: 30,
											borderRadius: 15,
										}}
										resizeMode="cover"
									/>
								: <View
										style={{
											width: 30,
											height: 30,
											borderRadius: 15,
											backgroundColor: currentTheme.themeBackground,
											justifyContent: 'center',
											alignItems: 'center',
										}}
									>
										<Text
											style={{
												color: currentTheme.white,
												fontSize: 12,
												fontWeight: 'bold',
											}}
										>
											{getUserInitials(currentMessage?.user)}
										</Text>
									</View>)}
					</View>
				)}

				<Bubble
					{...props}
					renderCustomView={props => {
						// Custom document message
						if (props.currentMessage.file) {
							// Your custom pdf message
							return <RenderMessageFile {...props} />
						}
					}}
					renderMessageImage={RenderMessageImage}
					containerStyle={{
						right: {},
						left: {},
					}}
					wrapperStyle={{
						right: {
							backgroundColor: currentTheme.lightGold,
							borderTopRightRadius: 0,
							marginBottom: 3,
							marginRight: 0,
						},
						left: {
							marginBottom: 3,
							backgroundColor: currentTheme.F4F5F6,
							borderTopLeftRadius: 0,
							maxWidth: '90%',
							marginLeft: 0, // No margin needed since avatar space is handled above
						},
					}}
					textStyle={{
						right: {
							color: currentTheme.white,
							fontSize: 12,
							fontWeight: '400',
						},
						left: {
							color: currentTheme.black,
							fontSize: 12,
							fontWeight: '400',
						},
					}}
				/>
			</View>
		)
	}

	// Animated typing indicator component
	const TypingBubble = () => {
		const dot1 = useRef(new Animated.Value(0)).current
		const dot2 = useRef(new Animated.Value(0)).current
		const dot3 = useRef(new Animated.Value(0)).current

		useEffect(() => {
			const animateDot = (dot, delay) => {
				Animated.loop(
					Animated.sequence([
						Animated.delay(delay),
						Animated.timing(dot, {
							toValue: 1,
							duration: 500,
							useNativeDriver: true,
						}),
						Animated.timing(dot, {
							toValue: 0,
							duration: 500,
							useNativeDriver: true,
						}),
					])
				).start()
			}

			animateDot(dot1, 0)
			animateDot(dot2, 150)
			animateDot(dot3, 300)
		}, [])

		return (
			<View style={[styles().flexRow, styles().alignCenter]}>
				<Animated.View
					style={[
						{
							width: 10,
							height: 10,
							borderRadius: 5,
							backgroundColor: '#8E8E93',
							marginRight: 4,
							opacity: dot1.interpolate({
								inputRange: [0, 1],
								outputRange: [0.4, 1],
							}),
							transform: [
								{
									translateY: dot1.interpolate({
										inputRange: [0, 1],
										outputRange: [0, -8],
									}),
								},
							],
						},
					]}
				/>
				<Animated.View
					style={[
						{
							width: 10,
							height: 10,
							borderRadius: 5,
							backgroundColor: '#8E8E93',
							marginRight: 4,
							opacity: dot2.interpolate({
								inputRange: [0, 1],
								outputRange: [0.4, 1],
							}),
							transform: [
								{
									translateY: dot2.interpolate({
										inputRange: [0, 1],
										outputRange: [0, -8],
									}),
								},
							],
						},
					]}
				/>
				<Animated.View
					style={[
						{
							width: 10,
							height: 10,
							borderRadius: 5,
							backgroundColor: '#8E8E93',
							opacity: dot3.interpolate({
								inputRange: [0, 1],
								outputRange: [0.4, 1],
							}),
							transform: [
								{
									translateY: dot3.interpolate({
										inputRange: [0, 1],
										outputRange: [0, -8],
									}),
								},
							],
						},
					]}
				/>
			</View>
		)
	}

	// Facebook-style typing indicator with smooth animations
	const renderTypingIndicator = () => {
		console.log('🔥 RENDER TYPING - users:', typingUsers.length, typingUsers)

		if (typingUsers.length === 0) return null

		console.log('🔥 SHOWING TYPING INDICATOR FOR:', typingUsers)
		return (
			<Animated.View
				style={[
					styles().ph15,
					styles().pb10,
					styles().pt5,
					{
						transform: [
							{ scale: typingIndicatorScale },
							{
								translateY: typingIndicatorScale.interpolate({
									inputRange: [0, 1],
									outputRange: [20, 0],
								}),
							},
						],
						opacity: typingIndicatorOpacity,
					},
				]}
			>
				<View style={[styles().flexRow, styles().alignItems]}>
					{/* Avatar matching message style */}
					<View
						style={{
							width: 30,
							height: 30,
							marginRight: 8,
							marginBottom: 2,
						}}
					>
						<View
							style={{
								width: 30,
								height: 30,
								borderRadius: 15,
								backgroundColor: currentTheme.themeBackground,
								justifyContent: 'center',
								alignItems: 'center',
							}}
						>
							<Text
								style={{
									color: currentTheme.white,
									fontSize: 12,
									fontWeight: 'bold',
								}}
							>
								{typingUsers.length > 0
									? typingUsers[0].split(' ').length >= 2
										? `${typingUsers[0].split(' ')[0][0]}${typingUsers[0].split(' ')[1][0]}`.toUpperCase()
										: typingUsers[0][0].toUpperCase()
									: 'U'}
							</Text>
						</View>
					</View>

					{/* Typing bubble */}
					<View
						style={[
							{
								backgroundColor: currentTheme.F4F5F6,
								borderRadius: 20,
								borderTopLeftRadius: 4,
								paddingHorizontal: 15,
								paddingVertical: 12,
								minWidth: 60,
								shadowColor: '#000',
								shadowOffset: {
									width: 0,
									height: 1,
								},
								shadowOpacity: 0.1,
								shadowRadius: 1.41,
								elevation: 2,
							},
						]}
					>
						<TypingBubble />
					</View>
				</View>
			</Animated.View>
		)
	}

	const handleTextChange = text => {
		setText(text)

		if (typingTimeout) {
			clearTimeout(typingTimeout)
		}

		if (text.trim().length > 0 && !isTyping) {
			setIsTyping(true)
			startTyping(user?._id, roomId)
		} else if (text.trim().length === 0 && isTyping) {
			setIsTyping(false)
			stopTyping(roomId)
		}

		if (text.trim().length > 0) {
			const timeout = setTimeout(() => {
				if (isTyping) {
					setIsTyping(false)
					stopTyping(roomId)
				}
			}, 2000)
			setTypingTimeout(timeout)
		}
	}

	const renderComposer = props => (
		<View style={{ flex: 1 }}>
			{renderActionView()}
			<Composer
				{...props}
				multiline={false}
				textInputStyle={{
					color: '#222B45',
					paddingHorizontal: 12,
					marginLeft: 0,
					marginRight: 5,
				}}
				placeholder={'Write a Message...'}
			/>
		</View>
	)

	const scrollToBottomComponent = () => {
		return (
			<FontAwesome
				name="angle-down"
				color={currentTheme.black}
				size={22}
			/>
		)
	}

	const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
		const paddingToBottom = 30
		return layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
	}

	const Listener = async () => {
		// Listen for new messages
		message_listener(roomId, cb => {
			if (isMountedRef.current && cb?.from?.id !== user?._id) {
				const docType = get_url_extension(cb?.files[0])
				const new_message = {
					_id: cb.id,
					text: cb?.message,
					createdAt: cb.at,
					user: {
						_id: cb.from?.id,
						name: cb.from?.name,
						avatar: cb.from?.photo,
					},
					cardData: {
						title: cb?.cardData?.title,
						description: cb?.cardData?.description,
						photo: cb?.cardData?.photo,
						cardID: cb?.cardData?.cardID,
						shareType: cb.cardData?.shareType,
						cardType: cb.cardData?.cardType,
					},
					type: cb.type,
					file: docType === 'pdf' ? cb?.files[0] : '',
					image: docType !== 'pdf' ? cb?.files[0] : '',
				}
				if (isMountedRef.current) {
					setMessages(previousMessage => [new_message, ...previousMessage])
				}
			}
		})

		// Listen for typing indicators (match backend events)
		socket?.on(`typing-${roomId}`, data => {
			if (!isMountedRef.current) return

			console.log('🔥 TYPING START RECEIVED:', JSON.stringify(data))
			console.log('🔥 Current user name:', user?.name)
			console.log('🔥 Current typing users:', typingUsers)

			if (data.typing && data.message) {
				const userName = data.message.replace(' is Typing', '')
				console.log('🔥 Extracted username:', userName)

				if (!typingUsers.includes(userName) && userName !== user?.name) {
					console.log('🔥 ADDING USER TO TYPING:', userName)
					if (isMountedRef.current) {
						setTypingUsers(prev => [...prev, userName])
					}
				} else {
					console.log('🔥 NOT ADDING USER - already in list or is current user')
				}
			}
		})

		socket?.on(`stopTyping-${roomId}`, data => {
			if (!isMountedRef.current) return

			console.log('🔥 TYPING STOP RECEIVED:', JSON.stringify(data))
			if (!data.typing) {
				console.log('🔥 CLEARING ALL TYPING USERS')
				if (isMountedRef.current) {
					setTypingUsers([]) // Clear all typing users
				}
			}
		})
	}

	async function join() {
		// Join room via GraphQL
		await mutateJoinRoom({
			variables: {
				joinRoomId: roomId,
			},
		})

		// Join room via WebSocket for real-time updates
		roomJoin(user?._id, roomId)

		// Join app
		appJoin(user?._id)
	}

	const leave = useCallback(async () => {
		try {
			// Stop typing if currently typing
			if (isTyping) {
				stopTyping(roomId)
				setIsTyping(false)
			}

			// Clear typing timeout if exists
			if (typingTimeout) {
				clearTimeout(typingTimeout)
				setTypingTimeout(null)
			}

			// Leave room via WebSocket first (faster)
			roomLeave(user?._id, roomId)

			// Then leave room via GraphQL
			await mutateLeaveRoom({
				variables: {
					leaveRoomId: roomId,
				},
			})
		} catch (error) {
			console.log('Error leaving room:', error)
		}
	}, [isTyping, typingTimeout, user?._id, roomId, mutateLeaveRoom])

	React.useEffect(() => {
		join()
		Listener()

		// Cleanup socket listeners on unmount
		return () => {
			if (socket) {
				socket.off(`typing-${roomId}`)
				socket.off(`stopTyping-${roomId}`)
			}
		}
	}, [roomId])

	// Animate typing indicator and messages when typing users change
	React.useEffect(() => {
		if (typingUsers.length > 0) {
			// Show typing indicator with spring animation
			Animated.parallel([
				Animated.spring(typingIndicatorScale, {
					toValue: 1,
					tension: 100,
					friction: 8,
					useNativeDriver: true,
				}),
				Animated.timing(typingIndicatorOpacity, {
					toValue: 1,
					duration: 200,
					useNativeDriver: true,
				}),
				Animated.spring(messagesTranslateY, {
					toValue: -10,
					tension: 120,
					friction: 10,
					useNativeDriver: true,
				}),
			]).start()
		} else {
			// Hide typing indicator
			Animated.parallel([
				Animated.spring(typingIndicatorScale, {
					toValue: 0,
					tension: 100,
					friction: 8,
					useNativeDriver: true,
				}),
				Animated.timing(typingIndicatorOpacity, {
					toValue: 0,
					duration: 150,
					useNativeDriver: true,
				}),
				Animated.spring(messagesTranslateY, {
					toValue: 0,
					tension: 120,
					friction: 10,
					useNativeDriver: true,
				}),
			]).start()
		}
	}, [typingUsers.length])

	React.useEffect(() => {
		const onBlur = props.navigation.addListener('blur', async () => {
			// roomLeave(user?._id, roomId,;
			leave()
		})
		return onBlur
	}, [props.navigation])

	React.useEffect(() => {
		const onFocus = props.navigation.addListener('focus', async () => {
			// await roomJoin(user?._id, roomId,);
			await refetch()
		})
		return onFocus
	}, [props.navigation])

	// Cleanup on unmount
	React.useEffect(() => {
		// Set mounted flag
		isMountedRef.current = true

		return () => {
			// Set unmounted flag
			isMountedRef.current = false

			// Clear typing timeout
			if (typingTimeout) {
				clearTimeout(typingTimeout)
			}
			// Stop typing if active
			if (isTyping) {
				stopTyping(roomId)
			}
		}
	}, [])

	return (
		<SafeAreaView
			style={[
				styles().flex,

				{
					backgroundColor: currentTheme.white,
					// paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
				},
			]}
		>
			<KeyboardAvoidingView
				behavior={Platform.OS === 'ios' ? 'height' : 'height'}
				keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 15}
				style={[{ flexGrow: 1 }]}
			>
				<View
					style={[
						styles().flexRow,
						styles().alignCenter,
						Platform.OS === 'android' && styles().mt20,
						styles().h80px,
						// styles().boxpeshadowCart,
						styles().ph20,
						styles().zIndex1,
						styles().posRel,
						{ borderBottomWidth: 1, borderColor: currentTheme.F8F9FA },
						{ paddingTop: androidSafeTop },
					]}
				>
					<TouchableOpacity
						onPress={() => props.navigation.goBack()}
						style={[styles().alignCenter, styles().justifyCenter, styles().h50px, styles().w25px]}
					>
						<FontAwesome
							name="angle-left"
							size={30}
							color={currentTheme.blackish}
						/>
					</TouchableOpacity>
					{/* {true ? ( */}
					{initialLoad
						? <ChatHeaderSkeleton />
						: <>
								<View
									style={[
										styles().wh50px,
										styles().ml10,
										styles().mr10,
										styles().br25,
										styles().bw1,
										styles().justifyCenter,
										styles().alignCenter,
										styles().overflowH,
										{
											borderWidth: isAnonymousChat ? 1 : chatUser?.photo ? 0 : 1,
											borderColor: currentTheme.themeBackground,
										},
									]}
								>
									{roomInfo?.chatName
										? <FontAwesome5
												name="users"
												size={20}
												color={currentTheme.themeBackground}
											/>
										: isAnonymousChat
											? <FontAwesome5
													name="user-secret"
													size={20}
													color={currentTheme.themeBackground}
												/>
											: chatUser?.photo
												? <Image
														source={{ uri: chatUser?.photo }}
														style={styles().wh100}
														resizeMode="cover"
													/>
												: <FontAwesome5
														name="user-alt"
														size={20}
														color={currentTheme.themeBackground}
													/>}
								</View>

								{!getRoomLoader && (
									<View>
										{isAnonymousChat
											? <Text style={[styles().fs16, styles().fontMedium]}>{'Anonymous User'}</Text>
											: <Text style={[styles().fontMedium, styles().fs16]}>{roomInfo?.chatName ? roomInfo?.chatName : chatUser?.name || 'User'}</Text>}
										{/* {isAnonymousChat ? null : ( */}
										<Text style={[styles().textCapitalize, styles().fontRegular, { fontSize: 12 }]}>
											{chatUser?.isOnline === true ? 'Online' : chatUser?.isOnline === false ? 'Offline' : 'Unknown'}
										</Text>
										{/* )} */}
									</View>
								)}
							</>}
				</View>
				<View style={{ flex: 1 }}>
					{Loading
						? <ActivityIndicator
								size={'small'}
								color={currentTheme.themeBackground}
								style={{ marginTop: 10, marginBottom: 5 }}
							/>
						: null}

					{/* {renderActionView()} */}
					<Animated.View style={[{ flex: 1 }, { transform: [{ translateY: messagesTranslateY }] }]}>
						<CustomChat
							renderFooter={typingUsers.length > 0 ? renderTypingIndicator : null}
							messages={messages?.sort((a, b) => b.createdAt - a.createdAt)}
							onSend={onSend}
							onInputTextChanged={handleTextChange}
							user={user}
							text={text}
							renderActions={renderActions}
							onLoadEarlier={nextPage}
							isLoadingEarlier={Loading}
							currentTheme={currentTheme}
							navigation={navigation}
							renderBubble={RenderBubble}
							renderDay={renderDay}
							renderChatEmpty={renderChatEmpty}
							renderComposer={renderComposer}
							renderSend={RenderSend}
							listViewProps={{
								scrollEventThrottle: 900,
								onScroll: async ({ nativeEvent }) => {
									if (isCloseToBottom(nativeEvent)) {
										await nextPage()
									}
								},
							}}
						/>
					</Animated.View>
				</View>
			</KeyboardAvoidingView>
		</SafeAreaView>
	)
}

export default React.memo(Chat)
