import { Text, View, Keyboard, TouchableOpacity, ImageBackground, LayoutAnimation, Platform, UIManager } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Feather from '@expo/vector-icons/Feather'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { mobileLogin, sendVerificationEmail } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { CommonActions, useIsFocused } from '@react-navigation/native'
import { appJoin } from '../../socket/Socket'
import Loader from '../../context/Loading/Loading'
import CheckBox from '../../component/CheckBox/CheckBox'
import messaging from '@react-native-firebase/messaging'
import { AuthContext } from '../../context/Auth/auth'
import getEnvVars from '../../../environment'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Login = props => {
	const themeContext = useContext(ThemeContext)
	const { token, refreshToken, setTokenAsync } = useContext(AuthContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [email, setEmail] = useState('')
	const { isLoader } = useContext(Loader)
	const [emailError, setEmailError] = useState(false)
	const [password, setPassword] = useState('')
	const [passwordError, setPasswordError] = useState(false)
	const [Loading, setLoading] = useState(false)
	const [ConfirmiconEye, setConfirmIconEye] = useState('eye-slash')
	const [_notification_token, setNotification_token] = useState('')
	const [rememberMe, setRememberMe] = useState(false)

	function onChangeIconConfirm() {
		if (ConfirmiconEye === 'eye') {
			setConfirmIconEye('eye-slash')
		} else {
			setConfirmIconEye('eye')
		}
	}

	const [email_verify_mutate] = useMutation(sendVerificationEmail, {
		errorPolicy: 'all',
		onCompleted: ({ sendVerificationEmail }) => {
			console.log('sendVerificationEmail res :', sendVerificationEmail)
			setLoading(false)
			FlashMessage({
				msg: 'Email verification code has been send to your email address.',
				type: 'success',
			})
			props.navigation.navigate('Verification', {
				email: email.toLowerCase().trim(),
				verification: true,
			})
		},
		onError: error => {
			console.log('sendVerificationEmail Err :', error)
			FlashMessage({ msg: error.message?.toString(), type: 'warning' })
			setLoading(false)
		},
	})

	const [mutate, { client }] = useMutation(mobileLogin, {
		onCompleted,
		onError,
		errorPolicy: 'all',
	})

	async function onCompleted(data) {
		try {
			console.log('Login res :', JSON.stringify(data?.mobileLogin, null, 8))
			if (data?.mobileLogin?.isEmailVerified === false) {
				await email_verify_mutate({
					variables: {
						email: email.toLowerCase().trim(),
						verification: true,
					},
				})
			} else {
				setLoading(false)
				LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
				await AsyncStorage.setItem('token', data?.mobileLogin?.token?.toString())
				await AsyncStorage.setItem('refreshToken', data?.mobileLogin?.refreshToken?.toString())
				await AsyncStorage.setItem('isProfileCompleted', data?.mobileLogin?.isProfileCompleted?.toString())
				await AsyncStorage.setItem('userId', data?.mobileLogin?._id?.toString())
				await AsyncStorage.setItem('user', JSON.stringify(data?.mobileLogin))
				FlashMessage({ msg: 'Login Successfully!', type: 'success' })
				appJoin(data?.mobileLogin?._id)
				// initiateSocketConnection();
				setTokenAsync(data?.mobileLogin?.token)
				props.navigation.dispatch(
					CommonActions.reset({
						index: 0,
						routes: [
							{
								name: data?.mobileLogin?.isProfileCompleted ? 'noDrawer' : 'WizardNavigator',
							},
						],
					})
				)
			}
		} catch (e) {
			console.log('login catch :', e)
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('login error  :', error)
	}

	async function Login() {
		Keyboard.dismiss()
		const notificationToken = await messaging().getToken()
		const emailregex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
		let status = true
		if (email === '') {
			FlashMessage({ msg: 'Enter Email Address', type: 'warning' })
			setEmailError(true)
			status = false
			return
		}
		if (!emailregex.test(email.trim().toLowerCase())) {
			FlashMessage({ msg: 'Invalid Email Address', type: 'warning' })
			setEmailError(true)
			status = false
			return
		}
		if (password === '') {
			FlashMessage({ msg: 'Enter Password', type: 'warning' })
			setPasswordError(true)
			status = false
			return
		}
		if (status) {
			await AsyncStorage.setItem('rememberMe', JSON.stringify(rememberMe))
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			const login_data = {
				email: email.toLowerCase().trim(),
				password: password.trim(),
				notificationToken: notificationToken,
			}
			console.log('login_data :', login_data)
			setLoading(true)
			await mutate({
				variables: login_data,
			})
		}
	}

	async function _getNotification_token() {
		const notification_token = await messaging().getToken()
		setNotification_token(notification_token)
		console.log('notification_token :', notification_token)
	}

	const isFocus = useIsFocused()
	useEffect(() => {
		// getNotification_token();
		const timer = setTimeout(() => {
			isLoader(false)
		}, 3000)
		return () => {
			clearTimeout(timer)
		}
	}, [isFocus])

	return (
		<AuthLayout>
			<ImageBackground
				source={require('../../assets/images/login-bg.png')}
				resizeMode="cover"
				style={[styles().h350px, styles().alignCenter, styles().justifyCenter]}
			>
				<Text
					style={[
						styles().fs24,
						// styles().fw700,
						styles().textCenter,
						styles().lh30,
						styles().fontBold,
						styles().textUpper,
						{ color: currentTheme.headingColor },
					]}
				>
					Welcome to {'\n'} River Bank
				</Text>
				<Text
					style={[
						styles().fs16,
						// styles().fw700,
						styles().textCenter,
						styles().lh30,
						styles().fontMedium,
						styles().mt20,
						{ color: currentTheme.headingColor },
					]}
				>
					Login as Crew Member
				</Text>
			</ImageBackground>
			<View style={[styles().mt10, styles().flex, styles().ph20]}>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={email}
						errorText={emailError}
						autoCapitalize="none"
						placeholder={'Email'}
						style
						onChangeText={text => {
							setEmailError(false)
							setEmail(text)
						}}
						FieldIcon={
							<FontAwesome
								name="envelope-o"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>

				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={password}
						errorText={passwordError}
						autoCapitalize="none"
						placeholder={'Password'}
						style
						onChangeText={text => {
							setPasswordError(false)
							setPassword(text)
						}}
						FieldIcon={
							<Feather
								name="lock"
								size={20}
								color={currentTheme.black}
							/>
						}
						secureTextEntry={ConfirmiconEye !== 'eye'}
						childrenPassword={
							<TouchableOpacity
								onPress={onChangeIconConfirm.bind()}
								style={[styles().passEye]}
							>
								<FontAwesome5
									name={ConfirmiconEye}
									size={16}
									color={ConfirmiconEye === 'eye' ? currentTheme.themeBackground : currentTheme.headingColor}
								/>
							</TouchableOpacity>
						}
					/>
				</View>

				<View style={[styles().flexRow, styles().alignCenter, styles().justifyStart, styles().mb20]}>
					<CheckBox
						setValue={boolean => setRememberMe(boolean)}
						value={rememberMe}
					/>
					<Text style={[styles().ml10, styles().fontRegular, styles().fs12, { marginTop: 2, color: currentTheme.headingColor }]}>Remember me</Text>
				</View>

				<View style={[styles().mb35, styles().alignEnd]}>
					<TouchableOpacity
						activeOpacity={0.7}
						onPress={() => props.navigation.navigate('ForgetPassword')}
					>
						<Text
							style={[
								styles().fs14,
								// styles().fw400,
								// styles().fw700,
								styles().fontRegular,
								{ color: currentTheme.headingColor },
							]}
						>
							Forgot Password?
						</Text>
					</TouchableOpacity>
				</View>

				<View>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => Login()}
								Title={'Login'}
								Style={{
									backgroundColor: currentTheme.headingColor,
									borderColor: currentTheme.headingColor,
								}}
							/>}
				</View>

				<View style={[styles().flexRow, styles().mt15, styles().mb15, styles().justifyBetween, styles().alignCenter]}>
					<View style={[styles().flex, { height: 2, backgroundColor: currentTheme.EFEFEF }]} />
					<Text
						style={[
							styles().fs16,
							// styles().fw400,
							styles().mh10,
							styles().fontRegular,
							{ color: currentTheme.headingColor },
						]}
					>
						or
					</Text>
					<View style={[styles().flex, { height: 2, backgroundColor: currentTheme.EFEFEF }]} />
				</View>

				<View>
					<ThemeButton
						Title={'Register Now'}
						onPress={() => {
							Keyboard.dismiss()
							props.navigation.navigate('SignUp')
						}}
					/>
				</View>
			</View>
			<View style={styles().wh30px} />
		</AuthLayout>
	)
}

export default React.memo(Login)
