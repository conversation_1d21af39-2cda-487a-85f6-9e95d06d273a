import { Text, View, TouchableOpacity, Image, FlatList, RefreshControl } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import LayoutView from '../../component/Layout/Layout'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation, useQuery } from '@apollo/client'
import { getNotifications, readAll, readNotification } from '../../apollo/server'
import moment from 'moment'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import NotificationContext from '../../context/Notification/Notification'

const Notifications = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { reloadAppData, unreads } = useContext(NotificationContext)
	const [notification, setNotification] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 40

	const { data, loading, error, refetch } = useQuery(getNotifications, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			// filters: null,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log(
			//   'getNotifications res :',
			//   JSON.stringify(data?.getNotifications),
			// );
			reloadAppData(true)
			setNotification(prev => [...prev, ...data?.getNotifications?.results])
			setLoading(false)
		},
		onError: err => {
			// console.log('getNotifications err :', err);
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	const [mutate] = useMutation(readNotification, {
		errorPolicy: 'all',
		onCompleted: async data => {
			reloadAppData(true)
			console.log('readNotification res', data)
			await refetch().then(res => {
				setNotification(res.data?.getNotifications?.results)
				setLoading(false)
			})
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('readNotification error  :', error)
		},
	})

	const [mutateAll] = useMutation(readAll, {
		errorPolicy: 'all',
		onCompleted: async data => {
			console.log('readAll res', data)
			await refetch({
				options: {
					page: 1,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			}).then(res => {
				reloadAppData(true)
				setNotification(res.data?.getNotifications?.results)
				setLoading(false)
			})
		},
		onError: error => {
			FlashMessage({ msg: error.message?.toString(), type: 'danger' })
			console.log('readAll error  :', error)
		},
	})

	const markasread = async id => {
		await mutate({ variables: { notificationId: id } })
	}

	const markllassread = async _ids => {
		await mutateAll()
	}

	const refresh = async () => {
		reloadAppData(true)
		setLoading(true)
		setNotification([])
		setPage(1)
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			reloadAppData(true)
			setNotification(res.data?.getNotifications?.results)
			setLoading(false)
		})
	}

	const nextPage = async () => {
		if (page < data?.getNotifications?.totalPages) {
			reloadAppData(true)
			setLoading(true)
			setPage(old => old + 1)
			await refetch()
		}
	}

	const handleNavigation = async item => {
		console.log('current notification :', item)
		const jobParams = {
			item: { _id: item?.meta?.jobId },
			jobId: item?.meta?.jobId,
			// alreadyApplied: true,
		}

		const postParams = {
			_id: item?.meta?.postId,
		}

		const badgeParams = {
			edit: false,
			rating: false,
			recommendation: false,
		}

		const ratingParams = {
			rating: true,
			edit: false,
			recommendation: false,
		}

		const recommendationParams = {
			recommendation: true,
			edit: false,
			rating: false,
		}

		const invoiceParams = {
			_id: item?.meta?.invoiceId,
		}

		const appointmenetParams = {
			_id: item?.meta?.appointmentId,
		}

		if (!item?.seen) {
			await markasread(item?._id)
		}
		if (item?.type === 'job' || item?.type === 'offer') {
			props.navigation.navigate('JobDetails', jobParams)
		}
		if (item?.type === 'invite' || item?.type === 'newJob' || item?.type === 'closeJob') {
			props.navigation.navigate('JobDetails', jobParams)
		}
		if (item?.type === 'checkride') {
			props.navigation.navigate('MyCheckRides', { activeTab: 0 })
		}
		if (item?.type === 'friendship') {
			props.navigation.navigate('FriendsNavigator', { activeTab: 0 })
		}
		if (item?.type === 'ratings') {
			props.navigation.navigate('EditProfile', ratingParams)
		}
		if (item?.type === 'recommendation') {
			props.navigation.navigate('EditProfile', recommendationParams)
		}
		if (item?.type === 'post') {
			props.navigation.navigate('PostDetails', {
				item: postParams,
				postId: item?.meta?.postId,
			})
		}
		if (item?.type === 'badge') {
			props.navigation.navigate('EditProfile', badgeParams)
		}
		if (item?.type === 'invoice') {
			props.navigation.navigate('PaymentDetails', {
				invoice: invoiceParams,
				invoiceId: item?.meta?.invoiceId,
			})
		}
		if (item?.type === 'appointment') {
			props.navigation.navigate('AppointmenetDetails', {
				appointmenetParams: appointmenetParams,
				appointmentId: item?.meta?.appointmentId,
			})
		}
		if (item?.type === 'verification') {
			props.navigation.navigate('Menu')
		}
	}

	useEffect(() => {
		const focus = props.navigation.addListener('focus', async () => {
			// reloadAppData(true);
			await refresh()
		})
		return focus
	}, [props.navigation])

	useEffect(() => {
		setLoading(loading)
	}, [loading])

	return (
		<LayoutView
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			LeftIconFunc={() => props.navigation.navigate('Home')}
			keyBoardArea={50}
			pagetitle={'Notifications'}
			ContentArea={[styles().ph0]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flexRow, styles().ph20, styles().mb20, styles().alignCenter, styles().justifyBetween]}>
					<View>
						<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.themeBackground }]}>{moment(new Date()).format('LL')}</Text>
					</View>
					<TouchableOpacity
						onPress={() => markllassread()}
						activeOpacity={0.5}
					>
						<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.B7B7B7 }]}>Mark all as read</Text>
					</TouchableOpacity>
				</View>

				<FlatList
					data={notification}
					showsVerticalScrollIndicator={false}
					onEndReachedThreshold={0.75}
					contentContainerStyle={{ flexGrow: 1 }}
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					onEndReached={() => nextPage()}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No Notifications'}
								</Text>
							</View>
						)
					}}
					renderItem={({ item, index }) => {
						return (
							<TouchableOpacity
								activeOpacity={0.8}
								onPress={() => handleNavigation(item)}
								key={index}
								style={[
									styles().flexRow,
									styles().pv10,
									styles().ph20,
									styles().alignCenter,
									styles().bbw1,
									{
										borderColor: currentTheme.F3F0E4,
										backgroundColor: item?.seen ? currentTheme.white : currentTheme.F3F3F3,
									},
								]}
							>
								<View
									style={[
										styles().wh40px,
										styles().mr15,
										styles().overflowH,
										styles().br25,
										styles().alignCenter,
										styles().justifyCenter,
										{
											borderWidth: item?.from?.photo ? 0 : 1,
											borderColor: currentTheme.themeBackground,
										},
									]}
								>
									{item?.from?.photo
										? <Image
												source={{ uri: item?.from?.photo }}
												style={styles().wh100}
												resizeMode="cover"
											/>
										: <MaterialCommunityIcons
												name="bell"
												size={20}
												color={currentTheme.themeBackground}
											/>}
								</View>
								<View style={{ flex: 1 }}>
									<Text
										numberOfLines={2}
										style={[
											styles().fs12,
											styles().fontMedium,
											styles().textCapitalize,
											{
												color: currentTheme.headingColor,
												marginBottom: 3,
											},
										]}
									>
										{item?.from?.name ? `${item?.from?.name} ` : null}
										<Text
											style={[
												styles().fontRegular,
												{
													textTransform: item?.from?.name ? 'lowercase' : 'capitalize',
												},
											]}
										>
											{item?.text?.trim()}
											{/* {capitalizeFirstLetter(item?.text)} */}
										</Text>
									</Text>
									<Text style={[styles().fs11, styles().fontRegular, { color: currentTheme.c9E9E9E }]}>{moment(item?.createdAt).fromNow()}</Text>
								</View>
							</TouchableOpacity>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
					ListFooterComponent={<View style={styles().wh30px} />}
				/>
			</View>
		</LayoutView>
	)
}

export default React.memo(Notifications)
