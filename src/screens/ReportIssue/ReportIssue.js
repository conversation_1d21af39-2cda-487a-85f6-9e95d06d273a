import { Platform, View } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Dropdown from '../../component/DropDown/Dropdopwn'
import { useMutation } from '@apollo/client'
import { createThread } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import UserContext from '../../context/User/User'
import Spinner from '../../component/Spinner/Spinner'

const ReportIssue = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [title, setTitle] = useState('')
	const [titleError, setTitleError] = useState(false)
	const [category, setCategory] = useState('')
	const [content, setContent] = useState('')
	const [Loading, setLoading] = useState(false)
	const [CategoryList, _setCategoryList] = useState(['jobs', 'crewmember', 'company', 'checkride', 'mobileapp', 'general'])

	const [mutate, {}] = useMutation(createThread, {
		errorPolicy: 'all',
		onCompleted: async ({ createThread }) => {
			console.log('createThread res :', createThread)
			FlashMessage({ msg: 'Your query has been sent.', type: 'success' })
			setLoading(false)
			setTitle('')
			setContent('')
			setCategory('')
			props.navigation.goBack()
		},
		onError: err => {
			console.log('createThread err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	async function CreateThread() {
		let status = true
		if (title === '') {
			FlashMessage({ msg: 'Enter Title', type: 'warning' })
			status = false
			return
		}
		if (category === '') {
			FlashMessage({ msg: 'Select Category', type: 'warning' })
			status = false
			return
		}
		if (content === '') {
			FlashMessage({ msg: 'Enter Your Issue', type: 'warning' })
			status = false
			return
		}
		if (status) {
			const data = {
				author: user?._id,
				content: content,
				type: category,
				title: title,
			}
			setLoading(true)
			await mutate({
				variables: {
					inputThread: data,
				},
			})
		}
	}

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Report an Issue'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={title}
						errorText={titleError}
						autoCapitalize="none"
						placeholder={'Title'}
						onChangeText={text => {
							setTitleError(false)
							setTitle(text)
						}}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.C3C3C3 }]}
						placeholderTextColor={currentTheme.headingColor}
					/>
				</View>
				<View style={[styles().mb20]}>
					<Dropdown
						placeholder={category ? category : 'Category'}
						data={CategoryList}
						selectedValue={value => setCategory(value)}
					/>
				</View>

				<View style={[styles().mb20]}>
					<TextField
						keyboardType="default"
						value={content}
						autoCapitalize="none"
						multiline={true}
						placeholder={'Message/problem'}
						onChangeText={text => {
							setContent(text)
						}}
						stylesInput={[
							styles().h150px,
							{
								textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
							},
						]}
						style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.C3C3C3 }]}
						placeholderTextColor={currentTheme.headingColor}
					/>
				</View>
			</View>
			{Loading
				? <View style={[styles().mb20]}>
						<Spinner />
					</View>
				: <ThemeButton
						Title={'Send'}
						onPress={() => CreateThread()}
						Style={styles().mb20}
					/>}
		</Layout>
	)
}

export default ReportIssue
