import {
	Text,
	View,
	RefreshControl,
	SafeAreaView,
	KeyboardAvoidingView,
	TouchableOpacity,
	StatusBar,
	Image,
	Platform,
	LayoutAnimation,
	UIManager,
	Dimensions,
	ScrollView,
	StyleSheet,
	Linking,
} from 'react-native'
import { useContext, useEffect, useRef, useState } from 'react'
import styles from '../../screens/styles'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Octicons from '@expo/vector-icons/Octicons'
import Entypo from '@expo/vector-icons/Entypo'
import AntDesign from '@expo/vector-icons/AntDesign'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Tooltip from 'react-native-walkthrough-tooltip'
import TextField from '../../component/FloatTextField/FloatTextField'
import moment from 'moment'
import ImageView from 'react-native-image-viewing'
import Video from 'react-native-video'
import { deletePost, getPostById, postComment, postLike } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import UserContext from '../../context/User/User'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Loading from '../../context/Loading/Loading'
import { useIsFocused } from '@react-navigation/native'
import fontStyles from '../../utils/fonts/fontStyles'
import { urlRegex } from '../../utils/Constants'
import Report from '../../context/Report/Report'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const { width, height } = Dimensions.get('window')

const PostDetails = props => {
	const scrollViewRef = useRef(null)
	const themeContext = useContext(ThemeContext)
	const { showReportModal, setOnUpdateReport, onUpdateReport, setShowReportModal, setReport } = useContext(Report)
	const user = useContext(UserContext)
	const [refreshLoading, setRefreshLoading] = useState(false)
	const isFocused = useIsFocused()
	const currentTheme = theme[themeContext.ThemeValue]
	const { isLoader } = useContext(Loading)
	const { isReply, postId } = props?.route?.params
	const [toolTipVisible, settoolTipVisible] = useState(false)
	const [mute, setMute] = useState(true)
	const [pause, setPause] = useState(true)
	const [readmore, setReadmore] = useState(3)
	const [readomoreComments, setReadomoreComments] = useState({})
	const [readmoreReply, setReadmoreReply] = useState({})
	const [post, setPost] = useState('')
	const videoRef = useRef(null)
	const [replyto, setReplyto] = useState('')
	const [replyComment, setReplyComment] = useState('')
	const [showCommentBox, setshowCommentBox] = useState(false)
	const [newComment, setNewComment] = useState('')
	const [isVideoWidgets, setIsVideoWidgets] = useState(true)
	const [visible, setIsVisible] = useState({
		dp: false,
		cp: false,
	})
	const postContentLength = 150
	const postCommentLength = 100
	const postReplyLength = 100

	const MutedButton = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => setMute(!mute)}
				style={postStyles.mute}
			>
				<FontAwesome5
					name={mute ? 'volume-mute' : 'volume-up'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const ReplayButton = ({ index }) => {
		const handleReplay = () => {
			if (videoRef.current) {
				videoRef.current.seek(0) // Seek to the beginning of the video
				setPause(false)
			}
		}
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={handleReplay}
				style={postStyles.reply}
			>
				<MaterialCommunityIcons
					name={'reload'}
					color={currentTheme.themeBackground}
					size={20}
				/>
			</TouchableOpacity>
		)
	}

	const PausePlay = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.7}
				onPress={() => {
					setPause(!pause)
					setMute(false)
					setTimeout(() => {
						if (pause) {
							LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
							setIsVideoWidgets(false)
						}
					}, 2000)
				}}
				style={postStyles.pausePlay}
			>
				<AntDesign
					name={pause ? 'play' : 'pausecircle'}
					color={currentTheme.themeBackground}
					size={18}
				/>
			</TouchableOpacity>
		)
	}

	const RenderTextWithLinks = ({ text, readmore }) => {
		if (text) {
			// Split text by URLs
			const parts = text?.split(urlRegex)
			return parts.map((part, index) => {
				if (urlRegex.test(part)) {
					// Ensure the URL is complete (prepend "http://" for non-prefixed URLs like "www" or domain names)
					const url = part.startsWith('http') ? part : `http://${part}`

					// Render URLs as clickable blue text
					return (
						<TouchableOpacity
							activeOpacity={0.5}
							key={index}
							onPress={() => Linking.openURL(url)}
						>
							<Text
								style={[
									{
										color: currentTheme.darkBlue,
										textDecorationLine: 'underline',
									},
								]}
							>
								{part}
							</Text>
						</TouchableOpacity>
					)
				}
				// Render normal text
				return (
					<Text
						numberOfLines={readmore}
						style={[styles().fs12, styles().fontRegular, { color: currentTheme.c444D6E }]}
						key={index}
					>
						{part}
					</Text>
				)
			})
		}
	}

	const FullScreenIcon = ({ index }) => {
		return (
			<TouchableOpacity
				activeOpacity={0.5}
				onPress={() => setIsVisible({ ...visible, dp: true })}
				style={postStyles.fullscreen}
			>
				<Octicons
					name={'screen-full'}
					color={currentTheme.themeBackground}
					size={16}
				/>
			</TouchableOpacity>
		)
	}

	const { data, loading, error, refetch } = useQuery(getPostById, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			postId: postId,
		},
		onCompleted: data => {
			console.log('getPostById res :', JSON.stringify(data))
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			setPost(data?.getPostById)
			setReadomoreComments(data?.getPostById.comments)
			setshowCommentBox(true)
			isLoader(false)
			setRefreshLoading(false)
		},
		onError: err => {
			console.log('getPostById err :', err)
			isLoader(false)
			setRefreshLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [LikeMutate, {}] = useMutation(postLike, {
		errorPolicy: 'all',
		onCompleted: async ({ postLike }) => {
			console.log('postLike res :', postLike)
		},
		onError: err => {
			console.log('postLike err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	async function LikePost(id) {
		console.log('post id :', id)
		const isLiked = post?.likes?.includes(user?._id)
		setPost(prevPost => ({
			...prevPost,
			likes: isLiked ? post?.likes?.filter(id => id !== user?._id) : [...prevPost?.likes, user?._id],
		}))
		await LikeMutate({
			variables: {
				postLikeId: id,
				inputLike: {
					user: user?._id,
				},
			},
		})
	}

	const [commentMutate, {}] = useMutation(postComment, {
		errorPolicy: 'all',
		onCompleted: async ({ postComment }) => {
			console.log('postComment res :', postComment)
			setPost(prev => ({ ...prev, comments: postComment?.comments }))
			setReplyto('')
			setReplyComment('')
			setNewComment('')
			scrollToBottom()
			// setshowCommentBox(false)
			// if (replyto !== '') {
			//   updateReply()
			// } else {
			//   updateComment()
			// }
		},
		onError: err => {
			console.log('postComment err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const onUserNavigation = () => {
		if (post?.author?._id === user?._id) {
			props.navigation.navigate('Profile')
		} else {
			props.navigation.navigate('ViewFriendProfile', {
				userId: post?.author?._id,
				isFriend: true,
			})
		}
	}

	const onCommenterNavigation = item => {
		if (item?.user?._id === user?._id) {
			props.navigation.navigate('Profile')
		} else {
			props.navigation.navigate('ViewFriendProfile', {
				userId: item?.user?._id,
				isFriend: false,
			})
		}
	}

	const onReplyerNavigation = reply => {
		if (reply?.user?._id === user?._id) {
			props.navigation.navigate('Profile')
		} else {
			props.navigation.navigate('ViewFriendProfile', {
				userId: reply?.user?._id,
				isFriend: false,
			})
		}
	}

	async function Reply() {
		if (replyComment === '') {
			return
		}
		await commentMutate({
			variables: {
				postId: post?._id,
				inputComment: {
					text: replyComment,
					user: user?._id,
				},
				commentId: replyto?._id,
			},
		})
	}

	async function Comment() {
		if (newComment === '') {
			return
		}
		await commentMutate({
			variables: {
				postId: post?._id,
				inputComment: {
					text: newComment,
					user: user?._id,
				},
			},
		})
	}

	const refresh = async () => {
		setReadmoreReply({})
		setReadomoreComments({})
		setRefreshLoading(true)
		await refetch().then(res => {
			setPost(res?.data?.getPostById)
			setRefreshLoading(false)
			// console.log('refresh');
		})
	}

	const scrollToBottom = () => {
		// Use a reference to the ScrollView component to scroll
		if (scrollViewRef.current) {
			scrollViewRef.current.scrollToEnd({ animated: true })
		}
	}

	async function _updateReply() {
		const myReply = {
			_id: Math.floor(Math.random() * 101),
			text: replyComment,
			user: {
				_id: user?._id,
				name: user?.name,
				photo: user?.photo,
			},
			createdAt: Date(),
		}

		const updateReply = post?.comments?.map(comment => {
			if (comment?._id === replyto?._id) {
				console.log(true)
				return {
					...comment,
					reply: [...comment?.reply, myReply],
				}
			}
			return comment
		})
		// setCommentsList(updateReply);
		setPost(prev => ({ ...prev, comments: updateReply }))
		setReplyto('')
		setReplyComment('')
	}

	async function _updateComment() {
		const myComment = {
			text: newComment,
			user: {
				_id: user._id,
				name: user?.name,
				photo: user?.photo,
			},
			_id: Math.floor(Math.random() * 101),
			reply: [],
			createdAt: Date(),
		}
		setPost(prev => ({ ...prev, comments: [...prev.comments, myComment] }))
		setNewComment('')
		setshowCommentBox(false)
	}

	const [postDeleteMutate, {}] = useMutation(deletePost, {
		errorPolicy: 'all',
		onCompleted: async ({ deletePost }) => {
			console.log('deletePost res :', deletePost)
			settoolTipVisible(false)
			handleBack()
		},
		onError: err => {
			console.log('deletePost err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			settoolTipVisible(false)
		},
	})

	async function DeletePost(id) {
		console.log('post id :', id)
		await postDeleteMutate({
			variables: {
				deletePostInput: {
					id: id,
				},
			},
		})
	}

	const handleBack = () => {
		props.navigation.goBack()
	}

	const DP = [{ uri: post?.images ? post?.images[0] : null }]

	useEffect(() => {
		isLoader(loading)
		if (!loading && isReply) {
			scrollToBottom()
			// setReplyto(lastComment)
		}
	}, [loading])

	useEffect(() => {
		if (!isFocused) {
			setPause(true)
			setMute(true)
		}
	}, [isFocused])

	useEffect(() => {
		if (onUpdateReport) {
			handleBack()
			setOnUpdateReport(false)
			setReport('')
		}
	}, [onUpdateReport])

	// console.log('post._id :', JSON.stringify(post?._id));
	if (loading) {
		return null
	}
	return (
		<View style={[styles().flex, { backgroundColor: currentTheme.white }]}>
			{post?.images?.length > 0 && (
				<ImageView
					images={DP}
					imageIndex={0}
					presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'fullScreen'}
					visible={visible.dp}
					onRequestClose={() => setIsVisible({ ...visible, dp: false })}
				/>
			)}

			<SafeAreaView style={[styles().flex, styles().mt30]}>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					keyboardVerticalOffset={Platform.OS === 'ios' ? 15 : 15}
					style={[styles().flex]}
				>
					<ScrollView
						ref={scrollViewRef}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={refreshLoading}
							/>
						}
						showsVerticalScrollIndicator={false}
						keyboardShouldPersistTaps={'handled'}
						contentContainerStyle={{ flexGrow: 1 }}
					>
						<View style={[styles().flex, styles().ph15]}>
							<View
								style={[
									// styles().mt25,
									styles().pt15,
									styles().pb15,
									styles().br25,
								]}
							>
								<View style={[styles().flexRow, styles().mb5, styles().justifyBetween, styles().alignCenter]}>
									<TouchableOpacity
										style={[styles().pr15]}
										onPress={() => handleBack()}
									>
										<FontAwesome
											name="angle-left"
											size={30}
											color={currentTheme.blackish}
										/>
									</TouchableOpacity>
									{post?.author?.photo
										? <TouchableOpacity
												activeOpacity={0.9}
												onPress={() => onUserNavigation()}
												style={[styles().wh35px, styles().overflowH, styles().br50]}
											>
												<Image
													source={{ uri: post?.author?.photo }}
													style={styles().wh100}
													resizeMode="cover"
												/>
											</TouchableOpacity>
										: <TouchableOpacity
												activeOpacity={0.9}
												onPress={() => onUserNavigation()}
												style={[
													styles().overflowH,
													styles().justifyCenter,
													styles().alignCenter,
													styles().br50,
													styles().wh35px,
													{
														borderWidth: 1,
														borderColor: currentTheme.themeBackground,
													},
												]}
											>
												<FontAwesome5
													name="user-alt"
													size={15}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>}
									<View style={[styles().flex, styles().ml10]}>
										<View
											style={[
												styles().flexRow,
												styles().alignCenter,
												// {marginBottom: 3},
											]}
										>
											<Text style={[styles().fs14, styles().fontMedium, styles().textCapitalize, { color: currentTheme.headingColor }]}>
												{post?.author?.name}
											</Text>
											{post?.author?.profileVerified
												? <View style={[styles().wh15px, styles().ml5, styles().overflowH]}>
														<Image
															source={require('../../assets/images/verified-icon.png')}
															style={styles().wh100}
															resizeMode="contain"
														/>
													</View>
												: null}
										</View>
										<View style={[styles().flexRow, styles().alignCenter]}>
											<View style={[styles().wh10px, styles().mr5, styles().overflowH]}>
												<Image
													source={require('../../assets/images/globe.png')}
													style={styles().wh100}
													resizeMode="contain"
												/>
											</View>
											<Text style={[styles().fs10, styles().fontRegular, { color: currentTheme.BABDC9 }]}>{moment(post?.createdAt).fromNow()}</Text>
										</View>
									</View>

									<Tooltip
										isVisible={toolTipVisible}
										content={
											<View style={{ flex: 1 }}>
												{post?.author?._id === user?._id
													? <TouchableOpacity
															onPress={() => DeletePost(post?._id)}
															style={[styles().alignCenter, styles().mb5]}
														>
															<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c737373 }]}>{'Delete'}</Text>
														</TouchableOpacity>
													: <TouchableOpacity
															onPress={() => {
																setReport(post)
																setShowReportModal(true)
																settoolTipVisible(false)
															}}
															style={[
																styles().alignCenter,
																// styles().mb5,
															]}
														>
															<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c737373 }]}>{'Report Post'}</Text>
														</TouchableOpacity>}
											</View>
										}
										contentStyle={
											[
												// styles().pt0,
												// styles().w100px,
												// styles().h40px,
											]
										}
										placement="bottom"
										disableShadow={true}
										// showChsildInTooltip={false}
										topAdjustment={Platform.OS === 'android' ? -StatusBar.currentHeight : 0}
										onClose={() => settoolTipVisible(!toolTipVisible)}
									>
										{/* {post?.author?._id === user?._id ? ( */}
										<TouchableOpacity
											style={[styles().wh25px, styles().br50, styles().alignCenter, styles().justifyCenter, { backgroundColor: currentTheme.EFF2F7 }]}
											onPress={() => {
												// settoolTipVisible(props.index);
												settoolTipVisible(!toolTipVisible)
											}}
										>
											<FontAwesome
												name="ellipsis-h"
												size={16}
												color={currentTheme.D9D9D9}
											/>
										</TouchableOpacity>
										{/* ) : null} */}
									</Tooltip>
								</View>
								<View>
									<View style={[styles().mv10]}>
										<RenderTextWithLinks
											text={post?.content}
											readmore={readmore}
										/>
										{readmore && post?.content?.length > postContentLength
											? <TouchableOpacity onPress={() => setReadmore(undefined)}>
													<Text style={[styles().fs12, styles().fontRegular, styles().fw700, { color: currentTheme.c444D6E }]}>{'read more...'}</Text>
												</TouchableOpacity>
											: null}
									</View>
									{post?.images?.length > 0
										? <TouchableOpacity
												activeOpacity={0.7}
												onPress={() => setIsVisible({ ...visible, dp: true })}
												style={[
													styles().w100,
													styles().mt15,
													styles().overflowH,
													{
														borderRadius: 0,
														backgroundColor: currentTheme.F3F0E4,
														// aspectRatio: 1,
														height: height * 0.5,
													},
												]}
											>
												<Image
													source={{ uri: post?.images[0] }}
													style={styles().wh100}
													resizeMode="cover"
												/>
												<FullScreenIcon />
											</TouchableOpacity>
										: null}
									{post?.videos?.length > 0
										? <TouchableOpacity
												activeOpacity={0.9}
												onPress={() => {
													LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
													setIsVideoWidgets(true)
												}}
												style={[{ height: 300 }, styles().w100, styles().mt10, styles().overflowH, styles().br15, styles().mt10]}
											>
												<Video
													ref={videoRef}
													useNativeControls
													resizeMode="contain"
													autoplay={false}
													source={{ uri: post?.videos[0] }}
													style={{
														height: 300,
														width: '100%',
														// aspectRatio: 1,
													}}
													paused={pause}
													muted={mute}
													onEnd={() => {
														videoRef.current.seek(0)
														setPause(true)
														setIsVideoWidgets(true)
													}}
												/>
												{isVideoWidgets
													? <>
															<ReplayButton />
															<MutedButton />
															<PausePlay />
														</>
													: null}
											</TouchableOpacity>
										: null}
								</View>
								<View style={[styles().flexRow, styles().mt10, styles().justifyBetween, styles().alignCenter]}>
									<View style={[styles().flexRow]}>
										<TouchableOpacity
											activeOpacity={0.7}
											onPress={() => LikePost(post?._id)}
											style={[
												styles().wh35px,
												styles().alignCenter,
												styles().justifyCenter,
												styles().ph5,
												styles().mr5,
												styles().br50,
												styles().boxpeshadowCart,
											]}
										>
											<View style={[{ width: 27, height: 27 }, styles().ph5, styles().alignCenter, styles().justifyCenter, styles().overflowH]}>
												{/* {post?.likes?.includes(user?._id) ?
                        <Image source={require('../../assets/images/like-img.png')} style={[styles().wh100, { transform: [{ rotate: '180deg' }] }]} resizeMode='contain' />
                        : */}
												<Image
													source={require('../../assets/images/like-img.png')}
													style={styles().wh100}
													resizeMode="contain"
												/>
												{/* } */}
											</View>
										</TouchableOpacity>
										<TouchableOpacity
											activeOpacity={0.7}
											onPress={() => {
												scrollToBottom()
												setshowCommentBox(true)
												setReplyto('')
											}}
											style={[
												styles().wh35px,
												styles().alignCenter,
												styles().justifyCenter,
												styles().ph5,
												styles().mr10,
												styles().br50,
												styles().boxpeshadowCart,
											]}
										>
											<View style={[{ width: 27, height: 27 }, styles().ph5, styles().alignCenter, styles().justifyCenter, styles().overflowH]}>
												<Image
													source={require('../../assets/images/comment-img.png')}
													style={styles().wh100}
													resizeMode="contain"
												/>
											</View>
										</TouchableOpacity>
									</View>
									<View style={[styles().flexRow, styles().alignCenter]}>
										<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c747EA0 }]}>
											{post?.likes?.length ? post?.likes?.length : 0} likes
										</Text>
										<View style={[styles().wh5px, styles().mh5, styles().br50, { backgroundColor: currentTheme.c747EA0 }]} />
										<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.c747EA0 }]}>
											{post?.comments?.length ? post?.comments?.length : 0} comments
										</Text>
									</View>
								</View>
							</View>

							<View style={[styles().btw1, styles().flex, styles().pt15, { borderTopColor: currentTheme.c707070 }]}>
								{post?.comments?.length === 0
									? <View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
											<Text
												style={[
													{
														color: currentTheme.E8E8C8,
														fontSize: 14,
														fontFamily: fontStyles.PoppinsRegular,
													},
												]}
											>
												{'No Comments'}
											</Text>
										</View>
									: null}
								{post?.comments?.map((item, index) => {
									return (
										<>
											<View
												key={index}
												style={[styles().flexRow, styles().mb15]}
											>
												<TouchableOpacity
													activeOpacity={0.5}
													onPress={() => onCommenterNavigation(item)}
													style={[styles().wh30px, styles().mr10, styles().overflowH, styles().br50]}
												>
													{item?.user?.photo
														? <Image
																source={{ uri: item?.user?.photo }}
																style={styles().wh100}
																resizeMode="cover"
															/>
														: <View
																style={[
																	styles().overflowH,
																	styles().justifyCenter,
																	styles().alignCenter,
																	styles().br50,
																	styles().wh30px,
																	{
																		borderWidth: 1,
																		borderColor: currentTheme.themeBackground,
																	},
																]}
															>
																<FontAwesome5
																	name="user-alt"
																	size={13}
																	color={currentTheme.themeBackground}
																/>
															</View>}
												</TouchableOpacity>
												<View style={{ flex: 1 }}>
													<Text style={[styles().fs12, styles().fontBold, styles().textCapitalize, { color: currentTheme.c1B1B1B }]}>{item.user?.name}</Text>
													<Text
														numberOfLines={readomoreComments[`readcomment${index}`] === undefined ? 3 : undefined}
														style={[styles().fs12, styles().fontRegular, { color: currentTheme.c9E9E9E }]}
													>
														{item.text}
													</Text>
													{!readomoreComments[`readcomment${index}`] && item?.text?.length > postCommentLength
														? <TouchableOpacity
																TouchableOpacity
																activeOpacity={0.7}
																onPress={() =>
																	setReadomoreComments({
																		...readomoreComments,
																		[`readcomment${index}`]: true,
																	})
																}
															>
																<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.blackish }]}>{'read more...'}</Text>
															</TouchableOpacity>
														: null}
													<View style={[styles().flexRow]}>
														<Text
															style={[
																styles().fs11,
																styles().fontRegular,
																{
																	color: currentTheme.themeBackground,
																	fontStyle: 'italic',
																},
															]}
														>
															{moment(item.createdAt).fromNow()}
														</Text>
														<TouchableOpacity
															onPress={() => {
																setReplyto(prev => (item !== prev ? item : ''))
																setshowCommentBox(false)
															}}
															style={[styles().ml10]}
														>
															<Text
																style={[
																	styles().fs11,
																	styles().fw500,
																	{
																		color: currentTheme.themeBackground,
																		fontStyle: 'italic',
																		textDecorationLine: 'underline',
																	},
																]}
															>
																Reply
															</Text>
														</TouchableOpacity>
													</View>
												</View>
											</View>
											<View style={[styles().pl20]}>
												{replyto === item && (
													<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().mb15]}>
														<View style={[styles().flex]}>
															<TextField
																keyboardType="default"
																value={replyComment}
																autoCapitalize="none"
																placeholder={`Reply to ${replyto?.user?.name}`}
																style={[
																	styles().bw1,
																	styles().br10,

																	styles().overflowH,
																	{ borderColor: currentTheme.B7B7B7 },
																]}
																stylesInput={[styles().fs12, styles().h40px]}
																onChangeText={text => setReplyComment(text)}
															/>
														</View>
														<TouchableOpacity
															onPress={() => Reply()}
															activeOpacity={0.5}
															style={[
																styles().wh30px,
																styles().ml5,
																styles().alignCenter,
																styles().justifyCenter,
																styles().br20,
																{ backgroundColor: currentTheme.EEE8D5 },
															]}
														>
															<Entypo
																name="paper-plane"
																size={16}
																color={currentTheme.themeBackground}
															/>
														</TouchableOpacity>
													</View>
												)}
												{item?.reply?.map((reply, index) => {
													return (
														<>
															<View
																key={index}
																style={[styles().flexRow, styles().mb15]}
															>
																<FontAwesome
																	name="mail-reply-all"
																	size={12}
																	color={currentTheme.navyBlue}
																	style={[styles().mt5, { marginRight: 5 }]}
																/>
																<TouchableOpacity
																	activeOpacity={0.5}
																	onPress={() => onReplyerNavigation(reply)}
																	style={[styles().wh30px, styles().mr10, styles().overflowH, styles().br50]}
																>
																	{reply?.user?.photo
																		? <Image
																				source={{ uri: reply?.user?.photo }}
																				style={styles().wh100}
																				resizeMode="cover"
																			/>
																		: <View
																				style={[
																					styles().overflowH,
																					styles().justifyCenter,
																					styles().alignCenter,
																					styles().br50,
																					styles().wh30px,
																					{
																						borderWidth: 1,
																						borderColor: currentTheme.themeBackground,
																					},
																				]}
																			>
																				<FontAwesome5
																					name="user-alt"
																					size={13}
																					color={currentTheme.themeBackground}
																				/>
																			</View>}
																</TouchableOpacity>
																<View style={{ flex: 1 }}>
																	<Text style={[styles().fs12, styles().fontBold, styles().textCapitalize, { color: currentTheme.c1B1B1B }]}>
																		{reply?.user?.name}
																	</Text>
																	<Text
																		numberOfLines={readmoreReply[`readreply${index}`] === undefined ? 2 : undefined}
																		style={[styles().fs12, styles().fontRegular, { color: currentTheme.c9E9E9E }]}
																	>
																		{reply.text}
																	</Text>
																	{!readmoreReply[`readreply${index}`] && reply?.text?.length > postReplyLength
																		? <TouchableOpacity
																				TouchableOpacity
																				activeOpacity={0.7}
																				onPress={() =>
																					setReadmoreReply({
																						...readomoreComments,
																						[`readreply${index}`]: true,
																					})
																				}
																			>
																				<Text style={[styles().fs12, styles().fontRegular, styles().fw600, { color: currentTheme.blackish }]}>
																					{'read more...'}
																				</Text>
																			</TouchableOpacity>
																		: null}
																	<View>
																		<Text
																			style={[
																				styles().fs11,
																				styles().fontRegular,
																				{
																					color: currentTheme.themeBackground,
																					fontStyle: 'italic',
																				},
																			]}
																		>
																			{moment(reply.createdAt).fromNow()}
																		</Text>
																	</View>
																</View>
															</View>
														</>
													)
												})}
											</View>
										</>
									)
								})}
								{showCommentBox
									? <View style={[styles().flexRow, styles().mt10, styles().alignStart, styles().justifyBetween, styles().mb20]}>
											<View style={[styles().flex]}>
												<TextField
													keyboardType="default"
													multiline={true}
													value={newComment}
													autoCapitalize="none"
													placeholder={'Write your comment'}
													style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
													stylesInput={[
														styles().fs12,
														styles().h80px,
														{
															textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
														},
													]}
													onChangeText={text => setNewComment(text)}
												/>
											</View>
											<TouchableOpacity
												onPress={() => Comment()}
												activeOpacity={0.5}
												style={[
													styles().wh30px,
													styles().ml5,
													styles().mt5,
													styles().alignCenter,
													styles().justifyCenter,
													styles().br20,
													{ backgroundColor: currentTheme.EEE8D5 },
												]}
											>
												<Entypo
													name="paper-plane"
													size={16}
													color={currentTheme.themeBackground}
												/>
											</TouchableOpacity>
										</View>
									: null}
							</View>
							<View style={styles().wh30px} />
						</View>
					</ScrollView>
				</KeyboardAvoidingView>
			</SafeAreaView>
		</View>
	)
}

export default PostDetails

const postStyles = StyleSheet.create({
	mute: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
	reply: {
		position: 'absolute',
		bottom: 0,
		right: 35,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: 100,
		height: 30,
		width: 30,
		backgroundColor: 'white',
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
	pausePlay: {
		position: 'absolute',
		bottom: 0,
		right: 70,
		margin: 10,
		zIndex: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,

		elevation: 10,
	},
	fullscreen: {
		position: 'absolute',
		bottom: 0,
		right: 0,
		margin: 10,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'white',
		borderRadius: 100,
		height: 30,
		width: 30,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 5,
		},
		shadowOpacity: 0.34,
		shadowRadius: 6.27,
		elevation: 10,
	},
})
