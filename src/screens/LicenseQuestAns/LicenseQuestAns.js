import { Text, View, TouchableOpacity, LayoutAnimation, Platform, UIManager, FlatList } from 'react-native'
import { useContext } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Layout from '../../component/Layout/Layout'
import { GetLicensingQuestions } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const LicenseQuestAns = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]

	const { parentItem } = props?.route?.params
	const { data, loading, error, refetch } = useQuery(GetLicensingQuestions, {
		variables: {
			licensingId: parentItem?._id,
		},
		onCompleted: data => {
			console.log('GetLicensingQuestions res :', data)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		},
		onError: err => {
			console.log('GetLicensingQuestions err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Licensing Guidelines'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex]}>
				<FlatList
					data={data?.getLicensingQuestions}
					showsVerticalScrollIndicator={false}
					onEndReachedThreshold={0.75}
					contentContainerStyle={{ padding: 3, flexGrow: 1 }}
					renderItem={({ item, index }) => {
						return (
							<TouchableOpacity
								key={index}
								activeOpacity={0.7}
								onPress={() =>
									props.navigation.navigate('LicenseQuestAnsDetail', {
										item: item,
										parentItem: parentItem,
									})
								}
								style={[
									styles().flexRow,
									styles().mb10,
									styles().pv15,
									styles().ph20,
									styles().flex,
									styles().br10,
									{ backgroundColor: currentTheme.F8F9FA },
									styles().alignCenter,
								]}
							>
								<Text style={[styles().flex, styles().fs14, styles().lh20, styles().fontMedium, { color: currentTheme.headingColor }]}>
									{item?.question ? item?.question : 'N/A'}
								</Text>
								<View style={[styles().ml15]}>
									<Text>{item?.appointmentPrice ? `$${Number.parseInt(item?.appointmentPrice)?.toFixed(2)}` : 0}</Text>
								</View>
								<View style={[styles().ml15]}>
									<FontAwesome
										name="angle-right"
										size={24}
										color={currentTheme.blackish}
									/>
								</View>
							</TouchableOpacity>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
				/>
			</View>
		</Layout>
	)
}

export default LicenseQuestAns
