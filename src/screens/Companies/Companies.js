import { Text, View, TouchableOpacity, FlatList, RefreshControl } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Entypo from '@expo/vector-icons/Entypo'
import TextField from '../../component/FloatTextField/FloatTextField'
import { useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Layout from '../../component/Layout/Layout'
import { companies } from '../../apollo/server'
import { useIsFocused } from '@react-navigation/native'
import CompaniesComponent from '../../component/CompaniesComponent/CompaniesComponent'
import CompanyFilter from '../../component/Modals/CompanyFilter'
import fontStyles from '../../utils/fonts/fontStyles'

const Companies = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const isFocus = useIsFocused()
	const [search, setSearch] = useState('')
	const [compamies, setCompamies] = useState([])
	const [searchData, setSearchData] = useState([])
	const [page, setPage] = useState(1)
	const [Loading, setLoading] = useState(false)
	const [totalPages, setTotalPages] = useState('')
	const [filter, setFilter] = useState(null)
	const [showclearfilter, setShowclearfilter] = useState(false)
	const [isFilter, setIsFilter] = useState(false)
	const pageSize = 25
	const deletedFilter = { deleted: false }

	const { data, loading, error, refetch } = useQuery(companies, {
		errorPolicy: 'all',
		fetchPolicy: 'no-cache',
		variables: {
			filters: { ...filter, ...deletedFilter },
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log('companies res :', JSON.stringify(data?.companies));
			setLoading(false)
			setTotalPages(data?.companies?.totalPages)
			setCompamies(prev => [...prev, ...data?.companies?.results])
			setSearchData(prev => [...prev, ...data?.companies?.results])
		},
		onError: err => {
			console.log('companies err :', err)
			setLoading(false)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	// const searchFilterFunction = text => {
	//   if (text) {
	//     const newData = compamies.filter(function (item) {
	//       const itemData = item.name ? item.name.toUpperCase() : ''.toUpperCase();
	//       const textData = text.toUpperCase();
	//       return itemData.indexOf(textData) > -1;
	//     });
	//     console.log('search result :', newData);
	//     setSearchData(newData);
	//     setSearch(text);
	//   } else {
	//     setSearchData(compamies);
	//     setSearch(text);
	//   }
	// };

	const refresh = async () => {
		setShowclearfilter(false)
		setFilter(null)
		setLoading(true)
		setPage(1)
		setSearchData([])
		setCompamies([])
		setSearch('')
		await refetch({ filters: deletedFilter }).then(res => {
			console.log('company refresh ', res?.data?.companies?.results)
			setCompamies(res?.data?.companies?.results)
			setSearchData(res?.data?.companies?.results)
			setTotalPages(res?.data?.companies?.totalPages)
			setLoading(false)
		})
	}
	console.log('companies length :', searchData?.length)
	console.log('companies page :', page)

	const nextPage = async () => {
		console.log('check page', page, data?.companies?.totalPages, totalPages, page < data?.companies?.totalPages)
		if (page < totalPages) {
			setPage(old => old + 1)
			setLoading(true)
			await refetch({
				filters: { ...filter, ...deletedFilter },
				options: {
					page: page,
					limit: pageSize,
					sortBy: 'createdAt:desc',
				},
			})
			// setSearch('');
		}
	}

	const onFilter = async filter => {
		setFilter({ ...filter, ...deletedFilter })
		setShowclearfilter(true)
		console.log('filter values :', filter)
		setLoading(true)
		setSearch('')
		setPage(1)
		setSearchData([])
		setCompamies([])
		await refetch({
			filters: { ...filter, ...deletedFilter },
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			// console.log('filter data =======>', JSON.stringify(res?.data?.companies));
			setCompamies(res?.data?.companies?.results)
			setSearchData(res?.data?.companies?.results)
			setLoading(false)
		})
	}

	const clearFiter = async () => {
		setLoading(true)
		setFilter({ ...filter, ...deletedFilter })
		setSearch('')
		setPage(1)
		setShowclearfilter(false)
		setSearchData([])
		setCompamies([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
			filters: { ...filter, ...deletedFilter },
		})
	}

	React.useEffect(() => {
		if (search.length > 2) {
			setPage(1)
			const searchData = setTimeout(async () => {
				setLoading(true)
				setShowclearfilter(false)
				setFilter(null)
				await refetch({
					filters: search ? { name: search.trim() } : {},
					options: { page: page, limit: pageSize, sortBy: 'createdAt:desc' },
				}).then(res => {
					setSearchData(res?.data?.companies?.results)
					console.log(`search result on ${search}:`, res?.data?.companies?.results)
					setLoading(false)
				})
			}, 1500)
			return () => clearTimeout(searchData)
		}
		setSearchData(compamies)
	}, [search])

	useEffect(() => {
		setPage(1)
	}, [isFocus])

	useEffect(() => {
		setLoading(loading)
	}, [loading])

	// console.log('total compamies =========>', compamies?.length);

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Companies'}
			ContentArea={styles().ph20}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flexRow, styles().alignCenter, styles().mb20, styles().justifyBetween]}>
					<View style={[styles().flex]}>
						<TextField
							keyboardType="default"
							value={search}
							// errorText={searchError}
							autoCapitalize="none"
							placeholder={'Search'}
							style={[styles().bw1, styles().br10, styles().overflowH, { borderColor: currentTheme.B7B7B7 }]}
							onChangeText={text => {
								// searchFilterFunction(text);
								setSearch(text)
							}}
						/>
					</View>
					<TouchableOpacity
						activeOpacity={0.5}
						onPress={() => setIsFilter(true)}
						style={[styles().wh35px, styles().ml15, styles().alignCenter, styles().justifyCenter, styles().br20, { backgroundColor: currentTheme.EEE8D5 }]}
					>
						<FontAwesome
							name="filter"
							size={20}
							color={currentTheme.themeBackground}
						/>
					</TouchableOpacity>
				</View>
				{showclearfilter
					? <TouchableOpacity
							onPress={() => clearFiter()}
							style={[styles().flexRow, styles().alignCenter, styles().mb15, { width: 130 }]}
						>
							<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black, marginTop: 1 }]}>Clear Filters</Text>
							<Entypo
								name={'cross'}
								size={20}
							/>
						</TouchableOpacity>
					: null}
				<View style={styles().flex}>
					<FlatList
						data={searchData}
						onEndReached={() => nextPage()}
						onEndReachedThreshold={0.5}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={{ flexGrow: 1 }}
						refreshControl={
							<RefreshControl
								colors={[currentTheme.themeBackground, currentTheme.black]}
								onRefresh={() => refresh()}
								refreshing={Loading}
							/>
						}
						ListEmptyComponent={() => {
							return (
								<View style={[styles().alignCenter, styles().justifyCenter, styles().mt100, styles().flex]}>
									<Text
										style={{
											color: currentTheme.E8E8C8,
											fontFamily: fontStyles.PoppinsRegular,
											fontSize: 14,
										}}
									>
										{Loading ? 'Loading...' : 'No companies'}
									</Text>
								</View>
							)
						}}
						renderItem={({ item, index }) => {
							return (
								<CompaniesComponent
									item={item}
									index={index}
									navigation={props.navigation}
								/>
							)
						}}
						keyExtractor={(_item, index) => index.toString()}
						ListFooterComponent={<View style={styles().wh20px} />}
					/>
				</View>
			</View>
			<CompanyFilter
				ModalHeading={'Filters'}
				modalVisible={isFilter}
				onClose={() => setIsFilter(false)}
				filters={filters => onFilter(filters)}
			/>
		</Layout>
	)
}

export default React.memo(Companies)
