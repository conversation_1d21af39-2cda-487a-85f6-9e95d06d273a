import { Text, View, Keyboard } from 'react-native'
import { useContext, useState, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { CodeField, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field'
import { verifyRecoveryEmail } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Layout from '../../component/Layout/Layout'

const CELL_COUNT = 6

const RecoveryVerification = props => {
	const recoveryEmail = props?.route?.params?.email
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [value, setValue] = useState('')
	const [Loading, setLoading] = useState(false)

	const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT })
	const [_propss, getCellOnLayoutHandler] = useClearByFocusCell({
		value,
		setValue,
	})

	const [mutate, { client }] = useMutation(verifyRecoveryEmail, {
		errorPolicy: 'all',
		onCompleted: async data => {
			console.log('verifyRecoveryEmail res', data)
			FlashMessage({ msg: 'Email has been verified', type: 'success' })
			setLoading(false)
			props.navigation.navigate('Menu')
		},
		onError: async err => {
			console.log('verifyRecoveryEmail err  :', err)
			FlashMessage({ msg: err?.message?.toString(), type: 'danger' })
			setLoading(false)
		},
	})

	async function Verify() {
		Keyboard.dismiss()
		let _status = true
		if (value.length !== 6) {
			FlashMessage({ msg: 'Enter 6-Digits OTP!', type: 'warning' })
			_status = false
			return
		}
		setLoading(true)
		const data = {
			code: value,
			email: recoveryEmail,
		}
		console.log(data)
		await mutate({
			variables: data,
		})
	}

	useEffect(() => {
		setLoading(false)
	}, [])

	return (
		<Layout
			navigation={props.navigation}
			withoutScroll={true}
			pagetitle={'Verify Recovery Email'}
			headerShown={true}
			keyBoardArea={-10}
			LeftIcon={true}
		>
			<View style={[styles().mt20, styles().flex, styles().ph20]}>
				<View style={[styles().flex]}>
					<View style={[styles().mb20]}>
						<Text style={[styles().fs16, styles().mb15, styles().fontBold, { color: currentTheme.headingColor }]}>Verify Email</Text>
						<Text style={[styles().fs16, styles().w80, styles().fontRegular, { color: currentTheme.headingColor }]}>
							{`Please enter the 6 digits code sent to ${recoveryEmail}.`}
						</Text>
					</View>
					<View style={[styles().w100, styles().alignSelfCenter, styles().mb25]}>
						<CodeField
							ref={ref}
							{...props}
							// Use `caretHidden={false}` when users can't paste a text value, because context menu doesn't appear
							value={value}
							onChangeText={setValue}
							cellCount={CELL_COUNT}
							rootStyle={[]}
							keyboardType="number-pad"
							textContentType="oneTimeCode"
							renderCell={({ index, symbol, isFocused }) => {
								return (
									<View
										key={index}
										style={[
											styles(currentTheme).verifyContainer,
											isFocused && {
												borderColor: currentTheme.themeBackground,
												borderWidth: 1,
											},
										]}
									>
										<Text
											style={[
												styles().fs24,
												styles().fontRegular,
												{
													color: currentTheme.themeBackground,
													backgroundColor: currentTheme.white,
												},
											]}
											onLayout={getCellOnLayoutHandler(index)}
										>
											{symbol || (isFocused ? <Cursor /> : null)}
										</Text>
									</View>
								)
							}}
						/>
					</View>
				</View>
				<View style={[styles().mb20]}>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => Verify()}
								Title={'Confirm'}
							/>}
				</View>
			</View>
		</Layout>
	)
}

export default RecoveryVerification
