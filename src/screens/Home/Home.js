import { Text, View, TouchableOpacity, FlatList, Image, RefreshControl, Dimensions, Platform } from 'react-native'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Layout from '../../component/Layout/Layout'
import HeaderHome from '../../component/Header/HeaderHome'
import UserContext from '../../context/User/User'
import TextField from '../../component/FloatTextField/FloatTextField'
import PostComponent from '../../component/PostComponent/PostComponent'
import ImageAndDocumentPicker from '../../component/ImageAndDocumentPicker/ImageAndDocumentPicker'
import { createPost, getWallPosts } from '../../apollo/server'
import { useQuery, useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Video from 'react-native-video'
import AntDesign from '@expo/vector-icons/AntDesign'
import Spinner from '../../component/Spinner/Spinner'
import { useIsFocused } from '@react-navigation/native'
import PostSkeleton from '../../component/Skeleton/PostSkeleton'
import StickyCaptain from '../../component/StickyCaptain/StickyCaptain'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Loader from '../../context/Loading/Loading'
import TestAccount from '../../component/TestAccount/TestAccount'
import { AuthContext } from '../../context/Auth/auth'
import Report from '../../context/Report/Report'

const { width, height } = Dimensions.get('window')

const Home = props => {
	const { token, refreshToken } = useContext(AuthContext)
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const { onUpdateReport, report, setOnUpdateReport, setReport } = useContext(Report)
	const isFocused = useIsFocused()
	const [enablePost, setEnablePost] = useState(true)
	const [activeIndex, setActiveIndex] = useState('')
	const [post, setPost] = useState({
		author: user?._id,
		images: '',
		videos: '',
		content: '',
	})
	const [postLoader, setPostLoader] = useState(false)
	const [getPosts, setGetPosts] = useState([])
	const [ispostPicker, setIspostPicker] = useState({
		image: false,
		video: false,
	})
	const [page, setPage] = useState(1)
	const pageSize = 20
	const { isLoader } = useContext(Loader)
	const [Loading, setLoading] = useState(false)

	const { data, loading, error, refetch } = useQuery(getWallPosts, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: res => {
			setLoading(false)
			console.log('getWallPosts res :', res)
			setGetPosts(prev => [...prev, ...res?.getWallPosts?.results])
		},
		onError: err => {
			setLoading(false)
			console.log('getWallPosts err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
			// ErrorHandler(err.message);
		},
	})

	const refresh = async () => {
		setLoading(true)
		setPage(1)
		setGetPosts([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setGetPosts(res?.data?.getWallPosts?.results)
			setLoading(false)
			// console.log('refresh');
		})
	}

	const nextPage = async () => {
		if (page < data?.getWallPosts?.totalPages) {
			console.log('post nextPage fire')
			setPage(old => old + 1)
			setLoading(true)
			await refetch()
		}
	}

	const removePost = async _id => {
		const removePost = await getPosts?.filter(obj => obj?._id !== _id)
		setGetPosts(removePost)
	}

	const likePost = async _id => {
		try {
			const updateLikes = getPosts?.map(post => {
				if (post._id === _id) {
					const isLiked = post?.likes?.includes(user?._id)
					return {
						...post,
						likes: isLiked ? post?.likes?.filter(id => id !== user?._id) : [...post?.likes, user?._id],
					}
				}
				return post
			})

			if (updateLikes) {
				setGetPosts(updateLikes)
			}
		} catch (err) {
			console.log('likePost catch:', err)
		}
	}

	const [postMutate, {}] = useMutation(createPost, {
		errorPolicy: 'all',
		onCompleted: async ({ createPost }) => {
			console.log('createPost res :', createPost)
			setPost({
				...post,
				images: '',
				videos: '',
				content: '',
				author: user?._id,
			})
			setPostLoader(false)
			setEnablePost(true)
			if (createPost?.status === 'active') {
				const newPost = []
				newPost.push(createPost)
				setGetPosts(prev => [...newPost, ...prev])
			} else {
				FlashMessage({
					msg: 'Post Submited, Will be Approved By Admin',
					type: 'warning',
					duration: 7000,
				})
			}
		},
		onError: err => {
			console.log('createPost err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setPostLoader(false)
		},
	})

	const handleInputChange = text => {
		setPost({ ...post, content: text })
		setEnablePost(!(text.trim().length > 0))
	}

	async function Post() {
		if (post.images === '' && post.videos === '' && post.content === '') {
			// FlashMessage({msg: 'Post data requied', type: 'info'});
			setEnablePost(false)
			return
		}
		let data
		if (post.videos !== '') {
			data = {
				author: user?._id,
				videos: post.videos,
				content: post.content?.trim(),
			}
		}
		if (post.images !== '') {
			data = {
				author: user?._id,
				images: post.images,
				content: post.content?.trim(),
			}
		}
		if (post.images === '' && post.videos === '') {
			data = {
				author: user?._id,
				content: post.content?.trim(),
			}
		}
		console.log(data)
		setPostLoader(true)
		await postMutate({
			variables: {
				inputPost: data,
			},
		})
	}

	const onViewableItemsChanged = useCallback(({ viewableItems }) => {
		// console.log('viewableItems=======>', viewableItems)
		setActiveIndex(viewableItems[0]?.index)
	}, [])

	const viewabilityConfig = useRef({
		minimumViewTime: 0,
		itemVisiblePercentThreshold: '80%',
	}).current

	const viewabilityConfigCallbackPairs = useRef([{ viewabilityConfig, onViewableItemsChanged }])

	useEffect(() => {
		setPost({ ...post, author: user?._id })
	}, [user])

	useEffect(() => {
		refresh()
	}, [isFocused])

	useEffect(() => {
		refresh()
	}, [token])

	useEffect(() => {
		if (onUpdateReport) {
			removePost(report?._id)
			setOnUpdateReport(false)
			setReport('')
		}
	}, [onUpdateReport])

	return (
		<>
			<Layout
				navigation={props.navigation}
				withoutScroll={true}
				containerStyle={styles().ph0}
				headerShown={false}
			>
				<View style={styles().flex}>
					<HeaderHome navigation={props.navigation} />
					<View style={[styles().mt5, styles().flex, { backgroundColor: currentTheme.postBackground }]}>
						{user?.role === 'master' && user?.disposable === 'false' ? <StickyCaptain navigation={props.navigation} /> : null}
						<TestAccount />
						<FlatList
							data={getPosts}
							showsVerticalScrollIndicator={false}
							onEndReachedThreshold={0.5}
							onScroll={e => {
								e.nativeEvent
							}}
							contentContainerStyle={{ flexGrow: 1 }}
							viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
							// onViewableItemsChanged={onViewableItemsChanged}
							viewabilityConfig={viewabilityConfig}
							onEndReached={() => nextPage()}
							refreshControl={
								<RefreshControl
									colors={[currentTheme.themeBackground, currentTheme.black]}
									onRefresh={() => refresh()}
									refreshing={Loading}
								/>
							}
							ListHeaderComponent={
								<View style={[styles().boxpeshadowCart, styles().mb20, { borderBottomRightRadius: 20, borderBottomLeftRadius: 20 }]}>
									<View style={[styles().ph20, styles().pb20]}>
										<View
											style={[
												styles().flexRow,
												styles().flex,
												styles().alignStart,
												styles().justifyBetween,
												styles().pt15,
												styles().mb10,
												styles().btw1,
												{ borderTopColor: currentTheme.c707070 },
											]}
										>
											{user?.photo
												? <TouchableOpacity
														activeOpacity={0.7}
														onPress={() => props.navigation.navigate('Profile')}
														style={[styles().wh40px, styles().overflowH, styles().br100]}
													>
														<Image
															source={{ uri: user?.photo }}
															style={styles().wh100}
															resizeMode="cover"
														/>
													</TouchableOpacity>
												: <View
														style={[
															styles().overflowH,
															styles().justifyCenter,
															styles().alignCenter,
															styles().br50,
															styles().wh40px,
															{
																borderWidth: 1,
																borderColor: currentTheme.themeBackground,
															},
														]}
													>
														<FontAwesome5
															name="user-alt"
															size={16}
															color={currentTheme.themeBackground}
														/>
													</View>}
											<TextField
												keyboardType="default"
												value={post.content}
												multiline={true}
												autoCapitalize="none"
												placeholder={'What are you thinking?'}
												// placeholder={`What are you thinking ${
												//   user?.name ? user?.name : ''
												// }?`}
												style={[styles().overflowH, styles().flex, { borderBottomWidth: 0 }]}
												stylesInput={[
													{
														height: 75,
														textAlignVertical: Platform.OS === 'ios' ? 'top' : 'top',
													},
												]}
												onChangeText={text => handleInputChange(text)}
											/>
										</View>
										<View style={{}}>
											{post?.images
												? <View style={[styles().h100px, styles().w200px, styles().br10]}>
														<TouchableOpacity
															onPress={() => {
																setPost({ ...post, images: '' })
																setEnablePost(true)
															}}
															activeOpacity={0.9}
															style={{
																position: 'absolute',
																zIndex: 10,
																right: -5,
																top: -5,
																backgroundColor: 'white',
																borderRadius: 100,
															}}
														>
															<AntDesign
																name={'closecircle'}
																color={currentTheme.themeBackground}
																size={20}
															/>
														</TouchableOpacity>
														<Image
															style={[styles().wh100, styles().br10]}
															resizeMode="cover"
															source={{ uri: post?.images }}
														/>
													</View>
												: null}
											{post?.videos
												? <View
														style={{
															borderColor: currentTheme.c737373,
															// borderRadius: 10,
															backgroundColor: currentTheme.F3F0E4,
														}}
													>
														<Video
															autoplay={false}
															source={{ uri: post?.videos }}
															style={{
																height: 200,
																width: '100%',
																// borderRadius: 10,
															}}
															paused={true}
															muted={true}
														/>
														<TouchableOpacity
															onPress={() => {
																setPost({ ...post, videos: '' })
																setEnablePost(true)
															}}
															activeOpacity={0.9}
															style={{
																position: 'absolute',
																zIndex: 10,
																right: -7,
																top: -10,
																backgroundColor: 'white',
																borderRadius: 100,
															}}
														>
															<AntDesign
																name={'closecircle'}
																color={currentTheme.themeBackground}
																size={20}
															/>
														</TouchableOpacity>
													</View>
												: null}
										</View>
										<View style={[styles().ph20, styles().flexRow, styles().alignCenter, styles().mt15]}>
											<TouchableOpacity
												activeOpacity={0.7}
												onPress={() => setIspostPicker({ ...ispostPicker, image: true })}
												style={[
													styles().ph15,
													styles().br10,
													styles().flexRow,
													styles().alignCenter,
													styles().justifyCenter,
													styles().h35px,
													styles().mr10,
													{ backgroundColor: currentTheme.EEE8D5 },
												]}
											>
												<FontAwesome
													name="camera"
													size={14}
													color={currentTheme.themeBackground}
												/>
												<Text style={[styles().fs12, styles().ml10, styles().fontRegular, { color: currentTheme.c444D6E }]}>Photos</Text>
											</TouchableOpacity>
											<TouchableOpacity
												activeOpacity={0.7}
												onPress={() => setIspostPicker({ ...ispostPicker, video: true })}
												style={[
													styles().ph15,
													styles().br10,
													styles().flexRow,
													styles().alignCenter,
													styles().justifyCenter,
													styles().h35px,
													styles().mr10,
													{ backgroundColor: currentTheme.EEE8D5 },
												]}
											>
												<FontAwesome
													name="video-camera"
													size={15}
													color={currentTheme.themeBackground}
												/>
												<Text style={[styles().fs12, styles().ml10, styles().fontRegular, { color: currentTheme.c444D6E }]}>Video</Text>
											</TouchableOpacity>
											{postLoader
												? <Spinner size={25} />
												: <TouchableOpacity
														disabled={enablePost}
														onPress={() => Post()}
														activeOpacity={0.7}
														style={[
															styles().w80px,
															styles().br10,
															styles().alignCenter,
															styles().justifyCenter,
															styles().h35px,
															{
																backgroundColor: enablePost ? currentTheme.CFCFCF : currentTheme.headingColor,
															},
														]}
													>
														<Text style={[styles().fs12, styles().fontMedium, { color: currentTheme.white }]}>Post</Text>
													</TouchableOpacity>}
										</View>
									</View>
								</View>
							}
							renderItem={({ item, index }) => {
								return (
									<View style={[styles().ph15]}>
										<PostComponent
											activeIndex={activeIndex}
											isFocused={isFocused}
											navigation={props.navigation}
											item={item}
											index={index}
											update={(updateType, post_id) => {
												if (updateType === 'deletePost') {
													removePost(post_id)
												}
												if (updateType === 'postLike') {
													likePost(post_id)
												}
											}}
										/>
									</View>
								)
							}}
							ListEmptyComponent={() => {
								return (
									<View style={[styles().flex]}>
										{Loading || loading
											? Array.from({ length: 2 }).map(item => {
													return <PostSkeleton data={item} />
												})
											: <View style={[styles().ph20, styles().flex, { alignItems: 'center', justifyContent: 'center' }]}>
													<View style={[styles().h150px, { width: '50%' }]}>
														<Image
															source={require('../../assets/images/no-posts.png')}
															resizeMode="contain"
															style={[styles().wh100]}
														/>
													</View>
													<Text style={[styles().fontBold, styles().mt10, { fontSize: 20, color: currentTheme.black }]}>No Posts Yet!</Text>
													<Text style={[styles().fontRegular, styles().w80, styles().textCenter, { fontSize: 14, color: currentTheme.black }]}>
														Nothing to show.
													</Text>
													<ThemeButton
														Title={'Find Friends'}
														Style={[styles().w80]}
														onPress={() => {
															props.navigation.navigate('HomeSearch')
														}}
													/>
												</View>}
									</View>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>
				</View>
			</Layout>
			<ImageAndDocumentPicker
				isImagePicker={ispostPicker.image}
				setIsImagePicker={() => {
					setIspostPicker({ ...ispostPicker, image: false })
				}}
				setImage={data => {
					setPost({ ...post, images: data, videos: '' })
					setIspostPicker({ ...ispostPicker, image: false })
					setEnablePost(false)
				}}
				progress={percentage => console.log('in home percentage 2:', percentage)}
			/>
			<ImageAndDocumentPicker
				isImagePicker={ispostPicker.video}
				setIsImagePicker={() => {
					setIspostPicker({ ...ispostPicker, video: false })
				}}
				setImage={data => {
					setPost({ ...post, videos: data, images: '' })
					setIspostPicker({ ...ispostPicker, video: false })
					setEnablePost(false)
				}}
				isVideo={true}
				progress={percentage => console.log('video upload percentage :', percentage)}
			/>
		</>
	)
}

export default React.memo(Home)
