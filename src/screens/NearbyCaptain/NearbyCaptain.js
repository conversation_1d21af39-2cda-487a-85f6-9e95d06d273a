import { Text, View, TouchableOpacity, Platform, Image, UIManager, Dimensions, RefreshControl } from 'react-native'
import React, { useContext, useState, useRef, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Ionicons from '@expo/vector-icons/Ionicons'
import Layout from '../../component/Layout/Layout'
import MapView, { PROVIDER_GOOGLE, PROVIDER_DEFAULT, Marker } from 'react-native-maps'
import { Modalize } from 'react-native-modalize'
import GeolocationService from 'react-native-geolocation-service'
import { useIsFocused } from '@react-navigation/native'
import UserContext from '../../context/User/User'
import { NearMeCaptian, createChatRoom } from '../../apollo/server'
import { useMutation, useQuery } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import Lottie from 'lottie-react-native'
import fontStyles from '../../utils/fonts/fontStyles'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const { width, height } = Dimensions.get('window')
const LATITUDE_DELTA = 0.04
const LONGITUDE_DELTA = LATITUDE_DELTA * (width / height)

const NearbyCaptain = props => {
	const themeContext = useContext(ThemeContext)
	const user = useContext(UserContext)
	const [myLocation, setMyLocation] = useState('')
	const currentTheme = theme[themeContext.ThemeValue]
	const [selected, setSelected] = useState(false)
	const [nearCaptains, setNearCaptains] = useState([])
	const [chatLoader, setChatLoader] = useState(false)
	const [selectedIndex, setSelectedIndex] = useState('')
	const modalizeRef = useRef(null)
	const _isFocus = useIsFocused()
	const pageSize = 20

	const _onOpen = () => {
		modalizeRef.current?.open()
	}

	const _onClose = () => {
		modalizeRef.current?.close()
	}

	const { data, loading, error, refetch } = useQuery(NearMeCaptian, {
		errorPolicy: 'all',
		fetchPolicy: 'cache-and-network',
		variables: {
			latlng: `${String(myLocation?.latitude)},${String(myLocation?.longitude)}`,
			maxDistance: '2000000', //miles
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: async data => {
			console.log('NearMeCaptian res :', JSON.stringify(data?.nearMeCaptian))
			const myArray = data?.nearMeCaptian?.results?.filter(obj => obj?._id !== user?._id)
			setNearCaptains(myArray)
		},
		onError: async err => {
			console.log('NearMeCaptian err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
		},
	})

	const refresh = async () => {
		setSelected('')
		setNearCaptains([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			const myArray = res?.data?.nearMeCaptian?.results?.filter(obj => obj?._id !== user?._id)
			setNearCaptains(myArray)
			console.log('refreshed nearby')
		})
	}

	const [createChatroomMutate, {}] = useMutation(createChatRoom, {
		errorPolicy: 'all',
		onCompleted: async ({ createChatRoom }) => {
			console.log('createChatRoom res :', createChatRoom)
			const filterUser = await createChatRoom?.users?.find(users => {
				return users?._id !== user?._id
			})
			props.navigation.navigate('Chat', {
				item: createChatRoom,
				chatUser: filterUser,
				roomId: createChatRoom?._id,
				isAnonymousChat: true,
			})
			setChatLoader(false)
		},
		onError: err => {
			console.log('createChatRoom err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setChatLoader(false)
		},
	})

	async function Chat(captainID, index) {
		setSelectedIndex(index)
		setChatLoader(true)
		const data = {
			inputRoom: {
				users: [user?._id, captainID],
				isAnonymousChat: true,
			},
		}
		console.log(data)
		await createChatroomMutate({
			variables: data,
		})
	}

	const handleMarkerPress = marker => {
		console.log(marker)
		const selectedItemIndex = nearCaptains.findIndex(item => item?._id === marker?._id)
		if (selectedItemIndex !== -1) {
			const selectedItem = nearCaptains[selectedItemIndex]
			const updatedNearbyList = [selectedItem, ...nearCaptains.slice(0, selectedItemIndex), ...nearCaptains.slice(selectedItemIndex + 1)]
			setNearCaptains(updatedNearbyList)
			setSelected(marker)
		}
		// console.log(marker?.nativeEvent?.coordinate);

		// LayoutAnimation.configureNext(LayoutAnimation.Presets.spring)
	}

	console.log(`${String(myLocation?.latitude)},${String(myLocation?.longitude)}`)

	async function geoLocation() {
		GeolocationService.getCurrentPosition(
			async position => {
				const { latitude, longitude } = position.coords
				setMyLocation(position.coords)
			},
			_error => {
				// See error code charts below.
			},
			{ enableHighAccuracy: true, timeout: 20000, maximumAge: 10000 }
		)
	}

	useEffect(() => {
		geoLocation()
	}, [])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			refreshControl={
				<RefreshControl
					colors={[currentTheme.themeBackground, currentTheme.black]}
					onRefresh={() => refresh()}
					refreshing={loading}
				/>
			}
			pagetitle={'Nearby Captains'}
			ContentArea={[styles().ph0]}
		>
			<View style={[styles().flex]}>
				<View style={[styles().flex]}>
					{myLocation?.latitude && myLocation?.longitude
						? <MapView
								toolbarEnabled={false}
								provider={Platform.OS === 'ios' ? PROVIDER_DEFAULT : PROVIDER_GOOGLE}
								style={[{ height: height / 2 }, styles().w100]}
								region={{
									latitude: myLocation?.latitude,
									longitude: myLocation?.longitude,
									longitudeDelta: LONGITUDE_DELTA,
									latitudeDelta: LATITUDE_DELTA,
								}}
								//   zoomControlEnabled = {true}
								initialRegion={{
									latitude: myLocation?.latitude,
									longitude: myLocation?.longitude,
									longitudeDelta: LONGITUDE_DELTA,
									latitudeDelta: LATITUDE_DELTA,
								}}
							>
								<Marker
									coordinate={{
										latitude: Number.parseFloat(myLocation?.latitude),
										longitude: Number.parseFloat(myLocation?.longitude),
									}}
								>
									<Image
										style={{ height: 30, width: 30, borderRadius: 100 }}
										source={{ uri: user?.photo }}
										resizeMode="cover"
									/>
								</Marker>
								{nearCaptains?.map((item, i) => {
									return (
										<Marker
											style={{ zIndex: 100 }}
											key={i}
											onPress={_captain => handleMarkerPress(item)}
											coordinate={{
												latitude: Number.parseFloat(item?.loc?.coordinates[1]),
												longitude: Number.parseFloat(item?.loc?.coordinates[0]),
											}}
										>
											<View
												style={[
													styles().wh35px,
													styles().mr15,
													styles().br50,
													styles().overflowH,
													styles().alignCenter,
													styles().justifyCenter,
													{
														borderWidth: 0.5,
														borderColor: currentTheme.themeBackground,
														backgroundColor: currentTheme.white,
													},
												]}
											>
												<Image
													source={require('../../assets/images/home-logo.png')}
													style={{ height: 25, width: 25 }}
													resizeMode="contain"
												/>
											</View>
										</Marker>
									)
								})}
							</MapView>
						: null}
				</View>
				<Modalize
					ref={modalizeRef}
					handlePosition="inside"
					modalTopOffset={Platform.OS === 'android' ? 100 : 130}
					alwaysOpen={height * 0.45}
					modalStyle={[styles().boxpeshadow, { paddingTop: 30, paddingHorizontal: 10 }]}
					scrollViewProps={{
						showsVerticalScrollIndicator: false,
						// stickyHeaderIndices: [0]
					}}
				>
					{nearCaptains?.length === 0
						? <View
								style={{
									width: '100%',
									alignItems: 'center',
									justifyContent: 'center',
									marginTop: 35,
								}}
							>
								<Text
									style={{
										fontSize: 14,
										color: currentTheme.c737373,
										fontFamily: fontStyles.PoppinsRegular,
									}}
								>
									{loading ? 'Searching for captains...' : 'No captain found!'}
								</Text>
							</View>
						: nearCaptains?.map((item, index) => {
								return (
									<View
										key={index}
										style={[
											styles().flexRow,
											styles().flex,
											styles().mb15,
											styles().alignCenter,
											styles().pv10,
											styles().ph20,
											selected?._id === item._id && styles().boxpeshadowCart,
											styles().mall5,
											styles().br10,
											{
												borderColor: selected?._id === item._id ? currentTheme.themeBackground : currentTheme.C3C3C3,
												borderWidth: selected?._id === item._id ? 1.5 : 1,
											},
										]}
									>
										<View
											style={[
												styles().wh50px,
												styles().mr15,
												styles().br50,
												styles().overflowH,
												styles().alignCenter,
												styles().justifyCenter,
												{
													// borderWidth: item?.photo ? 0 : 1,
													borderWidth: 1,
													borderColor: currentTheme.themeBackground,
												},
											]}
										>
											{/* {item?.photo ? (
                      <Image
                        source={{uri: item?.photo}}
                        style={styles().wh100}
                        resizeMode="cover"
                      />
                    ) : ( */}
											<Image
												source={require('../../assets/images/home-logo.png')}
												style={{ height: 35, width: 35 }}
												resizeMode="contain"
											/>
											{/* )} */}
										</View>
										<View style={[styles().flex]}>
											<Text style={[styles().fs16, styles().fw600, { color: currentTheme.headingColor }]}>
												{/* {item.name} */}
												Nearby Captain
											</Text>
											<View style={[styles().flexRow, styles().mv5, styles().alignCenter]}>
												<Ionicons
													name="paper-plane-sharp"
													size={13}
													color={currentTheme.C3C3C3}
												/>
												<Text style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.C3C3C3 }]}>
													{`${Number.parseFloat(item?.distance)?.toFixed(2)} Miles Away`}
												</Text>
											</View>
											<View style={[styles().flexRow, styles().alignStart]}>
												<Ionicons
													name="md-briefcase"
													size={13}
													color={currentTheme.C3C3C3}
												/>
												<Text
													numberOfLines={2}
													style={[styles().fs11, styles().ml5, styles().fontRegular, { color: currentTheme.C3C3C3 }]}
												>
													Worked at {item?.companyWorkFor}
												</Text>
											</View>
										</View>
										<View style={[styles().flexRow, styles().alignCenter]}>
											{chatLoader && index === selectedIndex
												? <Lottie
														source={require('./../../assets/other/chat_loader.json')}
														autoPlay={true}
														loop={true}
														style={{ height: 25, width: 25, marginLeft: 5 }}
													/>
												: <TouchableOpacity
														onPress={() => Chat(item?._id, index)}
														activeOpacity={0.7}
														style={[styles().wh35px, styles().br50, styles().pall10, { backgroundColor: currentTheme.themeBackground }, styles().overflowH]}
													>
														<Image
															source={require('../../assets/images/message-icon.png')}
															style={styles().wh100}
															resizeMode="contain"
														/>
													</TouchableOpacity>}
											{/* <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() =>
                        props.navigation.navigate('ViewFriendProfile', {
                          userId: item?._id,
                          isFriend: undefined,
                          isRequested: undefined,
                        })
                      }
                      style={[
                        styles().wh35px,
                        styles().br50,
                        styles().pall10,
                        styles().ml10,
                        {backgroundColor: currentTheme.themeBackground},
                        styles().overflowH,
                      ]}>
                      <Image
                        source={require('../../assets/images/user-icon.png')}
                        style={styles().wh100}
                        resizeMode="contain"
                      />
                    </TouchableOpacity> */}
										</View>
									</View>
								)
							})}
					{/* </ScrollView> */}
				</Modalize>
			</View>
		</Layout>
	)
}

export default React.memo(NearbyCaptain)
