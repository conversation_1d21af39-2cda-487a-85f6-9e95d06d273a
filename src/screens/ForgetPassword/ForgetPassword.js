import { Text, View, Keyboard, TouchableOpacity, ImageBackground, LayoutAnimation } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import AuthLayout from '../../component/AuthLayout/AuthLayout'
import TextField from '../../component/FloatTextField/FloatTextField'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import { useMutation } from '@apollo/client'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { forgotPassword } from '../../apollo/server'
import Spinner from '../../component/Spinner/Spinner'

const ForgetPassword = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const _user_id = props?.route?.params?.user_id
	console.log(props?.route?.params)
	const [email, setEmail] = useState('')
	const [emailError, setEmailError] = useState(false)
	const [Loading, setLoading] = useState(false)

	const [mutate, { client }] = useMutation(forgotPassword, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			console.log('forgotPassword res :', data)
			FlashMessage({
				msg: 'Verification code sent to your email.',
				type: 'success',
			})
			props.navigation.navigate('Verification', {
				email: email.toLowerCase().trim(),
			})
		} catch (e) {
			console.log(e)
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setLoading(false)
		console.log('forgotPassword error  :', error)
	}

	async function CheckEmail() {
		Keyboard.dismiss()
		const emailregex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
		let status = true
		if (email === '') {
			FlashMessage({ msg: 'Enter Email Address', type: 'warning' })
			setEmailError(true)
			status = false
			return
		}
		if (!emailregex.test(email.trim().toLowerCase())) {
			FlashMessage({ msg: 'Invalid Email Address', type: 'warning' })
			setEmailError(true)
			status = false
			return
		}
		if (status) {
			setLoading(true)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
			await mutate({
				variables: {
					email: email.toLowerCase().trim(),
				},
			})
		}
	}

	return (
		<AuthLayout>
			<ImageBackground
				source={require('../../assets/images/login-bg.png')}
				resizeMode="cover"
				style={[styles().h350px, styles().alignCenter, styles().justifyCenter]}
			>
				<TouchableOpacity
					onPress={() => props.navigation.goBack()}
					activeOpacity={0.8}
					style={[
						styles().alignCenter,
						styles().justifyCenter,
						styles().wh35px,
						styles().br25,
						styles().boxpeshadow,
						styles().posAbs,
						styles().top35,
						styles().left20,
						{ backgroundColor: currentTheme.white },
					]}
				>
					<FontAwesome
						name="angle-left"
						size={25}
						color={currentTheme.blackish}
						style={{ marginTop: -3, marginLeft: -5 }}
					/>
				</TouchableOpacity>
				<Text
					style={[
						styles().fs24,
						// styles().fw700,
						styles().textCenter,
						styles().fontBold,
						styles().lh30,
						styles().textUpper,
						{ color: currentTheme.headingColor },
					]}
				>
					Forget Password
				</Text>
			</ImageBackground>
			<View style={[styles().mt20, styles().flex, styles().ph20]}>
				<Text style={[styles().alignSelfCenter, styles().mv20, styles().fs14, styles().fontRegular, { color: currentTheme.black }]}>
					Enter the email address link with your account to reset your password.
				</Text>
				<View style={[styles().mb50]}>
					<TextField
						keyboardType="default"
						value={email}
						errorText={emailError}
						autoCapitalize="none"
						placeholder={'Email'}
						style
						onChangeText={text => {
							setEmailError(false)
							setEmail(text)
						}}
						FieldIcon={
							<FontAwesome
								name="envelope-o"
								size={20}
								color={currentTheme.black}
							/>
						}
					/>
				</View>

				<View>
					{Loading
						? <Spinner />
						: <ThemeButton
								onPress={() => CheckEmail()}
								Title={'Send'}
								Style={{
									backgroundColor: currentTheme.headingColor,
									borderColor: currentTheme.headingColor,
								}}
							/>}
				</View>
			</View>
		</AuthLayout>
	)
}

export default ForgetPassword
