import { Text, View, TouchableOpacity } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import AntDesign from '@expo/vector-icons/AntDesign'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import { useIsFocused } from '@react-navigation/native'

const LearnWaterWays = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [waterways, setWaterways] = useState(null)
	const isFocus = useIsFocused()
	const isLearn = user?.waterways_learning_enabled
	const myLearningRoutes =
		user?.waterways_learning?.length > 0
			? user?.waterways_learning?.map(item => {
					return `${item?.name}.\n`
				})
			: 'No Routes'

	const createParentObjects = async arr => {
		try {
			const otherArr = {
				parentName: 'Other',
				children: [{ name: user?.otherWaterWays }],
			}
			const result = []
			const parentMap = {}
			await arr?.forEach(item => {
				const parentId = item?.parentWaterWay?._id
				if (!parentMap[parentId]) {
					parentMap[parentId] = {
						parentName: item?.parentWaterWay?.name,
						children: [],
					}
				}
				parentMap[parentId].children.push({
					name: item?.name,
					_id: item?._id,
				})
			})

			for (const parentId in parentMap) {
				result?.push(parentMap[parentId])
			}
			if (user?.otherWaterWays) {
				result?.push(otherArr)
			}
			// console.log('=========>', JSON.stringify(result));

			setWaterways(result)
		} catch (e) {
			console.log('catch waterways filter :', e)
		}
	}

	useEffect(() => {
		createParentObjects(user?.waterWays)
	}, [isFocus])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Waterways'}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<View style={[styles().ph20, styles().flex]}>
				<TouchableOpacity
					onPress={() => props.navigation.navigate('LearnWaterWaysDetail')}
					activeOpacity={0.8}
					style={[styles().mv20, styles().pall15, styles().br10, styles().bw1, { borderColor: currentTheme.B7B7B7 }]}
				>
					<View style={[styles().flexRow, styles().alignCenter, styles().justifyBetween, styles().mb10]}>
						<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.black }]}>Learn New Waterways</Text>
						<AntDesign
							name={'arrowright'}
							color={currentTheme.black}
							size={20}
						/>
					</View>

					<View style={[styles().flexRow, styles().alignCenter, styles().justifyStart, styles().mb10]}>
						<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.c737373 }]}>Status:</Text>

						<View
							style={[
								styles().br100,

								styles().flexRow,
								styles().mh5,
								styles().alignCenter,
								{
									paddingHorizontal: 10,
									paddingVertical: 7,
									backgroundColor: isLearn ? currentTheme.backgroundGreen : currentTheme.lightBlue,
								},
							]}
						>
							<FontAwesome5
								name={'graduation-cap'}
								size={16}
								color={isLearn ? currentTheme.lightGreen : currentTheme.darkBlue}
							/>
							<Text
								style={[
									styles().fs10,
									styles().fontMedium,
									styles().ml5,
									{
										color: isLearn ? currentTheme.lightGreen : currentTheme.darkBlue,
									},
								]}
							>
								{isLearn ? 'Activated' : 'Not Active'}
							</Text>
						</View>
					</View>
					{isLearn
						? <>
								<Text style={[styles().fs14, styles().fontMedium, styles().mb10, { color: currentTheme.black }]}>The waterways you need to explore are:</Text>
								<Text
									style={[
										styles().fs12,
										styles().fontRegular,
										{
											textDecorationLine: 'underline',
											color: currentTheme.themeBackground,
										},
									]}
								>
									{myLearningRoutes}
								</Text>
							</>
						: <Text style={[styles().fs12, styles().fontRegular, styles().w80, { color: currentTheme.c737373 }]}>
								Gain the knowledge needed to navigate rivers safely and efficiently.
							</Text>}
				</TouchableOpacity>

				<View style={[styles().flexRow, styles().mb20, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs18, styles().fontBold, { color: currentTheme.black }]}>Routes on Waterways</Text>
					<TouchableOpacity
						onPress={() => props.navigation.navigate('EditRoutes')}
						activeOpacity={0.5}
					>
						<Text style={[styles().fs16, styles().fontRegular, { color: currentTheme.c737373 }]}>Edit</Text>
					</TouchableOpacity>
				</View>
				{waterways?.map((ways, i) => {
					return (
						<View
							key={i}
							style={[styles().sectionContent, styles().mb15]}
						>
							<Text style={[styles().fs14, styles().fontMedium, styles().mb5, { color: currentTheme.themeBackground }]}>
								{/* {`${ways?.children?.length}) ${ways.parentName}`} */}
								{`${i + 1}) ${ways?.parentName}`}
							</Text>
							<View style={[styles().pl15]}>
								{ways?.children?.map((child, j) => {
									return (
										<View
											key={j}
											style={[styles().flexRow, styles().alignCenter]}
										>
											<View
												style={[
													styles().br5,
													styles().mr5,
													styles().wh5px,
													{
														backgroundColor: currentTheme.themeBackground,
													},
												]}
											/>
											<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.headingColor }]}>{child?.name ? child?.name : 'N/A'}</Text>
										</View>
									)
								})}
							</View>
						</View>
					)
				})}
			</View>
		</Layout>
	)
}

export default LearnWaterWays
