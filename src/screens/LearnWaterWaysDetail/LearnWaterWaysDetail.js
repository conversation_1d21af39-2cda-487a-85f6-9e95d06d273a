import { Text, View, TouchableOpacity, FlatList, Switch, LayoutAnimation } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import Layout from '../../component/Layout/Layout'
import UserContext from '../../context/User/User'
import { switchSize } from '../../utils/Constants'
import { useMutation, useQuery } from '@apollo/client'
import { ToggleWaterWaysLearning, waterWays } from '../../apollo/server'
import ThemeButton from '../../component/ThemeButton/ThemeButton'
import Spinner from '../../component/Spinner/Spinner'
import FlashMessage from '../../component/FlashMessage/FlashMessage'

const LearnWaterWaysDetail = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const { refetch } = useContext(UserContext)
	const [isLearn, setIsLearn] = useState(true)
	const [show, setShow] = useState(false)
	const [Index, setIndex] = useState('')
	const [buttonLoader, setButtonLoader] = useState(false)
	const [selectedRoutes, setSelectedRoutes] = useState([])

	const { data, loading, error } = useQuery(waterWays, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			options: {
				limit: 1000,
			},
		},
		onCompleted: _data => {
			//   console.log('waterways res :', data.WaterWays);
		},
		onError: err => {
			console.log('waterways Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [mutate, { client }] = useMutation(ToggleWaterWaysLearning, {
		errorPolicy: 'all',
		onCompleted,
		onError,
	})

	async function onCompleted(data) {
		try {
			setButtonLoader(false)
			console.log('ToggleWaterWaysLearning res :', data)
			FlashMessage({
				msg: 'Routes Updated.',
				type: 'success',
			})
			props.navigation.goBack()
			await refetch()
		} catch (e) {
			console.log('ToggleWaterWaysLearning catch:', e)
			setButtonLoader(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		} finally {
			setButtonLoader(false)
		}
	}

	function onError(error) {
		FlashMessage({ msg: error.message?.toString(), type: 'danger' })
		setButtonLoader(false)
		console.log('ToggleWaterWaysLearning error  :', error)
	}

	async function UpdateRoutes() {
		let status = true
		if (isLearn) {
			if (selectedRoutes?.length === 0) {
				FlashMessage({ msg: 'Select Routes', type: 'danger' })
				status = false
				return
			}
		}
		if (selectedRoutes?.length > 3) {
			FlashMessage({ msg: 'Select maximum 3 routes.', type: 'warning' })
			status = false
			return
		}
		if (status) {
			const data = {
				waterWayIds: selectedRoutes,
				isEnabled: isLearn,
			}
			console.log('data :', data)
			setButtonLoader(true)
			await mutate({
				variables: data,
			})
		}
	}

	const multiSelect = item => {
		const x = selectedRoutes.indexOf(item._id)
		if (x === -1) {
			setSelectedRoutes(prev => [...prev, item?._id])
		} else {
			selectedRoutes.splice(x, 1)
			setSelectedRoutes(prev => [...prev])
		}
	}

	const routes = arr => {
		const prevRoutes = []
		arr?.forEach(item => {
			prevRoutes.push(item._id)
		})
		setSelectedRoutes(prev => [...prev, ...prevRoutes])
	}

	useEffect(() => {
		routes(user?.waterways_learning)
		setIsLearn(user?.waterways_learning_enabled)
	}, [user?.waterways_learning])

	return (
		<Layout
			LeftIcon={true}
			headerShown={true}
			pagetitle={'Learn Waterways'}
			// ContentArea={styles().ph20}
			navigation={props.navigation}
			keyBoardArea={55}
		>
			<View style={[styles().ph20, styles().flex]}>
				<View style={[styles().flexRow, styles().mb10, styles().mt20, styles().alignCenter, styles().justifyBetween]}>
					<Text style={[styles().fs18, styles().fontBold, { color: currentTheme.themeBackground }]}>Learn New WaterWays</Text>
					<Switch
						style={{ transform: switchSize }}
						trackColor={{
							false: currentTheme.E2E2E2,
							true: currentTheme.E2E2E2,
						}}
						thumbColor={isLearn ? currentTheme.lightGreen : currentTheme.themeBackground}
						ios_backgroundColor={currentTheme.F4F5F6}
						onValueChange={boolean => setIsLearn(boolean)}
						value={isLearn}
					/>
				</View>
				<Text style={[styles().fs14, styles().fontRegular, { color: currentTheme.black }]}>
					Gain the knowledge needed to navigate rivers safely and efficiently.
				</Text>
				<Text style={[styles().fs16, styles().mt20, styles().fontMedium, { color: currentTheme.black }]}>What waterways would you like to learn about?</Text>
				<Text style={[styles().fs14, styles().mt5, styles().fontRegular, { color: currentTheme.c737373 }]}>Select atleast 1 or maximum 3 waterways.</Text>
				<FlatList
					data={data?.WaterWays?.results}
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ flexGrow: 1 }}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={[
										styles().fontRegular,
										{
											color: currentTheme.E8E8C8,
											fontSize: 16,
										},
									]}
								>
									{loading ? 'Loading...' : 'No Routes'}
								</Text>
							</View>
						)
					}}
					renderItem={({ item, index }) => {
						return (
							<>
								<TouchableOpacity
									activeOpacity={0.5}
									onPress={() => {
										setShow(!show)
										setIndex(index)
										LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
									}}
									style={[
										styles().flexRow,
										styles().mt25,
										// styles().pb10,
										styles().alignCenter,
										styles().justifyBetween,
										styles().br10,
										styles().zIndex1,
										styles().pall15,
										{
											borderColor: currentTheme.B7B7B7,
											borderWidth: 1,
										},
									]}
								>
									<Text
										numberOfLines={1}
										style={[styles().fs14, styles().fontRegular, { color: currentTheme.headingColor }]}
									>
										{item.name}
									</Text>
									<MaterialIcons
										name={show && Index === index ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
										size={20}
										color={currentTheme.blackish}
									/>
								</TouchableOpacity>
								{show && Index === index
									? <View
											style={[
												styles().ph20,
												{
													marginHorizontal: 1,
													borderBottomWidth: 1,
													borderLeftWidth: 1,
													borderRightWidth: 1,
													borderColor: currentTheme.B7B7B7,
													borderBottomLeftRadius: 10,
													borderBottomRightRadius: 10,
													bottom: 5,
												},
											]}
										>
											<FlatList
												data={item.subWaterWays}
												showsVerticalScrollIndicator={false}
												contentContainerStyle={{ flexGrow: 1 }}
												renderItem={({ item, index }) => {
													return (
														<TouchableOpacity
															activeOpacity={0.5}
															style={[
																styles().flexRow,
																styles().pb10,
																styles().pt15,
																styles().alignCenter,
																styles().justifyBetween,
																{
																	borderTopWidth: index === 0 ? 0 : 1,
																	borderTopColor: currentTheme.B7B7B7,
																	backgroundColor: currentTheme.white,
																},
															]}
															onPress={() => multiSelect(item)}
														>
															<View style={[styles().flexRow, styles().alignCenter]}>
																<View
																	style={[
																		styles().wh20px,
																		styles().alignCenter,
																		styles().justifyCenter,
																		styles().mr10,
																		styles().bw1,
																		styles().br5,
																		{
																			backgroundColor: selectedRoutes.includes(item._id) ? currentTheme.themeBackground : currentTheme.white,
																			borderColor: currentTheme.themeBackground,
																		},
																	]}
																>
																	{selectedRoutes.includes(item._id) && (
																		<FontAwesome
																			name="check"
																			size={16}
																			color={currentTheme.white}
																		/>
																	)}
																</View>
																<Text
																	numberOfLines={2}
																	style={[styles().fs12, styles().fontRegular, styles().flex, { color: currentTheme.headingColor }]}
																>
																	{item.name}
																</Text>
															</View>
														</TouchableOpacity>
													)
												}}
												keyExtractor={(_item, index) => index.toString()}
												ListFooterComponent={<View style={styles().wh20px} />}
											/>
										</View>
									: null}
							</>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
				/>
			</View>
			<View style={[styles().justifyEnd, styles().mv20, styles().ph20]}>
				{buttonLoader
					? <Spinner />
					: <ThemeButton
							Title={'Save'}
							onPress={() => UpdateRoutes()}
						/>}
			</View>
		</Layout>
	)
}

export default LearnWaterWaysDetail
