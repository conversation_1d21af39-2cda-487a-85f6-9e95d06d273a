import { Text, View, TouchableOpacity, LayoutAnimation, Platform, Image, UIManager, FlatList, RefreshControl } from 'react-native'
import { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import { Licensings } from '../../apollo/server'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'
import LicensingPopup from '../../component/Modals/LicensingPopup'
import UserContext from '../../context/User/User'
import ThemeButton from '../../component/ThemeButton/ThemeButton'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const Licensing = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const user = useContext(UserContext)
	const [licences, setLicences] = useState([])
	const [Loading, setLoading] = useState(false)
	const [page, setPage] = useState(1)
	const [_isLicensingReq, setIsLicensingReq] = useState(false)
	const [isLicensingPopup, setIsLicensingPopup] = useState(false)
	const pageSize = 40

	const { data, loading, error, refetch } = useQuery(Licensings, {
		fetchPolicy: 'no-cache',
		variables: {
			// filters: null,
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		},
		onCompleted: data => {
			// console.log('Licensings res :', JSON.stringify(data?.Licensings));
			setLicences(prev => [...prev, ...data?.Licensings?.results])
			setLoading(false)
			LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
		},
		onError: err => {
			console.log('Licensings err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			setLoading(false)
		},
	})

	const refresh = async () => {
		setLoading(true)
		setLicences([])
		await refetch({
			options: {
				page: 1,
				limit: pageSize,
				sortBy: 'createdAt:desc',
			},
		}).then(res => {
			setLicences(res.data?.Licensings?.results)
			setLoading(false)
			setPage(1)
			console.log('refreshed!')
		})
	}

	const nextPage = async () => {
		if (page < data?.Licensings?.totalPages) {
			setLoading(true)
			setPage(old => old + 1)
			await refetch()
		}
	}

	useEffect(() => {
		setLoading(loading)
		setIsLicensingReq(user?.licenceingReq)
	}, [loading])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			pagetitle={'Licensing Guidelines'}
			ContentArea={[styles().ph0]}
		>
			<View style={[styles().flex, styles().ph20]}>
				<FlatList
					data={licences}
					showsVerticalScrollIndicator={false}
					contentContainerStyle={{ padding: 3, flexGrow: 1 }}
					onEndReachedThreshold={0.75}
					refreshControl={
						<RefreshControl
							colors={[currentTheme.themeBackground, currentTheme.black]}
							onRefresh={() => refresh()}
							refreshing={Loading}
						/>
					}
					onEndReached={() => nextPage()}
					ListFooterComponent={<View style={[styles().h30px]} />}
					ListEmptyComponent={() => {
						return (
							<View style={[styles().alignCenter, styles().justifyCenter, styles().flex]}>
								<Text
									style={{
										color: currentTheme.E8E8C8,
										fontSize: 14,
									}}
								>
									{Loading ? 'Loading...' : 'No Licensing guidline found'}
								</Text>
							</View>
						)
					}}
					renderItem={({ item, index }) => {
						return (
							<TouchableOpacity
								key={index}
								activeOpacity={0.7}
								onPress={() =>
									props.navigation.navigate('LicenseQuestAns', {
										parentItem: item,
									})
								}
								style={[
									styles().flexRow,
									styles().mb10,
									styles().pv15,
									styles().ph15,
									styles().bw1,
									styles().flex,
									styles().br10,
									{ borderColor: currentTheme.CFCFCF },
									styles().alignCenter,
								]}
							>
								<View
									style={[
										styles().wh45px,
										styles().br50,
										styles().mr10,
										styles().overflowH,
										styles().alignCenter,
										styles().justifyCenter,
										{
											borderWidth: item?.addedBy?.photo ? 0 : 1,
											borderColor: currentTheme.themeBackground,
										},
									]}
								>
									{item?.addedBy?.photo
										? <Image
												source={{ uri: item?.addedBy?.photo }}
												style={styles().wh100}
												resizeMode="cover"
											/>
										: <FontAwesome5
												name="user-alt"
												size={20}
												color={currentTheme.themeBackground}
											/>}
								</View>
								<Text style={[styles().flex, styles().fs13, styles().fontMedium, { color: currentTheme.headingColor }]}>
									{item?.title ? item?.title : 'N/A'}
								</Text>
							</TouchableOpacity>
						)
					}}
					keyExtractor={(_item, index) => index.toString()}
				/>
			</View>
			<View
				style={[
					styles().alignCenter,
					styles().justifyCenter,
					styles().pt10,
					styles().pb20,
					styles().ph30,
					{
						borderTopRightRadius: 30,
						borderTopLeftRadius: 30,
						backgroundColor: currentTheme.F8F9FA,
					},
				]}
			>
				<ThemeButton
					onPress={() => props.navigation.navigate('Appointments')}
					Style={[
						styles().w100,
						{
							backgroundColor: currentTheme.black,
							borderColor: currentTheme.black,
						},
					]}
					StyleText={{ color: currentTheme.white }}
					Title={'View Appointment History'}
				/>
				{/* <TouchableOpacity
            disabled={!isLicensingReq ? false : true}
            onPress={() => setIsLicensingPopup(true)}
            style={[styles().mt15, styles().w100]}
            activeOpacity={0.7}>
            <LinearGradient
              colors={
                !isLicensingReq
                  ? ['#B3A36E', '#AA9750', '#A4903C']
                  : ['#B7B7B7', '#737373', '#737373']
              }
              style={[
                styles().h70px,
                styles().alignCenter,
                styles().flexRow,
                styles().justifyCenter,
                styles().br10,
                styles().pv15,
                styles().ph25,
                styles().mb5,
              ]}>
              <View
                style={[styles().wh35px, styles().mr15, styles().overflowH]}>
                <Image
                  source={require('../../assets/images/consultancy-img.png')}
                  style={styles().wh100}
                  resizeMode="contain"
                />
              </View>
              <Text
                style={[
                  styles().fs18,
                  styles().fw600,
                  {color: currentTheme.white},
                ]}>
                Need Help About Licensing?
              </Text>
            </LinearGradient>
          </TouchableOpacity> */}
			</View>
			<LicensingPopup
				callBack={boolean => setIsLicensingReq(boolean)}
				visible={isLicensingPopup}
				onClose={() => setIsLicensingPopup(false)}
			/>
		</Layout>
	)
}

export default Licensing
