import { Text, View, TouchableOpacity, Image, FlatList, Dimensions, Animated } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons'
import Ionicons from '@expo/vector-icons/Ionicons'
import AntDesign from '@expo/vector-icons/AntDesign'
import FontAwesome5 from '@expo/vector-icons/FontAwesome5'
import FontAwesome from '@expo/vector-icons/FontAwesome'
import Layout from '../../component/Layout/Layout'
import { CommonActions } from '@react-navigation/native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import FriendsComponent from '../../component/Friends/Friends'
import { AuthContext } from '../../context/Auth/auth'
import HeaderMenu from '../../component/Header/HeaderMenu'
import { createStripeAccount, getChatRooms, updateUser } from '../../apollo/server'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useMutation, useQuery } from '@apollo/client'
import { MessageContext } from '../../context/Notification/Notification'
import { useThrottledPress } from '../../utils/Constants'
import { openLink } from '../../utils/InAppBrowser'
import Eligible from '../../context/EligibleContext/EligibleContext'
import UserContext from '../../context/User/User'
import TestAccount from '../../component/TestAccount/TestAccount'
import fontStyles from '../../utils/fonts/fontStyles'
import Loading from '../../context/Loading/Loading'
import LinearGradient from 'react-native-linear-gradient'

const { width } = Dimensions.get('window')

const Menu = props => {
	const themeContext = useContext(ThemeContext)
	const { logout } = useContext(AuthContext)
	const user = useContext(UserContext)
	const { isLoader } = useContext(Loading)
	// const [user, setUser] = useState('');
	const [asyncUser, setAsyncUser] = useState('')
	const { setCheckrEligible, setStripeEligible } = useContext(Eligible)
	const { newMessage, isNewMessage } = useContext(MessageContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [Chats, setChats] = useState([])
	const [_recentChatLoading, setRecentChatLoading] = useState(false)
	const [page, setPage] = useState(1)
	const pageSize = 6
	const isLearn = user?.waterways_learning_enabled
	const [opacity] = useState(new Animated.Value(1))
	const _isStripe = user?.payouts_enabled && user?.transfer_enabled
	const _checkr = user?.checkr_decision !== 'ENGAGE'
	// console.log('checkr :', checkr, user?.checkr_decision);
	// console.log('checkr_status', user?.checkr_status); //status will be "ENGAGE" or "DISENGAGE"
	// console.log('checkr_decision', user?.checkr_decision);

	const isRBPVerification =
		user?.checkr_decision === 'PENDING'
			? false
			: user?.checkr_decision === 'COMPLETED' || user?.checkr_status === 'ENGAGE'
				? true
				: user?.checkr_decision === 'NEED_REVIEW'
					? false
					: false

	const isRBPVerificationText =
		user?.checkr_decision === 'PENDING'
			? 'Verification Under Process'
			: user?.checkr_decision === 'COMPLETED'
				? 'RBP Verified'
				: user?.checkr_decision === 'NEED_REVIEW'
					? 'Review Your Application'
					: 'RBP Verification'

	const { data, loading, error, refetch } = useQuery(getChatRooms, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filter: {
				// updatedAt: 'updatedAt:desc',
			},
			options: {
				page: page,
				limit: pageSize,
				sortBy: 'lastMessageAt:desc',
			},
		},
		onCompleted: async res => {
			setChats(res?.getChatRooms?.results)
			setRecentChatLoading(false)
		},
		onError: err => {
			setRecentChatLoading(false)
			// ErrorHandler(err.message);
			console.log('getChatRooms err :', err)
			// FlashMessage({msg: err.message, type: 'danger'});
			// ErrorHandler(err.message, props);
		},
	})

	const [_mutate, { client }] = useMutation(createStripeAccount, {
		errorPolicy: 'all',
		onCompleted: ({ createStripeAccount }) => {
			console.log('createStripeAccount res :', createStripeAccount)
			openLink(createStripeAccount?.url)
		},
		onError: err => {
			console.log('createStripeAccount Err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
		},
	})

	const [mutateUpdateUser] = useMutation(updateUser, {
		errorPolicy: 'all',
		onCompleted: async data => {
			console.log('updateUser res:', data)
			Logout()
			isLoader(false)
		},
		onError: err => {
			console.log('updateUser Err:', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			Logout()
			isLoader(false)
		},
	})

	async function _onLogout() {
		isLoader(true)
		await mutateUpdateUser({
			variables: { updateUserInput: { notificationToken: '' } },
		})
	}

	async function Logout() {
		try {
			// appLeave(user?._id);
			await AsyncStorage.removeItem('token')
			await AsyncStorage.removeItem('user')
			await AsyncStorage.removeItem('isProfileCompleted')
			await AsyncStorage.removeItem('userId')
			logout()
			console.log('async clear - logout!'),
				props.navigation.dispatch(
					CommonActions.reset({
						index: 0,
						routes: [{ name: 'Auth' }],
					})
				)
		} catch (error) {
			console.log('logout Err :', error)
		}
	}

	const menuList = [
		{
			id: 0,
			name: 'Nearby Captains',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/captains.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			navigateTo: 'NearbyCaptain',
		},
		{
			id: 1,
			name: 'Weather',
			icon: (
				<FontAwesome5
					name="cloud-sun"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'Weather',
		},
		{
			id: 2,
			name: 'Tides & Currents',
			icon: (
				<FontAwesome5
					name="water"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'TidesAndCurrents',
		},
		{
			id: 3,
			name: 'Vessel Finder',
			icon: (
				<FontAwesome5
					name="ship"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'VesselFinder',
		},
		{
			id: 4,
			name: 'Jobs',
			icon: (
				<Ionicons
					name="md-briefcase"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'Jobs',
		},
		{
			id: 5,
			name: 'Companies',
			icon: (
				<MaterialCommunityIcons
					name="city-variant"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'Companies',
		},
		{
			id: 6,
			name: 'My Jobs',
			icon: (
				<FontAwesome
					name="bookmark"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'MyJobs',
		},
		{
			id: 11,
			name: 'Check Rides',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/check-ride.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			// navigateTo: 'CheckRide',
			navigateTo: 'MyCheckRides',
		},
		{
			id: 7,
			name: 'Report an issue',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/report-issue.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			navigateTo: 'AllIssues',
		},
		{
			id: 8,
			name: 'Licensing Help',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/licensing-img.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			navigateTo: 'Licensing',
		},
		{
			id: 9,
			name: 'Payments',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/payments-img.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			navigateTo: 'Payments',
		},
		{
			id: 10,
			name: 'Friends',
			icon: (
				<FontAwesome5
					name="user-friends"
					size={24}
					color={currentTheme.lightGold}
				/>
			),
			navigateTo: 'FriendsNavigator',
		},
		{
			id: 12,
			name: 'Water Ways',
			icon: (
				<View style={[styles().wh30px, styles().overflowH]}>
					<Image
						source={require('../../assets/images/waterways.png')}
						style={styles().wh100}
						resizeMode="contain"
					/>
				</View>
			),
			navigateTo: 'LearnWaterWays',
		},
	]

	const handleNavigation = item => {
		item.navigateTo && props.navigation.navigate(item.navigateTo)
	}

	const throttledButtonPress = useThrottledPress(handleNavigation)

	const check_disposable = user?.disposable
	const idsToRemoveDisposable = [0, 6, 9, 7, 11, 8]
	const idsToRemoveDeckhand = [0, 4, 6, 9, 11]
	const idsToRemoveTankerman = [12]
	let menu

	if (check_disposable === 'true') {
		menu = menuList?.filter(item => !idsToRemoveDisposable.includes(item.id))
	} else if (user?.role === 'master') {
		menu = menuList
	} else if (user?.role === 'deckhand') {
		menu = menuList?.filter(item => !idsToRemoveDeckhand.includes(item.id))
	} else if (user?.role === 'tankerman') {
		menu = menuList?.filter(item => !idsToRemoveTankerman.includes(item.id))
	} else {
		menu = menuList?.filter(item => item.id !== 0)
	}

	const getuser = async () => {
		const userAsync = await AsyncStorage.getItem('user')
		const user = JSON.parse(userAsync)
		setAsyncUser(user)
	}

	useEffect(() => {
		const focus = props.navigation.addListener('focus', async () => {
			getuser()
			setPage(1)
			// await getDataAsync('user').then(res => {
			//   setUser(res);
			// });
			await refetch().then(res => {
				setChats(res?.data?.getChatRooms?.results)
			})
		})
		return focus
	}, [props.navigation])

	useEffect(() => {
		if (user?.checkr_status !== 'ENGAGE') {
			const intervalId = setInterval(() => {
				Animated.sequence([
					Animated.timing(opacity, {
						toValue: 0.3,
						duration: 500, // Duration for opacity to animate to 0
						useNativeDriver: true,
					}),
					Animated.timing(opacity, {
						toValue: 1,
						duration: 500, // Duration for opacity to animate to 1
						useNativeDriver: true,
					}),
				]).start()
			}, 1500) // Interval for blinking animation

			return () => clearInterval(intervalId)
		}
	}, [])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={false}
			LeftIconFunc={() => props.navigation.navigate('Home')}
			pagetitle={'Menu'}
			withoutScroll={false}
			// ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex, { backgroundColor: '#FCFCFC' }]}>
				{/* <TouchableOpacity
          onPress={() => {
            Linking.openURL('app.riverbankpro://job/1');
          }}
          style={{padding: 30, backgroundColor: 'teal'}}
        /> */}
				<View style={[styles().ph20, styles().pb20, styles().boxpeshadowCart, styles().br20, styles().overflowH]}>
					<HeaderMenu
						LeftIcon={true}
						pagetitle={'Menu'}
						navigation={props.navigation}
					/>

					<View
						style={[
							styles().flexRow,
							styles().flex,
							styles().alignCenter,
							styles().justifyBetween,
							styles().pt15,
							styles().btw1,
							{ borderTopColor: currentTheme.c707070 },
						]}
					>
						<TouchableOpacity
							style={[styles().flexRow, styles().alignCenter, styles().flex]}
							activeOpacity={0.5}
							onPress={() => props.navigation.navigate('Profile')}
						>
							{user?.photo
								? <LinearGradient
										start={{ x: 0, y: 0 }}
										colors={isLearn ? currentTheme.learningGradient : currentTheme.whiteGradient}
										style={{
											padding: 4,

											borderRadius: 100,
											alignItems: 'center',
											justifyContent: 'center',
										}}
									>
										<Image
											source={{ uri: user?.photo }}
											style={[
												styles().wh55px,
												styles().overflowH,
												styles().br50,
												{
													borderWidth: isLearn ? 1 : 0,
													borderColor: currentTheme.white,
												},
											]}
											resizeMode="cover"
										/>
									</LinearGradient>
								: <LinearGradient
										start={{ x: 0, y: 0 }}
										colors={isLearn ? currentTheme.learningGradient : currentTheme.themeGradient}
										style={{
											padding: isLearn ? 4 : 2,
											borderRadius: 100,
											alignItems: 'center',
											justifyContent: 'center',
										}}
									>
										<View
											style={[
												styles().overflowH,
												styles().justifyCenter,
												styles().alignCenter,
												styles().br50,
												styles().wh50px,
												{
													backgroundColor: 'white',
													// borderWidth: 1,
													borderColor: currentTheme.themeBackground,
												},
											]}
										>
											<FontAwesome5
												name="user-alt"
												size={22}
												color={isLearn ? currentTheme.lightGreen : currentTheme.themeBackground}
											/>
										</View>
									</LinearGradient>}
							<View style={[styles().mh10, styles().flex]}>
								<View
									style={[
										styles().flexRow,
										styles().alignCenter,
										// {marginBottom: 3},
									]}
								>
									<Text
										numberOfLines={1}
										style={[styles().fs16, styles().fontMedium, { color: currentTheme.headingColor }]}
									>
										{user?.name ? user?.name : asyncUser?.name ? asyncUser?.name : 'N/A'}
									</Text>
									{user?.profileVerified
										? <View style={[{ height: 18, width: 18 }, styles().ml5, styles().overflowH]}>
												<Image
													source={require('../../assets/images/verified-icon.png')}
													style={styles().wh100}
													resizeMode="contain"
												/>
											</View>
										: null}
								</View>

								<Text style={[styles().fs12, styles().fontRegular, { color: currentTheme.C3C3C3 }]}>See your profile</Text>
							</View>
						</TouchableOpacity>
						<TouchableOpacity
							activeOpacity={0.7}
							// onPress={() => onLogout()}
							onPress={() => Logout()}
							style={[styles().alignCenter, styles().justifyCenter, styles().flexRow]}
						>
							<View style={[styles().wh18px, styles().mr5, styles().overflowH]}>
								<Image
									source={require('../../assets/images/logout.png')}
									style={styles().wh100}
									resizeMode="contain"
								/>
							</View>
							<Text style={[styles().fs14, styles().fontMedium, { color: currentTheme.red }]}>Logout</Text>
						</TouchableOpacity>
					</View>
				</View>
				<TestAccount />
				<View>
					<View style={[styles().flexRow, styles().mb15, styles().mt20, styles().ph20, styles().alignCenter, styles().justifyBetween]}>
						<Text style={[styles().fs18, styles().fw700, { color: currentTheme.black }]}>Recent Chats</Text>
						<TouchableOpacity
							activeOpacity={0.5}
							onPress={() => {
								props.navigation.navigate('Chats')
								isNewMessage(false)
							}}
						>
							<Text style={[styles().fs14, styles().fw400, { color: currentTheme.lightGold }]}>See all</Text>
						</TouchableOpacity>
					</View>
					<View style={[styles().mb30, styles().flex]}>
						<FlatList
							data={Chats}
							showsHorizontalScrollIndicator={false}
							horizontal
							ListEmptyComponent={() => {
								return (
									<View style={[styles().alignCenter, styles().justifyCenter, styles().mt20, { width: width * 0.9 }]}>
										<Text
											style={{
												color: currentTheme.E8E8C8,
												fontFamily: fontStyles.PoppinsRegular,
												fontSize: 14,
											}}
										>
											{loading ? 'Loading...' : ' No recent chats...'}
										</Text>
									</View>
								)
							}}
							renderItem={({ item, index }) => {
								return (
									<FriendsComponent
										navigation={props.navigation}
										item={item}
										index={index}
									/>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
						/>
					</View>
				</View>
				<View style={[styles().ph20]}>
					<View style={[styles().flexRow, styles().mb10, styles().alignCenter, styles().justifyBetween]}>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() => props.navigation.navigate('Help')}
							style={[
								styles().w48,
								styles().flexRow,
								styles().alignCenter,
								styles().ph10,
								styles().boxpeshadowCart,
								styles().br10,
								styles().h40px,
								{ backgroundColor: currentTheme.F5F9FC },
							]}
						>
							<AntDesign
								name="questioncircle"
								size={20}
								color={currentTheme.lightGreen}
							/>
							<Text style={[styles().fs12, styles().ml5, styles().fontRegular, { color: currentTheme.c444D6E }]}>Help and Support</Text>
						</TouchableOpacity>
						<TouchableOpacity
							activeOpacity={0.7}
							onPress={() => props.navigation.navigate('Settings')}
							style={[
								styles().w48,
								styles().flexRow,
								styles().alignCenter,
								styles().boxpeshadowCart,
								styles().ph10,
								styles().br10,
								styles().h40px,
								{ backgroundColor: currentTheme.F5F9FC },
							]}
						>
							<Ionicons
								name="settings"
								size={20}
								color={currentTheme.anotherBlue}
							/>
							<Text style={[styles().fs12, styles().ml5, styles().fontRegular, { color: currentTheme.c444D6E }]}>Settings and privacy</Text>
						</TouchableOpacity>
					</View>

					<View
						style={[
							// styles().flexRow,
							// styles().mb20,
							styles().mt5,
							styles().alignCenter,
							styles().justifyBetween,
						]}
					>
						{check_disposable === 'false' && (
							<TouchableOpacity
								disabled={isRBPVerification}
								onPress={() => props.navigation.navigate('RBPVerification')}
								activeOpacity={0.7}
								style={[
									styles().h45px,
									styles().w100,
									styles().br10,
									styles().alignCenter,
									styles().justifyCenter,
									{
										backgroundColor:
											user?.checkr_decision === 'PENDING'
												? currentTheme.starColor
												: user?.checkr_decision === 'COMPLETED'
													? currentTheme.lightGreen
													: user?.checkr_decision === 'NEED_REVIEW'
														? currentTheme.themeBackground
														: currentTheme.verifyBG,
									},
								]}
							>
								<Animated.View style={[styles().alignCenter, styles().flexRow, styles().justifyCenter, { opacity: opacity }]}>
									<View style={[styles().wh20px, { marginBottom: 5 }]}>
										<Image
											source={require('../../assets/images/verifyuser.png')}
											style={[styles().wh100]}
											resizeMode="contain"
										/>
									</View>
									<Text style={[styles().fontRegular, styles().fs14, styles().ml10, { color: currentTheme.white }]}>{`${isRBPVerificationText}`}</Text>
								</Animated.View>
							</TouchableOpacity>
						)}
						{/* {user.checkr_decision === 'PENDING' ? null : (
              <Text
                style={[
                  styles().fs14,
                  styles().mv10,
                  styles().fontRegular,
                  {color: currentTheme.navyBlue},
                ]}>
                {user?.checkr_status === 'ENGAGE'
                  ? 'User Engaged for Jobs'
                  : user?.checkr_status === 'DISENGAGED'
                  ? 'User Disengaged for jobs'
                  : user?.checkr_status == null
                  ? 'Please re-submit your application to engaged'
                  : ''}
              </Text>
            )} */}
						{/* {check_disposable == 'false' ? (
              <TouchableOpacity
                onPress={() => {
                  setCheckrEligible(true);
                  // setStripeEligible(true);
                }}
                activeOpacity={0.7}
                disabled={checkr ? false : true}
                style={[
                  styles().w48,
                  styles().flexRow,
                  styles().alignCenter,
                  styles().ph10,
                  checkr && styles().boxpeshadowCart,
                  styles().br10,
                  styles().h40px,
                  {
                    backgroundColor: !checkr
                      ? currentTheme.backgroundGreen
                      : currentTheme.F5F9FC,
                  },
                ]}>
                <View style={[styles().wh20px]}>
                  <Image
                    style={[styles().wh100, {borderRadius: 3}]}
                    resizeMode="contain"
                    source={require('../../assets/images/checkr.png')}
                  />
                </View>
                <Text
                  style={[
                    styles().fs12,
                    styles().ml5,
                    styles().fontRegular,
                    {
                      color: !checkr
                        ? currentTheme.green
                        : currentTheme.c444D6E,
                    },
                  ]}>
                  {!checkr ? `Checkr Verified` : `Checkr Verification`}
                </Text>
              </TouchableOpacity>
            ) : null} */}
						{/* {check_disposable == 'false' && user?.role !== 'deckhand' ? (
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={async () => await mutate()}
                disabled={isStripe ? true : false}
                style={[
                  styles().w48,
                  styles().flexRow,
                  styles().alignCenter,
                  !isStripe && styles().boxpeshadowCart,
                  styles().ph10,
                  styles().br10,
                  styles().h40px,
                  {
                    backgroundColor: isStripe
                      ? currentTheme.backgroundGreen
                      : currentTheme.F5F9FC,
                  },
                ]}>
                <View style={[styles().wh20px]}>
                  <Image
                    style={[styles().wh100, {borderRadius: 3}]}
                    resizeMode="contain"
                    source={require('../../assets/images/stripe.png')}
                  />
                </View>
                <Text
                  style={[
                    styles().fs12,
                    styles().ml5,
                    styles().fontRegular,
                    {
                      color: isStripe
                        ? currentTheme.green
                        : currentTheme.c444D6E,
                    },
                  ]}>
                  {isStripe ? `Stripe Connected` : `Connect Stripe`}
                </Text>
              </TouchableOpacity>
            ) : null} */}
					</View>

					<View style={[styles().mt15]}>
						<FlatList
							data={menu}
							showsVerticalScrollIndicator={false}
							numColumns={2}
							contentContainerStyle={{ flexGrow: 1 }}
							ListEmptyComponent={() => {
								return (
									<View
										style={[
											styles().alignCenter,
											styles().justifyCenter,

											styles().flex,
										]}
									>
										<Text
											style={{
												color: currentTheme.E8E8C8,
												fontSize: 14,
											}}
										>
											loading
										</Text>
									</View>
								)
							}}
							renderItem={({ item, index }) => {
								return (
									<TouchableOpacity
										key={index}
										activeOpacity={0.8}
										onPress={() => throttledButtonPress(item)}
										style={[
											{ marginRight: index % 2 === 0 ? '3%' : 0 },
											{ marginLeft: index % 2 === 0 ? '2%' : 0 },
											styles().w47,
											styles().boxpeshadowCart,
											styles().mb15,
											styles().pall15,
											styles().br15,
										]}
									>
										{item.icon}
										<Text
											style={[
												styles().fs12,
												// styles().fw400,
												styles().fontRegular,
												{ color: currentTheme.headingColor, marginTop: 5 },
											]}
										>
											{item.name}
										</Text>
									</TouchableOpacity>
								)
							}}
							keyExtractor={(_item, index) => index.toString()}
							ListFooterComponent={<View style={styles().wh20px} />}
						/>
					</View>
				</View>
			</View>
		</Layout>
	)
}

export default React.memo(Menu)
