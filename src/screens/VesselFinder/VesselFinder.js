import { Text, View, Image } from 'react-native'
import { useContext, useState } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import Loading from '../../context/Loading/Loading'

const VesselFinder = props => {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const [_loading, _setLoading] = useState(true)
	const { isLoader } = useContext(Loading)
	const _url =
		'https://www.marinetraffic.com/en/ais/home/<USER>/centery:24.9/zoom:4?utm_source=google&utm_medium=cpc&utm_device=c&utm_campaign=18676094330&utm_adgroup=144331662922&utm_extension=&utm_term=vessel%20finder&utm_landingpage={lpurl}&gad=1&gclid=CjwKCAjwv8qkBhAnEiwAkY-ahnyL_7Dlw7oIoKZqKEedeAymRihXaoOYMDVxjeUuX6VuIvf2LRprzhoCiGgQAvD_BwE'
	// useEffect(() => {
	//   isLoader(loading);
	// }, [loading]);

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={true}
			keyBoardArea={50}
			pagetitle={'Vessel Finder'}
			ContentArea={[styles().ph20]}
		>
			<View style={[styles().flex, styles().alignCenter, styles().justifyCenter]}>
				{/* <WebView
          source={{
            uri: url,
          }}
          style={{flex: 1}}
          onLoad={() => setLoading(true)}
          onLoadEnd={() => setLoading(false)}
        /> */}
				<View style={[styles().h150px, { aspectRatio: 1 }]}>
					<Image
						source={require('../../assets/images/comming-soon.png')}
						resizeMode="contain"
						style={[styles().wh100]}
					/>
				</View>
				<Text style={[styles().fontMedium, styles().mt20, { fontSize: 20, color: currentTheme.themeBackground }]}>Coming Soon...</Text>
			</View>
		</Layout>
	)
}

export default VesselFinder
