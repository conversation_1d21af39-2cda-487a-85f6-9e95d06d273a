import { Text, View, Platform, UIManager, Dimensions } from 'react-native'
import { useContext, useEffect } from 'react'
import ThemeContext from '../../context/ThemeContext/ThemeContext'
import { theme } from '../../context/ThemeContext/ThemeColor'
import styles from '../styles'
import Layout from '../../component/Layout/Layout'
import FlashMessage from '../../component/FlashMessage/FlashMessage'
import { useQuery } from '@apollo/client'
import { getKeyByFilter } from '../../apollo/server'
import UserContext from '../../context/User/User'
import Loading from '../../context/Loading/Loading'
import moment from 'moment'
import RenderHTML from 'react-native-render-html'

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
	UIManager.setLayoutAnimationEnabledExperimental(true)
}

const { width } = Dimensions.get('window')

const ApplyForVerify = props => {
	const themeContext = useContext(ThemeContext)
	const _user = useContext(UserContext)
	const currentTheme = theme[themeContext.ThemeValue]
	const { isLoader } = useContext(Loading)

	const { data, loading, refetch } = useQuery(getKeyByFilter, {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all',
		variables: {
			filters: {
				key: 'terms_cond_signup',
				specific_for: 'crewmember',
			},
		},
		onCompleted: data => {
			console.log('getKeyByFilter res :', JSON.stringify(data?.getKeyByFilter))
		},
		onError: err => {
			console.log('getKeyByFilter err :', err)
			FlashMessage({ msg: err.message, type: 'danger' })
			isLoader(false)
		},
	})

	useEffect(() => {
		isLoader(loading)
	}, [loading])

	return (
		<Layout
			navigation={props.navigation}
			LeftIcon={true}
			headerShown={true}
			withoutScroll={false}
			pagetitle={'Terms & Conditions'}
			ContentArea={[styles().ph10]}
		>
			<Text style={[styles().fs12, styles().mb20, styles().lh22, styles().fw400, styles().ml20, { color: currentTheme.E8E8C8 }]}>
				{moment(new Date()).format('LL')}
			</Text>

			<RenderHTML
				contentWidth={width}
				source={{ html: data?.getKeyByFilter?.value }}
				baseStyle={{
					paddingHorizontal: 0,
					paddingVertical: 0,
				}}
				tagsStyles={{
					p: {
						marginVertical: 0,
					},
				}}
			/>
			<View style={styles().wh20px} />
		</Layout>
	)
}

export default ApplyForVerify
