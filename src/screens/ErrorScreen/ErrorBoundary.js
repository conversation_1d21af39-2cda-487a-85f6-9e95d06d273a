// import React, {useState, useEffect} from 'react';
// import ErrorScreen from './ErrorScreen';
// import {View} from 'react-native';

// const ErrorBoundary = ({children}) => {
//   const [hasError, setHasError] = useState(false);
//   const [error, setError] = useState('');

//   const handleError = (error, isFatal) => {
//     // Log the error to an error reporting service
//     console.error('Error caught by ErrorBoundary:', error, isFatal);
//     setHasError(true);
//     setError(error?.message);
//     // Update state to indicate that an error occurred
//   };

//   useEffect(() => {
//     // Set up a global error handler
//     const errorHandler = ErrorUtils.setGlobalHandler(handleError);

//     return () => {
//       // Cleanup the error handler when unmounting
//       ErrorUtils.setGlobalHandler(null);
//       setError('');
//       setHasError(false);
//     };
//   }, []);

//   if (hasError) {
//     console.log('has error');
//     // Render the error screen component
//     return <ErrorScreen error={error} />;
//   }
//   // Render the child components normally
//   return children;
// };

// export default ErrorBoundary;

import { Component } from 'react'
import ErrorScreen from './ErrorScreen'

class ErrorBoundary extends Component {
	state = {
		hasError: false,
		message: '',
	}

	componentDidCatch(error, errorInfo) {
		// Log the error to an error reporting service
		console.error('Error caught by ErrorBoundary:', error, errorInfo)
		// Update state to indicate that an error occurred
		this.setState({ hasError: true, message: error?.message })
	}

	render() {
		if (this?.state?.hasError) {
			// Render the error screen component
			return <ErrorScreen error={this.state.message} />
		}
		// Render the child components normally
		return this.props.children
	}
}

export default ErrorBoundary
