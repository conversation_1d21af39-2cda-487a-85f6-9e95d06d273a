import { View, Text, Image } from 'react-native'
import styles from '../styles'

const ErrorScreen = ({ error }) => {
	return (
		<View
			style={[
				styles().ph20,
				styles().flex,
				{
					alignItems: 'center',
					justifyContent: 'center',
					backgroundColor: 'white',
				},
			]}
		>
			<View style={[styles().h250px, { width: '70%' }]}>
				<Image
					source={require('../../assets/images/no-internet.png')}
					resizeMode="contain"
					style={[styles().wh100, { tintColor: '#A79441' }]}
				/>
			</View>
			<Text style={[styles().fontBold, styles().mv10, { fontSize: 24, color: 'black' }]}>Whoops!!</Text>
			<Text style={[styles().fontRegular, styles().w90, styles().textCenter, styles().mb10, { fontSize: 14, color: 'black' }]}>
				{/* {`Something went wrong, \n Message: ${
          error ? error : 'Error Occoured'
        }`} */}
				{
					'Something went wrong, Please try again. if the update is available, then updated the app this may fix the issue. if the issue persist then contact support.'
				}
			</Text>
		</View>
	)
}

export default ErrorScreen
