import React, { useContext, useEffect } from 'react'
import { View, StyleSheet, Linking } from 'react-native'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { createDrawerNavigator } from '@react-navigation/drawer'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import navigationService from './navigationService'
import ThemeContext from '../context/ThemeContext/ThemeContext'
import { theme } from '../context/ThemeContext/ThemeColor'
import { tabIcon } from './customTab'
import CustomTabs from '../component/CustomTab/CustomTab'
import { AuthContext } from '../context/Auth/auth'
import { UserProvider } from '../context/User/User'
import GlobalWebSocketManager from '../components/GlobalWebSocketManager'
import Login from '../screens/Login/Login'
import SignUp from '../screens/SignUp/SignUp'
import Verification from '../screens/Verification/Verification'
import ForgetPassword from '../screens/ForgetPassword/ForgetPassword'
import ResetPassword from '../screens/ResetPassword/ResetPassword'
import ChangePassword from '../screens/ChangePassword/ChangePassword'
import WizardStep1 from '../screens/ProfileWizard/WizardStep1'
import WizardStep2 from '../screens/ProfileWizard/WizardStep2'
import WizardStep3 from '../screens/ProfileWizard/WizardStep3'
import WizardStep4 from '../screens/ProfileWizard/WizardStep4'
import WizardStep5 from '../screens/ProfileWizard/WizardStep5'
import Home from '../screens/Home/Home'
import Menu from '../screens/Menu/Menu'
import Jobs from '../screens/Jobs/Jobs'
import MyJobs from '../screens/MyJobs/MyJobs'
import JobDetails from '../screens/JobDetails/JobDetails'
import Companies from '../screens/Companies/Companies'
import CompanyDetails from '../screens/CompanyDetails/CompanyDetails'
import Profile from '../screens/Profile/Profile'
import EditProfile from '../screens/EditProfile/EditProfile'
import EditBio from '../screens/EditProfile/EditBio'
import EditEducation from '../screens/EditProfile/EditEducation'
import EditWork from '../screens/EditProfile/EditWork'
import EditDocument from '../screens/EditProfile/EditDocument'
import EditTWIC from '../screens/EditProfile/EditTWIC'
import EditOtherDoc from '../screens/EditProfile/EditOtherDoc'
import EditRoutes from '../screens/EditProfile/EditRoutes'
import Reviews from '../screens/Reviews/Reviews'
import Notifications from '../screens/Notifications/Notifications'
import Weather from '../screens/Weather/Weather'
import TidesAndCurrents from '../screens/TidesAndCurrents/TidesAndCurrents'
import VesselFinder from '../screens/VesselFinder/VesselFinder'
import EditSkills from '../screens/EditSkills/EditSkills'
import Help from '../screens/Help/Help'
import Settings from '../screens/Settings/Settings'
import CheckRide from '../screens/CheckRide/CheckRide'
import CheckRideStatus from '../screens/CheckRideStatus/CheckRideStatus'
import MyCheckRides from '../screens/MyCheckRides/MyCheckRides'
import Chats from '../screens/Chats/Chats'
import Chat from '../screens/Chat/Chat'
import FriendsScreen from '../screens/FriendsScreen/FriendsScreen'
import HomeSearch from '../screens/HomeSearch/HomeSearch'
import ViewFriendProfile from '../screens/ViewFriendProfile/ViewFriendProfile'
import ViewFriendAbout from '../screens/ViewFriendProfile/ViewFriendAbout'
import ReportIssue from '../screens/ReportIssue/ReportIssue'
import AllIssues from '../screens/AllIssues/AllIssues'
import PostDetails from '../screens/PostDetails/PostDetails'
import ThreadDetails from '../screens/ThreadDetails/ThreadDetails'
import ApplyForVerify from '../screens/ApplyForVerify/ApplyForVerify'
import AddBankAccount from '../screens/AddBankAccount/AddBankAccount'
import Payments from '../screens/Payments/Payments'
import Recommendation from '../screens/Recommendation/Recommendation'
import Recovery from '../screens/Recovery/Recovery'
import RecoveryVerification from '../screens/RecoveryVerification/RecoveryVerification'
import Licensing from '../screens/Licensing/Licensing'
import LicenseQuestAns from '../screens/LicenseQuestAns/LicenseQuestAns'
import LicenseQuestAnsDetail from '../screens/LicenseQuestAnsDetail/LicenseQuestAnsDetails'
import NearbyCaptain from '../screens/NearbyCaptain/NearbyCaptain'
import NetworkStatus from '../screens/NetworkStatus/NetworkStatus'
import { NotificationProvider } from '../context/Notification/Notification'
import WizardStep6 from '../screens/ProfileWizard/WizardStep6'
import PaymentDetails from '../screens/PaymentDetail/PaymentDetails'
import TermsAndConditions from '../screens/TermsAndConditions/TermsAndConditions'
import ToeBoater from '../screens/ToeBoater/ToeBoater'
import RBPVerification from '../screens/RBPVerification/RBPVerification'
import RBPForm from '../screens/RBPVerification/Form/Form'
import AppointmentBooking from '../screens/AppointmentBooking/AppointmentBooking'
import Appointments from '../screens/Appointments/Appointments'
import AppointmenetDetails from '../screens/AppointmentDetails/AppointmenetDetails'
import LearnWaterWays from '../screens/LearnWaterWays/LearnWaterWays'
import LearnWaterWaysDetail from '../screens/LearnWaterWaysDetail/LearnWaterWaysDetail'
import NewsLetter from '../screens/NewsLetter/NewsLetter'
import { urls } from '../utils/Constants'
import AllBadges from '../screens/AllBadges/AllBadges'
import ErrorScreen from '../screens/ErrorScreen/ErrorScreen'

const NavigationStack = createStackNavigator()
const AuthenticationStack = createStackNavigator()
const MainStack = createStackNavigator()
const Tab = createBottomTabNavigator()
const SideDrawer = createDrawerNavigator()
const HomeStack = createStackNavigator()
const WizardStack = createStackNavigator()
const MenuStack = createStackNavigator()
const NotiStack = createStackNavigator()
const FriendsStack = createStackNavigator()
const NetworkStack = createStackNavigator()

const JobStack = createStackNavigator()

const horizontalAnimation = {
	gestureDirection: 'horizontal',
	cardStyleInterpolator: ({ current, layouts }) => {
		return {
			cardStyle: {
				transform: [
					{
						translateX: current.progress.interpolate({
							inputRange: [0, 1],
							outputRange: [layouts.screen.width, 0],
						}),
					},
				],
			},
		}
	},
}

function AuthenticationNavigator() {
	return (
		<AuthenticationStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<AuthenticationStack.Screen
				name="Login"
				component={Login}
			/>
			<AuthenticationStack.Screen
				name="SignUp"
				component={SignUp}
			/>
			<AuthenticationStack.Screen
				name="ForgetPassword"
				component={ForgetPassword}
			/>
			<AuthenticationStack.Screen
				name="Verification"
				component={Verification}
			/>
			<AuthenticationStack.Screen
				name="ResetPassword"
				component={ResetPassword}
			/>
			<AuthenticationStack.Screen
				name="TermsAndConditions"
				component={TermsAndConditions}
			/>
		</AuthenticationStack.Navigator>
	)
}

function WizardNavigator() {
	return (
		<WizardStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<WizardStack.Screen
				name="WizardStep1"
				component={WizardStep1}
			/>
			<WizardStack.Screen
				name="WizardStep2"
				component={WizardStep2}
			/>
			<WizardStack.Screen
				name="WizardStep3"
				component={WizardStep3}
			/>
			<WizardStack.Screen
				name="WizardStep4"
				component={WizardStep4}
			/>
			<WizardStack.Screen
				name="WizardStep5"
				component={WizardStep5}
			/>
			<WizardStack.Screen
				name="WizardStep6"
				component={WizardStep6}
			/>
		</WizardStack.Navigator>
	)
}

function NetworkNavigator() {
	return (
		<NetworkStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<NetworkStack.Screen
				name="NetworkStatus"
				component={NetworkStatus}
			/>
			<NetworkStack.Screen
				name="noDrawer"
				component={NoDrawer}
			/>
		</NetworkStack.Navigator>
	)
}

function HomeNavigator(_props) {
	return (
		<HomeStack.Navigator
			// screenOptions={({route}) => ({
			//   headerShown: false,
			//   ...(route.name === 'HomeSearch'
			//     ? {presentation: 'modal'}
			//     : horizontalAnimation),
			// })}
			screenOptions={{ headerShown: false, ...horizontalAnimation }}
		>
			<HomeStack.Screen
				name="Home"
				component={Home}
			/>
			<HomeStack.Screen
				name="MyJobs"
				component={MyJobs}
			/>
			<HomeStack.Screen
				name="Jobs"
				component={Jobs}
			/>
			<HomeStack.Screen
				name="JobDetails"
				component={JobDetails}
			/>
			<HomeStack.Screen
				name="Profile"
				component={Profile}
			/>
			<HomeStack.Screen
				name="EditProfile"
				component={EditProfile}
			/>
			<HomeStack.Screen
				name="EditBio"
				component={EditBio}
			/>
			<HomeStack.Screen
				name="EditEducation"
				component={EditEducation}
			/>
			<HomeStack.Screen
				name="EditWork"
				component={EditWork}
			/>
			<HomeStack.Screen
				name="EditDocument"
				component={EditDocument}
			/>
			<HomeStack.Screen
				name="EditTWIC"
				component={EditTWIC}
			/>
			<HomeStack.Screen
				name="EditOtherDoc"
				component={EditOtherDoc}
			/>
			<HomeStack.Screen
				name="EditRoutes"
				component={EditRoutes}
			/>
			<HomeStack.Screen
				name="Reviews"
				component={Reviews}
			/>
			<HomeStack.Screen
				name="Chats"
				component={Chats}
			/>
			<HomeStack.Screen
				name="Weather"
				component={Weather}
			/>
			<HomeStack.Screen
				name="Chat"
				component={Chat}
			/>
			<HomeStack.Screen
				name="HomeSearch"
				component={HomeSearch}
				options={{ presentation: 'modal' }}
			/>
			<HomeStack.Screen
				name="CompanyDetails"
				component={CompanyDetails}
			/>
			<HomeStack.Screen
				name="FriendsNavigator"
				component={FriendsNavigator}
			/>
			<HomeStack.Screen
				name="ViewFriendProfile"
				component={ViewFriendProfile}
			/>
			<HomeStack.Screen
				name="ViewFriendAbout"
				component={ViewFriendAbout}
			/>
			<HomeStack.Screen
				name="AllIssues"
				component={AllIssues}
			/>
			<HomeStack.Screen
				name="PostDetails"
				component={PostDetails}
			/>
			<HomeStack.Screen
				name="NearbyCaptain"
				component={NearbyCaptain}
			/>
			<HomeStack.Screen
				name="Recommendation"
				component={Recommendation}
			/>
			<HomeStack.Screen
				name="AllBadges"
				component={AllBadges}
			/>
		</HomeStack.Navigator>
	)
}

function JobNavigator(_props) {
	return (
		<JobStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<JobStack.Screen
				name="Jobs"
				component={Jobs}
			/>
			<JobStack.Screen
				name="JobDetails"
				component={JobDetails}
			/>
			<JobStack.Screen
				name="RBPVerification"
				component={RBPVerification}
			/>
			<JobStack.Screen
				name="RBPForm"
				component={RBPForm}
			/>
		</JobStack.Navigator>
	)
}

function NotificationNavigator(_props) {
	return (
		<NotiStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<NotiStack.Screen
				name="Notifications"
				component={Notifications}
			/>
			<NotiStack.Screen
				name="JobDetails"
				component={JobDetails}
			/>
			<NotiStack.Screen
				name="CheckRideStatus"
				component={CheckRideStatus}
			/>
			<NotiStack.Screen
				name="MyCheckRides"
				component={MyCheckRides}
			/>
			<NotiStack.Screen
				name="EditProfile"
				component={EditProfile}
			/>
			<NotiStack.Screen
				name="PostDetails"
				component={PostDetails}
			/>
			<NotiStack.Screen
				name="PaymentDetails"
				component={PaymentDetails}
			/>
			<NotiStack.Screen
				name="AppointmenetDetails"
				component={AppointmenetDetails}
			/>
		</NotiStack.Navigator>
	)
}

function MenuNavigator(_props) {
	return (
		<MenuStack.Navigator screenOptions={{ headerShown: false, ...horizontalAnimation }}>
			<MenuStack.Screen
				name="Menu"
				component={Menu}
			/>
			<MenuStack.Screen
				name="Jobs"
				component={Jobs}
			/>
			<MenuStack.Screen
				name="MyJobs"
				component={MyJobs}
			/>
			<MenuStack.Screen
				name="JobDetails"
				component={JobDetails}
			/>
			<MenuStack.Screen
				name="Companies"
				component={Companies}
			/>
			<MenuStack.Screen
				name="CompanyDetails"
				component={CompanyDetails}
			/>
			<MenuStack.Screen
				name="Profile"
				component={Profile}
			/>
			<MenuStack.Screen
				name="EditProfile"
				component={EditProfile}
			/>
			<MenuStack.Screen
				name="EditBio"
				component={EditBio}
			/>
			<MenuStack.Screen
				name="EditEducation"
				component={EditEducation}
			/>
			<MenuStack.Screen
				name="EditWork"
				component={EditWork}
			/>
			<MenuStack.Screen
				name="EditDocument"
				component={EditDocument}
			/>
			<MenuStack.Screen
				name="EditTWIC"
				component={EditTWIC}
			/>
			<MenuStack.Screen
				name="EditOtherDoc"
				component={EditOtherDoc}
			/>
			<MenuStack.Screen
				name="EditRoutes"
				component={EditRoutes}
			/>
			<MenuStack.Screen
				name="ChangePassword"
				component={ChangePassword}
			/>
			<MenuStack.Screen
				name="Reviews"
				component={Reviews}
			/>
			<MenuStack.Screen
				name="VesselFinder"
				component={VesselFinder}
			/>
			<MenuStack.Screen
				name="Weather"
				component={Weather}
			/>
			<MenuStack.Screen
				name="TidesAndCurrents"
				component={TidesAndCurrents}
			/>
			<MenuStack.Screen
				name="EditSkills"
				component={EditSkills}
			/>
			<MenuStack.Screen
				name="Help"
				component={Help}
			/>
			<MenuStack.Screen
				name="Settings"
				component={Settings}
			/>
			<MenuStack.Screen
				name="CheckRide"
				component={CheckRide}
			/>
			<MenuStack.Screen
				name="CheckRideStatus"
				component={CheckRideStatus}
			/>
			<MenuStack.Screen
				name="MyCheckRides"
				component={MyCheckRides}
			/>
			<MenuStack.Screen
				name="Chats"
				component={Chats}
			/>
			<MenuStack.Screen
				name="Chat"
				component={Chat}
			/>
			<MenuStack.Screen
				name="FriendsNavigator"
				component={FriendsNavigator}
			/>
			<MenuStack.Screen
				name="ViewFriendProfile"
				component={ViewFriendProfile}
			/>
			<MenuStack.Screen
				name="ViewFriendAbout"
				component={ViewFriendAbout}
			/>
			<MenuStack.Screen
				name="ReportIssue"
				component={ReportIssue}
			/>
			<MenuStack.Screen
				name="AllIssues"
				component={AllIssues}
			/>
			<MenuStack.Screen
				name="ThreadDetails"
				component={ThreadDetails}
			/>
			<MenuStack.Screen
				name="ApplyForVerify"
				component={ApplyForVerify}
			/>
			<MenuStack.Screen
				name="AddBankAccount"
				component={AddBankAccount}
			/>
			<MenuStack.Screen
				name="PostDetails"
				component={PostDetails}
			/>
			<MenuStack.Screen
				name="Payments"
				component={Payments}
			/>
			<MenuStack.Screen
				name="PaymentDetails"
				component={PaymentDetails}
			/>
			<MenuStack.Screen
				name="Recommendation"
				component={Recommendation}
			/>
			<MenuStack.Screen
				name="Recovery"
				component={Recovery}
			/>
			<MenuStack.Screen
				name="RecoveryVerification"
				component={RecoveryVerification}
			/>
			<MenuStack.Screen
				name="Licensing"
				component={Licensing}
			/>
			<MenuStack.Screen
				name="LicenseQuestAns"
				component={LicenseQuestAns}
			/>
			<MenuStack.Screen
				name="LicenseQuestAnsDetail"
				component={LicenseQuestAnsDetail}
			/>
			<MenuStack.Screen
				name="AppointmentBooking"
				component={AppointmentBooking}
			/>
			<MenuStack.Screen
				name="Appointments"
				component={Appointments}
			/>
			<MenuStack.Screen
				name="AppointmenetDetails"
				component={AppointmenetDetails}
			/>
			<MenuStack.Screen
				name="NearbyCaptain"
				component={NearbyCaptain}
			/>
			<MenuStack.Screen
				name="TermsAndConditions"
				component={TermsAndConditions}
			/>
			<MenuStack.Screen
				name="ToeBoater"
				component={ToeBoater}
			/>
			<MenuStack.Screen
				name="RBPVerification"
				component={RBPVerification}
			/>
			<MenuStack.Screen
				name="RBPForm"
				component={RBPForm}
			/>
			<MenuStack.Screen
				name="LearnWaterWays"
				component={LearnWaterWays}
			/>
			<MenuStack.Screen
				name="LearnWaterWaysDetail"
				component={LearnWaterWaysDetail}
			/>
			<MenuStack.Screen
				name="NewsLetter"
				component={NewsLetter}
			/>
			<MenuStack.Screen
				name="AllBadges"
				component={AllBadges}
			/>
		</MenuStack.Navigator>
	)
}

function FriendsNavigator() {
	return (
		<FriendsStack.Navigator screenOptions={{ headerShown: false }}>
			<FriendsStack.Screen
				name="FriendsScreen"
				component={FriendsScreen}
			/>
			<FriendsStack.Screen
				name="ViewFriendProfile"
				component={ViewFriendProfile}
			/>
			<FriendsStack.Screen
				name="ViewFriendAbout"
				component={ViewFriendAbout}
			/>
			<FriendsStack.Screen
				name="Recommendation"
				component={Recommendation}
			/>
		</FriendsStack.Navigator>
	)
}

function Drawer() {
	// Animation is currently not being used, so we'll pass a simple style
	const staticStyle = {
		borderRadius: 0,
		transform: [{ scale: 1 }],
	}

	return (
		<UserProvider>
			<GlobalWebSocketManager>
				<NotificationProvider>
					<View style={{ flex: 1, backgroundColor: '#fff' }}>
						<SideDrawer.Navigator
						drawerType="slide"
						overlayColor="transparent"
						drawerStyle={{
							flex: 1,
							width: '70%',
							backgroundColor: 'transparent',
							overflow: 'hidden',
						}}
						contentContainerStyle={{ flex: 1 }}
						screenOptions={{
							headerShown: false,
							swipeEnabled: false,
							// drawerActiveBackgroundColor: "transparent",
							// drawerActiveTintColor: "white",
							// drawerInactiveTintColor: "white"
						}}
						sceneContainerStyle={{ backgroundColor: 'transparent' }}
						// drawerContent={props => {
						//   setProgress(props.progress);
						//   return <SideBar {...props} />;
						// }}
					>
						<SideDrawer.Screen name="BottomTabs">
							{props => (
								<MyTabs
									{...props}
									style={staticStyle}
								/>
							)}
						</SideDrawer.Screen>
					</SideDrawer.Navigator>
					</View>
				</NotificationProvider>
			</GlobalWebSocketManager>
		</UserProvider>
	)
}

function MyTabs(props) {
	const themeContext = useContext(ThemeContext)
	const currentTheme = theme[themeContext.ThemeValue]
	return (
		<View
			style={StyleSheet.flatten([
				{
					flex: 1,
				},
				props.style,
			])}
		>
			<Tab.Navigator
				backBehavior="initialRoute"
				initialRouteName="Home"
				tabBar={props => <CustomTabs {...props} />}
				screenOptions={({ route }) => tabIcon(route, currentTheme)}
			>
				<Tab.Screen
					name="Jobs"
					component={JobNavigator}
					options={{ title: 'Jobs', headerShown: false }}
				/>
				<Tab.Screen
					name="Videos"
					component={FriendsNavigator}
					options={{ title: 'Videos', headerShown: false }}
				/>
				<Tab.Screen
					name="Home"
					component={HomeNavigator}
					options={{ title: 'Home', headerShown: false }}
				/>
				<Tab.Screen
					name="NotificationStack"
					component={NotificationNavigator}
					options={{ title: 'NotificationStack', headerShown: false }}
				/>
				<Tab.Screen
					name="Menu"
					component={MenuNavigator}
					options={{ title: 'Menu', headerShown: false }}
				/>
			</Tab.Navigator>
		</View>
	)
}

function NoDrawer() {
	return (
		<NavigationStack.Navigator screenOptions={{ headerShown: false }}>
			<NavigationStack.Screen
				name="Drawer"
				component={Drawer}
			/>
		</NavigationStack.Navigator>
	)
}

function AppContainer(_props) {
	const { token, isProfileCompleted, networkInfo } = useContext(AuthContext)
	const isLoggedIn = !!token
	let initialRouteName = ''
	// console.log('net info :', networkInfo);
	if (networkInfo?.isConnected) {
		if (isLoggedIn) {
			if (JSON.parse(isProfileCompleted) === true) {
				initialRouteName = 'noDrawer'
			}
			if (JSON.parse(isProfileCompleted) === false) {
				initialRouteName = 'WizardNavigator'
			}
		} else {
			initialRouteName = 'Auth'
		}
	} else {
		initialRouteName = 'NetworkNavigator'
	}

	const linking = {
		prefixes: [urls.domain, urls.scheme], // Schemes and domains
		config: {
			// initialRouteName: initialRouteName,
			screens: {
				noDrawer: {
					screens: {
						Drawer: {
							screens: {
								BottomTabs: {
									screens: {
										Menu: {
											screens: {
												JobDetails: 'job/:jobId', // Dynamic route with job ID
												Jobs: 'jobs',
												CompanyDetails: 'company/:companyId',
												Chat: 'newMessage/:roomId',
												PostDetails: 'post/:postId',
												MyCheckRides: 'checkride',
												PaymentDetails: 'invoice/:invoiceId',
												AppointmenetDetails: 'appointment/:appointmentId',
												EditProfile: 'editprofile',
												Profile: 'profile',
												FriendsNavigator: 'friendship',
												Menu: 'verification',
												ThreadDetails: 'ticket/:ticketId',
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	const authLinking = {
		prefixes: [urls.domain, urls.scheme], // Schemes and domains
		config: {
			// initialRouteName: initialRouteName,
			screens: {
				Auth: {
					screens: {
						ForgetPassword: 'forget-password/:userid',
						SignUp: 'signup',
						Login: 'login',
					},
				},
			},
		},
	}

	useEffect(() => {
		const getInitialUrl = async () => {
			const url = await Linking.getInitialURL()
			if (url) {
				console.log('App opened with deep link:', url)
				// Handle the initial deep link URL here
			}
		}

		getInitialUrl()

		const linkingListener = Linking.addEventListener('url', event => {
			console.log('Deep link activated:', event)

			// Handle URL while app is open
		})

		return () => {
			linkingListener.remove()
		}
	}, [])

	return (
		<SafeAreaProvider>
			<NavigationContainer
				onStateChange={async state => {
					// console.log('onStateChange', JSON.stringify(state));
					const currentRoute = navigationService.getActiveRouteName(state)
					navigationService.setCurrentRouteName(currentRoute)
				}}
				linking={isLoggedIn ? linking : authLinking}
				ref={ref => {
					navigationService.setGlobalRef(ref)
				}}
			>
				<MainStack.Navigator
					initialRouteName={initialRouteName}
					screenOptions={{
						headerShown: false,
						gestureEnabled: false,
					}}
				>
					<MainStack.Screen
						name="Auth"
						component={AuthenticationNavigator}
					/>
					<MainStack.Screen
						name="noDrawer"
						component={NoDrawer}
					/>
					<MainStack.Screen
						name="NetworkNavigator"
						component={NetworkNavigator}
					/>
					<MainStack.Screen
						name="WizardNavigator"
						component={WizardNavigator}
					/>
					<MainStack.Screen
						name="ErrorScreen"
						component={ErrorScreen}
					/>
				</MainStack.Navigator>
			</NavigationContainer>
		</SafeAreaProvider>
	)
}

export default AppContainer
