import { DrawerActions, CommonActions } from '@react-navigation/native'
let navObj = null
let currentRouteName = ''
function setGlobalRef(ref) {
	navObj = ref
}

function navigate(path, props = {}) {
	navObj.navigate(path, props)
}

function toggleDrawer() {
	navObj.dispatch(DrawerActions.toggleDrawer())
}

function ResetNavigation() {
	navObj.dispatch(
		CommonActions.reset({
			index: 0,
			routes: [{ name: 'Auth' }],
		})
	)
}

function ResetNavigationTo() {
	navObj.dispatch(
		CommonActions.reset({
			index: 0,
			routes: [{ name: 'NetworkNavigator' }],
		})
	)
}

function ResetNavigationHome() {
	navObj.dispatch(
		CommonActions.reset({
			index: 0,
			routes: [{ name: 'noDrawer' }],
		})
	)
}

function setCurrentRouteName(routeName) {
	currentRouteName = routeName
}

function getCurrentRouteName() {
	return currentRouteName
}

function getActiveRouteName(state) {
	if (!state || !state.routes) {
		return null
	}
	const route = state.routes[state.index]

	// Dive into nested navigators
	if (route.state) {
		return getActiveRouteName(route.state)
	}

	return route.name
}

export default {
	setGlobalRef,
	navigate,
	toggleDrawer,
	ResetNavigation,
	ResetNavigationTo,
	setCurrentRouteName,
	getCurrentRouteName,
	ResetNavigationHome,
	getActiveRouteName,
}
