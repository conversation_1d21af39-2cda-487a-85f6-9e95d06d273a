Object.defineProperty(exports, '__esModule', {
	value: true,
})
exports.default = void 0

var _native = require('@react-navigation/native')

var navObj = null

function setGlobalRef(ref) {
	navObj = ref
}

function navigate(path) {
	var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}
	navObj.navigate(path, props)
}

function toggleDrawer() {
	navObj.dispatch(_native.DrawerActions.toggleDrawer())
}

function ResetNavigation() {
	navObj.dispatch(
		_native.CommonActions.reset({
			index: 0,
			routes: [
				{
					name: 'Auth',
				},
			],
		})
	)
}

function ResetNavigationTo() {
	navObj.dispatch(
		_native.CommonActions.reset({
			index: 0,
			routes: [
				{
					name: 'NetworkNavigator',
				},
			],
		})
	)
}

var _default = {
	setGlobalRef: setGlobalRef,
	navigate: navigate,
	toggleDrawer: toggleDrawer,
	ResetNavigation: ResetNavigation,
	ResetNavigationTo: ResetNavigationTo,
}
exports.default = _default
