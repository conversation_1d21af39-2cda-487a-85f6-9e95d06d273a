const { withAppDelegate } = require('@expo/config-plugins');

module.exports = function withCustomAppDelegate(config) {
  return withAppDelegate(config, async config => {
    const appDelegate = config.modResults;
    
    // Ensure Firebase import is added
    if (!appDelegate.contents.includes('#import <Firebase.h>')) {
      appDelegate.contents = appDelegate.contents.replace(
        '#import <React/RCTLinkingManager.h>',
        '#import <React/RCTLinkingManager.h>\n#import <Firebase.h>'
      );
    }
    
    // Ensure Firebase is initialized
    if (!appDelegate.contents.includes('[FIRApp configure];')) {
      appDelegate.contents = appDelegate.contents.replace(
        '- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions\n{',
        '- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions\n{\n  // Initialize Firebase\n  [FIRApp configure];\n  '
      );
    }
    
    // Ensure module name is 'riverbank' not 'main'
    appDelegate.contents = appDelegate.contents.replace(
      'self.moduleName = @"main";',
      'self.moduleName = @"riverbank";'
    );
    
    return config;
  });
};