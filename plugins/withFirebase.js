const { withPlugins, withDangerousMod, withInfoPlist, withGradleProperties } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

function withFirebaseIOS(config) {
  return withInfoPlist(config, (config) => {
    // Firebase iOS configuration is handled by GoogleService-Info.plist
    return config;
  });
}

function withFirebaseAndroid(config) {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      const googleServicesPath = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'google-services.json'
      );
      
      // Copy google-services.json to android/app if it exists in root
      const sourceFile = path.join(config.modRequest.projectRoot, 'google-services.json');
      if (fs.existsSync(sourceFile)) {
        const targetDir = path.dirname(googleServicesPath);
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        fs.copyFileSync(sourceFile, googleServicesPath);
      }
      
      return config;
    },
  ]);
}

module.exports = function withFirebase(config) {
  return withPlugins(config, [
    withFirebaseIOS,
    withFirebaseAndroid,
  ]);
};