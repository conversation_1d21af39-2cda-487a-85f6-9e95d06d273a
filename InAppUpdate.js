import { useEffect, useRef, useState } from 'react'
import { Alert, AppState, Linking, NativeModules, Platform } from 'react-native'
import DeviceInfo from 'react-native-device-info'
const { InAppUpdate } = NativeModules

const InAppUpdateComponent = () => {
	const APP_STORE_ID = '**********'
	const currentVersion = DeviceInfo.getVersion()
	const packageName = 'com.riverbank'
	const rapidapi_key = '**************************************************'
	const appState = useRef(AppState.currentState)
	const [appStateVisible, setAppStateVisible] = useState(appState.current)
	const [alreadyPopup, setAlreadyPopup] = useState(false)
	//   console.log('NativeModules', InAppUpdate);

	async function checkAndroidUpdate() {
		const isEmulator = await DeviceInfo.isEmulator()
		if (Platform.OS !== 'ios') {
			if (isEmulator) {
				console.log('use physical android device to test updates')
				return
			}
			try {
				const result = await InAppUpdate.checkUpdate()
				console.log('checkUpdate success:', result) // Success message or status
			} catch (error) {
				console.log('checkUpdate error:', error) // Error message or status
			}
		}
	}

	async function checkIOSUpdate() {
		const isEmulator = await DeviceInfo.isEmulator()
		if (Platform.OS === 'ios') {
			if (isEmulator) {
				console.log('use physical ios device to test updates')
				return
			}
			const compareVersions = (currentVersion, appStoreVersion) => {
				console.log('currentVersion', currentVersion)
				console.log('appStoreVersion', appStoreVersion)
				if (currentVersion < appStoreVersion) {
					setAlreadyPopup(true)
					Alert.alert(
						'Update Available',
						'A new version of this app is available with exciting new features and improvements! Please update now to enjoy the latest version.',
						[
							{
								text: 'Update Now',
								onPress: () => {
									setAlreadyPopup(false)
									Linking.openURL(`itms-apps://itunes.apple.com/app/id${APP_STORE_ID}`)
								},
								style: 'cancel',
							},
							{
								text: 'Later', // Add a "Later" button to make it dismissible
								onPress: () => setAlreadyPopup(false),
								style: 'destructive',
							},
						],
						{ cancelable: true } // Makes the alert non-dismissible
					)
				} else {
					console.log('iOS App is up to date')
				}
			}
			const storeURL = Platform.select({
				ios: `itms-apps://apps.apple.com/id/app/riverbank-pro/id${APP_STORE_ID}`,
				// android: `market://details?id=${packageName}`,
			})
			const appInfoURL = Platform.select({
				ios: `https://itunes.apple.com/lookup?id=${APP_STORE_ID}`,
				// android: `https://play.google.com/store/apps/details?id=${packageName}`,
				android: 'https://google-play-store-scraper-api.p.rapidapi.com/app-details',
			})
			const options = {
				method: 'POST',
				headers: {
					'x-rapidapi-key': rapidapi_key,
					'x-rapidapi-host': 'google-play-store-scraper-api.p.rapidapi.com',
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					language: 'en',
					appID: packageName,
				}),
			}
			if (storeURL) {
				Linking.canOpenURL(storeURL)
					.then(async supported => {
						if (supported) {
							const response = await fetch(appInfoURL, options)
							const data = await response.json()
							if (data) {
								const appStoreVersion = Platform.select({
									ios: data?.results[0]?.version, // Get the version from App Store
									android: data?.data?.version, // Get the version from play Store
								})
								compareVersions(currentVersion, appStoreVersion)
							}
						} else {
							console.log('Not supported in simulator:', storeURL)
						}
					})
					.catch(err => console.error('An error occurred', err))
			}
		}
	}

	useEffect(() => {
		const timeout = setTimeout(() => {
			checkAndroidUpdate()
		}, 3000)
		return () => {
			clearTimeout(timeout)
		}
	}, [appStateVisible])

	useEffect(() => {
		const subscription = AppState.addEventListener('change', nextAppState => {
			if (nextAppState === 'active' && alreadyPopup === false) {
				checkIOSUpdate()
			}
			setAppStateVisible(nextAppState)
		})
		// Cleanup function
		return () => {
			subscription.remove()
		}
	}, [appStateVisible])

	return null
}

export default InAppUpdateComponent

// import {NativeModules} from 'react-native';
// const {InAppUpdate} = NativeModules;
// module.exports = InAppUpdate;
