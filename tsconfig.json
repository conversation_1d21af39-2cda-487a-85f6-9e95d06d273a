{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"strict": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowJs": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "lib": ["esnext"], "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true, "skipLibCheck": true, "target": "esnext"}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}