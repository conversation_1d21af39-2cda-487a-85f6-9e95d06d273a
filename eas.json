{"cli": {"version": ">= 5.0.0", "promptToConfigurePushNotifications": false}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"gradleCommand": ":app:assembleDebug"}, "env": {"NODE_ENV": "development"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk"}, "env": {"NODE_ENV": "production"}}, "production": {"ios": {"resourceClass": "m-medium"}, "android": {"buildType": "app-bundle"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./path-to-your-service-account-key.json", "track": "internal"}}}}