export default {
  name: "riverbank",
  slug: "riverbank",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/icon.png",
  userInterfaceStyle: "light",
  splash: {
    image: "./assets/splash.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  assetBundlePatterns: [
    "**/*"
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.app.riverbank",
    infoPlist: {
      NSCameraUsageDescription: "This app uses the camera to capture photos and documents for banking transactions and verification purposes.",
      NSPhotoLibraryUsageDescription: "This app accesses your photo library to select images and documents for banking transactions and verification purposes.",
      NSLocationAlwaysAndWhenInUseUsageDescription: "Allow riverbank to access your location",
      NSLocationAlwaysUsageDescription: "Allow riverbank to access your location",
      NSLocationWhenInUseUsageDescription: "Allow riverbank to access your location"
    },
    config: {
      googleMapsApiKey: "AIzaSyCesRg8-LDizp8FO4XuSrtY56F6sUdvW64"
    },
    googleServicesFile: "./GoogleService-Info.plist"
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/icon.png",
      backgroundColor: "#ffffff"
    },
    package: "com.riverbank",
    googleServicesFile: "./google-services.json",
    permissions: [
      "CAMERA",
      "ACCESS_FINE_LOCATION",
      "ACCESS_COARSE_LOCATION",
      "READ_EXTERNAL_STORAGE",
      "WRITE_EXTERNAL_STORAGE"
    ],
    config: {
      googleMaps: {
        apiKey: "AIzaSyCesRg8-LDizp8FO4XuSrtY56F6sUdvW64"
      }
    }
  },
  extra: {
    eas: {
      projectId: "your-project-id"
    }
  },
  plugins: [
    "./plugins/withFirebase",
    [
      "expo-location",
      {
        locationAlwaysAndWhenInUsePermission: "Allow riverbank to use your location."
      }
    ],
    [
      "expo-document-picker",
      {
        iCloudContainerEnvironment: "Production"
      }
    ],
    [
      "expo-image-picker",
      {
        photosPermission: "The app accesses your photos to let you share them.",
        cameraPermission: "The app accesses your camera to let you take photos."
      }
    ],
    "expo-splash-screen",
    "expo-dev-client"
  ],
  scheme: "riverbank"
}