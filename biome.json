{"$schema": "https://biomejs.dev/schemas/2.0.5/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/app/**/*.{tsx,ts,json}", "!**/ios", "!**/android", "!**/node_modules/", "!**/babel.config.js", "!**/metro.config.js"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 160, "formatWithErrors": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": {"level": "warn", "options": {"attributes": ["className"], "functions": ["cn", "cva"]}}}, "correctness": {"noUnusedImports": "warn", "noUnusedFunctionParameters": "warn", "noUnusedVariables": "warn", "useExhaustiveDependencies": "off", "noInvalidUseBeforeDeclaration": "warn", "noUnsafeOptionalChaining": "warn", "noUnsafeFinally": "warn"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off", "noAssignInExpressions": "warn", "noConsole": "off", "noAsyncPromiseExecutor": "off"}, "security": {"noDangerouslySetInnerHtml": "warn", "noDangerouslySetInnerHtmlWithChildren": "warn"}, "a11y": {"useKeyWithClickEvents": "warn", "noLabelWithoutControl": "warn", "useAltText": "warn", "useButtonType": "warn", "useIframeTitle": "warn", "noSvgWithoutTitle": "warn"}, "complexity": {"noForEach": "warn", "noStaticOnlyClass": "off"}, "style": {"noParameterAssign": "warn", "noNonNullAssertion": "warn", "useNodejsImportProtocol": "off", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "arrowParentheses": "asNeeded", "jsxQuoteStyle": "double", "attributePosition": "multiline"}}, "json": {"parser": {"allowComments": true, "allowTrailingCommas": true}}}