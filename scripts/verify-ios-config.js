#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying iOS configuration...');

const appDelegatePath = path.join(__dirname, '../ios/riverbank/AppDelegate.mm');

if (fs.existsSync(appDelegatePath)) {
  let content = fs.readFileSync(appDelegatePath, 'utf-8');
  let modified = false;

  // Check and fix module name
  if (content.includes('self.moduleName = @"main"')) {
    content = content.replace('self.moduleName = @"main"', 'self.moduleName = @"riverbank"');
    modified = true;
    console.log('✅ Fixed module name from "main" to "riverbank"');
  }

  // Check for Firebase import
  if (!content.includes('#import <Firebase.h>')) {
    content = content.replace(
      '#import <React/RCTLinkingManager.h>',
      '#import <React/RCTLinkingManager.h>\n#import <Firebase.h>'
    );
    modified = true;
    console.log('✅ Added Firebase import');
  }

  // Check for Firebase initialization
  if (!content.includes('[FIRApp configure];')) {
    const pattern = /- \(BOOL\)application:\(UIApplication \*\)application didFinishLaunchingWithOptions:\(NSDictionary \*\)launchOptions\n\{/;
    content = content.replace(
      pattern,
      '- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions\n{\n  // Initialize Firebase\n  [FIRApp configure];\n  '
    );
    modified = true;
    console.log('✅ Added Firebase initialization');
  }

  if (modified) {
    fs.writeFileSync(appDelegatePath, content, 'utf-8');
    console.log('✅ AppDelegate.mm has been updated');
  } else {
    console.log('✅ AppDelegate.mm is already configured correctly');
  }
} else {
  console.log('⚠️  AppDelegate.mm not found. Run expo prebuild first.');
}

console.log('✅ iOS configuration verified!');