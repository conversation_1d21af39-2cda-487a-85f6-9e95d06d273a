#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('Running iOS module fix script...');

// Fix React-RuntimeApple.modulemap conflict
const runtimeAppleModulemap = path.resolve('./ios/Pods/Target Support Files/React-RuntimeApple/React-RuntimeApple.modulemap');
const reactCommonModulemap = path.resolve('./ios/Pods/Target Support Files/ReactCommon/ReactCommon.modulemap');
const publicReactCommonModulemap = path.resolve('./ios/Pods/Headers/Public/ReactCommon/ReactCommon.modulemap');
const publicRuntimeAppleModulemap = path.resolve('./ios/Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap');

try {
  // Fix React-RuntimeApple modulemap
  if (fs.existsSync(runtimeAppleModulemap)) {
    let content = fs.readFileSync(runtimeAppleModulemap, 'utf-8');
    if (content.includes('module ReactCommon')) {
      content = content.replace('module ReactCommon', 'module React_RuntimeApple');
      fs.writeFileSync(runtimeAppleModulemap, content, 'utf-8');
      console.log('✅ Fixed React-RuntimeApple.modulemap');
    }
  }

  // Remove exclude header from ReactCommon modulemap
  if (fs.existsSync(reactCommonModulemap)) {
    let content = fs.readFileSync(reactCommonModulemap, 'utf-8');
    content = content.replace(/exclude header "React-RuntimeApple-umbrella.h"\n/g, '');
    fs.writeFileSync(reactCommonModulemap, content, 'utf-8');
    console.log('✅ Fixed ReactCommon.modulemap');
  }

  // Fix public ReactCommon modulemap
  if (fs.existsSync(publicReactCommonModulemap)) {
    let content = fs.readFileSync(publicReactCommonModulemap, 'utf-8');
    content = content.replace(/exclude header "React-RuntimeApple-umbrella.h"\n/g, '');
    fs.writeFileSync(publicReactCommonModulemap, content, 'utf-8');
    console.log('✅ Fixed Public ReactCommon.modulemap');
  }

  // Remove conflicting React-RuntimeApple.modulemap from ReactCommon directory
  if (fs.existsSync(publicRuntimeAppleModulemap)) {
    fs.unlinkSync(publicRuntimeAppleModulemap);
    console.log('✅ Removed conflicting React-RuntimeApple.modulemap from ReactCommon directory');
  }

  // Remove conflicting umbrella header symlink
  const conflictingUmbrellaHeader = path.resolve('./ios/Pods/Headers/Public/ReactCommon/React-RuntimeApple-umbrella.h');
  if (fs.existsSync(conflictingUmbrellaHeader)) {
    fs.unlinkSync(conflictingUmbrellaHeader);
    console.log('✅ Removed conflicting React-RuntimeApple-umbrella.h symlink');
  }

  console.log('✅ iOS module fixes applied successfully!');
} catch (error) {
  console.error('❌ Error fixing iOS modules:', error);
  process.exit(1);
}